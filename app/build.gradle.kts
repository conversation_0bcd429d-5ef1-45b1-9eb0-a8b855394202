import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.konan.properties.Properties
import org.jetbrains.kotlin.konan.properties.loadProperties

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    alias(libs.plugins.jetbrains.kotlin.serialization)
    alias(libs.plugins.ktlint.gradle)
    alias(libs.plugins.devtools.ksp)
}

val localProperties: Properties = loadProperties(rootProject.file("local.properties").path)
ksp {
    arg("room.incremental", "true")
    arg("room.schemaLocation", "$projectDir/schemas")
}
android {
    namespace = "com.buque.wakoo"
    compileSdk = 36

    defaultConfig {
        applicationId = "com.site.wakoo"
        minSdk = 28
        targetSdk = 35
        versionCode = 1
        versionName = "1.0.5"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        // -- 注入开发环境的所有配置 --
        buildConfigField("String", "APP_DEV_NAME", "${localProperties.getProperty("APP_DEV_NAME")}")
        buildConfigField("String", "APP_DEV_API_URL", "${localProperties.getProperty("APP_DEV_API_URL")}")
        buildConfigField(
            "String",
            "APP_DEV_API_SIGN_ACCESS_KEY",
            "${localProperties.getProperty("APP_DEV_API_SIGN_ACCESS_KEY")}",
        )
        buildConfigField(
            "String",
            "APP_DEV_API_SIGN_SECRET_KEY",
            "${localProperties.getProperty("APP_DEV_API_SIGN_SECRET_KEY")}",
        )
        buildConfigField(
            "String",
            "APP_DEV_SM_ORGANIZATION",
            "${localProperties.getProperty("APP_DEV_SM_ORGANIZATION")}",
        )
        buildConfigField("String", "APP_DEV_SM_APP_ID", "${localProperties.getProperty("APP_DEV_SM_APP_ID")}")
        buildConfigField("String", "APP_DEV_SM_PUBLISH_KEY", "${localProperties.getProperty("APP_DEV_SM_PUBLISH_KEY")}")
        buildConfigField(
            "boolean",
            "APP_DEV_ENABLE_ANALYTICS",
            localProperties.getProperty("APP_DEV_ENABLE_ANALYTICS").toString(),
        )
        buildConfigField("boolean", "APP_DEV_ENABLE_LOG", localProperties.getProperty("APP_DEV_ENABLE_LOG").toString())
        buildConfigField(
            "String",
            "APP_DEV_GOOGLE_WEB_CLIENT_ID",
            "${localProperties.getProperty("APP_DEV_GOOGLE_WEB_CLIENT_ID")}",
        )
        buildConfigField("Integer", "APP_DEV_TENCENT_APPID", localProperties.getProperty("APP_DEV_TENCENT_APPID"))
        buildConfigField("String", "APP_DEV_TENCENT_APPKEY", localProperties.getProperty("APP_DEV_TENCENT_APPKEY"))
        buildConfigField(
            "Integer",
            "APP_DEV_TENCENT_RTC_APPID",
            localProperties.getProperty("APP_DEV_TENCENT_RTC_APPID"),
        )

        // -- 注入生产环境的所有配置 --
        buildConfigField("String", "APP_PROD_NAME", "${localProperties.getProperty("APP_PROD_NAME")}")
        buildConfigField("String", "APP_PROD_API_URL", "${localProperties.getProperty("APP_PROD_API_URL")}")
        buildConfigField(
            "String",
            "APP_PROD_API_SIGN_ACCESS_KEY",
            "${localProperties.getProperty("APP_PROD_API_SIGN_ACCESS_KEY")}",
        )
        buildConfigField(
            "String",
            "APP_PROD_API_SIGN_SECRET_KEY",
            "${localProperties.getProperty("APP_PROD_API_SIGN_SECRET_KEY")}",
        )
        buildConfigField(
            "String",
            "APP_PROD_SM_ORGANIZATION",
            "${localProperties.getProperty("APP_PROD_SM_ORGANIZATION")}",
        )
        buildConfigField("String", "APP_PROD_SM_APP_ID", "${localProperties.getProperty("APP_PROD_SM_APP_ID")}")
        buildConfigField(
            "String",
            "APP_PROD_SM_PUBLISH_KEY",
            "${localProperties.getProperty("APP_PROD_SM_PUBLISH_KEY")}",
        )
        buildConfigField(
            "boolean",
            "APP_PROD_ENABLE_ANALYTICS",
            localProperties.getProperty("APP_PROD_ENABLE_ANALYTICS").toString(),
        )
        buildConfigField(
            "boolean",
            "APP_PROD_ENABLE_LOG",
            localProperties.getProperty("APP_PROD_ENABLE_LOG").toString(),
        )
        buildConfigField(
            "String",
            "APP_PROD_GOOGLE_WEB_CLIENT_ID",
            "${localProperties.getProperty("APP_PROD_GOOGLE_WEB_CLIENT_ID")}",
        )
        buildConfigField("Integer", "APP_PROD_TENCENT_APPID", localProperties.getProperty("APP_PROD_TENCENT_APPID"))
        buildConfigField("String", "APP_PROD_TENCENT_APPKEY", localProperties.getProperty("APP_PROD_TENCENT_APPKEY"))
        buildConfigField(
            "Integer",
            "APP_PROD_TENCENT_RTC_APPID",
            localProperties.getProperty("APP_PROD_TENCENT_RTC_APPID"),
        )
    }

    signingConfigs {
        create("test") {
            storeFile = file("../app-test.jks")
            storePassword = "12345678"
            keyAlias = "test"
            keyPassword = "12345678"
        }
        create("release") {
            storeFile = file("../app.jks")
            storePassword = localProperties.getProperty("SIGNING_PWD")
            keyAlias = localProperties.getProperty("SIGNING_ALIAS")
            keyPassword = localProperties.getProperty("SIGNING_PWD")
        }
    }

    buildTypes {

        debug {
            // 是否可调试：允许连接调试器
            isDebuggable = true
            applicationIdSuffix = ".debug"

            // 代码和资源优化：通常不进行混淆和资源压缩，方便调试
            isMinifyEnabled = false
            isShrinkResources = false

            manifestPlaceholders["appLabel"] = "wakoo-debug"

            signingConfig = signingConfigs.getByName("test")
        }

        create("profile") {
            initWith(getByName("release"))
            // 是否可调试：禁用，生产环境不可调试
            isDebuggable = false
            applicationIdSuffix = ".profile"

            manifestPlaceholders["appLabel"] = "wakoo-profile"

            // isDebuggable = true // 如果需要旧设备上的性能分析，但会降低真实性
            isProfileable = true // 推荐：用于 Android 10+ 设备的低开销性能分析

            signingConfig = signingConfigs.getByName("test")

            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro",
            )
        }

        release {
            // 是否可调试：禁用，生产环境不可调试
            isDebuggable = false

            // 代码和资源优化：启用混淆、资源压缩，减小包体积，提高安全性
            isMinifyEnabled = true // 启用代码和资源混淆/优化
            isShrinkResources = true // 移除未使用的资源文件

            manifestPlaceholders["appLabel"] = "wakoo"

            signingConfig = signingConfigs.getByName("release")

            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro",
            )
        }
    }

    flavorDimensions += "environment"

    productFlavors {
        create("development") {
            dimension = "environment"
        }

        create("production") {
            dimension = "environment"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlin {
        compilerOptions {
            freeCompilerArgs.add("-Xcontext-parameters")
            freeCompilerArgs.add("-opt-in=androidx.compose.material3.ExperimentalMaterial3Api")
            jvmTarget.set(JvmTarget.JVM_11)
        }
    }

    buildFeatures {
        compose = true
        buildConfig = true
    }
    kotlinter {
        ignoreFormatFailures = true
        ignoreLintFailures = false
        reporters = arrayOf("checkstyle")
    }

    packaging {
        jniLibs.pickFirsts.add("**/libc++_shared.so")
    }
}

dependencies {

    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar", "*.aar"))))

    implementation(libs.androidx.core.ktx)
//    implementation(libs.androidx.core.splashscreen)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.lifecycle.process)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)

    // Coil - 图片加载库
    implementation(libs.coil.compose)
    implementation(libs.coil.okhttp)
    implementation(libs.coil.gif)

    // Retrofit & OkHttp - 网络请求
    implementation(libs.retrofit.core)
    implementation(libs.retrofit.converter)
    implementation(libs.okhttp.core)
    implementation(libs.okhttp.logging)

    // navigation3
    implementation(libs.androidx.navigation3.runtime)
    implementation(libs.androidx.navigation3.ui)
    implementation(libs.androidx.lifecycle.viewmodel.navigation3)
//    implementation(libs.androidx.material3.adaptive.navigation3)
    implementation(libs.kotlinx.serialization.json)

    // mmkv
    implementation(libs.tencent.mmkv)

    // 日志框架
    implementation(libs.timber)

    implementation("io.github.khubaibkhan4:alert-kmp-android:2.0.0")
    implementation("org.jetbrains.kotlinx:kotlinx-datetime:0.6.2")
    implementation("com.qcloud.cos:cos-android-lite:5.9.45")

    // 支付
    implementation(libs.google.billing)
    implementation(libs.google.billing.ktx)

    // Google Login
    implementation(libs.androidx.credentials)
    implementation(libs.androidx.credentials.play.services.auth)
    implementation(libs.googleid)
    api(project(":lib-webview"))

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)

    // Tencent IM
    implementation(libs.imsdk.plus)
    implementation(libs.timquic.plugin)
    implementation(libs.timui.core)
    implementation(libs.timpush)
    implementation(libs.tencent.av)

    implementation("org.jetbrains.kotlin:kotlin-reflect:2.1.21")
    implementation("me.saket.telephoto:zoomable-image-coil3:0.16.0")
    implementation("me.saket.telephoto:zoomable:0.16.0")
    implementation("me.saket.telephoto:zoomable-peek-overlay:0.16.0")
    implementation("androidx.media3:media3-ui-compose:1.7.1")
    implementation("androidx.media3:media3-exoplayer:1.7.1")
//    implementation(libs.timpush.fcm)

    // room数据库
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx)
    ksp(libs.androidx.room.compiler)

    implementation(project(":gift"))

    ktlint(libs.ktlint.compose.rules)
}
