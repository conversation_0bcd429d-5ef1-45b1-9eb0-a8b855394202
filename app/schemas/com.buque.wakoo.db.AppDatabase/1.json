{"formatVersion": 1, "database": {"version": 1, "identityHash": "d2039355b2d2ceeca6ad7e2da1f8de6d", "entities": [{"tableName": "users", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userid` TEXT NOT NULL, `public_id` TEXT NOT NULL, `nickname` TEXT NOT NULL, `avatar_url` TEXT NOT NULL, `gender` INTEGER NOT NULL, `age` INTEGER NOT NULL, `birthday` TEXT NOT NULL, `is_member` INTEGER NOT NULL, `level` INTEGER NOT NULL, `follow_cnt` INTEGER NOT NULL, `fans_cnt` INTEGER NOT NULL, `type` INTEGER NOT NULL, PRIMARY KEY(`userid`))", "fields": [{"fieldPath": "id", "columnName": "userid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "publishId", "columnName": "public_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "nickname", "affinity": "TEXT", "notNull": true}, {"fieldPath": "avatar", "columnName": "avatar_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "gender", "columnName": "gender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "age", "columnName": "age", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "birthday", "columnName": "birthday", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isVip", "columnName": "is_member", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "level", "columnName": "level", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "followingCount", "columnName": "follow_cnt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "followersCount", "columnName": "fans_cnt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["userid"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'd2039355b2d2ceeca6ad7e2da1f8de6d')"]}}