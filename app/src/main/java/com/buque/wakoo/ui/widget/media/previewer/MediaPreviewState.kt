package com.buque.wakoo.ui.widget.media.previewer

import androidx.activity.compose.BackHandler
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.VectorConverter
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.toSize
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

/**
 * 核心状态类，管理共享元素转场的所有状态和动画逻辑。
 *
 * @param coroutineScope 用于启动动画协程的作用域。
 * @param imageBoundsMap 用于存储网格中每个媒体项在屏幕根坐标系下的位置和尺寸。
 */
@Stable
class MediaPreviewState(
    private val coroutineScope: CoroutineScope,
    private val imageBoundsMap: MutableMap<IndexedMediaItem, Rect>,
) {
    private var boxWidth by mutableIntStateOf(0)
    private var boxHeight by mutableIntStateOf(0)

    /** 从媒体查看器（Pager）获取当前页面索引的回调函数。 */
    var currentPageIndex: (() -> Int)? = null

    var currentPageOffset: (() -> Offset)? = null

    /** 当前的转场状态，驱动UI的显示/隐藏和动画。 */
    var transitionState by mutableStateOf<TransitionState>(TransitionState.Idle)
        private set

    /** 可动画化的矩形边界，用于在初始和目标位置之间平滑地创建动画。 */
    val animatableBounds = Animatable(Rect.Zero, Rect.VectorConverter)

    val animatableRadius = Animatable(0f)

    val animatableFraction = mutableFloatStateOf(0f)

    /**
     * 一个 Modifier，应用于网格中的每个媒体项。
     * 它负责记录该项在屏幕上的边界，并根据转场状态控制其透明度，
     * 以在动画期间隐藏原始项，避免视觉上出现两个元素。
     *
     * @param item 带索引的媒体项。
     */
    fun Modifier.registerGridItem(item: IndexedMediaItem) =
        this.composed(factory = {
            val hidden by remember(item) {
                derivedStateOf {
                    // 判断当前项是否应被隐藏。
                    when (val currentState = transitionState) {
                        is TransitionState.Enter -> if (currentState.anim <= AnimState.Running) null else currentState.indexItem
                        is TransitionState.Exit -> if (currentState.anim >= AnimState.Finished) null else currentState.indexItem
                        else -> null
                    } == item
                }
            }

            // 当项从Compose树中移除时，清理其边界数据以防内存泄漏。
            DisposableEffect(Unit) {
                onDispose {
                    imageBoundsMap.remove(item)
                }
            }
            Modifier
                .onGloballyPositioned { coordinates ->
                    // 实时更新项在屏幕上的位置和尺寸。
                    val position = coordinates.positionInRoot()
                    imageBoundsMap[item] = Rect(position, coordinates.size.toSize())
                }.alpha(if (!hidden) 1f else 0f)
        })

    /**
     * 一个 Composable，用于从其布局约束中获取并更新容器的尺寸。
     * 这个尺寸对于计算动画的目标边界至关重要。
     */
    @Composable
    fun UpdateContainerConstraints(constraints: Constraints) {
        this.boxWidth = constraints.maxWidth
        this.boxHeight = constraints.maxHeight
    }

    /**
     * 一个 Composable，负责管理转场动画的状态机演进和处理返回按钮事件。
     * 它通过 LaunchedEffect 驱动动画状态从一个阶段流转到下一个阶段。
     */
    @Composable
    fun ManageTransitionLifecycle() {
        val currentState = transitionState

        // 状态机演进：处理进入动画的状态流转。
        if (currentState is TransitionState.Enter && (currentState.anim == AnimState.Ready || currentState.anim == AnimState.Finished)) {
            LaunchedEffect(currentState) {
                val current = transitionState
                if (current is TransitionState.Enter) {
                    if (current.anim == AnimState.Ready) {
                        transitionState = current.copy(anim = AnimState.Running)
                    } else if (current.anim == AnimState.Finished) {
                        transitionState = current.copy(anim = AnimState.End)
                    }
                }
            }
        } else if (currentState is TransitionState.Exit &&
            (currentState.anim == AnimState.Ready || currentState.anim == AnimState.Finished || currentState.anim == AnimState.End)
        ) {
            LaunchedEffect(currentState) {
                val current = transitionState
                if (current is TransitionState.Exit) {
                    if (current.anim == AnimState.Ready) {
                        transitionState = current.copy(anim = AnimState.Running)
                    } else if (current.anim == AnimState.Finished) {
                        transitionState = current.copy(anim = AnimState.End)
                    } else if (current.anim == AnimState.End) {
                        transitionState = TransitionState.Idle
                    }
                }
            }
        }

        // 拦截系统的返回操作，以触发自定义的退出动画。
        BackHandler { exitPreview() }
    }

    fun enterPreview(
        key: MediaViewerKey,
        radius: Float = 0f,
    ) {
        if (transitionState != TransitionState.Idle) return
        coroutineScope.launch {
            val currentState = transitionState
            if (currentState is TransitionState.Idle) {
                val initialItem = key.initialItem

                val containerWidth = boxWidth.toFloat()
                val containerHeight = boxHeight.toFloat()

                val startBounds =
                    imageBoundsMap[initialItem] ?: Rect(Offset(containerWidth / 2, containerHeight / 2), 0f)
                val imageAspectRatio = initialItem.item.aspectRatio
                val containerAspectRatio = containerWidth / containerHeight

                val targetWidth: Float
                val targetHeight: Float

                if (imageAspectRatio > containerAspectRatio) {
                    targetWidth = containerWidth
                    targetHeight = containerWidth / imageAspectRatio
                } else {
                    targetHeight = containerHeight
                    targetWidth = containerHeight * imageAspectRatio
                }

                val targetOffsetX = (containerWidth - targetWidth) / 2
                val targetOffsetY = (containerHeight - targetHeight) / 2

                val targetBounds =
                    Rect(
                        left = targetOffsetX,
                        top = targetOffsetY,
                        right = targetOffsetX + targetWidth,
                        bottom = targetOffsetY + targetHeight,
                    )
                transitionState = TransitionState.Enter(key, radius)
                animatableBounds.snapTo(startBounds)

                if (radius > 0f) {
                    launch {
                        animatableRadius.snapTo(radius)
                        animatableRadius.animateTo(0f, animationSpec = spring(stiffness = 250f))
                    }
                }

                animatableBounds.animateTo(
                    targetValue = targetBounds,
                    animationSpec = sharedElementTransitionSpring(),
                ) {
                    animatableFraction.floatValue =
                        (this.value.height - startBounds.height) / (targetHeight - startBounds.height)
                }
                val current = transitionState
                if (current is TransitionState.Enter) {
                    transitionState = current.copy(anim = AnimState.Finished)
                }
            }
        }
    }

    fun exitPreview() {
        if (transitionState !is TransitionState.Enter) return

        coroutineScope.launch {
            val currentState = transitionState
            if (currentState is TransitionState.Enter) {
                val indexItem =
                    currentPageIndex?.invoke()?.let { IndexedMediaItem(it, currentState.key.album.items[it]) }
                        ?: currentState.indexItem

                val containerWidth = boxWidth.toFloat()
                val containerHeight = boxHeight.toFloat()

                val imageAspectRatio = indexItem.item.aspectRatio
                val containerAspectRatio = containerWidth / containerHeight

                val startWidth: Float
                val startHeight: Float

                if (imageAspectRatio > containerAspectRatio) {
                    startWidth = containerWidth
                    startHeight = containerWidth / imageAspectRatio
                } else {
                    startHeight = containerHeight
                    startWidth = containerHeight * imageAspectRatio
                }

                val offset = currentPageOffset?.invoke() ?: Offset.Zero

                val startOffsetX = (containerWidth - startWidth) / 2 + offset.x
                val startOffsetY = (containerHeight - startHeight) / 2 + offset.y

                val startBounds =
                    Rect(
                        left = startOffsetX,
                        top = startOffsetY,
                        right = startOffsetX + startWidth,
                        bottom = startOffsetY + startHeight,
                    )

                val targetBounds =
                    imageBoundsMap[indexItem]?.takeIf {
                        isRectVisible(it)
                    } ?: Rect(Offset(containerWidth / 2, containerHeight / 2), 0f)

                if (!animatableBounds.isRunning) {
                    transitionState = TransitionState.Exit(currentState.key, currentState.radius, indexItem)
                    animatableBounds.snapTo(startBounds)
                } else {
                    transitionState = TransitionState.Exit(currentState.key, currentState.radius, indexItem)
                }

                if (currentState.radius > 0f) {
                    launch {
                        animatableRadius.snapTo(0f)
                        animatableRadius.animateTo(currentState.radius, animationSpec = spring(stiffness = 250f))
                    }
                }

                animatableBounds.animateTo(
                    targetValue = targetBounds,
                    animationSpec = sharedElementTransitionSpring(),
                ) {
                    animatableFraction.floatValue =
                        (this.value.height - startBounds.height) / (targetBounds.height - startBounds.height)
                }

                val current = transitionState
                if (current is TransitionState.Exit) {
                    transitionState = current.copy(anim = AnimState.Finished)
                }
            }
        }
    }

    private fun isRectVisible(rect: Rect): Boolean = rect.width > 0 && rect.height > 0
}

@Composable
fun rememberPreviewState(): MediaPreviewState {
    val coroutineScope = rememberCoroutineScope()
    return remember(coroutineScope) {
        MediaPreviewState(coroutineScope, mutableStateMapOf())
    }
}

// --- 以下所有状态定义都严格按照您的原始代码保留 ---

@Serializable
enum class AnimState {
    Ready, // 准备
    Running, // 运行中
    Finished, // 动画刚执行结束
    End, // 动画完成
}

@Serializable
sealed interface TransitionState {
    @Serializable
    object Idle : TransitionState

    @Serializable
    sealed interface Transiting : TransitionState {
        val key: MediaViewerKey
        val indexItem: IndexedMediaItem
        val anim: AnimState
        val radius: Float
    }

    @Serializable
    data class Enter(
        override val key: MediaViewerKey,
        override val radius: Float,
        override val anim: AnimState = AnimState.Ready,
    ) : Transiting {
        override val indexItem: IndexedMediaItem = key.initialItem
    }

    @Serializable
    data class Exit(
        override val key: MediaViewerKey,
        override val radius: Float,
        override val indexItem: IndexedMediaItem,
        override val anim: AnimState = AnimState.Ready,
    ) : Transiting
}

private fun <T> sharedElementTransitionSpring() = tween<T>(300)
