package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.StarFill: ImageVector
    get() {
        val current = _starFill
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.StarFill",
                defaultWidth = 29.0.dp,
                defaultHeight = 28.0.dp,
                viewportWidth = 29.0f,
                viewportHeight = 28.0f,
            ).apply {
                // m14.5 21.3 -8.23 4.61 1.84 -9.25 -6.92 -6.4 9.36 -1.11 3.95 -8.57 3.95 8.57 9.37 1.1 -6.93 6.41 1.84 9.25z
                path(
                    fill =
                        Brush.linearGradient(
                            0.0f to Color(0xFFA3FF2C),
                            1.0f to Color(0xFF31FFA1),
                            start = Offset(x = 1.186f, y = 13.247f),
                            end = Offset(x = 27.815f, y = 13.247f),
                        ),
                ) {
                    // M 14.5 21.3
                    moveTo(x = 14.5f, y = 21.3f)
                    // l -8.23 4.61
                    lineToRelative(dx = -8.23f, dy = 4.61f)
                    // l 1.84 -9.25
                    lineToRelative(dx = 1.84f, dy = -9.25f)
                    // l -6.92 -6.4
                    lineToRelative(dx = -6.92f, dy = -6.4f)
                    // l 9.36 -1.11
                    lineToRelative(dx = 9.36f, dy = -1.11f)
                    // l 3.95 -8.57
                    lineToRelative(dx = 3.95f, dy = -8.57f)
                    // l 3.95 8.57
                    lineToRelative(dx = 3.95f, dy = 8.57f)
                    // l 9.37 1.1
                    lineToRelative(dx = 9.37f, dy = 1.1f)
                    // l -6.93 6.41
                    lineToRelative(dx = -6.93f, dy = 6.41f)
                    // l 1.84 9.25z
                    lineToRelative(dx = 1.84f, dy = 9.25f)
                    close()
                }
            }.build()
            .also { _starFill = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.StarFill,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((29.0).dp)
                        .height((28.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _starFill: ImageVector? = null
