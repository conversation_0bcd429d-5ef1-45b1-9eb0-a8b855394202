package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.MicLine: ImageVector
    get() {
        if (_MicLine != null) {
            return _MicLine!!
        }
        _MicLine =
            ImageVector
                .Builder(
                    name = "<PERSON><PERSON><PERSON><PERSON>",
                    defaultWidth = 32.dp,
                    defaultHeight = 32.dp,
                    viewportWidth = 32f,
                    viewportHeight = 32f,
                ).apply {
                    path(fill = SolidColor(Color(0xFF111111))) {
                        moveTo(16f, 3.999f)
                        curveTo(13.791f, 3.999f, 12f, 5.79f, 12f, 7.999f)
                        verticalLineTo(13.332f)
                        curveTo(12f, 15.541f, 13.791f, 17.332f, 16f, 17.332f)
                        curveTo(18.209f, 17.332f, 20f, 15.541f, 20f, 13.332f)
                        verticalLineTo(7.999f)
                        curveTo(20f, 5.79f, 18.209f, 3.999f, 16f, 3.999f)
                        close()
                        moveTo(16f, 1.332f)
                        curveTo(19.682f, 1.332f, 22.667f, 4.317f, 22.667f, 7.999f)
                        verticalLineTo(13.332f)
                        curveTo(22.667f, 17.014f, 19.682f, 19.999f, 16f, 19.999f)
                        curveTo(12.318f, 19.999f, 9.333f, 17.014f, 9.333f, 13.332f)
                        verticalLineTo(7.999f)
                        curveTo(9.333f, 4.317f, 12.318f, 1.332f, 16f, 1.332f)
                        close()
                        moveTo(4.073f, 14.665f)
                        horizontalLineTo(6.761f)
                        curveTo(7.408f, 19.188f, 11.298f, 22.665f, 16f, 22.665f)
                        curveTo(20.702f, 22.665f, 24.591f, 19.188f, 25.239f, 14.665f)
                        horizontalLineTo(27.927f)
                        curveTo(27.312f, 20.228f, 22.895f, 24.644f, 17.333f, 25.259f)
                        verticalLineTo(30.665f)
                        horizontalLineTo(14.667f)
                        verticalLineTo(25.259f)
                        curveTo(9.104f, 24.644f, 4.688f, 20.228f, 4.073f, 14.665f)
                        close()
                    }
                }.build()

        return _MicLine!!
    }

@Suppress("ObjectPropertyName")
private var _MicLine: ImageVector? = null
