package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.MicOff: ImageVector
    get() {
        val current = _MicOff
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.MicOff",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // M7.58 17.84 2.8 22.6 l-1.42 -1.42 L21.2 1.4 l1.42 1.42 L17 8.4 V10 a5 5 0 0 1 -6.39 4.8 l-1.55 1.55 A7 7 0 0 0 18.93 11 h2.01 A9 9 0 0 1 13 18.95 V23 h-2 v-4.05 a9 9 0 0 1 -3.42 -1.11 m4.87 -4.87 a3 3 0 0 0 2.52 -2.52z m-7.82 2.19 1.44 -1.44 a7 7 0 0 1 -1 -2.72 H3.05 a9 9 0 0 0 1.58 4.16 m2.9 -2.9 1.55 -1.56 A3 3 0 0 1 9 10 V6 a3 3 0 0 1 5.82 -1.03 l1.5 -1.5 A5 5 0 0 0 7 6 v4 q.02 1.22 .53 2.25
                path(
                    fill = SolidColor(Color(0xFFFFFFFF)),
                ) {
                    // M 7.58 17.84
                    moveTo(x = 7.58f, y = 17.84f)
                    // L 2.8 22.6
                    lineTo(x = 2.8f, y = 22.6f)
                    // l -1.42 -1.42
                    lineToRelative(dx = -1.42f, dy = -1.42f)
                    // L 21.2 1.4
                    lineTo(x = 21.2f, y = 1.4f)
                    // l 1.42 1.42
                    lineToRelative(dx = 1.42f, dy = 1.42f)
                    // L 17 8.4
                    lineTo(x = 17.0f, y = 8.4f)
                    // V 10
                    verticalLineTo(y = 10.0f)
                    // a 5 5 0 0 1 -6.39 4.8
                    arcToRelative(
                        a = 5.0f,
                        b = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -6.39f,
                        dy1 = 4.8f,
                    )
                    // l -1.55 1.55
                    lineToRelative(dx = -1.55f, dy = 1.55f)
                    // A 7 7 0 0 0 18.93 11
                    arcTo(
                        horizontalEllipseRadius = 7.0f,
                        verticalEllipseRadius = 7.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        x1 = 18.93f,
                        y1 = 11.0f,
                    )
                    // h 2.01
                    horizontalLineToRelative(dx = 2.01f)
                    // A 9 9 0 0 1 13 18.95
                    arcTo(
                        horizontalEllipseRadius = 9.0f,
                        verticalEllipseRadius = 9.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 13.0f,
                        y1 = 18.95f,
                    )
                    // V 23
                    verticalLineTo(y = 23.0f)
                    // h -2
                    horizontalLineToRelative(dx = -2.0f)
                    // v -4.05
                    verticalLineToRelative(dy = -4.05f)
                    // a 9 9 0 0 1 -3.42 -1.11
                    arcToRelative(
                        a = 9.0f,
                        b = 9.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -3.42f,
                        dy1 = -1.11f,
                    )
                    // m 4.87 -4.87
                    moveToRelative(dx = 4.87f, dy = -4.87f)
                    // a 3 3 0 0 0 2.52 -2.52z
                    arcToRelative(
                        a = 3.0f,
                        b = 3.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 2.52f,
                        dy1 = -2.52f,
                    )
                    close()
                    // m -7.82 2.19
                    moveToRelative(dx = -7.82f, dy = 2.19f)
                    // l 1.44 -1.44
                    lineToRelative(dx = 1.44f, dy = -1.44f)
                    // a 7 7 0 0 1 -1 -2.72
                    arcToRelative(
                        a = 7.0f,
                        b = 7.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.0f,
                        dy1 = -2.72f,
                    )
                    // H 3.05
                    horizontalLineTo(x = 3.05f)
                    // a 9 9 0 0 0 1.58 4.16
                    arcToRelative(
                        a = 9.0f,
                        b = 9.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 1.58f,
                        dy1 = 4.16f,
                    )
                    // m 2.9 -2.9
                    moveToRelative(dx = 2.9f, dy = -2.9f)
                    // l 1.55 -1.56
                    lineToRelative(dx = 1.55f, dy = -1.56f)
                    // A 3 3 0 0 1 9 10
                    arcTo(
                        horizontalEllipseRadius = 3.0f,
                        verticalEllipseRadius = 3.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 9.0f,
                        y1 = 10.0f,
                    )
                    // V 6
                    verticalLineTo(y = 6.0f)
                    // a 3 3 0 0 1 5.82 -1.03
                    arcToRelative(
                        a = 3.0f,
                        b = 3.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 5.82f,
                        dy1 = -1.03f,
                    )
                    // l 1.5 -1.5
                    lineToRelative(dx = 1.5f, dy = -1.5f)
                    // A 5 5 0 0 0 7 6
                    arcTo(
                        horizontalEllipseRadius = 5.0f,
                        verticalEllipseRadius = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        x1 = 7.0f,
                        y1 = 6.0f,
                    )
                    // v 4
                    verticalLineToRelative(dy = 4.0f)
                    // q 0.02 1.22 0.53 2.25
                    quadToRelative(
                        dx1 = 0.02f,
                        dy1 = 1.22f,
                        dx2 = 0.53f,
                        dy2 = 2.25f,
                    )
                }
            }.build()
            .also { _MicOff = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.MicOff,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _MicOff: ImageVector? = null
