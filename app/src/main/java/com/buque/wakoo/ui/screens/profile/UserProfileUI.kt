package com.buque.wakoo.ui.screens.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.buque.wakoo.app.emptyContent
import com.buque.wakoo.bean.User
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.navigation.VoiceListTab
import com.buque.wakoo.ui.icons.More
import com.buque.wakoo.ui.icons.Report
import com.buque.wakoo.ui.icons.Settings
import com.buque.wakoo.ui.icons.UserForbidLine
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.voice.VoiceLazyListPage
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooSecondaryUnSelected
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.WakooTitleBar
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.media.manager.pauseIf
import com.buque.wakoo.ui.widget.media.manager.releaseIf
import com.buque.wakoo.ui.widget.popup.BubbleShape
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch

@Composable
fun UserProfilePage(
    user: User,
    tagPrefix: String,
    modifier: Modifier = Modifier,
    refreshEnable: Boolean = true,
    pagerContentPadding: PaddingValues = PaddingValues.Zero,
) {
    val tabs: List<VoiceListTab.IUser> =
        remember(user.id) {
            listOf(
                VoiceListTab.MyPublish(user.id),
                VoiceListTab.Like(user.id),
                VoiceListTab.Favorite(user.id),
            )
        }
    Column(
        modifier =
            modifier.background(
                color = Color.White,
                shape =
                    RoundedCornerShape(
                        topStart = 24.dp,
                        topEnd = 24.dp,
                    ),
            ),
    ) {
        // Tab选项卡
        val scope = rememberCoroutineScope()
        val pagerState = rememberPagerState(pageCount = { tabs.size })
        val selectedTabIndex = pagerState.currentPage

        TabRow(
            selectedTabIndex = selectedTabIndex,
            modifier =
                Modifier.padding(
                    vertical = 20.dp,
                    horizontal = 30.dp,
                ),
            divider = {},
            containerColor = Color.Transparent,
            indicator = { tabPositions ->
                if (selectedTabIndex < tabPositions.size) {
                    Box(
                        modifier =
                            Modifier
                                .tabIndicatorOffset(tabPositions[selectedTabIndex])
                                .requiredWidth(12.dp)
                                .height(3.dp)
                                .background(
                                    WakooSecondarySelected,
                                    CircleShape,
                                ),
                    )
                }
            },
        ) {
            tabs.forEachIndexed { index, tab ->
                Tab(
                    selected = selectedTabIndex == index,
                    selectedContentColor = WakooSecondarySelected,
                    unselectedContentColor = WakooSecondaryUnSelected,
                    onClick = { scope.launch { pagerState.animateScrollToPage(index) } },
                    content = {
                        Box(modifier = Modifier.padding(bottom = 4.dp)) {
                            tab.TabContent(selectedTabIndex == index)
                        }
                    },
                )
            }
        }

        val lifecycleOwner = LocalLifecycleOwner.current

        DisposableEffect(Unit) {
            val observer =
                LifecycleEventObserver { source, event ->
                    if (event == Lifecycle.Event.ON_STOP) {
                        MediaPlayerManager.pauseIf {
                            it.tag.startsWith(tagPrefix)
                        }
                    }
                }

            lifecycleOwner.lifecycle.addObserver(observer)

            onDispose {
                // Composable 离开组合时执行清理
                MediaPlayerManager.releaseIf {
                    it.tag.startsWith(tagPrefix)
                }
                lifecycleOwner.lifecycle.removeObserver(observer)
            }
        }

        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize(),
            beyondViewportPageCount = 1,
        ) { page ->
            val tab = tabs[page]

            LaunchedEffect(Unit) {
                snapshotFlow {
                    pagerState.currentPage != page
                }.filter { it }.collectLatest {
                    MediaPlayerManager.releaseIf {
                        it.tag.startsWith("$tagPrefix$tab")
                    }
                }
            }

            // 内容区域
            VoiceLazyListPage(
                user = user,
                tagPrefix = tagPrefix,
                tab = tab,
                indexInParent = page,
                parentPagerState = pagerState,
                refreshEnable = refreshEnable,
                pagerContentPadding = pagerContentPadding,
            )
        }
    }
}

/** 顶部应用栏 */
@Composable
fun TopAppBar(
    user: User,
    toSettings: () -> Unit,
    toEditUserInfo: () -> Unit,
    modifier: Modifier = Modifier,
    showBack: Boolean = false,
    onReport: () -> Unit = {},
    onBlacked: () -> Unit = {},
) {
    WakooTitleBar(
        title = null,
        modifier = modifier,
        navigationIcon =
            if (showBack) WakooTitleBarDefaults.backIconNavigation() else emptyContent,
    ) {
        if (user.isSelf) {
            // 编辑资料按钮
            OutlinedButton(
                text = "编辑资料",
                onClick = toEditUserInfo,
                height = 28.dp,
                borderWidth = 1f,
                fontSize = 12.sp,
                paddingValues = PaddingValues(horizontal = 8.dp),
            )

            SizeWidth(15.dp)

            // 设置按钮
            WakooTitleBarDefaults.IconButtonAction(
                imageVector = WakooIcons.Settings,
                onClick = toSettings,
                modifier = Modifier.size(24.dp),
            )

            SizeWidth(10.dp)
        } else {
            Box {
                // 设置按钮
                var expanded by rememberSaveable { mutableStateOf(false) }

                WakooTitleBarDefaults.IconButtonAction(
                    imageVector = WakooIcons.More,
                    onClick = {
                        expanded = true
                    },
                )

                val bubbleShape =
                    remember {
                        BubbleShape(arrowPositionBias = 0.85f)
                    }

                DropdownMenu(
                    expanded = expanded, // 菜单的展开状态
                    onDismissRequest = { expanded = false }, // 点击菜单外部或按返回键时关闭菜单
                    shape = bubbleShape,
                    offset = DpOffset((-6).dp, (-12).dp),
                    tonalElevation = 0.dp,
                    shadowElevation = 0.dp,
                    containerColor = Color.White,
                ) {
                    // 菜单项
                    Row(
                        modifier =
                            Modifier
                                .clickable(onClick = {
                                    onReport()
                                    expanded = false
                                })
                                .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                    ) {
                        Icon(
                            imageVector = WakooIcons.Report,
                            modifier = Modifier.size(20.dp, 20.dp),
                            contentDescription = null,
                            tint = Color(0xFF111111),
                        )
                        Text(
                            text = "举报",
                            modifier = Modifier.weight(1f),
                            color = Color(0xFF666666),
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center,
                        )
                    }

                    HorizontalDivider(
                        modifier = Modifier.padding(horizontal = 12.dp),
                        thickness = 0.5.dp,
                        color = Color(0xFFE5E5E5),
                    )

                    Row(
                        modifier =
                            Modifier
                                .clickable(onClick = {
                                    onBlacked()
                                    expanded = false
                                })
                                .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                    ) {
                        Icon(
                            imageVector = WakooIcons.UserForbidLine,
                            modifier = Modifier.size(20.dp, 20.dp),
                            contentDescription = null,
                            tint = Color(0xFF111111),
                        )
                        Text(
                            text = "拉黑",
                            modifier = Modifier.weight(1f),
                            color = Color(0xFF666666),
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center,
                        )
                    }
                }
            }
        }
    }
}

/** 粉丝关注信息区域 */
@Composable
fun FollowInfoSection(
    user: User,
    toUserRelations: (Int) -> Unit = {},
) {
    Row(
        modifier =
            Modifier
                .padding(horizontal = 24.dp)
                .fillMaxWidth()
                .height(intrinsicSize = IntrinsicSize.Min),
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // 关注
        Column(
            modifier = Modifier.noEffectClick(onClick = { toUserRelations(0) }),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                text = user.followingCount.toString(),
                style = MaterialTheme.typography.titleLarge,
                fontFamily = FontFamily.MI_SANS,
                color = Color(0xFF111111),
            )
            Text(
                text = "关注",
                style = MaterialTheme.typography.labelLarge,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF999999),
            )
        }

        // 分隔线
        VerticalDivider(
            color = Color(0xFFE5E5E5),
            thickness = 0.5.dp,
            modifier = Modifier.fillMaxHeight(0.7f),
        )

        // 粉丝
        Column(
            modifier = Modifier.noEffectClick(onClick = { toUserRelations(1) }),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                text = user.followersCount.toString(),
                style = MaterialTheme.typography.titleLarge,
                fontFamily = FontFamily.MI_SANS,
                color = Color(0xFF111111),
            )
            Text(
                text = "粉丝",
                style = MaterialTheme.typography.labelLarge,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF999999),
            )
        }
    }
}
