package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.ArrowRight: ImageVector
    get() {
        val current = _arrowRight
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.ArrowRight",
                defaultWidth = 12.0.dp,
                defaultHeight = 12.0.dp,
                viewportWidth = 12.0f,
                viewportHeight = 12.0f,
            ).apply {
                // M4.5 9.96 7.76 6.7 a1 1 0 0 0 0 -1.4 L4.5 2.04
                path(
                    fillAlpha = 0.5f,
                    stroke = SolidColor(Color(0xFF999999)),
                    strokeAlpha = 0.5f,
                    strokeLineCap = StrokeCap.Round,
                    strokeLineJoin = StrokeJoin.Round,
                    strokeLineMiter = 10.0f,
                    strokeLineWidth = 1.5f,
                ) {
                    // M 4.5 9.96
                    moveTo(x = 4.5f, y = 9.96f)
                    // L 7.76 6.7
                    lineTo(x = 7.76f, y = 6.7f)
                    // a 1 1 0 0 0 0 -1.4
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 0.0f,
                        dy1 = -1.4f,
                    )
                    // L 4.5 2.04
                    lineTo(x = 4.5f, y = 2.04f)
                }
            }.build()
            .also { _arrowRight = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.ArrowRight,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((12.0).dp)
                        .height((12.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _arrowRight: ImageVector? = null
