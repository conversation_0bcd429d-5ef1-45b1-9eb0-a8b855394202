package com.buque.wakoo.ui.screens.settings

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.bean.UserRelations
import com.buque.wakoo.navigation.RelationsKey
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.SimpleDoubleActionDialog
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.ButtonStyles
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.UserListItem
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.UserRelationsViewModel

@Composable
fun BlackListScreen() {
    SegColorTitleScreenScaffold(title = "黑名单") {
        val viewModel = viewModel<UserRelationsViewModel>()
        val listState = rememberLazyListState()

        var cancelBlackId by remember {
            mutableStateOf<String?>(null)
        }

        if (cancelBlackId != null) {
            Dialog(onDismissRequest = {
                cancelBlackId == null
            }) {
                SimpleDoubleActionDialog(
                    content = "确定要取消拉黑吗？",
                    cancelButtonConfig = DialogButtonStyles.Secondary.copy(text = "取消"),
                    confirmButtonConfig = DialogButtonStyles.Primary.copy(text = "确认"),
                    onCancel = {
                        cancelBlackId = null
                    },
                    onConfirm = {
                        viewModel.cancelBlackState(cancelBlackId!!)
                        cancelBlackId = null
                    },
                )
            }
        }

        CStateListPaginateLayout<String, Int, RelationsKey, UserRelations, UserRelationsViewModel>(
            reqKey = "",
            tabKey = RelationsKey.BlackList,
            modifier = Modifier.padding(it),
            listState = listState,
            viewModel = viewModel,
            emptyText = "暂无黑名单",
            emptyId = R.drawable.ic_empty_for_blacklist,
        ) { paginateState, list ->
            LazyColumn(modifier = Modifier.fillMaxSize(), state = listState) {
                items(list) { item ->
                    UserListItem(
                        user = item,
                        modifier = Modifier.padding(16.dp),
                    ) {
                        SolidButton(
                            text = "取消拉黑",
                            onClick = {
                                cancelBlackId = item.id
                            },
                            height = 32.dp,
                            textColor = Color(0xFF111111),
                            backgroundColor = Color(0xFF66FE6B),
                            fontSize = 14.sp,
                            config = ButtonStyles.Solid.copy(minWidth = 72.dp),
                            paddingValues = PaddingValues(horizontal = 12.dp),
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun BlackListScreenPreview() {
    WakooTheme {
        BlackListScreen()
    }
}
