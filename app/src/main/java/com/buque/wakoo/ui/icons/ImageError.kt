package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.ImageError: ImageVector
    get() {
        val current = _imageError
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.ImageError",
                defaultWidth = 32.0.dp,
                defaultHeight = 32.0.dp,
                viewportWidth = 32.0f,
                viewportHeight = 32.0f,
            ).apply {
                // m19 4 -1.65 2.67 H5.33 v18.66 l7.83 -7.82 4.55 2.56 L12.55 28 H4 a1.3 1.3 0 0 1 -1.32 -1.32 V5.32 C2.67 4.6 3.27 4 3.99 4z M28 4 c.73 0 1.32 .6 1.32 1.32 v21.36 c0 .73 -.6 1.32 -1.32 1.32 H16.13 l4.97 -7.64 .87 -1.34 -1.4 -.78 -5.02 -2.83 .45 -.74 1.72 -1.73 a1.33 1.33 0 0 1 1.89 0 l7.06 7.07 V6.67 h-5.8 l1.5 -2.42 L22.53 4z M10.67 9.33 a2.67 2.67 0 1 1 0 5.34 2.67 2.67 0 0 1 0 -5.34
                path(
                    fill = SolidColor(Color(0xFF9CA0B8)),
                ) {
                    // M 19 4
                    moveTo(x = 19.0f, y = 4.0f)
                    // l -1.65 2.67
                    lineToRelative(dx = -1.65f, dy = 2.67f)
                    // H 5.33
                    horizontalLineTo(x = 5.33f)
                    // v 18.66
                    verticalLineToRelative(dy = 18.66f)
                    // l 7.83 -7.82
                    lineToRelative(dx = 7.83f, dy = -7.82f)
                    // l 4.55 2.56
                    lineToRelative(dx = 4.55f, dy = 2.56f)
                    // L 12.55 28
                    lineTo(x = 12.55f, y = 28.0f)
                    // H 4
                    horizontalLineTo(x = 4.0f)
                    // a 1.3 1.3 0 0 1 -1.32 -1.32
                    arcToRelative(
                        a = 1.3f,
                        b = 1.3f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.32f,
                        dy1 = -1.32f,
                    )
                    // V 5.32
                    verticalLineTo(y = 5.32f)
                    // C 2.67 4.6 3.27 4 3.99 4z
                    curveTo(
                        x1 = 2.67f,
                        y1 = 4.6f,
                        x2 = 3.27f,
                        y2 = 4.0f,
                        x3 = 3.99f,
                        y3 = 4.0f,
                    )
                    close()
                    // M 28 4
                    moveTo(x = 28.0f, y = 4.0f)
                    // c 0.73 0 1.32 0.6 1.32 1.32
                    curveToRelative(
                        dx1 = 0.73f,
                        dy1 = 0.0f,
                        dx2 = 1.32f,
                        dy2 = 0.6f,
                        dx3 = 1.32f,
                        dy3 = 1.32f,
                    )
                    // v 21.36
                    verticalLineToRelative(dy = 21.36f)
                    // c 0 0.73 -0.6 1.32 -1.32 1.32
                    curveToRelative(
                        dx1 = 0.0f,
                        dy1 = 0.73f,
                        dx2 = -0.6f,
                        dy2 = 1.32f,
                        dx3 = -1.32f,
                        dy3 = 1.32f,
                    )
                    // H 16.13
                    horizontalLineTo(x = 16.13f)
                    // l 4.97 -7.64
                    lineToRelative(dx = 4.97f, dy = -7.64f)
                    // l 0.87 -1.34
                    lineToRelative(dx = 0.87f, dy = -1.34f)
                    // l -1.4 -0.78
                    lineToRelative(dx = -1.4f, dy = -0.78f)
                    // l -5.02 -2.83
                    lineToRelative(dx = -5.02f, dy = -2.83f)
                    // l 0.45 -0.74
                    lineToRelative(dx = 0.45f, dy = -0.74f)
                    // l 1.72 -1.73
                    lineToRelative(dx = 1.72f, dy = -1.73f)
                    // a 1.33 1.33 0 0 1 1.89 0
                    arcToRelative(
                        a = 1.33f,
                        b = 1.33f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.89f,
                        dy1 = 0.0f,
                    )
                    // l 7.06 7.07
                    lineToRelative(dx = 7.06f, dy = 7.07f)
                    // V 6.67
                    verticalLineTo(y = 6.67f)
                    // h -5.8
                    horizontalLineToRelative(dx = -5.8f)
                    // l 1.5 -2.42
                    lineToRelative(dx = 1.5f, dy = -2.42f)
                    // L 22.53 4z
                    lineTo(x = 22.53f, y = 4.0f)
                    close()
                    // M 10.67 9.33
                    moveTo(x = 10.67f, y = 9.33f)
                    // a 2.67 2.67 0 1 1 0 5.34
                    arcToRelative(
                        a = 2.67f,
                        b = 2.67f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = 5.34f,
                    )
                    // a 2.67 2.67 0 0 1 0 -5.34
                    arcToRelative(
                        a = 2.67f,
                        b = 2.67f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = -5.34f,
                    )
                }
            }.build()
            .also { _imageError = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.ImageError,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((32.0).dp)
                        .height((32.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _imageError: ImageVector? = null
