package com.buque.wakoo.ui.widget.media.selector

import android.Manifest
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.icons.ImagePlaceholder
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.ButtonStyles
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.WakooTitleBar
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.media.data.common.ImageItem
import com.buque.wakoo.ui.widget.media.data.common.MediaItem
import com.buque.wakoo.ui.widget.media.data.common.MediaType
import com.buque.wakoo.ui.widget.media.data.common.PlaceholderMediaItem
import com.buque.wakoo.ui.widget.media.data.common.VideoItem
import com.buque.wakoo.ui.widget.state.StateComponent
import com.buque.wakoo.utils.PermissionUtils
import com.buque.wakoo.utils.upload.rememberMediaSelectLauncher
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch

// 根据 Android 版本确定需要请求的权限列表
private val permissions =
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        // Android 13 (API 33) 及以上
        arrayOf(
            Manifest.permission.READ_MEDIA_IMAGES,
            Manifest.permission.READ_MEDIA_VIDEO,
        )
    } else {
        // Android 12 (API 32) 及以下
        arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
    }

@Composable
fun MediaSelectorScreen(
    resultKey: String,
    mediaType: MediaType,
    maxSelectCount: Int,
    modifier: Modifier = Modifier,
) {
    val controller = LocalAppNavController.root
    MediaSelectorPage(
        mediaType = mediaType,
        maxSelectCount = maxSelectCount,
        modifier = modifier,
    ) {
        controller.setResult(resultKey, it)
        controller.popIs<Route.MediaSelector>()
    }
}

@Composable
fun MediaSelectorPage(
    mediaType: MediaType,
    maxSelectCount: Int,
    modifier: Modifier = Modifier,
    onSelected: (List<MediaItem>) -> Unit,
) {
    val context = LocalContext.current
    var hasPermission by
        remember {
            mutableStateOf(PermissionUtils.hasPermissions(context, permissions))
        }

    val viewModel = viewModel<MediaListViewModel>(factory = MediaListViewModel.Factory(mediaType, maxSelectCount))
    val launcher =
        rememberLauncherForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { result ->
            hasPermission = result.values.all { it }
            if (!hasPermission) {
                showToast("您拒绝了相册权限，您可以使用系统选择器继续选择，也可以去设置中授予App相册权限")
            }
        }

    var useOriginMedia by rememberSaveable { mutableStateOf(false) }

    val scope = rememberCoroutineScope()

    Scaffold(
        modifier =
            Modifier
                .fillMaxWidth()
                .then(modifier),
        topBar = {
            WakooTitleBar(
                title = {
                    Text(
                        text =
                            when (mediaType) {
                                MediaType.IMAGE -> "所有图片"
                                MediaType.VIDEO -> "所有视频"
                                MediaType.ALL -> "图片和视频"
                            },
                        style = MaterialTheme.typography.titleMedium,
                    )
                },
                actions = {
                    SizeWidth(10.dp)
                },
            )
        },
        bottomBar = {
            BottomAppBar(modifier = Modifier.height(56.dp)) {
                WakooTitleBarDefaults.TextButtonAction(
                    text = if (viewModel.selectedItems.isNotEmpty()) "预览(${viewModel.selectedItems.size})" else "预览",
                    modifier = Modifier.widthIn(min = 70.dp),
                    enabled = viewModel.selectedItems.isNotEmpty(),
                    onClick = {
                    },
                )
                Weight(1f)

                Row(verticalAlignment = Alignment.CenterVertically) {
                    RadioButton(
                        selected = useOriginMedia,
                        modifier = Modifier.requiredSize(8.dp),
                        enabled = hasPermission,
                        onClick = {
                            useOriginMedia = !useOriginMedia
                        },
                        colors =
                            RadioButtonDefaults.colors(
                                selectedColor = Color(0xFF66FE6B),
                                unselectedColor = Color(0x80111111),
                            ),
                    )

                    SizeWidth(8.dp)

                    Text(
                        text = "原图",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color(0xFF111111),
                    )
                }
                Weight(1f)
                SolidButton(
                    text = if (viewModel.selectedItems.isNotEmpty()) "完成(${viewModel.selectedItems.size})" else "完成",
                    onClick = {
                        onSelected(viewModel.selectedItems)
                    },
                    modifier = Modifier.padding(end = 10.dp),
                    height = 30.dp,
                    fontSize = 14.sp,
                    enabled = viewModel.selectedItems.isNotEmpty(),
                    paddingValues = PaddingValues(horizontal = 8.dp),
                    config = ButtonStyles.Solid.copy(shape = RoundedCornerShape(8.dp), minWidth = 60.dp),
                )
            }
        },
    ) { padding ->
        Box(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(padding),
            contentAlignment = Alignment.Center,
        ) {
            if (hasPermission) {
                val mediaFeed by viewModel.mediaFeedFlow.collectAsState()

                val state: LazyGridState = rememberLazyGridState()

                if (!mediaFeed.isLoading && !mediaFeed.mediaFilling && mediaFeed.anchorIdIndex > -1) {
                    LaunchedEffect(Unit) {
                        snapshotFlow {
                            if (state.layoutInfo.visibleItemsInfo.isNotEmpty()) {
                                // 获取当前可见的最后一个项的索引
                                val lastVisibleItemIndex =
                                    state.layoutInfo.visibleItemsInfo
                                        .last()
                                        .index
                                if (lastVisibleItemIndex + 50 > mediaFeed.anchorIdIndex) {
                                    lastVisibleItemIndex
                                } else {
                                    -1
                                }
                            } else {
                                -1
                            }
                        }.filter { it > -1 }.collectLatest {
                            viewModel.loadMoreMediaInfo()
                        }
                    }
                }

                LazyVerticalGrid(
                    columns = GridCells.Fixed(4),
                    state = state,
                    verticalArrangement = Arrangement.spacedBy(1.dp), // 行之间的间距
                    horizontalArrangement = Arrangement.spacedBy(1.dp), // 列之间的间距
                ) {
                    items(mediaFeed.mediaItems) { item ->

                        val selectedNum by remember {
                            derivedStateOf {
                                viewModel.selectedItems
                                    .indexOfFirst { it.id == item.id }
                                    .takeIf { it > -1 }
                                    ?.plus(1)
                                    ?.toString()
                            }
                        }

                        val maskColor by animateColorAsState(
                            if (selectedNum == null) {
                                Color.Black.copy(0.2f)
                            } else {
                                Color.Black.copy(0.5f)
                            },
                        )
                        Box(
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .aspectRatio(1f),
                        ) {
                            Box(
                                modifier =
                                    Modifier.drawWithContent {
                                        drawContent()
                                        drawRect(maskColor)
                                    },
                            ) {
                                when (item) {
                                    is PlaceholderMediaItem -> {
                                        Image(
                                            imageVector = WakooIcons.ImagePlaceholder,
                                            contentDescription = null,
                                            modifier = Modifier.fillMaxSize(),
                                        )
                                    }

                                    is ImageItem -> {
                                        NetworkImage(
                                            data = item.uriString,
                                            modifier = Modifier.fillMaxSize(),
                                        )
                                    }

                                    is VideoItem -> {
                                        NetworkImage(
                                            data = item.uriString,
                                            modifier = Modifier.fillMaxSize(),
                                        )
                                    }
                                }
                            }

                            Box(
                                modifier =
                                    Modifier
                                        .align(Alignment.TopEnd)
                                        .noEffectClick(enabled = item is MediaItem) {
                                            viewModel.changeSelected(item as MediaItem)
                                        }.padding(4.dp)
                                        .size(20.dp)
                                        .clip(CircleShape)
                                        .run {
                                            if (selectedNum != null) {
                                                background(WakooGreen)
                                            } else {
                                                border(1.5.dp, WakooWhite, CircleShape)
                                            }
                                        },
                                contentAlignment = Alignment.Center,
                            ) {
                                if (selectedNum != null) {
                                    Text(
                                        text = selectedNum!!,
                                        color = Color(0xFF111111),
                                        fontSize = 12.sp,
                                    )
                                }
                            }
                        }
                    }
                }
            } else {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    StateComponent.Error(
                        text = "需要申请相册访问权限，才能继续使用相册功能",
                        buttonText = "授权",
                        onClick = {
                            launcher.launch(permissions)
                        },
                    )

                    SizeHeight(20.dp)

                    val launcher =
                        rememberMediaSelectLauncher(mediaType, maxSelectCount > 1) { list ->
                            if (list.isNotEmpty()) {
                                scope.launch {
                                    viewModel.loadMediaItems(context, list.take(maxSelectCount)).join()
                                    onSelected(viewModel.selectedItems)
                                }
                            }
                        }

                    OutlinedButton(
                        text = "使用系统选择器",
                        onClick = {
                            launcher.launch()
                        },
                    )
                }
            }
        }
    }
}
