package com.buque.wakoo.ui.screens.liveroom.panel

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Badge
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.ext.formatUnreadCount
import com.buque.wakoo.im_business.conversation.AppConversationManger
import com.buque.wakoo.im_business.conversation.C2CConversation
import com.buque.wakoo.im_business.viewmodel.C2CChatViewModel
import com.buque.wakoo.navigation.MessageTab
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.icons.Close
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.messages.chat.C2CChatScreen
import com.buque.wakoo.ui.screens.messages.chat.ConversationScreen
import com.buque.wakoo.ui.screens.messages.notification.NotificationInfoScreen
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooSecondaryUnSelected
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults
import kotlinx.coroutines.launch

private val TABS: List<MessageTab> =
    listOf(
        MessageTab.IMMessage, // 关注
        MessageTab.Notification, // 粉丝
    )

@Composable
fun DialogScope.MessagePanel(roomInfoState: LiveRoomInfoState) {
    val pagerState = rememberPagerState(pageCount = { TABS.size })
    val selectedTabIndex = pagerState.currentPage
    val scope = rememberCoroutineScope()

    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.7f)
                .background(color = Color.White, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)),
    ) {
        Box {
            TabRow(
                selectedTabIndex = selectedTabIndex,
                modifier =
                    Modifier
                        .align(Alignment.Center)
                        .padding(horizontal = 75.dp),
                divider = {},
                containerColor = Color.Transparent,
                indicator = { tabPositions ->
                    if (selectedTabIndex < tabPositions.size) {
                        Box(
                            modifier =
                                Modifier
                                    .tabIndicatorOffset(tabPositions[selectedTabIndex])
                                    .requiredWidth(12.dp)
                                    .height(3.dp)
                                    .background(
                                        WakooSecondarySelected,
                                        CircleShape,
                                    ),
                        )
                    }
                },
            ) {
                TABS.forEachIndexed { index, tab ->
                    Tab(
                        selected = selectedTabIndex == index,
                        selectedContentColor = WakooSecondarySelected,
                        unselectedContentColor = WakooSecondaryUnSelected,
                        onClick = {
                            scope.launch {
                                pagerState.animateScrollToPage(index)
                            }
                        },
                        content = {
                            Box(
                                modifier =
                                    Modifier
                                        .padding(bottom = 4.dp),
                                contentAlignment = Alignment.Center,
                            ) {
                                tab.TabContent(
                                    selectedTabIndex == index,
                                    modifier = Modifier.padding(horizontal = 18.dp),
                                )
                                if (index == 0) {
                                    val unReadCount by AppConversationManger.unReadCountFlow.collectAsStateWithLifecycle()
                                    if (unReadCount > 0) {
                                        Badge(modifier = Modifier.align(Alignment.TopEnd)) {
                                            Text(
                                                text = unReadCount.formatUnreadCount(99),
                                                style =
                                                    TextStyle(
                                                        fontSize = 11.sp,
                                                        lineHeight = 16.sp,
                                                        fontWeight = FontWeight.Medium,
                                                        color = Color.White,
                                                    ),
                                            )
                                        }
                                    }
                                }
                            }
                        },
                    )
                }
            }

            WakooTitleBarDefaults.IconButtonAction(WakooIcons.Close, onClick = {
                dismiss()
            }, modifier = Modifier.align(Alignment.CenterEnd))
        }

        HorizontalPager(pagerState) { index ->
            val page = TABS[selectedTabIndex]
            when (page) {
                MessageTab.IMMessage -> {
                    ConversationScreen {
                        when (it) {
                            is C2CConversation -> {
                                val user = it.user
                                roomInfoState.sendEvent(
                                    RoomEvent.PanelDialog {
                                        val viewModel =
                                            remember(user.id) {
                                                C2CChatViewModel(user)
                                            }
                                        Box(
                                            modifier =
                                                Modifier
                                                    .fillMaxHeight(0.7f)
                                                    .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)),
                                        ) {
                                            C2CChatScreen(
                                                user = user,
                                                viewModel = viewModel,
                                                modifier =
                                                    Modifier.consumeWindowInsets(
                                                        WindowInsets.statusBars,
                                                    ),
                                            )
                                        }
                                    },
                                )
                            }
                        }
                    }
                }

                MessageTab.Notification -> {
                    NotificationInfoScreen()
                }
            }
        }
    }
}
