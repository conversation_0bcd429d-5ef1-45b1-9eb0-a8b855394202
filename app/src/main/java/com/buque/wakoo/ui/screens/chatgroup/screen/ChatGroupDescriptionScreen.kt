package com.buque.wakoo.ui.screens.chatgroup.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.emptyAction
import com.buque.wakoo.bean.chatgroup.ChatGroupBean
import com.buque.wakoo.bean.chatgroup.ChatGroupMember
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.toastWhenError
import com.buque.wakoo.navigation.ChatGroupRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.dialog.LongTextDialog
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.More
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.chatgroup.ChatGroupNavCtrlKey
import com.buque.wakoo.ui.screens.chatgroup.chat.ChatGroupAdmin
import com.buque.wakoo.ui.screens.chatgroup.chat.ChatGroupOwner
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.ExpandableText
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.WakooTitleBar
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.image.SquareNetworkImage
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.ui.widget.state.StateListPaginateLayout
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupOnlineMemberListViewModel
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupSettingsViewModel
import com.buque.wakoo.viewmodel.chatgroup.OnlineKey


@Composable
fun ChatGroupDetailScreen(
    groupId: String, viewModel: ChatGroupSettingsViewModel = viewModel<ChatGroupSettingsViewModel>(initializer = {
        ChatGroupSettingsViewModel(groupId)
    })
) {
    val lm = LocalLoadingManager.current
    val state by viewModel.state
    LaunchedEffect(viewModel) {
        viewModel.refreshState()
    }
    val nav = LocalAppNavController.current
    val rootNav = LocalAppNavController.root
    CStateLayout(state) {
        ChatGroupDescriptionScreen(it, onApply = {
            lm.show(null) {
                viewModel.applyJoin()
                    .toastWhenError()
            }
        }, onOpen = {
            if (nav.key == ChatGroupNavCtrlKey) {
                nav.pop()
            } else {
                nav.push(Route.ChatGroup(viewModel.groupId))
            }
        }, onInvite = {
            rootNav.push(Route.SelectUser(1, groupId, Route.SelectUser.SELECT_FOR_INVITE_GROUP, "邀请加入群组", "邀请加入"))
        })
    }
}

@Composable
fun ChatGroupDescriptionScreen(
    chatGroupBean: ChatGroupBean,
    onApply: () -> Unit = emptyAction,
    onInvite: OnAction = emptyAction,
    onOpen: OnAction = emptyAction
) {
    val showDialog =
        rememberSaveable {
            mutableStateOf(false)
        }
    val nav = LocalAppNavController.current

    val memberViewModel = viewModel<ChatGroupOnlineMemberListViewModel>(initializer = {
        ChatGroupOnlineMemberListViewModel(chatGroupBean.id, chatGroupBean.firstPageMembers)
    })
    Box(modifier = Modifier.fillMaxSize()) {
        NetworkImage(
            data = chatGroupBean.avatarUrl,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.26f)
                    .drawWithContent {
                        drawContent()
                        drawRect(Color(0x99000000))
                    },
        )
        WakooTitleBar(
            title = "",
            titleContentColor = Color.White,
            actions = {
                if (chatGroupBean.joinState.isMember) {
                    Box {
                        WakooTitleBarDefaults.IconButtonAction(
                            imageVector = WakooIcons.More,
                            onClick = {
                                nav.push(ChatGroupRoute.GroupSettings(chatGroupBean.id))
                            },
                            colors =
                                IconButtonDefaults.iconButtonColors(
                                    contentColor = Color.White,
                                ),
                        )
                    }
                }
            },
        )

        Box(
            modifier =
                Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .fillMaxHeight(0.82f),
        ) {
            Spacer(
                modifier =
                    Modifier
                        .padding(top = 32.dp)
                        .fillMaxSize()
                        .background(Color.White, RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)),
            )

            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                SquareNetworkImage(
                    data = chatGroupBean.avatarUrl,
                    size = 96.dp,
                    shape = RoundedCornerShape(8.dp),
                )

                SizeHeight(16.dp)

                Text(
                    text = chatGroupBean.name,
                    style = MaterialTheme.typography.titleSmall,
                    color = Color(0xFF111111),
                )

                SizeHeight(8.dp)

                Text(
                    text = "ID:${chatGroupBean.publicId}",
                    style = MaterialTheme.typography.labelLarge,
                    color = Color(0xFF999999),
                )

                SizeHeight(16.dp)

                ExpandableText(
                    text = chatGroupBean.bulletin,
                    modifier = Modifier.padding(horizontal = 36.dp),
                    style =
                        MaterialTheme.typography.labelLarge.copy(
                            lineHeight = 15.sp,
                        ),
                    color = Color(0xFF666666),
                    collapsedMaxLines = 5,
                    expandableTextColor = Color(0xFF15ABFF),
                ) {
                    showDialog.value = true
                }

                SizeHeight(24.dp)

                HorizontalDivider(thickness = 8.dp, color = Color(0xFFF7F7F7))

                Row(
                    modifier =
                        Modifier
                            .padding(start = 16.dp, top = 24.dp, end = 16.dp)
                            .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    Text(
                        text = "群组成员",
                        style = MaterialTheme.typography.titleSmall,
                        color = Color(0xFF111111),
                    )

                    Text(
                        text = "${chatGroupBean.onlineMemberCnt}/${chatGroupBean.memberCnt}",
                        style = MaterialTheme.typography.labelLarge,
                        color = Color(0xFF999999),
                    )
                }

                SizeHeight(10.dp)

                ChatGroupMemberGrid(chatGroupBean.id, memberViewModel)
            }

            Box(
                modifier =
                    Modifier
                        .align(Alignment.BottomCenter)
                        .fillMaxWidth()
                        .height(120.dp)
                        .background(
                            Brush.verticalGradient(
                                0f to Color(0x00FFFFFF),
                                0.5f to Color(0xCCFFFFFF),
                                1f to Color(0xFFFFFFFF),
                            ),
                        )
                        .padding(top = 22.dp),
            ) {
                val joinState = chatGroupBean.joinState
                if (!joinState.isMember) {
                    GradientButton(
                        text =
                            "申请中".takeIf { joinState.isApplying } ?: "申请加入",
                        onClick = onApply,
                        enabled = joinState.isNone,
                        modifier =
                            Modifier
                                .padding(horizontal = 32.dp)
                                .fillMaxWidth(),
                    )
                } else {
                    Row {
                        SizeWidth(16.dp)
                        SolidButton(
                            text = "进入聊天",
                            onClick = onOpen,
                            modifier = Modifier.weight(1f),
                            backgroundColor = Color(0xFFC8FFC9)
                        )
                        SizeWidth(12.dp)
                        SolidButton(
                            text = "邀请",
                            onClick = onInvite,
                            modifier = Modifier.weight(1.5f),
                        )
                        SizeWidth(16.dp)
                    }
                }
            }
        }
    }

    if (showDialog.value) {
        Dialog(
            onDismissRequest = {
                showDialog.value = false
            },
        ) {
            LongTextDialog(
                title = "群聊介绍",
                content = chatGroupBean.bulletin,
                buttonContent = {
                    SolidButton(
                        text = "确定",
                        onClick = {
                            showDialog.value = false
                        },
                        modifier =
                            Modifier
                                .padding(vertical = 20.dp)
                                .align(Alignment.CenterHorizontally),
                        height = 36.dp,
                        minWidth = 160.dp,
                    )
                },
            )
        }
    }
}

@Composable
private fun ChatGroupMemberGrid(
    groupId: String, viewModel: ChatGroupOnlineMemberListViewModel = viewModel<ChatGroupOnlineMemberListViewModel>(initializer = {
        ChatGroupOnlineMemberListViewModel(groupId)
    })
) {
    val nav = LocalAppNavController.root
    StateListPaginateLayout<OnlineKey, ChatGroupMember, ChatGroupOnlineMemberListViewModel>(viewModel = viewModel) { _, list ->
        LazyVerticalGrid(
            columns = GridCells.Fixed(5),
            modifier =
                Modifier
                    .padding(horizontal = 12.dp)
                    .fillMaxSize(),
            horizontalArrangement = Arrangement.spacedBy(7.75.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(top = 10.dp, bottom = 150.dp),
        ) {
            items(list) { item ->
                Column(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .widthIn(max = 64.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Box {
                        AvatarNetworkImage(
                            modifier = Modifier.noEffectClick(onClick = {
                                nav.push(Route.UserProfile(item.user))
                            }),
                            user = item.user,
                            size = 56.dp,
                        )
                        if (item.role == ChatGroupMember.ROLE_OWNER) {
                            ChatGroupOwner(modifier = Modifier.align(Alignment.BottomCenter))
                        }
                        if (item.role == ChatGroupMember.ROLE_ADMIN) {
                            ChatGroupAdmin(modifier = Modifier.align(Alignment.BottomCenter))
                        }
                        if (item.isOnline) {
                            Box(
                                modifier = Modifier
                                    .size(14.dp)
                                    .align(Alignment.TopEnd)
                                    .padding(2.dp)
                                    .background(Color.White, CircleShape)
                                    .padding(2.dp)
                                    .background(
                                        WakooGreen, CircleShape
                                    )
                            )
                        }
                    }
                    SizeHeight(6.dp)
                    Text(
                        text = item.user.name,
                        color = Color(0xFF111111),
                        style = MaterialTheme.typography.labelMedium,
                        textAlign = TextAlign.Center,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                }
            }
        }
    }

}

@Preview
@Composable
private fun PreviewChatGroupDescriptionScreen() {
    WakooTheme {
        val bean = ChatGroupBean(
            name = "星空闪耀", publicId = "100001", bulletin = "val descriptionContent =\n" +
                "        \"在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\" +\n" +
                "            \"在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\" +\n" +
                "            \"在这里，分享不设限，聊天无拘束。每一句话都可能是故事的开始，每一个人都是特别的存在。欢迎你的加入，一起制造点不一样的热闹。\""
        )
        ChatGroupDescriptionScreen(bean)
    }
}
