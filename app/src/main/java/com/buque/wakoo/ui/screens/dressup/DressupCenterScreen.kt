package com.buque.wakoo.ui.screens.dressup

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.DressUpMineProp
import com.buque.wakoo.bean.DressUpMineTab
import com.buque.wakoo.network.api.service.DressupApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.icons.DressUpShop
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooSecondaryUnSelected
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.dressup.DressUpCenterViewModel
import kotlinx.coroutines.launch

@Composable
fun DressupCenterScreen(
    modifier: Modifier = Modifier,
    initTab: Int = -1,
    toDressShop: (tabType: Int?, tabName: String) -> Unit = { _, _ -> },
) {
    val mineTabs =
        rememberSaveable {
            mutableStateListOf<DressUpMineTab>()
        }

    val scope = rememberCoroutineScope()
    val pagerState = rememberPagerState(initialPage = 0, pageCount = { mineTabs.size })
    val selectedTabIndex = pagerState.currentPage

    LaunchedEffect(Unit) {
        executeApiCallExpectingData {
            DressupApiService.instance.getMineDressupTabs()
        }.onSuccess {
            mineTabs.clear()
            mineTabs.addAll(it.list)
            if (initTab != -1) {
                val i = it.list.indexOfFirst { it.type == initTab }
                if (i != -1) {
                    scope.launch {
                        pagerState.scrollToPage(i)
                    }
                }
            }
        }
    }

    SegColorTitleScreenScaffold("我的装扮", actions = {
        WakooTitleBarDefaults.IconButtonAction(WakooIcons.DressUpShop, onClick = {
            toDressShop(null, "")
        })
    }) { pv ->
        Column(
            modifier =
                Modifier
                    .background(color = Color.White)
                    .padding(pv)
                    .fillMaxSize(),
        ) {
            ScrollableTabRow(
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier.heightIn(min = 48.dp),
                edgePadding = 0.dp,
                divider = {},
                containerColor = Color.Transparent,
                indicator = { tabPositions ->
                    if (selectedTabIndex < tabPositions.size) {
                        Box(
                            modifier =
                                Modifier
                                    .tabIndicatorOffset(tabPositions[selectedTabIndex])
                                    .requiredWidth(12.dp)
                                    .height(3.dp)
                                    .background(
                                        WakooSecondarySelected,
                                        CircleShape,
                                    ),
                        )
                    }
                },
            ) {
                mineTabs.forEachIndexed { index, tab ->
                    Tab(
                        selected = selectedTabIndex == index,
                        selectedContentColor = WakooSecondarySelected,
                        unselectedContentColor = WakooSecondaryUnSelected,
                        onClick = {
                            scope.launch {
                                pagerState.animateScrollToPage(index)
                            }
                        },
                        content = {
                            Box(
                                modifier = Modifier.padding(bottom = 4.dp),
                                contentAlignment = Alignment.Center,
                            ) {
                                Text(
                                    text = tab.name,
                                    modifier = Modifier.padding(horizontal = 18.dp),
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = if (selectedTabIndex == index) FontWeight.SemiBold else FontWeight.Normal,
                                )
                            }
                        },
                    )
                }
            }

            HorizontalPager(
                pagerState,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .weight(1f),
            ) {
                DressUpCenterTabContent(mineTabs[it], toDressShop = { tab ->
                    toDressShop(tab.type, tab.name)
                })
            }
        }
    }
}

@Composable
fun DressUpCenterTabContent(
    tab: DressUpMineTab,
    modifier: Modifier = Modifier,
    toDressShop: (DressUpMineTab) -> Unit = {},
) {
    val viewModel = viewModel<DressUpCenterViewModel>()
    val listState = rememberLazyGridState()
    CStateListPaginateLayout<String, Int, DressUpMineTab, DressUpMineProp, DressUpCenterViewModel>(
        reqKey = SelfUser?.id ?: "",
        tabKey = tab,
        modifier = modifier,
        listState = listState,
        viewModel = viewModel,
        emptyText = "暂无${tab.name}",
        emptyId = R.drawable.ic_empty_for_dressup,
        emptyButton = "去装扮商城获取",
        onEmptyClick = {
            toDressShop(tab)
        },
    ) { paginateState, list ->
        LazyVerticalGrid(
            GridCells.Fixed(3),
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(horizontal = 24.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            items(list) { item ->
                Column(
                    modifier =
                        Modifier
                            .background(Color(0xFFF7F7F7), shape = RoundedCornerShape(16.dp))
                            .aspectRatio(0.71631206f)
                            .padding(8.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                ) {
                    NetworkImage(item.prop.icon, modifier = Modifier.size(58.dp), contentScale = ContentScale.Inside)
                    SizeHeight(3.dp)
                    Text(
                        item.prop.name,
                        color = Color(0xff222222),
                        fontSize = 11.sp,
                        lineHeight = 22.sp,
                        textAlign = TextAlign.Center,
                        fontWeight = FontWeight.SemiBold,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                    SizeHeight(3.dp)
                    if (item.isUsing) {
                        // 取消佩戴
                        OutlinedButton(
                            "取消佩戴",
                            onClick = {
                                viewModel.onWearPropFunc(tab, item)
                            },
                            fontSize = 12.sp,
                            textColor = Color(0xff999999),
                            borderColor = Color(0xff999999),
                            height = 26.dp,
                        )
                    } else {
                        // 佩戴
                        SolidButton(
                            "佩戴",
                            onClick = {
                                viewModel.onWearPropFunc(tab, item)
                            },
                            fontSize = 12.sp,
                            backgroundColor = Color(0xff111111),
                            textColor = Color(0xFF66FE6B),
                            height = 26.dp,
                        )
                    }
                    SizeHeight(4.dp)
                    Text(
                        item.expireTimeStr,
                        fontWeight = FontWeight.Medium,
                        fontSize = 10.sp,
                        lineHeight = 15.sp,
                        textAlign = TextAlign.Center,
                        color = Color(0xffa3a9b5),
                    )
                }
            }
        }
    }
}
