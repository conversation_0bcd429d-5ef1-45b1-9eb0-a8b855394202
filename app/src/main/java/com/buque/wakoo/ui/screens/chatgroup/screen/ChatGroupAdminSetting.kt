package com.buque.wakoo.ui.screens.chatgroup.screen

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.buque.wakoo.R
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.OnContent
import com.buque.wakoo.bean.BasicUser
import com.buque.wakoo.bean.User
import com.buque.wakoo.bean.chatgroup.ChatGroupBean
import com.buque.wakoo.bean.chatgroup.ChatGroupMember
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.ext.toastWhenError
import com.buque.wakoo.navigation.ChatGroupRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.UserListItem
import com.buque.wakoo.ui.widget.pagination.rememberPaginateState
import com.buque.wakoo.ui.widget.state.StateComponent
import com.buque.wakoo.ui.widget.state.StateListPaginateLayout
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupMemberListViewModel

@Composable
fun ChatGroupAdminSettingScreen(groupId: String) {
    val adminRoleViewModel =
        viewModel<ChatGroupMemberListViewModel>(
            factory =
                viewModelFactory {
                    initializer { ChatGroupMemberListViewModel(groupId, ChatGroupMember.ROLE_ADMIN.toString()) }
                },
        )
    val scope = rememberCoroutineScope()
    val lm = LocalLoadingManager.current
    val nav = LocalAppNavController.current
    val paginateState = rememberPaginateState<Int>(nextEnabled = false)

    Content {
        StateListPaginateLayout<Int, ChatGroupMember, ChatGroupMemberListViewModel>(
            emptyText = "暂无管理员",
            modifier = Modifier.fillMaxSize(),
            viewModel = adminRoleViewModel,
            autoRefresh = true,
            paginateState = paginateState,
            emptyContent = {
                StateComponent.Empty(
                    modifier = Modifier.align(Alignment.Center),
                    text = "暂无管理员",
                    emptyId = R.drawable.ic_empty_for_all,
                    buttonText = null,
                    onClick = {
                    },
                )
                Column(modifier = Modifier.align(Alignment.BottomCenter)) {
                    GradientButton(
                        "添加管理员",
                        onClick = {
                            val sz = adminRoleViewModel.getListState().dataOrNull?.size ?: 0
                            val navKey = ChatGroupRoute.ChatGroupAdminIncrease(groupId, (3 - sz).coerceIn(0, 3))
                            nav.push(navKey)
                        },
                        Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 32.dp),
                    )
                    Text(
                        "最多设置3名管理员",
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp),
                        textAlign = TextAlign.Center,
                        fontSize = 12.sp,
                        color = WakooGrayText,
                    )
                    SizeHeight(20.dp)
                }
            },
        ) { p1, list ->
            Column(modifier = Modifier.fillMaxSize()) {
                LazyColumn(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .weight(1f),
                ) {
                    items(list) { item ->
                        ChatGroupAdminItem(item.user, onCancel = {
                            lm.show(scope) {
                                adminRoleViewModel
                                    .setAdmin(item, false)
                                    .onSuccess {
                                        showToast("取消成功")
                                    }.toastWhenError()
                            }
                        })
                    }
                }
                if (list.size < 3) {
                    GradientButton(
                        "添加管理员",
                        onClick = {
                            nav.push(ChatGroupRoute.ChatGroupAdminIncrease(groupId, (3 - list.size).coerceIn(0, 3)))
                        },
                        Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 32.dp),
                        enabled = list.size < 3,
                    )
                }
                Text(
                    "最多设置3名管理员",
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                    textAlign = TextAlign.Center,
                    fontSize = 12.sp,
                    color = WakooGrayText,
                )
                SizeHeight(20.dp)
            }
        }
    }
}

@Composable
private fun Content(content: OnContent) {
    TitleScreenScaffold("设置管理员") { pd ->
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(pd),
        ) {
            content()
        }
    }
}

@Composable
fun ChatGroupAdminItem(
    user: User,
    onCancel: OnAction = {},
) {
    UserListItem(
        user,
        modifier = Modifier.padding(16.dp, 12.dp),
        centerContent = {
            Column {
                Text(
                    text = user.name,
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color(0xFF111111),
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
            }
        },
        endContent = {
            SolidButton(
                "取消管理员",
                backgroundColor = Color(0xFFE9EAEF),
                textColor = Color(0xFF999999),
                height = 32.dp,
                minWidth = 64.dp,
                fontSize = 14.sp,
                onClick = onCancel,
            )
        },
    )
}

@Preview
@Composable
fun ChatGroupAdminItemPreview(modifier: Modifier = Modifier) {
    WakooTheme {
        ChatGroupAdminItem(BasicUser.sampleGirl)
    }
}

@Preview
@Composable
private fun ContentPreview() {
    WakooTheme {
        Content { }
    }
}
