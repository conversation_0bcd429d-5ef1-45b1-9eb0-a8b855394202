package com.buque.wakoo.ui.screens.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.rememberLauncherForResult
import com.buque.wakoo.navigation.rememberSerializableList
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.AddRound
import com.buque.wakoo.ui.icons.DeleteCircle
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.theme.WakooLightGrayBg
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.ImeButtonScaffold
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.media.data.common.MediaItem
import com.buque.wakoo.viewmodel.ReportViewModel

// 举报原因选项
enum class ReportReason(
    val description: String,
    val value: Int,
) {
    POLITICAL("政治相关", 1),
    PORNOGRAPHY("色情低俗", 2),
    VIOLENCE("血腥暴力", 3),
    ADVERTISING("广告营销", 4),
    CHILD_SAFETY("儿童安全相关", 7),
    UNCIVILIZED("不文明语言", 6),
    FRAUD("恶意诈骗", 5),
    OTHER("其他", 0),
}

/**
 * 举报页面
 */
@Composable
fun ReportScreen(route: Route.Report) {
    var selectedReason by remember { mutableStateOf<ReportReason?>(null) }
    var description by remember { mutableStateOf("") }
    val selectedImages =
        rememberSerializableList(elementSerializer = MediaItem.serializer()) {
            mutableStateListOf()
        }

    val scope = rememberCoroutineScope()
    val loading = LocalLoadingManager.current
    val rootNavController = LocalAppNavController.root

    val viewModel = viewModel<ReportViewModel>()

    val launcher =
        rootNavController.rememberLauncherForResult<Route.MediaSelector, List<MediaItem>> { result ->
            if (selectedImages.size >= 9) {
                return@rememberLauncherForResult
            }
            if (result.isNotEmpty()) {
                selectedImages.addAll(result)
            }
        }

    SegColorTitleScreenScaffold(
        title = "举报",
    ) { paddingValues ->
        ImeButtonScaffold(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
            buttonModifier =
                Modifier
                    .fillMaxWidth()
                    .background(Color(0xFFF7F7F7))
                    .padding(
                        top = 20.dp,
                        bottom = 24.dp,
                    ),
            buttonContent = {
                SolidButton(
                    text = "提交举报",
                    onClick = {
                        if (selectedReason == null) {
                            showToast("请选择举报类型")
                            return@SolidButton
                        }
                        loading.show(scope) {
                            val result =
                                viewModel
                                    .report(
                                        route.type,
                                        route.targetId,
                                        selectedReason!!,
                                        description,
                                        selectedImages,
                                    )
                            if (result) {
                                showToast("举报成功")
                                rootNavController.popIs<Route.Report>()
                            }
                        }
                    },
                    modifier = Modifier.width(220.dp),
                    enabled = selectedReason != null,
                )
            },
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
            ) {
                // 说明文本
                Text(
                    text = "请选择举报类型",
                    modifier = Modifier.padding(vertical = 16.dp),
                    style = MaterialTheme.typography.labelLarge,
                    color = Color(0xFF111111),
                    fontWeight = FontWeight.Medium,
                )

                // 举报原因列表
                FlowRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                ) {
                    ReportReason.entries.forEach { reason ->
                        ReportReasonChip(
                            text = reason.description,
                            isSelected = reason == selectedReason,
                            onClick = { selectedReason = reason },
                        )
                    }
                }

                SizeHeight(20.dp)

                // 补充说明
                Text(
                    text = "填写举报描述 (选填)",
                    style = MaterialTheme.typography.labelLarge,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF111111),
                )

                SizeHeight(20.dp)

                AppTextField(
                    value = description,
                    onValueChange = {
                        description = it
                    },
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(140.dp),
                    placeholder = "请详细描述您遇到的问题...",
                    maxLength = 200,
                )

                SizeHeight(20.dp)

                Text(
                    text = "上传图片凭证 (选填)",
                    style = MaterialTheme.typography.labelLarge,
                    color = Color(0xFF111111),
                    fontWeight = FontWeight.Medium,
                )

                SizeHeight(20.dp)

                ImageGrid(
                    images = selectedImages,
                    onAddImage = {
                        if (selectedImages.size < 9) {
                            launcher.launch(Route.MediaSelector(maxSelectCount = 9 - selectedImages.size))
                        }
                    },
                    onRemoveImage = { image ->
                        selectedImages.remove(image)
                    },
                )

                SizeHeight(20.dp)

                Text(
                    text = "*最多可上传9张图片或长度不超过1分钟的视频",
                    style = MaterialTheme.typography.labelLarge,
                    color = WakooGrayText,
                )

                SizeHeight(16.dp)
            }
        }
    }
}

@Composable
private fun ReportReasonChip(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
) {
    Box(
        modifier =
            Modifier
                .clip(CircleShape)
                .widthIn(min = 96.dp)
                .height(40.dp)
                .background(if (isSelected) WakooGreen else WakooLightGrayBg)
                .clickable(onClick = onClick),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = text,
            color = if (isSelected) Color(0xFF111111) else Color(0xFF111111),
            style = MaterialTheme.typography.bodyMedium,
        )
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun ImageGrid(
    images: List<MediaItem>,
    onAddImage: () -> Unit,
    onRemoveImage: (MediaItem) -> Unit,
) {
    FlowRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        images.forEach { image ->
            ImagePreview(image = image.uriString, onRemoveClick = { onRemoveImage(image) })
        }
        if (images.size < 9) {
            AddImageButton(onClick = onAddImage)
        }
    }
}

@Composable
private fun ImagePreview(
    image: String,
    onRemoveClick: () -> Unit,
) {
    Box(
        modifier =
            Modifier
                .size(88.dp)
                .clip(RoundedCornerShape(8.dp)),
    ) {
        // Placeholder for image - could be an AsyncImage in a real app
        NetworkImage(
            data = image,
            modifier = Modifier.fillMaxSize(),
        )
        Icon(
            imageVector = WakooIcons.DeleteCircle,
            contentDescription = "Remove image",
            modifier =
                Modifier
                    .align(Alignment.TopEnd)
                    .clickable(onClick = onRemoveClick)
                    .padding(4.dp)
                    .size(16.dp),
            tint = Color.White,
        )
    }
}

@Composable
private fun AddImageButton(onClick: () -> Unit) {
    Box(
        modifier =
            Modifier
                .size(88.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(WakooLightGrayBg)
                .clickable(onClick = onClick),
        contentAlignment = Alignment.Center,
    ) {
        Icon(
            imageVector = WakooIcons.AddRound,
            contentDescription = "Add image",
            tint = WakooGrayText,
        )
    }
}

@Preview
@Composable
private fun ReportPagePreview() {
    WakooTheme {
        ReportScreen(Route.Report(0, ""))
    }
}
