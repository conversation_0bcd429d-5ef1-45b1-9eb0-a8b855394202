package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.UserForbidLine: ImageVector
    get() {
        if (_UserForbidLine != null) {
            return _UserForbidLine!!
        }
        _UserForbidLine = ImageVector.Builder(
            name = "UserForbidLine",
            defaultWidth = 20.dp,
            defaultHeight = 20.dp,
            viewportWidth = 20f,
            viewportHeight = 20f
        ).apply {
            path(fill = SolidColor(Color(0xFF000000))) {
                moveTo(6.667f, 5.834f)
                curveTo(6.667f, 3.993f, 8.159f, 2.501f, 10f, 2.501f)
                curveTo(11.841f, 2.501f, 13.333f, 3.993f, 13.333f, 5.834f)
                curveTo(13.333f, 7.675f, 11.841f, 9.167f, 10f, 9.167f)
                curveTo(8.159f, 9.167f, 6.667f, 7.675f, 6.667f, 5.834f)
                close()
                moveTo(10f, 0.834f)
                curveTo(7.238f, 0.834f, 5f, 3.073f, 5f, 5.834f)
                curveTo(5f, 8.595f, 7.238f, 10.834f, 10f, 10.834f)
                curveTo(12.761f, 10.834f, 15f, 8.595f, 15f, 5.834f)
                curveTo(15f, 3.073f, 12.761f, 0.834f, 10f, 0.834f)
                close()
                moveTo(12.5f, 15.001f)
                curveTo(12.5f, 13.62f, 13.619f, 12.501f, 15f, 12.501f)
                curveTo(15.386f, 12.501f, 15.751f, 12.588f, 16.078f, 12.744f)
                lineTo(12.744f, 16.079f)
                curveTo(12.587f, 15.752f, 12.5f, 15.387f, 12.5f, 15.001f)
                close()
                moveTo(13.922f, 17.257f)
                lineTo(17.256f, 13.923f)
                curveTo(17.412f, 14.249f, 17.5f, 14.615f, 17.5f, 15.001f)
                curveTo(17.5f, 16.381f, 16.381f, 17.501f, 15f, 17.501f)
                curveTo(14.614f, 17.501f, 14.248f, 17.413f, 13.922f, 17.257f)
                close()
                moveTo(15f, 10.834f)
                curveTo(12.699f, 10.834f, 10.833f, 12.7f, 10.833f, 15.001f)
                curveTo(10.833f, 17.302f, 12.699f, 19.167f, 15f, 19.167f)
                curveTo(17.301f, 19.167f, 19.167f, 17.302f, 19.167f, 15.001f)
                curveTo(19.167f, 12.7f, 17.301f, 10.834f, 15f, 10.834f)
                close()
                moveTo(10f, 11.667f)
                curveTo(10.07f, 11.667f, 10.14f, 11.668f, 10.21f, 11.671f)
                curveTo(9.853f, 12.183f, 9.576f, 12.756f, 9.398f, 13.37f)
                curveTo(6.92f, 13.667f, 5f, 15.776f, 5f, 18.334f)
                horizontalLineTo(3.333f)
                curveTo(3.333f, 14.652f, 6.318f, 11.667f, 10f, 11.667f)
                close()
            }
        }.build()

        return _UserForbidLine!!
    }

@Suppress("ObjectPropertyName")
private var _UserForbidLine: ImageVector? = null
