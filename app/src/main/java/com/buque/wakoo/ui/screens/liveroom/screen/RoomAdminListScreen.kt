package com.buque.wakoo.ui.screens.liveroom.screen

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.bean.UserPages
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.SimpleDoubleActionDialog
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.widget.ButtonStyles
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.UserListItem
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.liveroom.LiveRoomViewModel
import com.buque.wakoo.viewmodel.liveroom.RoomAdminListViewModel

@Composable
fun RoomAdminListScreen(
    viewModel: LiveRoomViewModel,
    modifier: Modifier = Modifier,
) {
    TitleScreenScaffold(title = "房间管理员", modifier = modifier) {
        val adminListViewModel = viewModel<RoomAdminListViewModel>()
        val listState = rememberLazyListState()

        var cancelAdminId by remember {
            mutableStateOf<String?>(null)
        }

        if (cancelAdminId != null) {
            Dialog(onDismissRequest = {
                cancelAdminId == null
            }) {
                SimpleDoubleActionDialog(
                    content = "确定要取消管理员吗？",
                    cancelButtonConfig = DialogButtonStyles.Secondary.copy(text = "取消"),
                    confirmButtonConfig = DialogButtonStyles.Primary.copy(text = "确认"),
                    onCancel = {
                        cancelAdminId = null
                    },
                    onConfirm = {
                        adminListViewModel.deleteItem(cancelAdminId!!)
                        viewModel.sendEvent(RoomEvent.SetAdminEvent(cancelAdminId!!, false))
                        cancelAdminId = null
                    },
                )
            }
        }

        CStateListPaginateLayout<String, Int, UserPages, RoomAdminListViewModel>(
            reqKey = viewModel.roomId,
            modifier = Modifier.padding(it),
            listState = listState,
            viewModel = adminListViewModel,
            emptyText = "暂无管理员",
            emptyId = R.drawable.ic_empty_for_all,
        ) { paginateState, list ->
            LazyColumn(modifier = Modifier.fillMaxSize(), state = listState) {
                items(list) { item ->
                    UserListItem(
                        user = item,
                        modifier = Modifier.padding(16.dp),
                    ) {
                        SolidButton(
                            text = "取消管理员",
                            onClick = {
                                cancelAdminId = item.id
                            },
                            height = 32.dp,
                            textColor = Color(0xFF111111),
                            backgroundColor = Color(0xFF66FE6B),
                            fontSize = 14.sp,
                            config = ButtonStyles.Solid.copy(minWidth = 72.dp),
                            paddingValues = PaddingValues(horizontal = 12.dp),
                        )
                    }
                }
            }
        }
    }
}
