package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Ok: ImageVector
    get() {
        val current = _OK
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.OK",
                defaultWidth = 28.0.dp,
                defaultHeight = 28.0.dp,
                viewportWidth = 28.0f,
                viewportHeight = 28.0f,
            ).apply {
                // M11.67 17.7 22.39 6.97 l1.65 1.65 L11.67 21 l-7.43 -7.43 1.65 -1.65z
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 11.67 17.7
                    moveTo(x = 11.67f, y = 17.7f)
                    // L 22.39 6.97
                    lineTo(x = 22.39f, y = 6.97f)
                    // l 1.65 1.65
                    lineToRelative(dx = 1.65f, dy = 1.65f)
                    // L 11.67 21
                    lineTo(x = 11.67f, y = 21.0f)
                    // l -7.43 -7.43
                    lineToRelative(dx = -7.43f, dy = -7.43f)
                    // l 1.65 -1.65z
                    lineToRelative(dx = 1.65f, dy = -1.65f)
                    close()
                }
            }.build()
            .also { _OK = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Ok,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((28.0).dp)
                        .height((28.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _OK: ImageVector? = null
