package com.buque.wakoo.ui.screens

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.buque.wakoo.R
import com.buque.wakoo.bean.BasicUser
import com.buque.wakoo.core.webview.AppLinkNavigator
import com.buque.wakoo.core.webview.BaseJsBridgeEventDelegate
import com.buque.wakoo.core.webview.WebBridgeHandler
import com.buque.wakoo.core.webview.WebFrameInfo
import com.buque.wakoo.core.webview.buildJSBEventHandlerList
import com.buque.wakoo.ext.getStringOrNull
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.widget.WakooTitleBar
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.rememberPermissionLauncher
import com.buque.webview.WebViewProxy
import com.buque.webview.getValueByUrl
import com.buque.webview.handler.BaseBridgeHandler
import com.buque.webview.putValueByUrl
import com.kevinnzou.web.LoadingState
import com.kevinnzou.web.WebView
import com.kevinnzou.web.rememberWebViewNavigator
import com.kevinnzou.web.rememberWebViewState
import com.smallbuer.jsbridge.core.BridgeHandler
import com.smallbuer.jsbridge.core.CallBackFunction
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun WebScreen(
    route: Route.Web,
    onOpenLink: (String) -> Unit,
    onOpenPage: (AppNavKey) -> Unit,
    onFinish: () -> Unit
) {
    WebViewContent(
        route.url,
        Modifier.fillMaxSize(),
        onOpenLink = onOpenLink,
        onOpenPage = onOpenPage,
        onFinish
    )
}

private fun logMessage(message: String) {
    LogUtils.d("WebView:$message")
}

@Composable
fun WebViewContent(
    url: String,
    modifier: Modifier = Modifier,
    onOpenLink: (String) -> Unit,
    onOpenPage: (AppNavKey) -> Unit,
    onFinish: () -> Unit = {},
    withStatus: Boolean = true,
) {
    val configuration = LocalConfiguration.current

    val statusBars = WindowInsets.statusBars
    val navigationBars = WindowInsets.navigationBars
    val density = LocalDensity.current
    val context = LocalContext.current
    val title = url.getValueByUrl("title")

    val urlLoad = remember {
        val lang = runCatching {
            configuration.locales[0].toLanguageTag()
        }.getOrNull() ?: "en-US"
        logMessage("lang:$lang")
        url.putValueByUrl("app-language", lang).also {
            logMessage("load url :$it")
        }
    }
    val state = rememberWebViewState(urlLoad)
    val navigator = rememberWebViewNavigator()
    var jsbTitle by remember {
        mutableStateOf<String?>(title)
    }
    var titleVisible by rememberSaveable {
        mutableStateOf(title?.isNotEmpty() == true)
    }
    var applyStatusBarPadding by remember {
        mutableStateOf(false)
    }
    var applyNavigationBarPadding by remember {
        mutableStateOf(false)
    }
    var permissionCallback by remember {
        mutableStateOf<Pair<BaseBridgeHandler, CallBackFunction>?>(null)
    }

    val launcher = rememberPermissionLauncher(onDenied = {
        val (handler, callback) = permissionCallback ?: return@rememberPermissionLauncher
        handler.apply {
            callback.sendFailure(context, -1)
        }
        permissionCallback = null
    }) {
        val (handler, callback) = permissionCallback ?: return@rememberPermissionLauncher
        handler.apply {
            callback.sendSuccess(context, 0)
        }
        permissionCallback = null
    }
    val appNav = LocalAppNavController.current
    val proxy = remember {
        val delegate = object : BaseJsBridgeEventDelegate(onFinish = onFinish, onTitleChanged = {
            jsbTitle = it
            titleVisible = it.isNullOrEmpty().not()
        }) {

            override fun onJumpAppPage(context: Context, jsonObject: JsonObject) {
                val targetName = jsonObject.getStringOrNull("target_name").orEmpty()
                if (AppLinkNavigator.isSupport(targetName)) {
                    onOpenLink(targetName)
                } else {
                    when (targetName) {
                        "user_home" -> {
                            runCatching {
                                jsonObject["params"]?.jsonObject?.get("user_id")?.jsonPrimitive?.contentOrNull
                            }.getOrNull().orEmpty().also { uid ->
                                onOpenPage(
                                    Route.UserProfile(
                                        BasicUser.fromUid(uid)
                                    )
                                )
                            }
                        }

                        "private_chat" -> {
                            runCatching {
                                jsonObject["params"]?.jsonObject?.get("user_id")?.jsonPrimitive?.contentOrNull
                            }.getOrNull().orEmpty().also { uid ->
                                onOpenPage(
                                    Route.Chat(
                                        BasicUser.fromUid(uid)
                                    )
                                )
                            }
                        }

                        else -> {}
                    }
                }
            }

            override fun onApiError(code: Int, msg: String) {
                when (code) {
                    -11 -> {//need charge coin
                        showToast(msg)
                        appNav.push(Route.Recharge)
                    }

                    -12 -> {//need vip
                        showToast(msg)
                        appNav.push(Route.Member)
                    }

                    else -> {}
                }
            }

            override fun onImmersive(statusBarVisible: Boolean, navigationBarVisible: Boolean) {
                applyStatusBarPadding = statusBarVisible
                applyNavigationBarPadding = navigationBarVisible
            }

            override fun onGetStatusBarHeight(context: Context): Float {
                return with(density) {
                    statusBars.getTop(density).toDp().value.also {
                        logMessage("onGetStatusBarHeight: $it")
                    }
                }
            }

            override fun onGetNavigationBarHeight(context: Context): Float {
                return with(density) {
                    navigationBars.getBottom(density).toDp().value.also {
                        logMessage("onGetNavigationBarHeight: $it")
                    }
                }
            }

            override fun onRequestAndroidPermission(
                bridgeHandler: BaseBridgeHandler,
                permissions: Array<String>?,
                callback: CallBackFunction
            ) {
                if (!permissions.isNullOrEmpty()) {
                    permissionCallback = bridgeHandler to callback
                    launcher.launch(permissions)
                }
            }
        }
        val eventHandlers: List<Pair<String, BridgeHandler>> = buildJSBEventHandlerList(delegate)
        WebViewProxy(eventHandlers = eventHandlers, onLackCameraPermission = {
            showToast(context.getString(R.string.lack_camera_permission))
        })
    }
    LaunchedEffect(navigator) {
        val bundle = state.viewState
        if (bundle == null) {
            // This is the first time load, so load the home page.
            val lowercase = url.lowercase()
            if (lowercase.endsWith(".jpg") || lowercase.endsWith(".jpeg") || lowercase.endsWith(
                    ".png"
                ) || lowercase.endsWith(".webp")
            ) {
                navigator.loadHtml(getWrapImageUrl(url))
            } else {
                navigator.loadUrl(url)
            }
        }
    }

    Column(modifier = modifier) {
        Box(modifier = Modifier.fillMaxWidth().background(Color.White)) {
            if (titleVisible) {
                WakooTitleBar(
                    title = jsbTitle ?: state.pageTitle,
                    onBack = onFinish,
                    windowInsets = if (withStatus) TopAppBarDefaults.windowInsets else navigationBars
                )
            }
            val loadingState = state.loadingState

            var showProgressIndicator by remember {
                mutableStateOf(false)
            }
            LaunchedEffect(key1 = state) {
                snapshotFlow {
                    state.loadingState
                }.filter {
                    it is LoadingState.Loading
                }.collectLatest {
                    showProgressIndicator = true
                }
            }

            if (showProgressIndicator) {
                val progress by animateFloatAsState(
                    targetValue =
                        when (loadingState) {
                            is LoadingState.Loading -> {
                                loadingState.progress
                            }

                            is LoadingState.Initializing -> {
                                0f
                            }

                            else -> {
                                1f
                            }
                        },
                    animationSpec =
                        if (state.loadingState is LoadingState.Finished) {
                            spring(visibilityThreshold = 0.01f, stiffness = 10f)
                        } else {
                            spring(visibilityThreshold = 0.01f)
                        },
                ) {
                    if (it == 1f) {
                        showProgressIndicator = false
                    }
                }

                LinearProgressIndicator(
                    progress = {
                        progress
                    },
                    trackColor = Color.Transparent,
                    modifier =
                        Modifier
                            .align(Alignment.BottomCenter)
                            .fillMaxWidth(),
                )
            }
        }
        WebBridgeHandler.init()

        WebView(
            state = state,
            navigator = navigator,
            modifier =
                Modifier
                    .run {
                        val then = if (applyStatusBarPadding && !titleVisible) {
                            statusBarsPadding()
                        } else {
                            this
                        }
                        if (applyNavigationBarPadding) {
                            then.navigationBarsPadding()
                        } else {
                            then
                        }
                    }
                    .fillMaxSize()
                    .alpha(0.99999f),
            onCreated = proxy.onCreated,
            onDispose = proxy.onDispose,
            client = proxy.client,
            chromeClient = proxy.chromeClient,
            factory = proxy.factory
        )
    }
}

private fun getWrapImageUrl(url: String): String {
    val head = """
                        <head>
                        <style>
                        *{
                        margin:0px;
                        }
                        </style>
                        <meta name="viewport" content="width=device-width,initial-scale=1" />
                        </head>
                    """.trimIndent()
    val imageUrl =
        "<html>$head<body><img src='$url' style='width:100%;max-width:100%;overflow:hidden;object-fit:contain;'/></body></html>"
    return imageUrl
}

@Composable
fun WebViewDialogContent(
    webFrameInfo: WebFrameInfo, onOpenLink: (String) -> Unit,
    onOpenPage: (AppNavKey) -> Unit, onDismiss: () -> Unit
) {

    WebViewContent(
        webFrameInfo.targetUrl, modifier = Modifier
            .then(if (webFrameInfo.width == -1) Modifier.fillMaxWidth() else Modifier.width(webFrameInfo.width.dp))
            .then(if (webFrameInfo.height == -1) Modifier.fillMaxHeight() else Modifier.height(webFrameInfo.height.dp))
            .then(webFrameInfo.radius?.let {
                Modifier.clip(
                    RoundedCornerShape(
                        topStart = it.leftTop.dp,
                        topEnd = it.rightTop.dp,
                        bottomEnd = it.rightBottom.dp,
                        bottomStart = it.leftBottom.dp
                    )
                )
            } ?: Modifier), onOpenLink, onOpenPage, onDismiss)
}