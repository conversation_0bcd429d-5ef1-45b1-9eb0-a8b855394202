package com.buque.wakoo.ui.screens.liveroom

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ui.icons.More
import com.buque.wakoo.ui.icons.RoomNotice
import com.buque.wakoo.ui.icons.RoomRank
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.liveroom.panel.LiveRoomOnlinePanel
import com.buque.wakoo.ui.screens.liveroom.panel.LiveRoomRankPanel
import com.buque.wakoo.ui.screens.liveroom.panel.LiveRoomSettingPanelDialog
import com.buque.wakoo.ui.screens.liveroom.panel.LiveRoomUserInfoPanel
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.IconTextButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage

@Composable
fun ColumnScope.LiveRoomTopLayout(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier =
            modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
    ) {
        Box(modifier = Modifier.weight(1f)) {
            Row(
                modifier =
                    Modifier
                        .padding(end = 20.dp)
                        .animateContentSize()
                        .height(34.dp)
                        .background(Color(0x26FFFFFF), CircleShape)
                        .padding(start = 4.dp, end = 6.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp),
            ) {
                val ownerUser = roomInfoState.basicInfo.owner

                AvatarNetworkImage(
                    user = ownerUser,
                    size = 26.dp,
                ) {
                    roomInfoState.sendEvent(
                        RoomEvent.PanelDialog { roomInfoState ->
                            LiveRoomUserInfoPanel(ownerUser, roomInfoState)
                        },
                    )
                }
                Column {
                    Text(
                        text = roomInfoState.basicInfo.title,
                        color = WakooWhite,
                        style = MaterialTheme.typography.labelLarge,
                        overflow = TextOverflow.Ellipsis,
                        maxLines = 1,
                    )
                    SizeHeight(1.dp)
                    Text(
                        text = "ID:${roomInfoState.basicInfo.publicId}",
                        color = WakooWhite,
                        style = MaterialTheme.typography.labelSmall,
                    )
                }

                if (!ownerUser.isSelf) {
                    val isFollowed by remember(ownerUser.id) {
                        derivedStateOf {
                            (roomInfoState.requireRoomUser(ownerUser) as? UserInfo)?.extra?.isFollowed
                                ?: true // 不是UserInfo就认为关注了
                        }
                    }

                    AnimatedVisibility(!isFollowed) {
                        GradientButton(
                            text = "关注",
                            onClick = {
                                roomInfoState.sendEvent(RoomEvent.FollowUser(ownerUser.id))
                            },
                            height = 26.dp,
                            paddingValues = PaddingValues(horizontal = 8.dp),
                            fontSize = 11.sp,
                        )
                    }
                }
            }
        }

        Row(
            modifier = Modifier.padding(top = 5.dp),
            horizontalArrangement = Arrangement.End,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Row(
                modifier =
                    Modifier
                        .padding(end = 8.dp)
                        .background(Color(0x26FFFFFF), CircleShape)
                        .noEffectClick {
                            roomInfoState.sendEvent(
                                RoomEvent.PanelDialog { roomInfoState ->
                                    LiveRoomOnlinePanel(roomInfoState)
                                },
                            )
                        }.padding(2.dp),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Row(
                    horizontalArrangement = Arrangement.spacedBy((-4).dp),
                ) {
                    roomInfoState.onlinePreviewList.forEach { user ->
                        AvatarNetworkImage(
                            user = user,
                            size = 20.dp,
                            modifier = Modifier.border(0.5.dp, WakooWhite, CircleShape),
                            enabled = false,
                        )
                    }
                }

                Text(
                    text = roomInfoState.onlineCount.toString(),
                    modifier = Modifier.padding(start = 4.dp, end = 6.dp),
                    color = WakooWhite,
                    style = MaterialTheme.typography.labelSmall,
                )
            }

            Box(
                modifier =
                    Modifier
                        .size(24.dp)
                        .clip(CircleShape)
                        .background(Color(0x26FFFFFF))
                        .clickable(onClick = {
                            roomInfoState.sendEvent(
                                RoomEvent.RestorableDialog(LiveRoomSettingPanelDialog()),
                            )
                        }),
                contentAlignment = Alignment.Center,
            ) {
                Icon(
                    imageVector = WakooIcons.More,
                    contentDescription = null,
                    tint = WakooWhite,
                    modifier = Modifier.size(18.dp),
                )
            }
        }
    }

    Row(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
    ) {
        IconTextButton(text = "公告", imageVector = WakooIcons.RoomNotice, iconSize = 12.dp)
        Weight(1f)
        IconTextButton(text = "房间榜单", imageVector = WakooIcons.RoomRank, iconSize = 12.dp) {
            roomInfoState.sendEvent(
                RoomEvent.PanelDialog {
                    LiveRoomRankPanel(roomInfoState)
                },
            )
        }
    }
}
