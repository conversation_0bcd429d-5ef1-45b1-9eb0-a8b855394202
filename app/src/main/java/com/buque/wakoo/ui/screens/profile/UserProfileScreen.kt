package com.buque.wakoo.ui.screens.profile

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.bean.User
import com.buque.wakoo.bean.toBasic
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.VipCrownTag
import com.buque.wakoo.ui.widget.coordinatorlayout.CustomCollapsibleHeader
import com.buque.wakoo.ui.widget.coordinatorlayout.rememberCustomCollapsibleHeaderState
import com.buque.wakoo.ui.widget.coordinatorlayout.rememberCustomCollapsibleScrollBehavior
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.viewmodel.UserProfileViewModel
import kotlinx.coroutines.launch

private const val PAGE_TAG_PREFIX = "profile-tab-"

@Composable
fun UserProfileScreen(
    user: User,
    toEditUserInfo: () -> Unit = {},
    toSettings: () -> Unit = {},
    toUserRelations: (Int) -> Unit = {},
) {
    val headerState = rememberCustomCollapsibleHeaderState()

    val viewModel = viewModel<UserProfileViewModel>()
    val coroutineScope = rememberCoroutineScope()
    val rootNavController = LocalAppNavController.root

    val user = viewModel.user ?: user

    LifecycleEventEffect(Lifecycle.Event.ON_START) {
        viewModel.requestUserInfo(user.id)
    }

    val scrollBehavior =
        rememberCustomCollapsibleScrollBehavior(
            state = headerState.topAppBarState,
            enableSnap = true,
        )

    val refreshEnable by remember(headerState) {
        derivedStateOf {
            headerState.collapsedFraction <= 0.0f
        }
    }

    Box(
        modifier = Modifier.fillMaxSize(),
    ) {
        Image(
            painter = painterResource(R.drawable.bg_common_top),
            contentDescription = null,
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.FillWidth,
            alignment = Alignment.TopCenter,
        )
        Scaffold(
            modifier =
                Modifier
                    .fillMaxSize()
                    .nestedScroll(scrollBehavior.nestedScrollConnection),
            containerColor = Color.Transparent,
            topBar = {
                Column {
                    TopAppBar(
                        user = user,
                        toSettings = toSettings,
                        toEditUserInfo = toEditUserInfo,
                        showBack = true,
                        onReport = {
                            rootNavController.push(Route.Report(1, user.id))
                        },
                        onBlacked = {
                            coroutineScope.launch {
                                viewModel.updateBlackState(user.id, true)
                            }
                        },
                    )
                    CustomCollapsibleHeader(
                        scrollBehavior = scrollBehavior,
                        state = headerState,
                        expandedContent = {
                            // --- 完全自定义的展开状态内容 ---
                            Column {
                                SizeHeight(5.dp)

                                // 个人资料区域
                                UserProfileSection(user)

                                SizeHeight(16.dp)

                                // 粉丝关注信息
                                FollowInfoSection(user = user, toUserRelations = toUserRelations)

                                SizeHeight(16.dp)
                            }
                        },
                    )
                }
            },
        ) { paddingValues ->
            UserProfilePage(
                user = user,
                tagPrefix = PAGE_TAG_PREFIX,
                modifier = Modifier.padding(paddingValues),
                refreshEnable = refreshEnable,
                pagerContentPadding = PaddingValues(bottom = if (user.isSelf) 20.dp else 74.dp),
            )
        }

        if (!user.isSelf) {
            Row(
                modifier =
                    Modifier
                        .align(Alignment.BottomCenter)
                        .fillMaxWidth()
                        .padding(
                            start = 16.dp,
                            bottom =
                                WindowInsets.navigationBars
                                    .asPaddingValues()
                                    .calculateBottomPadding()
                                    .plus(20.dp),
                            end = 16.dp,
                        ),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
            ) {
                AnimatedVisibility(viewModel.hasExtraInfo, modifier = Modifier.weight(1f)) {
                    if (viewModel.extraInfo.isFollowed) {
                        SolidButton(
                            text = "已关注",
                            onClick = {
                                viewModel.toggleFollowState(user.id)
                            },
                            modifier = Modifier.fillMaxWidth(),
                            backgroundColor = Color(0xFF111111),
                            textColor = Color(0xFF66FE6B),
                        )
                    } else {
                        OutlinedButton(
                            text = "关注",
                            onClick = {
                                viewModel.toggleFollowState(user.id)
                            },
                            modifier = Modifier.fillMaxWidth(),
                        )
                    }
                }

                GradientButton(
                    text = "私信",
                    onClick = {
                        rootNavController.push(Route.Chat(user.toBasic()))
                    },
                    modifier = Modifier.weight(1f),
                )
            }
        }
    }
}

/**
 * 用户个人资料区域
 */
@Composable
private fun UserProfileSection(
    user: User,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // 用户头像
        AvatarNetworkImage(
            user = user,
            size = 72.dp,
            enabled = false,
        )

        SizeHeight(8.dp)

        // 性别年龄和VIP标签
        Row(
            horizontalArrangement = Arrangement.spacedBy(4.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = user.name,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier =
                    Modifier.weight(
                        weight = 1f,
                        fill = false,
                    ),
                style = MaterialTheme.typography.titleSmall,
            )

            // 性别年龄标签
            GenderAgeTag(user = user)

            // VIP标签
            if (user.isVip) {
                VipCrownTag()
            }
        }
    }
}
