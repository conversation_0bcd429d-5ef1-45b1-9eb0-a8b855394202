package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.VoiceMiddle: ImageVector
    get() {
        if (_VoiceMiddle != null) {
            return _VoiceMiddle!!
        }
        _VoiceMiddle =
            ImageVector
                .Builder(
                    name = "VoiceMiddle",
                    defaultWidth = 20.dp,
                    defaultHeight = 20.dp,
                    viewportWidth = 20f,
                    viewportHeight = 20f,
                ).apply {
                    path(
                        stroke = SolidColor(Color(0xFF111111)),
                        strokeLineWidth = 2f,
                        strokeLineCap = StrokeCap.Square,
                    ) {
                        moveTo(8.907f, 14.243f)
                        curveTo(9.464f, 13.686f, 9.906f, 13.024f, 10.208f, 12.297f)
                        curveTo(10.509f, 11.569f, 10.664f, 10.788f, 10.664f, 10f)
                        curveTo(10.664f, 9.213f, 10.509f, 8.432f, 10.208f, 7.704f)
                        curveTo(9.906f, 6.976f, 9.464f, 6.315f, 8.907f, 5.758f)
                    }
                    path(fill = SolidColor(Color(0xFF111111))) {
                        moveTo(5.944f, 12.323f)
                        curveTo(6.249f, 12.018f, 6.491f, 11.656f, 6.657f, 11.257f)
                        curveTo(6.822f, 10.858f, 6.907f, 10.431f, 6.907f, 9.999f)
                        curveTo(6.907f, 9.568f, 6.822f, 9.141f, 6.657f, 8.742f)
                        curveTo(6.491f, 8.343f, 6.249f, 7.981f, 5.944f, 7.676f)
                        lineTo(3.621f, 9.999f)
                        lineTo(5.944f, 12.323f)
                        close()
                    }
                }.build()

        return _VoiceMiddle!!
    }

@Suppress("ObjectPropertyName")
private var _VoiceMiddle: ImageVector? = null
