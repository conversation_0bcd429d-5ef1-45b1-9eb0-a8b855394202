package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.RoomRank: ImageVector
    get() {
        val current = _roomRank
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.RoomRank",
                defaultWidth = 12.0.dp,
                defaultHeight = 12.0.dp,
                viewportWidth = 12.0f,
                viewportHeight = 12.0f,
            ).apply {
                group(
                    // M 0 0 H 12 V 12 H 0z
                    clipPathData =
                        PathData {
                            // M 0 0
                            moveTo(x = 0.0f, y = 0.0f)
                            // H 12
                            horizontalLineTo(x = 12.0f)
                            // V 12
                            verticalLineTo(y = 12.0f)
                            // H 0z
                            horizontalLineTo(x = 0.0f)
                            close()
                        },
                ) {
                    // M3 4.5 H2.25 a1.25 1.25 0 0 1 0 -2.5 H3 m0 2.5 V1 h6 v3.5 m-6 0 a3 3 0 1 0 6 0 m0 0 h.75 a1.25 1.25 0 0 0 0 -2.5 H9 m-7 9 h8 M5 7.33 V8.5 c0 .27 -.24 .49 -.49 .6 C3.93 9.37 3.5 10.12 3.5 11 M7 7.33 V8.5 c0 .27 .24 .49 .49 .6 C8.07 9.37 8.5 10.12 8.5 11
                    path(
                        stroke = SolidColor(Color(0xFFFFFFFF)),
                        strokeLineCap = StrokeCap.Round,
                        strokeLineJoin = StrokeJoin.Round,
                        strokeLineWidth = 1.0f,
                    ) {
                        // M 3 4.5
                        moveTo(x = 3.0f, y = 4.5f)
                        // H 2.25
                        horizontalLineTo(x = 2.25f)
                        // a 1.25 1.25 0 0 1 0 -2.5
                        arcToRelative(
                            a = 1.25f,
                            b = 1.25f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = true,
                            dx1 = 0.0f,
                            dy1 = -2.5f,
                        )
                        // H 3
                        horizontalLineTo(x = 3.0f)
                        // m 0 2.5
                        moveToRelative(dx = 0.0f, dy = 2.5f)
                        // V 1
                        verticalLineTo(y = 1.0f)
                        // h 6
                        horizontalLineToRelative(dx = 6.0f)
                        // v 3.5
                        verticalLineToRelative(dy = 3.5f)
                        // m -6 0
                        moveToRelative(dx = -6.0f, dy = 0.0f)
                        // a 3 3 0 1 0 6 0
                        arcToRelative(
                            a = 3.0f,
                            b = 3.0f,
                            theta = 0.0f,
                            isMoreThanHalf = true,
                            isPositiveArc = false,
                            dx1 = 6.0f,
                            dy1 = 0.0f,
                        )
                        // m 0 0
                        moveToRelative(dx = 0.0f, dy = 0.0f)
                        // h 0.75
                        horizontalLineToRelative(dx = 0.75f)
                        // a 1.25 1.25 0 0 0 0 -2.5
                        arcToRelative(
                            a = 1.25f,
                            b = 1.25f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = false,
                            dx1 = 0.0f,
                            dy1 = -2.5f,
                        )
                        // H 9
                        horizontalLineTo(x = 9.0f)
                        // m -7 9
                        moveToRelative(dx = -7.0f, dy = 9.0f)
                        // h 8
                        horizontalLineToRelative(dx = 8.0f)
                        // M 5 7.33
                        moveTo(x = 5.0f, y = 7.33f)
                        // V 8.5
                        verticalLineTo(y = 8.5f)
                        // c 0 0.27 -0.24 0.49 -0.49 0.6
                        curveToRelative(
                            dx1 = 0.0f,
                            dy1 = 0.27f,
                            dx2 = -0.24f,
                            dy2 = 0.49f,
                            dx3 = -0.49f,
                            dy3 = 0.6f,
                        )
                        // C 3.93 9.37 3.5 10.12 3.5 11
                        curveTo(
                            x1 = 3.93f,
                            y1 = 9.37f,
                            x2 = 3.5f,
                            y2 = 10.12f,
                            x3 = 3.5f,
                            y3 = 11.0f,
                        )
                        // M 7 7.33
                        moveTo(x = 7.0f, y = 7.33f)
                        // V 8.5
                        verticalLineTo(y = 8.5f)
                        // c 0 0.27 0.24 0.49 0.49 0.6
                        curveToRelative(
                            dx1 = 0.0f,
                            dy1 = 0.27f,
                            dx2 = 0.24f,
                            dy2 = 0.49f,
                            dx3 = 0.49f,
                            dy3 = 0.6f,
                        )
                        // C 8.07 9.37 8.5 10.12 8.5 11
                        curveTo(
                            x1 = 8.07f,
                            y1 = 9.37f,
                            x2 = 8.5f,
                            y2 = 10.12f,
                            x3 = 8.5f,
                            y3 = 11.0f,
                        )
                    }
                }
            }.build()
            .also { _roomRank = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.RoomRank,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((12.0).dp)
                        .height((12.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _roomRank: ImageVector? = null
