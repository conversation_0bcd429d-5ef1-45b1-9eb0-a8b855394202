package com.buque.wakoo.ui.screens.voice

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyHorizontalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.buque.wakoo.bean.VoiceCardItem
import com.buque.wakoo.network.api.bean.VoiceBackground
import com.buque.wakoo.network.api.bean.VoicePublishConfig
import com.buque.wakoo.network.api.bean.VoiceTag
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.GreenChecked
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.media.manager.PlayMediaItem
import com.buque.wakoo.ui.widget.voice.UserVoiceListCard
import com.buque.wakoo.viewmodel.VoicePublishViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VoicePublishPreviewScreen(
    item: VoiceCardItem,
    playItem: PlayMediaItem,
    config: VoicePublishConfig,
    viewModel: VoicePublishViewModel,
    backTo: () -> Unit = {},
    closeTo: () -> Unit = {},
) {
    val previewItem by remember(config) {
        derivedStateOf {
            item.copy(background = config.background[viewModel.selectedThemeIndex].resource)
        }
    }

    val coroutineScope = rememberCoroutineScope()

    TitleScreenScaffold(
        title = "预览声音内容",
        modifier = Modifier.fillMaxSize(),
    ) { paddingValues ->
        Column(modifier = Modifier.padding(paddingValues)) {
            val isPlaying by remember {
                derivedStateOf {
                    MediaPlayerManager.actuallyPlayingTag.value == playItem.tag
                }
            }

            var refreshFlag by remember {
                mutableIntStateOf(0)
            }

            if (isPlaying) {
                LaunchedEffect(Unit) {
                    while (isActive) {
                        delay(1000)
                        refreshFlag++
                    }
                }
            }

            val duration by remember(item) {
                derivedStateOf {
                    if (isPlaying) {
                        refreshFlag // 这是一个刷新标志，不能删除
                        MediaPlayerManager
                            .getPlayerDuration(playItem)
                            .minus(
                                MediaPlayerManager.getPlayerPosition(playItem),
                            ).div(
                                1000,
                            ).toInt()
                    } else {
                        item.duration
                    }
                }
            }

            val lifecycleOwner = LocalLifecycleOwner.current

            // 清理资源
            DisposableEffect(Unit) {
                val observer =
                    LifecycleEventObserver { source, event ->
                        if (event == Lifecycle.Event.ON_STOP) {
                            MediaPlayerManager.pause(playItem)
                        }
                    }

                lifecycleOwner.lifecycle.addObserver(observer)

                onDispose {
                    lifecycleOwner.lifecycle.removeObserver(observer)
                }
            }

            UserVoiceListCard(
                item = previewItem,
                showOwnerInfo = true,
                modifier =
                    Modifier
                        .padding(
                            start = 24.dp,
                            top = 12.dp,
                            end = 24.dp,
                            bottom = 20.dp,
                        )
                        .fillMaxWidth()
                        .weight(1f),
                isPlaying = isPlaying,
                duration = duration,
                useWeight = true,
                showActionArea = false,
            ) {
                if (MediaPlayerManager.playbackRequestedTag.value == playItem.tag) {
                    MediaPlayerManager.pause(playItem)
                } else {
                    MediaPlayerManager.play(playItem)
                }
            }

            Text(
                text = "请选择卡片主题样式",
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF111111),
                modifier = Modifier.padding(start = 16.dp),
            )

            SizeHeight(12.dp)

            // 主题选择器
            val sections = config.background.chunked(8)

            LazyHorizontalGrid(
                rows = GridCells.Fixed(if (config.background.size > 4) 2 else 1),
                modifier =
                    Modifier
                        .padding(horizontal = 16.dp)
                        .height(if (config.background.size > 4) 168.dp else 80.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                sections.forEachIndexed { index, items ->
                    itemsIndexed(
                        items = items,
                    ) { index, background ->
                        ThemeOption(
                            background = background,
                            isSelected = index == viewModel.selectedThemeIndex,
                            onClick = { viewModel.selectedThemeIndex = index },
                        )
                    }
                }
            }

            SizeHeight(24.dp)

            val loading = LocalLoadingManager.current

            // 底部按钮
            Row(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
            ) {
                // 返回修改按钮
                OutlinedButton(
                    text = "返回修改",
                    onClick = backTo,
                    modifier = Modifier.weight(1f),
                )

                // 确认发布按钮
                GradientButton(
                    text = "确认发布",
                    onClick = {
                        loading.show(coroutineScope) {
                            if (viewModel.publish(previewItem)) {
                                closeTo()
                            }
                        }
                    },
                    modifier = Modifier.weight(1f),
                )
            }

            SizeHeight(24.dp)
        }
    }
}

@Composable
private fun ThemeOption(
    background: VoiceBackground,
    isSelected: Boolean,
    onClick: () -> Unit,
) {
    Box(
        modifier =
            Modifier
                .requiredSize(80.dp)
                .clip(RoundedCornerShape(8.dp))
                .clickable(onClick = onClick)
                .border(
                    width = if (isSelected) 1.5.dp else 0.dp,
                    color = if (isSelected) Color(0xFF66FE6B) else Color.Transparent,
                    shape = RoundedCornerShape(8.dp),
                ),
        contentAlignment = Alignment.BottomEnd,
    ) {
        NetworkImage(
            data = background.icon,
            modifier = Modifier.fillMaxSize(),
        )
        if (isSelected) {
            Image(
                imageVector = WakooIcons.GreenChecked,
                contentDescription = null,
                modifier =
                    Modifier
                        .size(20.dp),
            )
        }
    }
}

@Preview
@Composable
private fun VoicePublishPreviewScreenPreview() {
    WakooTheme {
        val context = LocalContext.current
        VoicePublishPreviewScreen(
            VoiceCardItem.preview,
            playItem =
                PlayMediaItem.prefixTagAudio(
                    url = "",
                    prefix = "publish-voice-",
                ),
            VoicePublishConfig(
                background =
                    listOf(
                        VoiceBackground("", 0, ""),
                        VoiceBackground("", 1, ""),
                    ),
                tags = listOf(VoiceTag(0, "就是"), VoiceTag(1, "阿珂")),
            ),
            remember {
                VoicePublishViewModel(context)
            },
        )
    }
}
