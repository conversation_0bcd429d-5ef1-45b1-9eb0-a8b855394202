package com.buque.wakoo.ui.screens.voice

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.manager.AudioRecordManager
import com.buque.wakoo.navigation.AppNavController
import com.buque.wakoo.navigation.AppNavDisplay
import com.buque.wakoo.navigation.CtrlKey
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.VoicePublishRoute
import com.buque.wakoo.navigation.appEntry
import com.buque.wakoo.navigation.appEntryProvider
import com.buque.wakoo.navigation.rememberAppNavController
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.media.manager.releaseIf
import com.buque.wakoo.ui.widget.state.requireData
import com.buque.wakoo.viewmodel.VoicePublishViewModel

object VoicePublishNavCtrlKey : CtrlKey<AppNavController>

@Composable
fun VoicePublishHostScreen(onBack: () -> Unit) {
    val controller =
        rememberAppNavController(
            key = VoicePublishNavCtrlKey,
            VoicePublishRoute.Edit,
        )

    val context = LocalContext.current

    val viewModel = viewModel<VoicePublishViewModel>(factory = VoicePublishViewModel.Factory(context))

    // 当从返回栈退出的时候才清空录音缓存
    DisposableEffect(Unit) {
        onDispose {
            MediaPlayerManager.releaseIf {
                it.tag.startsWith("publish-voice-")
            }
            AudioRecordManager.singletonInstance.cleanup()
        }
    }

    LocalAppNavController.ProvideController(controller) {
        AppNavDisplay(
            backStack = controller.backStack,
            entryProvider =
                appEntryProvider {
                    appEntry<VoicePublishRoute.Edit> {
                        VoicePublishScreen(viewModel) { item, playItem ->
                            controller.push(VoicePublishRoute.Preview(item, playItem))
                        }
                    }
                    appEntry<VoicePublishRoute.Preview> {
                        val config =
                            viewModel.configStateFlow
                                .collectAsStateWithLifecycle()
                                .value.requireData

                        VoicePublishPreviewScreen(
                            item = it.item,
                            playItem = it.playItem,
                            config = config,
                            viewModel = viewModel,
                            backTo = {
                                controller.popIs<VoicePublishRoute.Preview>()
                            },
                        ) {
                            onBack()
                        }
                    }
                },
        )
    }
}
