package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Exit: ImageVector
    get() {
        val current = _exit
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.Exit",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // M5 22 a1 1 0 0 1 -1 -1 V3 a1 1 0 0 1 1 -1 h14 a1 1 0 0 1 1 1 v3 h-2 V4 H6 v16 h12 v-2 h2 v3 a1 1 0 0 1 -1 1z m13 -6 v-3 h-7 v-2 h7 V8 l5 4z
                path(
                    fill = SolidColor(Color(0xFF111111)),
                    stroke = null,
                    strokeLineWidth = 0.0f,
                    fillAlpha = 1.0f,
                    strokeAlpha = 1.0f,
                    pathFillType = androidx.compose.ui.graphics.PathFillType.Companion.NonZero,
                ) {
                    // 这是SVG路径的第一部分 (门框)
                    // M5 22 C4.447... 22 4 21.552... 4 21 V3...
                    moveTo(5.0f, 22.0f)
                    curveTo(4.4477f, 22.0f, 4.0f, 21.5523f, 4.0f, 21.0f)
                    verticalLineTo(3.0f)
                    curveTo(4.0f, 2.4477f, 4.4477f, 2.0f, 5.0f, 2.0f)
                    horizontalLineTo(19.0f)
                    curveTo(19.5523f, 2.0f, 20.0f, 2.4477f, 20.0f, 3.0f)
                    verticalLineTo(6.0f)
                    horizontalLineTo(18.0f)
                    verticalLineTo(4.0f)
                    horizontalLineTo(6.0f)
                    verticalLineTo(20.0f)
                    horizontalLineTo(18.0f)
                    verticalLineTo(18.0f)
                    horizontalLineTo(20.0f)
                    verticalLineTo(21.0f)
                    curveTo(20.0f, 21.5523f, 19.5523f, 22.0f, 19.0f, 22.0f)
                    horizontalLineTo(5.0f)
                    close()

                    // 这是SVG路径的第二部分 (箭头)
                    // M18 16 V13 H11...
                    moveTo(18.0f, 16.0f)
                    verticalLineTo(13.0f)
                    horizontalLineTo(11.0f)
                    verticalLineTo(11.0f)
                    horizontalLineTo(18.0f)
                    verticalLineTo(8.0f)
                    lineTo(23.0f, 12.0f)
                    lineTo(18.0f, 16.0f)
                    close()
                }
            }.build()
            .also { _exit = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Exit,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _exit: ImageVector? = null
