package com.buque.wakoo.ui.widget.media.previewer

import kotlinx.serialization.Serializable

/**
 * 代表一个媒体相册，包含一组媒体项目。
 * @property items 相册中的媒体项目列表。
 */
@Serializable
data class MediaAlbum(
    val items: List<MediaItem>,
)

/**
 * 代表一个可显示的媒体项目（如图片）的密封接口。
 * @property placeholderImageUrl 占位图/缩略图的URL。
 * @property aspectRatio 媒体的原始宽高比。
 */
@Serializable
sealed interface MediaItem {
    val placeholderImageUrl: String
    val aspectRatio: Float

    /**
     * 代表一个图片类型的媒体项目。
     * @property fullSizedUrl 完整尺寸图片的URL。
     */
    @Serializable
    data class Image(
        val fullSizedUrl: String,
        override val placeholderImageUrl: String,
        override val aspectRatio: Float,
    ) : MediaItem
}

/**
 * 将一个 [MediaItem] 与其在列表中的索引绑定。
 * 这对于在转场时定位原始UI元素至关重要。
 */
@Serializable
data class IndexedMediaItem(
    val index: Int,
    val item: MediaItem,
)

/**
 * 传递给媒体查看器屏幕的导航参数或“Key”。
 * @property album 包含所有媒体项的相册。
 * @property initialIndex 用户点击进入查看器时的初始图片索引。
 */
@Serializable
data class MediaViewerKey(
    val album: MediaAlbum,
    val initialIndex: Int,
) {
    /**
     * 方便地获取初始被点击的媒体项及其索引。
     */
    val initialItem = IndexedMediaItem(initialIndex, album.items[initialIndex])
}
