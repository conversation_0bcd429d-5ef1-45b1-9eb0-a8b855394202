package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.GreenChecked: ImageVector
    get() {
        val current = _greenChecked
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.GreenChecked",
                defaultWidth = 20.0.dp,
                defaultHeight = 20.0.dp,
                viewportWidth = 20.0f,
                viewportHeight = 20.0f,
            ).apply {
                // M0 8 a8 8 0 0 1 8 -8 h12 v12 a8 8 0 0 1 -8 8 H0z
                path(
                    fill = SolidColor(Color(0xFF66FE6B)),
                ) {
                    // M 0 8
                    moveTo(x = 0.0f, y = 8.0f)
                    // a 8 8 0 0 1 8 -8
                    arcToRelative(
                        a = 8.0f,
                        b = 8.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 8.0f,
                        dy1 = -8.0f,
                    )
                    // h 12
                    horizontalLineToRelative(dx = 12.0f)
                    // v 12
                    verticalLineToRelative(dy = 12.0f)
                    // a 8 8 0 0 1 -8 8
                    arcToRelative(
                        a = 8.0f,
                        b = 8.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -8.0f,
                        dy1 = 8.0f,
                    )
                    // H 0z
                    horizontalLineTo(x = 0.0f)
                    close()
                }
                // M8.33 12.64 16 4.98 l1.18 1.18 L8.33 15 l-5.3 -5.3 1.18 -1.18z
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 8.33 12.64
                    moveTo(x = 8.33f, y = 12.64f)
                    // L 16 4.98
                    lineTo(x = 16.0f, y = 4.98f)
                    // l 1.18 1.18
                    lineToRelative(dx = 1.18f, dy = 1.18f)
                    // L 8.33 15
                    lineTo(x = 8.33f, y = 15.0f)
                    // l -5.3 -5.3
                    lineToRelative(dx = -5.3f, dy = -5.3f)
                    // l 1.18 -1.18z
                    lineToRelative(dx = 1.18f, dy = -1.18f)
                    close()
                }
            }.build()
            .also { _greenChecked = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.GreenChecked,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((20.0).dp)
                        .height((20.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _greenChecked: ImageVector? = null
