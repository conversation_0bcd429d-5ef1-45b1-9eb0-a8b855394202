package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.MobilePhone: ImageVector
    get() {
        if (_mobilePhone != null) return _mobilePhone!!

        _mobilePhone =
            ImageVector
                .Builder(
                    name = "smartphoneFill",
                    defaultWidth = 20.dp,
                    defaultHeight = 20.dp,
                    viewportWidth = 20f,
                    viewportHeight = 20f,
                ).apply {
                    path(
                        fill = SolidColor(Color(0xFF1A1A1A)),
                    ) {
                        moveTo(4.99935f, 1.66797f)
                        horizontalLineTo(14.9993f)
                        curveTo(15.4596f, 1.66797f, 15.8327f, 2.04107f, 15.8327f, 2.5013f)
                        verticalLineTo(17.5013f)
                        curveTo(15.8327f, 17.9616f, 15.4596f, 18.3346f, 14.9993f, 18.3346f)
                        horizontalLineTo(4.99935f)
                        curveTo(4.53912f, 18.3346f, 4.16602f, 17.9616f, 4.16602f, 17.5013f)
                        verticalLineTo(2.5013f)
                        curveTo(4.16602f, 2.04107f, 4.53912f, 1.66797f, 4.99935f, 1.66797f)
                        close()
                        moveTo(9.99935f, 14.168f)
                        curveTo(9.5391f, 14.168f, 9.16602f, 14.5411f, 9.16602f, 15.0013f)
                        curveTo(9.16602f, 15.4616f, 9.5391f, 15.8346f, 9.99935f, 15.8346f)
                        curveTo(10.4596f, 15.8346f, 10.8327f, 15.4616f, 10.8327f, 15.0013f)
                        curveTo(10.8327f, 14.5411f, 10.4596f, 14.168f, 9.99935f, 14.168f)
                        close()
                    }
                }.build()

        return _mobilePhone!!
    }

private var _mobilePhone: ImageVector? = null
