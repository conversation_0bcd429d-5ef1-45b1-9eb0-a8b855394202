package com.buque.wakoo.ui.widget.gift

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import com.buque.wakoo.bean.User
import com.yy.yyeva.inter.IEvaFetchResource
import com.yy.yyeva.mix.EvaResource
import com.yy.yyeva.mix.EvaSrc

class YYEVAResourceFetcher(
    val params: Map<String, Any?> = mapOf(),
    val context: Context? = null
) : IEvaFetchResource {

    private val bmpSet = hashSetOf<Bitmap?>()

    override fun releaseSrc(resources: List<EvaResource>) {
        //不需要在此释放,bitmap已经由glide代为管理
        synchronized(bmpSet) {
            bmpSet.forEach {
                it?.let {
                    if (!it.isRecycled) {
                        it.recycle()
                    }
                }
            }
            bmpSet.clear()
        }
    }

    override fun setImage(resource: EvaResource, result: (Bitmap?, EvaSrc.FitType?) -> Unit) {
        val tag = resource.tag
        try {
            when (tag) {
                "avatar" -> {//头像
                    val user = params.get("user") as User
//                    val avatarBmp = Glide.with(app).asBitmap()
//                        .load(user.avatarUrl)
//                        .timeout(4500)
//                        .fallback(R.drawable.ic_default_male_unselected)
//                        .submit().get()
//                    result(avatarBmp, null)
                    result(null, null)
                }

                "level" -> { //等级
                    (context).let { ctx ->
//                        val user = params.get("user") as User
//                        if (ctx == null) {
//                            result(null, null)
//                        } else {
//                            if (AppUserPartition.isUCOO) {
//                                val bitmap = UserLevelView.generateBitmap(ctx, user.level)
//                                synchronized(bmpSet) {
//                                    bmpSet.add(bitmap)
//                                }
//                                result(bitmap, null)
//                            } else {
//                                createBitmapFromComposable(ctx,
//                                    content = {
//                                        WealthLevelComposeView(user)
//                                    }, callback = { bmp ->
//                                        synchronized(bmpSet) {
//                                            bmpSet.add(bmp)
//                                        }
//                                        result(bmp, null)
//                                    }
//                                )
//                            }
//                        }
                        result(null, null)
                    }
                }

                else -> {
                    result(null, null)
                }
            }
        } catch (e: Exception) {
            result(null, null)
        }
    }

    override fun setText(resource: EvaResource, result: (EvaResource) -> Unit) {
        val tag = resource.tag.trim()
        try {
            when (tag) {
                "nickname" -> {
                    val user = params.get("user") as User
                    resource.text = user.name
                    resource.textColor = Color.parseColor("#fffffe")
                    resource.textAlign = "left"
                    resource.fontSize = 22
                    result(resource)
                }

                "customText1" -> {
                    resource.text = "进入聊天室"
                    resource.textColor = Color.parseColor("#fffffe")
                    resource.textAlign = "left"
                    resource.fontSize = 22
                    result(resource)
                }

                else -> {
                    result(resource)
                }
            }
        } catch (e: Exception) {
            result(resource)
        }
    }
}