package com.buque.wakoo.ui.screens.liveroom.panel

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.IconLabel
import com.buque.wakoo.bean.LocalSelfUserProvider
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.BottomPanelWidgetDialog
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.dialog.AnyPopDialogProperties
import com.buque.wakoo.ui.dialog.BottomPanelScaffold
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.SimpleDoubleActionDialog
import com.buque.wakoo.ui.icons.Collapse
import com.buque.wakoo.ui.icons.Exit
import com.buque.wakoo.ui.icons.MicMode
import com.buque.wakoo.ui.icons.Report
import com.buque.wakoo.ui.icons.RoomMode
import com.buque.wakoo.ui.icons.RoomSettings
import com.buque.wakoo.ui.icons.Share
import com.buque.wakoo.ui.icons.Trash
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.RoomRole
import com.buque.wakoo.ui.widget.SizeHeight
import kotlinx.serialization.Serializable

// 房间管理
private const val MENU_TYPE_ROOM_MANAGE = 1

// 房间模式
private const val MENU_TYPE_ROOM_MODE = 2

// 上麦模式
private const val MENU_TYPE_MIC_MODE = 3

// 清空心动值
private const val MENU_TYPE_CLEAR_SCORE = 4

// 收起房间
private const val MENU_TYPE_COLLAPSE_ROOM = 5

// 分享房间
private const val MENU_TYPE_SHARE_ROOM = 6

// 举报房间
private const val MENU_TYPE_REPORT_ROOM = 7

// 退出房间
private const val MENU_TYPE_EXIT_ROOM = 8

@Composable
fun DialogScope.LiveRoomSettingPanel(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    val rootNavController = LocalAppNavController.root
    BottomPanelScaffold(
        title = "房间设置",
        useClose = true,
        modifier = modifier,
    ) {
        val selfRole by roomInfoState.rememberRoomRoleState(LocalSelfUserProvider.currentId)
        val menuItems by remember {
            derivedStateOf {
                buildList {
                    if (selfRole != RoomRole.Member) {
                        add(IconLabel(MENU_TYPE_ROOM_MANAGE, WakooIcons.RoomSettings, "房间管理"))
                        add(IconLabel(MENU_TYPE_ROOM_MODE, WakooIcons.RoomMode, "房间模式"))
                        add(IconLabel(MENU_TYPE_MIC_MODE, WakooIcons.MicMode, "上麦模式"))
                        add(IconLabel(MENU_TYPE_CLEAR_SCORE, WakooIcons.Trash, "清空心动值"))
                    }
                    add(IconLabel(MENU_TYPE_COLLAPSE_ROOM, WakooIcons.Collapse, "收起房间"))
                    add(IconLabel(MENU_TYPE_SHARE_ROOM, WakooIcons.Share, "分享房间"))

                    if (selfRole == RoomRole.Member) {
                        add(IconLabel(MENU_TYPE_REPORT_ROOM, WakooIcons.Report, "举报房间"))
                    }

                    add(IconLabel(MENU_TYPE_EXIT_ROOM, WakooIcons.Exit, "退出房间"))
                }
            }
        }

        FlowRow(
            modifier = Modifier.fillMaxWidth(),
            maxItemsInEachRow = 4,
            horizontalArrangement = Arrangement.SpaceAround,
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            menuItems.forEach {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier =
                        Modifier.noEffectClick {
                            when (it.id) {
                                MENU_TYPE_ROOM_MANAGE -> {
                                    dismiss()
                                    roomInfoState.sendEvent(
                                        RoomEvent.PanelDialog { roomInfoState ->
                                            LiveRoomManagePanel(roomInfoState) {
                                                roomInfoState.sendEvent(
                                                    RoomEvent.PanelDialog { roomInfoState ->
                                                        LiveRoomSettingPanel(roomInfoState)
                                                    },
                                                )
                                            }
                                        },
                                    )
                                }

                                MENU_TYPE_ROOM_MODE -> {
                                    dismiss()
                                    roomInfoState.sendEvent(
                                        RoomEvent.PanelDialog { roomInfoState ->
                                            LiveRoomModeSettingsPanel(roomInfoState) {
                                                roomInfoState.sendEvent(
                                                    RoomEvent.PanelDialog { roomInfoState ->
                                                        LiveRoomSettingPanel(roomInfoState)
                                                    },
                                                )
                                            }
                                        },
                                    )
                                }

                                MENU_TYPE_MIC_MODE -> {
                                    dismiss()
                                    roomInfoState.sendEvent(
                                        RoomEvent.PanelDialog { roomInfoState ->
                                            LiveMicModeSettingsPanel(roomInfoState) {
                                                roomInfoState.sendEvent(
                                                    RoomEvent.PanelDialog { roomInfoState ->
                                                        LiveRoomSettingPanel(roomInfoState)
                                                    },
                                                )
                                            }
                                        },
                                    )
                                }

                                MENU_TYPE_CLEAR_SCORE -> {
                                    roomInfoState.sendEvent(
                                        RoomEvent.CustomDialog(
                                            AnyPopDialogProperties(
                                                useSystemDialog = true,
                                                useCustomAnimation = false,
                                            ),
                                        ) {
                                            SimpleDoubleActionDialog(
                                                content = "确定要将麦位上所有心动值归0吗？",
                                                cancelButtonConfig = DialogButtonStyles.Primary.copy(text = "确认"),
                                                confirmButtonConfig = DialogButtonStyles.Secondary.copy(text = "取消"),
                                                onCancel = {
                                                    roomInfoState.sendEvent(RoomEvent.ClearLoveValue)
                                                    dismiss()
                                                },
                                                onConfirm = {
                                                    dismiss()
                                                },
                                            )
                                        },
                                    )
                                }

                                MENU_TYPE_COLLAPSE_ROOM -> {
                                    dismiss()
                                    roomInfoState.sendEvent(RoomEvent.CollapseRoom)
                                }

                                MENU_TYPE_SHARE_ROOM -> {
                                    dismiss()
                                    rootNavController.push(Route.SelectUser(2, roomInfoState.id))
                                }

                                MENU_TYPE_REPORT_ROOM -> {
                                    dismiss()
                                    rootNavController.push(Route.Report(2, roomInfoState.id))
                                }

                                MENU_TYPE_EXIT_ROOM -> {
                                    dismiss()
                                    roomInfoState.sendEvent(RoomEvent.ExitRoom)
                                }
                            }
                        },
                ) {
                    Box(
                        modifier =
                            Modifier
                                .size(56.dp)
                                .background(Color.White, CircleShape),
                        contentAlignment = Alignment.Center,
                    ) {
                        Icon(
                            imageVector = it.icon,
                            contentDescription = null,
                            modifier = Modifier.size(24.dp),
                        )
                    }
                    SizeHeight(12.dp)
                    BasicText(
                        it.label,
                        modifier = Modifier.width(56.dp),
                        style =
                            MaterialTheme.typography.labelLarge.copy(
                                color = Color(0xFF111111),
                                textAlign = TextAlign.Center,
                            ),
                        maxLines = 1,
                        autoSize =
                            TextAutoSize.StepBased(
                                minFontSize = 9.sp,
                                maxFontSize = MaterialTheme.typography.labelLarge.fontSize,
                            ),
                        overflow = TextOverflow.Ellipsis,
                    )
                }
            }

            val rem = menuItems.size.rem(4)
            if (rem > 0) {
                repeat(4 - rem) {
                    Box(modifier = Modifier.size(56.dp))
                }
            }
        }
    }
}

@Serializable
class LiveRoomSettingPanelDialog : BottomPanelWidgetDialog<LiveRoomInfoState>() {
    @Composable
    override fun DialogScope.Content(roomInfoState: LiveRoomInfoState) {
        LiveRoomSettingPanel(roomInfoState)
    }
}
