package com.buque.wakoo.ui.widget.image

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.translate
import androidx.compose.ui.graphics.isSpecified
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import kotlin.math.min

/**
 * 一个自定义 Painter，用于绘制带约束的、居中的 ImageVector。
 *
 * @param vectorPainter 从 ImageVector 创建的内部 Painter。
 * @param backgroundColor 背景颜色。
 * @param tintColor 图案的颜色。
 * @param scaleFactor 图案尺寸相对于容器尺寸的百分比上限（例如 0.3f 表示 30%）。
 */
class ConstrainedCenteredVectorPainter(
    private val vectorPainter: Painter,
    private val backgroundColor: Color,
    private val tintColor: Color = Color.Unspecified,
    private val scaleFactor: Float = 0.7f,
) : Painter() {
    override val intrinsicSize: Size = Size.Unspecified

    override fun DrawScope.onDraw() {
        // 1. 绘制纯色背景
        drawRect(color = backgroundColor)

        // 2. 获取 ImageVector 的固有尺寸和宽高比
        val vectorIntrinsicSize = vectorPainter.intrinsicSize
        if (vectorIntrinsicSize.width == 0f || vectorIntrinsicSize.height == 0f) {
            return
        }

        // 3. 计算图案的最大允许尺寸
        val maxDrawableWidth = size.width * scaleFactor
        val maxDrawableHeight = size.height * scaleFactor

        // 4. 根据宽高比和最大允许尺寸，计算最终绘制尺寸
        val scaleX = maxDrawableWidth / vectorIntrinsicSize.width
        val scaleY = maxDrawableHeight / vectorIntrinsicSize.height
        val finalScale = min(scaleX, scaleY)

        val finalDrawableWidth = vectorIntrinsicSize.width * finalScale
        val finalDrawableHeight = vectorIntrinsicSize.height * finalScale

        // 5. 计算居中偏移量
        val offsetX = (size.width - finalDrawableWidth) / 2f
        val offsetY = (size.height - finalDrawableHeight) / 2f

        // 6. 【最终正确的方式】
        //    先平移画布，然后在平移后的作用域内绘制 Painter
        translate(left = offsetX, top = offsetY) {
            // 使用 with(painter) 将 painter 引入作用域
            // 这样在其内部调用 draw() 时，就能正确匹配到 DrawScope receiver
            with(vectorPainter) {
                draw(
                    size = Size(finalDrawableWidth, finalDrawableHeight),
                    colorFilter = tintColor.takeIf { it.isSpecified }?.let { ColorFilter.tint(it) },
                )
            }
        }
    }
}

/**
 * 一个 Composable 函数，用于创建并记住我们的自定义 Painter。
 * 这样做可以利用 rememberVectorPainter 来高效处理 ImageVector 到 Painter 的转换。
 */
@Composable
fun rememberConstrainedCenteredVectorPainter(
    imageVector: ImageVector,
    backgroundColor: Color,
    tintColor: Color = Color.Unspecified,
    scaleFactor: Float = 0.7f,
): Painter {
    // 将 ImageVector 转换为一个基础的 VectorPainter
    val vectorPainter = rememberVectorPainter(image = imageVector)

    // 使用 remember 来创建并缓存我们的自定义 Painter 实例
    // 只有当参数变化时，才会重新创建
    return remember(vectorPainter, tintColor, backgroundColor, scaleFactor) {
        ConstrainedCenteredVectorPainter(
            vectorPainter = vectorPainter,
            backgroundColor = backgroundColor,
            tintColor = tintColor,
            scaleFactor = scaleFactor,
        )
    }
}
