package com.buque.wakoo.ui.screens.liveroom.screen

import android.Manifest
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.bean.LocalSelfUserProvider
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.navigation.AppNavController
import com.buque.wakoo.navigation.AppNavDisplay
import com.buque.wakoo.navigation.CtrlKey
import com.buque.wakoo.navigation.LiveRoomRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.appEntry
import com.buque.wakoo.navigation.appEntryProvider
import com.buque.wakoo.navigation.rememberAppNavController
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.utils.PermissionUtils

object LiveRoomNavCtrlKey : CtrlKey<AppNavController>

@Composable
fun LiveRoomHostScreen(basicInfo: BasicRoomInfo) {
    val controller =
        rememberAppNavController(
            key = LiveRoomNavCtrlKey,
            LiveRoomRoute.Home,
        )

    val context = LocalContext.current

    val viewModel = LiveRoomManager.attachLiveRoomViewModel(basicInfo)

    val isInMic by viewModel.roomInfoState.rememberInMicState(LocalSelfUserProvider.currentId)

    val launcher =
        rememberLauncherForActivityResult(ActivityResultContracts.RequestPermission()) { result ->
            if (!result && isInMic) {
                showToast("未获取录音权限，麦上说话其他用户将听不到")
            }
        }

    if (isInMic) {
        LaunchedEffect(Unit) {
            viewModel.rtcUpMic()
            if (!PermissionUtils.hasAudioPermission(context)) {
                launcher.launch(Manifest.permission.RECORD_AUDIO)
            }
        }
    }

    LocalAppNavController.ProvideController(controller) {
        AppNavDisplay(
            backStack = controller.backStack,
            entryProvider =
                appEntryProvider {
                    appEntry<LiveRoomRoute.Home> {
                        BackHandler {
                            viewModel.sendEvent(RoomEvent.CollapseRoom)
                        }
                        LiveRoomManager.AutoCollapse(viewModel.roomId)

                        LifecycleEventEffect(Lifecycle.Event.ON_START) {
                            viewModel.refreshRoomInfo()
                        }

                        LiveRoomScreen(viewModel)
                    }
                    appEntry<LiveRoomRoute.BlackList> {
                        RoomBlackListScreen(viewModel)
                    }
                    appEntry<LiveRoomRoute.AdminList> {
                        RoomAdminListScreen(viewModel)
                    }
                },
        )
    }
}
