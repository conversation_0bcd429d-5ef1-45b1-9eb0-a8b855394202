package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Favorited: ImageVector
    get() {
        val current = _favorited
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.Favorited",
                defaultWidth = 16.0.dp,
                defaultHeight = 16.0.dp,
                viewportWidth = 16.0f,
                viewportHeight = 16.0f,
            ).apply {
                // m9.58 5.54 .17 .38 .42 .05 3.74 .44 -2.77 2.56 -.3 .28 .08 .42 .73 3.69 -3.28 -1.84 L8 11.32 l-.37 .2 -3.28 1.84 .73 -3.7 .09 -.4 -.31 -.29 -2.77 -2.56 3.74 -.44 .42 -.05 .17 -.38 L8 2.12z
                path(
                    fill =
                        Brush.linearGradient(
                            0.0f to Color(0xFFA3FF2C),
                            1.0f to Color(0xFF31FFA1),
                            start = Offset(x = 0.392f, y = 7.57f),
                            end = Offset(x = 15.609f, y = 7.57f),
                        ),
                    stroke = SolidColor(Color(0xFF111111)),
                    strokeLineWidth = 1.5f,
                ) {
                    // M 9.58 5.54
                    moveTo(x = 9.58f, y = 5.54f)
                    // l 0.17 0.38
                    lineToRelative(dx = 0.17f, dy = 0.38f)
                    // l 0.42 0.05
                    lineToRelative(dx = 0.42f, dy = 0.05f)
                    // l 3.74 0.44
                    lineToRelative(dx = 3.74f, dy = 0.44f)
                    // l -2.77 2.56
                    lineToRelative(dx = -2.77f, dy = 2.56f)
                    // l -0.3 0.28
                    lineToRelative(dx = -0.3f, dy = 0.28f)
                    // l 0.08 0.42
                    lineToRelative(dx = 0.08f, dy = 0.42f)
                    // l 0.73 3.69
                    lineToRelative(dx = 0.73f, dy = 3.69f)
                    // l -3.28 -1.84
                    lineToRelative(dx = -3.28f, dy = -1.84f)
                    // L 8 11.32
                    lineTo(x = 8.0f, y = 11.32f)
                    // l -0.37 0.2
                    lineToRelative(dx = -0.37f, dy = 0.2f)
                    // l -3.28 1.84
                    lineToRelative(dx = -3.28f, dy = 1.84f)
                    // l 0.73 -3.7
                    lineToRelative(dx = 0.73f, dy = -3.7f)
                    // l 0.09 -0.4
                    lineToRelative(dx = 0.09f, dy = -0.4f)
                    // l -0.31 -0.29
                    lineToRelative(dx = -0.31f, dy = -0.29f)
                    // l -2.77 -2.56
                    lineToRelative(dx = -2.77f, dy = -2.56f)
                    // l 3.74 -0.44
                    lineToRelative(dx = 3.74f, dy = -0.44f)
                    // l 0.42 -0.05
                    lineToRelative(dx = 0.42f, dy = -0.05f)
                    // l 0.17 -0.38
                    lineToRelative(dx = 0.17f, dy = -0.38f)
                    // L 8 2.12z
                    lineTo(x = 8.0f, y = 2.12f)
                    close()
                }
            }.build()
            .also { _favorited = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Favorited,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((16.0).dp)
                        .height((16.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _favorited: ImageVector? = null
