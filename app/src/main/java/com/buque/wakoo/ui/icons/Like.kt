package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Like: ImageVector
    get() {
        val current = _Like
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.Like",
                defaultWidth = 16.0.dp,
                defaultHeight = 16.0.dp,
                viewportWidth = 16.0f,
                viewportHeight = 16.0f,
            ).apply {
                // M8 3.02 a4 4 0 0 1 5.65 5.64 L8 14.32 2.35 8.66 A4 4 0 0 1 8 3.02 m4.55 1.1 c-1 -1 -2.61 -1.05 -3.66 -.1 L8 4.8 l-.89 -.8 a2.67 2.67 0 0 0 -3.79 3.74 L8 12.44 l4.68 -4.7 a2.67 2.67 0 0 0 -.13 -3.63
                path(
                    fill = SolidColor(Color(0xFFB6B6B6)),
                ) {
                    // M 8 3.02
                    moveTo(x = 8.0f, y = 3.02f)
                    // a 4 4 0 0 1 5.65 5.64
                    arcToRelative(
                        a = 4.0f,
                        b = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 5.65f,
                        dy1 = 5.64f,
                    )
                    // L 8 14.32
                    lineTo(x = 8.0f, y = 14.32f)
                    // L 2.35 8.66
                    lineTo(x = 2.35f, y = 8.66f)
                    // A 4 4 0 0 1 8 3.02
                    arcTo(
                        horizontalEllipseRadius = 4.0f,
                        verticalEllipseRadius = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 8.0f,
                        y1 = 3.02f,
                    )
                    // m 4.55 1.1
                    moveToRelative(dx = 4.55f, dy = 1.1f)
                    // c -1 -1 -2.61 -1.05 -3.66 -0.1
                    curveToRelative(
                        dx1 = -1.0f,
                        dy1 = -1.0f,
                        dx2 = -2.61f,
                        dy2 = -1.05f,
                        dx3 = -3.66f,
                        dy3 = -0.1f,
                    )
                    // L 8 4.8
                    lineTo(x = 8.0f, y = 4.8f)
                    // l -0.89 -0.8
                    lineToRelative(dx = -0.89f, dy = -0.8f)
                    // a 2.67 2.67 0 0 0 -3.79 3.74
                    arcToRelative(
                        a = 2.67f,
                        b = 2.67f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -3.79f,
                        dy1 = 3.74f,
                    )
                    // L 8 12.44
                    lineTo(x = 8.0f, y = 12.44f)
                    // l 4.68 -4.7
                    lineToRelative(dx = 4.68f, dy = -4.7f)
                    // a 2.67 2.67 0 0 0 -0.13 -3.63
                    arcToRelative(
                        a = 2.67f,
                        b = 2.67f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -0.13f,
                        dy1 = -3.63f,
                    )
                }
            }.build()
            .also { _Like = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Like,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((16.0).dp)
                        .height((16.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _Like: ImageVector? = null
