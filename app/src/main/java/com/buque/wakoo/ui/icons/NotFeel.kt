package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import kotlin.Unit

public val WakooIcons.NotFeel: ImageVector
    get() {
        if (_not_fill != null) {
            return _not_fill!!
        }
        _not_fill = Builder(name = "Heart-line (1)", defaultWidth = 20.0.dp, defaultHeight =
                20.0.dp, viewportWidth = 20.0f, viewportHeight = 20.0f).apply {
            path(fill = SolidColor(Color(0xFF111111)), stroke = null, strokeLineWidth = 0.0f,
                    strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                    pathFillType = NonZero) {
                moveTo(10.001f, 3.774f)
                curveTo(11.958f, 2.017f, 14.983f, 2.075f, 16.869f, 3.964f)
                curveTo(18.754f, 5.854f, 18.819f, 8.864f, 17.066f, 10.828f)
                lineTo(10.0f, 17.904f)
                lineTo(2.934f, 10.828f)
                curveTo(1.181f, 8.864f, 1.246f, 5.849f, 3.131f, 3.964f)
                curveTo(5.018f, 2.078f, 8.038f, 2.014f, 10.001f, 3.774f)
                close()
                moveTo(15.689f, 5.142f)
                curveTo(14.44f, 3.89f, 12.423f, 3.839f, 11.114f, 5.014f)
                lineTo(10.002f, 6.013f)
                lineTo(8.888f, 5.015f)
                curveTo(7.576f, 3.838f, 5.563f, 3.89f, 4.31f, 5.143f)
                curveTo(3.068f, 6.384f, 3.006f, 8.373f, 4.15f, 9.686f)
                lineTo(10.0f, 15.545f)
                lineTo(15.85f, 9.686f)
                curveTo(16.994f, 8.372f, 16.933f, 6.388f, 15.689f, 5.142f)
                close()
            }
            path(fill = SolidColor(Color(0xFF111111)), stroke = null, strokeLineWidth = 0.0f,
                    strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                    pathFillType = NonZero) {
                moveTo(11.077f, 4.99f)
                lineTo(9.795f, 7.522f)
                horizontalLineTo(12.917f)
                lineTo(10.74f, 12.414f)
                horizontalLineTo(9.036f)
                lineTo(10.349f, 9.189f)
                horizontalLineTo(7.083f)
                lineTo(9.59f, 4.236f)
                lineTo(11.077f, 4.99f)
                close()
            }
        }
        .build()
        return _not_fill!!
    }

private var _not_fill: ImageVector? = null

@Preview
@Composable
private fun Preview(): Unit {
    Box(modifier = Modifier.padding(12.dp)) {
        Image(imageVector = WakooIcons.NotFeel, contentDescription = "")
    }
}
