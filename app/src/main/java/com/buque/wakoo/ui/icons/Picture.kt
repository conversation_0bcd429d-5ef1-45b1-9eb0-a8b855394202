package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.Picture: ImageVector
    get() {
        if (_ImageLine != null) {
            return _ImageLine!!
        }
        _ImageLine = ImageVector.Builder(
            name = "ImageLine",
            defaultWidth = 28.dp,
            defaultHeight = 28.dp,
            viewportWidth = 28f,
            viewportHeight = 28f
        ).apply {
            path(fill = SolidColor(Color(0xFF111111))) {
                moveTo(3.49f, 24.5f)
                curveTo(2.851f, 24.5f, 2.333f, 23.981f, 2.333f, 23.341f)
                verticalLineTo(4.659f)
                curveTo(2.333f, 4.019f, 2.865f, 3.5f, 3.49f, 3.5f)
                horizontalLineTo(24.51f)
                curveTo(25.149f, 3.5f, 25.667f, 4.019f, 25.667f, 4.659f)
                verticalLineTo(23.341f)
                curveTo(25.667f, 23.981f, 25.135f, 24.5f, 24.51f, 24.5f)
                horizontalLineTo(3.49f)
                close()
                moveTo(23.333f, 17.5f)
                verticalLineTo(5.833f)
                horizontalLineTo(4.667f)
                verticalLineTo(22.167f)
                lineTo(16.333f, 10.5f)
                lineTo(23.333f, 17.5f)
                close()
                moveTo(23.333f, 20.8f)
                lineTo(16.333f, 13.8f)
                lineTo(7.967f, 22.167f)
                horizontalLineTo(23.333f)
                verticalLineTo(20.8f)
                close()
                moveTo(9.333f, 12.833f)
                curveTo(8.045f, 12.833f, 7f, 11.789f, 7f, 10.5f)
                curveTo(7f, 9.211f, 8.045f, 8.167f, 9.333f, 8.167f)
                curveTo(10.622f, 8.167f, 11.667f, 9.211f, 11.667f, 10.5f)
                curveTo(11.667f, 11.789f, 10.622f, 12.833f, 9.333f, 12.833f)
                close()
            }
        }.build()

        return _ImageLine!!
    }

@Suppress("ObjectPropertyName")
private var _ImageLine: ImageVector? = null
