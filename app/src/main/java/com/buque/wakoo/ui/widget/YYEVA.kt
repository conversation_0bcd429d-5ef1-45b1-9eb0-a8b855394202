package com.buque.wakoo.ui.widget

import android.graphics.Bitmap
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.manager.DownloadManager
import com.buque.wakoo.utils.FileUtils
import com.yy.yyeva.EvaAnimConfig
import com.yy.yyeva.inter.IEvaAnimListener
import com.yy.yyeva.inter.IEvaFetchResource
import com.yy.yyeva.mix.EvaResource
import com.yy.yyeva.mix.EvaSrc
import com.yy.yyeva.util.EvaConstant
import com.yy.yyeva.util.ScaleType
import com.yy.yyeva.view.EvaAnimViewV3
import kotlinx.coroutines.delay
import kotlinx.coroutines.withTimeoutOrNull
import java.io.File
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

private val downloadPath: String by lazy {
    FileUtils.createDefaultGiftDownloadOutputFile(WakooApplication.instance).absolutePath
}

suspend fun getEffectFile(
    url: String?,
    timeout: Duration,
): File? {
    if (url.isNullOrBlank()) return null
    return withTimeoutOrNull(timeout) {
        try {
            DownloadManager
                .download(
                    url,
                    downloadPath,
                    url.split("/").last(),
                ).getOrNull()
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
}

/**
 * 动效组件
 *
 * @param key 标记运行的动效GiftEffect
 * @param files 需要运行的动效文件列表
 * @param iEvaFetchResource yyeva中的动效资源提供者, 如果不传的话有可能动效不播放
 * @param onStart 动效开始播放prepare = true时说明已经执行过startPlay, =false 时则反之
 * @param onComplete 所有的动效文件播放完毕
 */
@Composable
fun EffectVideoAnimationView(
    key: Any?,
    vararg files: File?,
    iEvaFetchResource: IEvaFetchResource? = null,
    onStart: (prepared: Boolean) -> Unit = {},
    onComplete: () -> Unit = {},
) {
    val playQueue =
        remember(key) {
            mutableListOf(*files)
        }
    val playStatus =
        remember(key) {
            mutableStateOf(false)
        }

    val evaResource by remember(iEvaFetchResource) {
        derivedStateOf {
            iEvaFetchResource
                ?: object : IEvaFetchResource {
                    override fun releaseSrc(resources: List<EvaResource>) {
                    }

                    override fun setImage(
                        resource: EvaResource,
                        result: (Bitmap?, EvaSrc.FitType?) -> Unit,
                    ) {
                        result(null, null)
                    }

                    override fun setText(
                        resource: EvaResource,
                        result: (EvaResource) -> Unit,
                    ) {
                        // 未定义
                        result(resource)
                    }
                }
        }
    }

    AndroidView(
        factory = {
            EvaAnimViewV3(it).apply {
                setVideoMode(EvaConstant.VIDEO_MODE_SPLIT_HORIZONTAL)
                setScaleType(ScaleType.FIT_CENTER)
            }
        },
        modifier = Modifier.fillMaxSize(),
        onReset = {
            it.stopPlay()
            it.setAnimListener(null)
        },
        onRelease = {
            it.stopPlay()
            it.setAnimListener(null)
            it.setFetchResource(null)
        },
    ) {
        if (playStatus.value) {
            return@AndroidView
        }
        playStatus.value = true
        onStart(false)
        it.setAnimListener(null)
        it.setFetchResource(evaResource)
        it.stopPlay()
        it.setAnimListener(
            object : IEvaAnimListener {
                override fun onFailed(
                    errorType: Int,
                    errorMsg: String?,
                ) {
                    onStart(true)
                    onComplete()
                }

                override fun onVideoStart(isRestart: Boolean) {
                    onStart(true)
                }

                override fun onVideoComplete(lastFrame: Boolean) {
                    var nextFile = playQueue.removeFirstOrNull()

                    while (nextFile == null) {
                        nextFile = playQueue.removeFirstOrNull()
                        if (nextFile == null && playQueue.isEmpty()) {
                            break
                        }
                    }

                    if (nextFile != null) {
                        it.startPlay(nextFile)
                    } else {
                        onComplete()
                    }
                }

                override fun onVideoDestroy() = Unit

                override fun onVideoRender(
                    frameIndex: Int,
                    config: EvaAnimConfig?,
                ) = Unit

                override fun onVideoRestart() = Unit
            },
        )

        var next = playQueue.removeFirstOrNull()

        while (next == null) {
            next = playQueue.removeFirstOrNull()
            if (next == null && playQueue.isEmpty()) {
                break
            }
        }
        if (next != null) {
            it.startPlay(next)
        } else {
            onComplete()
        }
    }
}

@Composable
fun EffectVideoAnimationLoopView(
    url: String,
    iEvaFetchResource: IEvaFetchResource? = null,
    onStart: (prepared: Boolean) -> Unit = {},
) {
    var loadFile by remember(url) {
        mutableStateOf<File?>(null)
    }
    LaunchedEffect(loadFile) {
        while (loadFile == null) {
            loadFile = getEffectFile(url, 15.seconds)
            if (loadFile != null) {
                delay(2000)
            }
        }
    }

    if (loadFile != null) {
        val playStatus =
            remember(url) {
                mutableStateOf(false)
            }
        val evaResource by remember(iEvaFetchResource) {
            derivedStateOf {
                iEvaFetchResource
                    ?: object : IEvaFetchResource {
                        override fun releaseSrc(resources: List<EvaResource>) {
                        }

                        override fun setImage(
                            resource: EvaResource,
                            result: (Bitmap?, EvaSrc.FitType?) -> Unit,
                        ) {
                            result(null, null)
                        }

                        override fun setText(
                            resource: EvaResource,
                            result: (EvaResource) -> Unit,
                        ) {
                            // 未定义
                            result(resource)
                        }
                    }
            }
        }

        AndroidView(
            factory = {
                EvaAnimViewV3(it).apply {
                    setVideoMode(EvaConstant.VIDEO_MODE_SPLIT_HORIZONTAL)
                    setScaleType(ScaleType.FIT_CENTER)
                }
            },
            modifier = Modifier.fillMaxSize(),
            onReset = {
                it.stopPlay()
                it.setAnimListener(null)
            },
            onRelease = {
                it.stopPlay()
                it.setAnimListener(null)
                it.setFetchResource(null)
            },
        ) {
            if (playStatus.value) {
                return@AndroidView
            }
            playStatus.value = true
            onStart(false)
            it.setAnimListener(null)
            it.setFetchResource(evaResource)
            it.stopPlay()
            it.setAnimListener(
                object : IEvaAnimListener {
                    override fun onFailed(
                        errorType: Int,
                        errorMsg: String?,
                    ) {
                        onStart(true)
                    }

                    override fun onVideoStart(isRestart: Boolean) {
                        onStart(true)
                    }

                    override fun onVideoComplete(lastFrame: Boolean) {
                        if (loadFile != null) {
                            it.startPlay(loadFile!!)
                        }
                    }

                    override fun onVideoDestroy() = Unit

                    override fun onVideoRender(
                        frameIndex: Int,
                        config: EvaAnimConfig?,
                    ) = Unit

                    override fun onVideoRestart() = Unit
                },
            )

            if (loadFile != null) {
                it.startPlay(loadFile!!)
            }
        }
    } else {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            CircularProgressIndicator()
        }
    }
}
