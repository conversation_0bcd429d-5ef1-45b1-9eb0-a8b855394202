package com.buque.wakoo.ui.screens.chatgroup

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.chatgroup.ChatGroupBean
import com.buque.wakoo.bean.chatgroup.WakooChatGroup
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.image.NetworkImage

@Composable
fun ChatGroupEntryCard(chatGroupBean: WakooChatGroup,modifier: Modifier= Modifier) {
    val rootNav = LocalAppNavController.root
    Row(
        modifier =
            modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(12.dp))
                .clickable(onClick = {
                    rootNav.push(Route.ChatGroup(chatGroupBean.id))
                })
                .background(Color.White)
                .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        NetworkImage(
            chatGroupBean.avatarUrl,
            modifier =
                Modifier
                    .size(64.dp)
                    .clip(RoundedCornerShape(8.dp)),
        )
        SizeWidth(12.dp)
        Column(modifier = Modifier.weight(1f)) {
            Text(chatGroupBean.name, fontSize = 16.sp, color = Color(0xFF111111), fontWeight = FontWeight.Medium)
            SizeHeight(8.dp)
            Text("${chatGroupBean.memberCnt}人", fontSize = 12.sp, color = WakooGrayText)
        }
        Icon(Icons.AutoMirrored.Filled.KeyboardArrowRight, "arrow", tint = WakooGrayText )
    }
}

@Preview
@Composable
private fun Preview() {
    WakooTheme {
        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .background(Color(0xFFF7F7F7))
                    .padding(12.dp),
        ) {
            ChatGroupEntryCard(ChatGroupBean(name = "星空闪耀", memberCnt = 122))
        }
    }
}
