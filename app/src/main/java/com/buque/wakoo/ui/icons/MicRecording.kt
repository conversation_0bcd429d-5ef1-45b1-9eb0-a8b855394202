package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.MicRecording: ImageVector
    get() {
        if (_MicRecording != null) {
            return _MicRecording!!
        }
        _MicRecording =
            ImageVector
                .Builder(
                    name = "MicRecording",
                    defaultWidth = 48.dp,
                    defaultHeight = 48.dp,
                    viewportWidth = 48f,
                    viewportHeight = 48f,
                ).apply {
                    path(fill = SolidColor(Color(0xFFFFFFFF))) {
                        moveTo(24f, 6f)
                        curveTo(20.686f, 6f, 18f, 8.686f, 18f, 12f)
                        verticalLineTo(20f)
                        curveTo(18f, 23.314f, 20.686f, 26f, 24f, 26f)
                        curveTo(27.313f, 26f, 30f, 23.314f, 30f, 20f)
                        verticalLineTo(12f)
                        curveTo(30f, 8.686f, 27.313f, 6f, 24f, 6f)
                        close()
                        moveTo(24f, 2f)
                        curveTo(29.522f, 2f, 34f, 6.477f, 34f, 12f)
                        verticalLineTo(20f)
                        curveTo(34f, 25.523f, 29.522f, 30f, 24f, 30f)
                        curveTo(18.477f, 30f, 14f, 25.523f, 14f, 20f)
                        verticalLineTo(12f)
                        curveTo(14f, 6.477f, 18.477f, 2f, 24f, 2f)
                        close()
                        moveTo(6.109f, 22f)
                        horizontalLineTo(10.141f)
                        curveTo(11.112f, 28.785f, 16.947f, 34f, 24f, 34f)
                        curveTo(31.052f, 34f, 36.887f, 28.785f, 37.858f, 22f)
                        horizontalLineTo(41.89f)
                        curveTo(40.967f, 30.343f, 34.343f, 36.968f, 26f, 37.89f)
                        verticalLineTo(46f)
                        horizontalLineTo(22f)
                        verticalLineTo(37.89f)
                        curveTo(13.656f, 36.968f, 7.032f, 30.343f, 6.109f, 22f)
                        close()
                    }
                    path(fill = SolidColor(Color(0xFF66FE6B))) {
                        moveTo(18f, 16.422f)
                        horizontalLineTo(30f)
                        verticalLineTo(20.022f)
                        curveTo(30f, 23.336f, 27.314f, 26.022f, 24f, 26.022f)
                        verticalLineTo(26.022f)
                        curveTo(20.686f, 26.022f, 18f, 23.336f, 18f, 20.022f)
                        verticalLineTo(16.422f)
                        close()
                    }
                }.build()

        return _MicRecording!!
    }

@Suppress("ObjectPropertyName")
private var _MicRecording: ImageVector? = null
