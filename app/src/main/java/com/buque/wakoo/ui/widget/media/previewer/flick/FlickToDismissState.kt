package com.buque.wakoo.ui.widget.media.previewer.flick

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.remember
import androidx.compose.ui.geometry.Offset
import com.buque.wakoo.ui.widget.media.previewer.flick.FlickToDismissState.RubberBandingSpec
import kotlin.time.Duration

/**
 * Create a [FlickToDismissState] that can be used with [FlickToDismiss()][FlickToDismiss].
 *
 * @param dismissThresholdRatio Minimum distance the user's finger should move as a fraction
 * of the content's height after which it can be dismissed.
 *
 * @param rotateOnDrag When enabled, a subtle rotation is applied to the content while its
 * being dragged.
 */
@Composable
fun rememberFlickToDismissState(
    dismissThresholdRatio: Float = 0.2f,
    rotateOnDrag: Boolean = true,
    rubberBandingSpec: RubberBandingSpec = RubberBandingSpec.Disabled,
): FlickToDismissState {
    check(dismissThresholdRatio > 0f) {
        "The dismiss threshold ratio must be a non-zero value."
    }

    return remember {
        RealFlickToDismissState()
    }.also {
        it.rotateOnDrag = rotateOnDrag
        it.dismissThresholdRatio = dismissThresholdRatio
        it.rubberBandingSpec = rubberBandingSpec
    }
}

@Stable
sealed interface FlickToDismissState {
    val offset: Offset
    val rotationZ: Float
    val gestureState: GestureState

    /**
     * Distance dragged as a fraction of the content's height.
     *
     * @return A value between 0 and 1, where 0 indicates that the content is fully settled in its
     * default position and 1 indicates that the content is past its layout height.
     */
    val offsetFraction: Float

    @Immutable
    sealed interface GestureState {
        /**
         * Content is resting at its default position with no ongoing drag gesture.
         */
        data object Idle : GestureState

        class Dragging(
            val offsetFraction: Float,
            /**
             * Whether the drag distance is sufficient to dismiss the content once it's released.
             *
             * The dismiss threshold is controlled by [dismissThresholdRatio][rememberFlickToDismissState].
             *
             * Keep in mind that the content can also be dismissed if the release velocity is
             * sufficient enough regardless of whether [willDismissOnRelease] is true.
             */
            val willDismissOnRelease: Boolean,
        ) : GestureState

        /**
         * Content is settling back to its default position after it was released because the drag
         * distance wasn't sufficient to dismiss the content.
         */
        data object Resetting : GestureState

        class Dismissing(
            /**
             * Determines how long the content animates before it is fully dismissed.
             * This can be used for scheduling an exit of your screen.
             *
             * ```
             * val gestureState = flickState.gestureState
             *
             * if (gestureState is Dismissing) {
             *   LaunchedEffect(Unit) {
             *     delay(gestureState.animationDuration / 2)
             *     navigator.goBack()
             *   }
             * }
             * ```
             *
             * You could also wait for the state to change to [Dismissed] as an exit signal,
             * but that might be too late as most navigation frameworks have a delay from when
             * an exit navigation is issued to when the screen actually hides from the UI.
             */
            val animationDuration: Duration,
        ) : GestureState

        /**
         * Content was dismissed. At this point, [FlickToDismiss] is no longer usable
         * and must be removed from composition.
         */
        data object Dismissed : GestureState
    }

    /**
     * Applies a rubber banding effect to the content while it is being dragged,
     * up until the dismiss threshold is reached.
     *
     * @param resistanceFactor Controls the strength of the rubber banding effect.
     * A value of `1f` disables the effect entirely (no resistance). Higher values
     * increase resistance by proportionally reducing the visible drag distance.
     * */

    class RubberBandingSpec(
        internal val resistanceFactor: Float = 2f,
    ) {
        companion object {
            val Disabled: RubberBandingSpec = RubberBandingSpec(1f)
        }

        init {
            require(resistanceFactor > 0f) {
                "resistanceFactor must be greater than 0 to avoid division by zero. " +
                    "To disable rubber banding, use 1f or RubberBandingSpec.Disabled."
            }
        }
    }
}

@Composable
@Suppress("unused")
@Deprecated("Kept for binary compatibility", level = DeprecationLevel.HIDDEN)
fun rememberFlickToDismissState(
    dismissThresholdRatio: Float = 0.2f,
    rotateOnDrag: Boolean = true,
): FlickToDismissState =
    rememberFlickToDismissState(
        dismissThresholdRatio = dismissThresholdRatio,
        rotateOnDrag = rotateOnDrag,
        rubberBandingSpec = RubberBandingSpec.Disabled,
    )
