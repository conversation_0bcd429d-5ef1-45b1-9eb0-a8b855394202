package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.GreenCircleChecked: ImageVector
    get() {
        if (_GreenCircleChecked != null) {
            return _GreenCircleChecked!!
        }
        _GreenCircleChecked =
            ImageVector
                .Builder(
                    name = "GreenCircleChecked",
                    defaultWidth = 18.dp,
                    defaultHeight = 18.dp,
                    viewportWidth = 18f,
                    viewportHeight = 18f,
                ).apply {
                    path(
                        fill = SolidColor(Color(0xFF66FE6B)),
                        stroke = SolidColor(Color(0xFF66FE6B)),
                        strokeLineWidth = 2f,
                        strokeLineCap = StrokeCap.Round,
                        strokeLineJoin = StrokeJoin.Round,
                    ) {
                        moveTo(1f, 9f)
                        curveTo(1f, 4.582f, 4.582f, 1f, 9f, 1f)
                        curveTo(13.418f, 1f, 17f, 4.582f, 17f, 9f)
                        curveTo(17f, 13.418f, 13.418f, 17f, 9f, 17f)
                        curveTo(4.582f, 17f, 1f, 13.418f, 1f, 9f)
                        close()
                    }
                    path(
                        stroke = SolidColor(Color(0xFF111111)),
                        strokeLineWidth = 2f,
                        strokeLineCap = StrokeCap.Round,
                        strokeLineJoin = StrokeJoin.Round,
                    ) {
                        moveTo(6f, 10f)
                        lineTo(7.5f, 11.5f)
                        lineTo(12f, 7f)
                    }
                }.build()

        return _GreenCircleChecked!!
    }

@Suppress("ObjectPropertyName")
private var _GreenCircleChecked: ImageVector? = null
