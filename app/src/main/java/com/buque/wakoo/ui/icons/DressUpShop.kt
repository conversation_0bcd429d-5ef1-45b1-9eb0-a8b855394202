package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.DressUpShop: ImageVector
    get() {
        if (_DressUpShop != null) {
            return _DressUpShop!!
        }
        _DressUpShop =
            ImageVector
                .Builder(
                    name = "DressUpShop",
                    defaultWidth = 24.dp,
                    defaultHeight = 24.dp,
                    viewportWidth = 24f,
                    viewportHeight = 24f,
                ).apply {
                    group(
                        clipPathData =
                            PathData {
                                moveTo(0f, 0f)
                                horizontalLineToRelative(24f)
                                verticalLineToRelative(24f)
                                horizontalLineToRelative(-24f)
                                close()
                            },
                    ) {
                        path(fill = SolidColor(Color(0xFF111111))) {
                            moveTo(18.544f, 12.869f)
                            verticalLineTo(17.6f)
                            horizontalLineTo(19.271f)
                            verticalLineTo(19f)
                            horizontalLineTo(4.727f)
                            verticalLineTo(17.6f)
                            horizontalLineTo(5.454f)
                            verticalLineTo(12.869f)
                            curveTo(5.007f, 12.582f, 4.64f, 12.192f, 4.386f, 11.735f)
                            curveTo(4.132f, 11.278f, 4f, 10.768f, 4f, 10.25f)
                            curveTo(4f, 9.671f, 4.163f, 9.113f, 4.46f, 8.638f)
                            lineTo(6.432f, 5.35f)
                            curveTo(6.496f, 5.244f, 6.588f, 5.155f, 6.699f, 5.094f)
                            curveTo(6.809f, 5.032f, 6.935f, 5f, 7.062f, 5f)
                            horizontalLineTo(16.937f)
                            curveTo(17.065f, 5f, 17.19f, 5.032f, 17.301f, 5.094f)
                            curveTo(17.411f, 5.155f, 17.503f, 5.244f, 17.567f, 5.35f)
                            lineTo(19.531f, 8.627f)
                            curveTo(19.965f, 9.321f, 20.105f, 10.149f, 19.921f, 10.939f)
                            curveTo(19.737f, 11.729f, 19.244f, 12.42f, 18.544f, 12.869f)
                            close()
                            moveTo(17.09f, 13.38f)
                            curveTo(16.59f, 13.434f, 16.083f, 13.377f, 15.61f, 13.212f)
                            curveTo(15.137f, 13.046f, 14.71f, 12.778f, 14.363f, 12.428f)
                            curveTo(14.057f, 12.736f, 13.691f, 12.98f, 13.284f, 13.148f)
                            curveTo(12.878f, 13.315f, 12.441f, 13.401f, 11.999f, 13.401f)
                            curveTo(11.557f, 13.401f, 11.12f, 13.315f, 10.714f, 13.148f)
                            curveTo(10.308f, 12.981f, 9.941f, 12.737f, 9.636f, 12.43f)
                            curveTo(9.288f, 12.78f, 8.861f, 13.047f, 8.388f, 13.212f)
                            curveTo(7.915f, 13.377f, 7.409f, 13.435f, 6.909f, 13.381f)
                            verticalLineTo(17.6f)
                            horizontalLineTo(17.09f)
                            verticalLineTo(13.381f)
                            verticalLineTo(13.38f)
                            close()
                            moveTo(7.483f, 6.4f)
                            lineTo(5.713f, 9.349f)
                            curveTo(5.541f, 9.758f, 5.536f, 10.214f, 5.699f, 10.627f)
                            curveTo(5.861f, 11.039f, 6.18f, 11.377f, 6.59f, 11.573f)
                            curveTo(7.001f, 11.769f, 7.473f, 11.809f, 7.913f, 11.684f)
                            curveTo(8.352f, 11.559f, 8.727f, 11.279f, 8.961f, 10.9f)
                            curveTo(9.205f, 10.314f, 10.066f, 10.314f, 10.311f, 10.9f)
                            curveTo(10.445f, 11.225f, 10.678f, 11.504f, 10.979f, 11.7f)
                            curveTo(11.28f, 11.897f, 11.636f, 12.002f, 11.999f, 12.002f)
                            curveTo(12.363f, 12.002f, 12.718f, 11.897f, 13.019f, 11.7f)
                            curveTo(13.32f, 11.504f, 13.553f, 11.225f, 13.688f, 10.9f)
                            curveTo(13.931f, 10.314f, 14.793f, 10.314f, 15.037f, 10.9f)
                            curveTo(15.132f, 11.124f, 15.273f, 11.326f, 15.453f, 11.495f)
                            curveTo(15.632f, 11.663f, 15.846f, 11.794f, 16.081f, 11.879f)
                            curveTo(16.315f, 11.964f, 16.566f, 12.002f, 16.816f, 11.989f)
                            curveTo(17.067f, 11.977f, 17.312f, 11.915f, 17.536f, 11.806f)
                            curveTo(17.76f, 11.698f, 17.958f, 11.547f, 18.119f, 11.361f)
                            curveTo(18.279f, 11.176f, 18.398f, 10.96f, 18.468f, 10.729f)
                            curveTo(18.538f, 10.497f, 18.557f, 10.253f, 18.524f, 10.014f)
                            curveTo(18.492f, 9.775f, 18.408f, 9.545f, 18.279f, 9.338f)
                            lineTo(16.515f, 6.4f)
                            horizontalLineTo(7.483f)
                            horizontalLineTo(7.483f)
                            close()
                        }
                    }
                }.build()

        return _DressUpShop!!
    }

@Suppress("ObjectPropertyName")
private var _DressUpShop: ImageVector? = null
