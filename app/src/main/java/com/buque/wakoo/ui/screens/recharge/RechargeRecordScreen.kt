package com.buque.wakoo.ui.screens.recharge

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.network.api.bean.Record
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.BillListViewModel

@Composable
fun RechargeRecordScreenUI() {
    val viewModel = viewModel<BillListViewModel>()
    val listState = rememberLazyListState()
    TitleScreenScaffold(stringResource(R.string.charge_records)) {
        CStateListPaginateLayout<Any, Int, Record, BillListViewModel>(
            "",
            emptyId = R.drawable.ic_empty_for_recharge,
            emptyText = stringResource(R.string.empty_charge_record),
            viewModel = viewModel,
            listState = listState,
        ) { _, list ->
            LazyColumn(state = listState) {
                items(list) {
                    RechargeRecordItem(it)
                    HorizontalDivider(
                        color = Color(0xFFE5E5E5),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp)
                    )
                }
            }
        }
    }
}


/**
 * 充值记录项
 */
@Composable
private fun RechargeRecordItem(record: Record) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors =
            CardDefaults.cardColors(
                containerColor = Color.White,
            ),
        elevation =
            CardDefaults.cardElevation(
                defaultElevation = 0.dp,
            ),
    ) {
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            SizeWidth(12.dp)
            // 记录信息
            Column(
                modifier = Modifier.weight(1f),
            ) {
                Text(
                    text = record.changeReason,
                    style =
                        MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.Medium,
                            fontSize = 14.sp,
                        ),
                    color = MaterialTheme.colorScheme.onBackground,
                )
                SizeHeight(4.dp)
                Text(
                    text = record.formatTime,
                    style =
                        MaterialTheme.typography.labelLarge.copy(
                            fontSize = 12.sp,
                        ),
                    color = WakooGrayText,
                )
            }
            // 钻石图标
            Image(
                painter = painterResource(id = R.drawable.ic_green_diamond_straight),
                contentDescription = null,
                modifier = Modifier.size(24.dp),
            )
            // 钻石数量变化
            Text(
                text = "${if (record.changeAmount < 0) "-" else "+"}${record.changeAmount}",
                style =
                    MaterialTheme.typography.titleSmall.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp,
                    ),
                color = if (record.changeAmount < 0) Color(0xFF0B570E) else Color(0xFFFF4444),
            )
        }
    }
}

@Preview
@Composable
private fun RecordItem() {
    RechargeRecordItem(
        com.buque.wakoo.network.api.bean.Record(
            999,
            "充值",
            changeType = 0,
            createTimestamp = 0,
            id = 0,
            remainBalance = 1
        )
    )
}



