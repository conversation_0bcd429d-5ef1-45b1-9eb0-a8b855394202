package com.buque.wakoo.ui.widget

import androidx.compose.animation.core.Animatable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.buque.wakoo.ui.icons.VoiceHigh
import com.buque.wakoo.ui.icons.VoiceLow
import com.buque.wakoo.ui.icons.VoiceMiddle
import com.buque.wakoo.ui.icons.WakooIcons
import kotlinx.coroutines.delay

/**
 * 语音播放动画组件，通过在不同语音级别图标间切换来创建动态播放效果
 *
 * @param isPlaying 控制动画是否正在播放
 * @param modifier 修饰符，用于控制组件的大小和样式
 * @param iconSize 图标的大小
 * @param iconColor 图标的颜色
 * @param animationDuration 每个图标切换的持续时间（毫秒）
 */
@Composable
fun VoicePlayingAnimation(
    isPlaying: Boolean,
    modifier: Modifier = Modifier,
    iconSize: Dp = 20.dp,
    iconColor: Color = Color.Black,
    animationDuration: Int = 600,
) {
    val voiceIcons =
        remember {
            mutableStateListOf(
                WakooIcons.VoiceLow,
                WakooIcons.VoiceMiddle,
                WakooIcons.VoiceHigh,
            )
        }

    val currentIconIndex = remember { Animatable(2f) }
    val alpha = remember { Animatable(1f) }

    LaunchedEffect(isPlaying) {
        if (isPlaying) {
            while (true) {
                // 按顺序循环：Low -> Middle -> High -> Low ...
                for (i in voiceIcons.indices) {
                    currentIconIndex.snapTo(i.toFloat())
                    delay(animationDuration.toLong())
                }
            }
        } else {
            alpha.snapTo(1f)
            currentIconIndex.snapTo(2f)
        }
    }

    val currentIcon =
        if (isPlaying) {
            voiceIcons[
                currentIconIndex.value
                    .toInt()
                    .coerceIn(0, voiceIcons.size - 1),
            ]
        } else {
            voiceIcons.last()
        }

    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center,
    ) {
        Icon(
            imageVector = currentIcon,
            contentDescription = if (isPlaying) "语音播放中" else "语音已停止",
            modifier =
                Modifier
                    .size(iconSize)
                    .alpha(alpha.value),
            tint = iconColor,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun VoicePlayingAnimationPreview() {
    VoicePlayingAnimation(
        isPlaying = false,
        modifier = Modifier.size(60.dp),
        iconSize = 24.dp,
        iconColor = Color(0xFF1DB954),
    )
}
