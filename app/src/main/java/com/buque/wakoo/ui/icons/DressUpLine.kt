package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.DressUpLine: ImageVector
    get() {
        if (_DressUpLine != null) {
            return _DressUpLine!!
        }
        _DressUpLine =
            ImageVector
                .Builder(
                    name = "DressUpLine",
                    defaultWidth = 24.dp,
                    defaultHeight = 24.dp,
                    viewportWidth = 24f,
                    viewportHeight = 24f,
                ).apply {
                    group(
                        clipPathData =
                            PathData {
                                moveTo(24f, 0f)
                                lineToRelative(-24f, 0f)
                                lineToRelative(-0f, 24f)
                                lineToRelative(24f, 0f)
                                close()
                            },
                    ) {
                        path(fill = SolidColor(Color(0xFF111111))) {
                            moveTo(7.998f, 10.412f)
                            curveTo(8.338f, 10.09f, 8.556f, 9.662f, 8.617f, 9.198f)
                            lineTo(8.932f, 6.786f)
                            lineTo(11.068f, 7.949f)
                            curveTo(11.479f, 8.173f, 11.954f, 8.248f, 12.413f, 8.162f)
                            lineTo(14.805f, 7.716f)
                            lineTo(14.359f, 10.107f)
                            curveTo(14.273f, 10.567f, 14.348f, 11.042f, 14.572f, 11.453f)
                            lineTo(15.735f, 13.589f)
                            lineTo(13.323f, 13.905f)
                            curveTo(12.859f, 13.965f, 12.431f, 14.183f, 12.11f, 14.523f)
                            lineTo(10.437f, 16.288f)
                            lineTo(9.392f, 14.092f)
                            curveTo(9.191f, 13.67f, 8.851f, 13.33f, 8.43f, 13.129f)
                            lineTo(6.233f, 12.084f)
                            lineTo(7.998f, 10.412f)
                            close()
                            moveTo(7.979f, 14.764f)
                            lineTo(9.71f, 18.403f)
                            curveTo(9.743f, 18.471f, 9.792f, 18.531f, 9.853f, 18.577f)
                            curveTo(9.914f, 18.622f, 9.985f, 18.652f, 10.061f, 18.664f)
                            curveTo(10.136f, 18.676f, 10.213f, 18.67f, 10.285f, 18.645f)
                            curveTo(10.357f, 18.621f, 10.422f, 18.579f, 10.475f, 18.524f)
                            lineTo(13.246f, 15.598f)
                            curveTo(13.32f, 15.52f, 13.419f, 15.47f, 13.525f, 15.456f)
                            lineTo(17.521f, 14.933f)
                            curveTo(17.597f, 14.923f, 17.669f, 14.895f, 17.731f, 14.851f)
                            curveTo(17.793f, 14.807f, 17.843f, 14.749f, 17.878f, 14.681f)
                            curveTo(17.913f, 14.613f, 17.93f, 14.538f, 17.929f, 14.462f)
                            curveTo(17.928f, 14.386f, 17.909f, 14.311f, 17.872f, 14.244f)
                            lineTo(15.946f, 10.704f)
                            curveTo(15.895f, 10.61f, 15.878f, 10.5f, 15.898f, 10.395f)
                            lineTo(16.635f, 6.433f)
                            curveTo(16.649f, 6.358f, 16.645f, 6.281f, 16.622f, 6.208f)
                            curveTo(16.6f, 6.135f, 16.56f, 6.069f, 16.506f, 6.015f)
                            curveTo(16.452f, 5.961f, 16.386f, 5.921f, 16.313f, 5.899f)
                            curveTo(16.24f, 5.876f, 16.163f, 5.872f, 16.088f, 5.886f)
                            lineTo(12.126f, 6.623f)
                            curveTo(12.021f, 6.643f, 11.911f, 6.626f, 11.817f, 6.575f)
                            lineTo(8.277f, 4.649f)
                            curveTo(8.21f, 4.612f, 8.135f, 4.593f, 8.059f, 4.592f)
                            curveTo(7.983f, 4.591f, 7.907f, 4.609f, 7.84f, 4.643f)
                            curveTo(7.772f, 4.678f, 7.713f, 4.728f, 7.669f, 4.791f)
                            curveTo(7.626f, 4.853f, 7.597f, 4.925f, 7.588f, 5.001f)
                            lineTo(7.065f, 8.996f)
                            curveTo(7.051f, 9.102f, 7.001f, 9.201f, 6.923f, 9.275f)
                            lineTo(3.997f, 12.046f)
                            curveTo(3.942f, 12.099f, 3.9f, 12.164f, 3.876f, 12.236f)
                            curveTo(3.851f, 12.308f, 3.845f, 12.385f, 3.857f, 12.46f)
                            curveTo(3.869f, 12.536f, 3.899f, 12.607f, 3.944f, 12.668f)
                            curveTo(3.99f, 12.729f, 4.05f, 12.778f, 4.118f, 12.811f)
                            lineTo(7.757f, 14.542f)
                            curveTo(7.854f, 14.589f, 7.933f, 14.667f, 7.979f, 14.764f)
                            close()
                            moveTo(7.355f, 16.272f)
                            lineTo(6.249f, 15.166f)
                            lineTo(2.929f, 18.485f)
                            lineTo(4.036f, 19.592f)
                            lineTo(7.355f, 16.272f)
                            close()
                        }
                        path(fill = SolidColor(Color(0xFF111111))) {
                            moveTo(21.355f, 11.482f)
                            lineTo(17.685f, 11.89f)
                            lineTo(17.511f, 10.334f)
                            lineTo(21.181f, 9.926f)
                            lineTo(21.355f, 11.482f)
                            close()
                        }
                        path(fill = SolidColor(Color(0xFF111111))) {
                            moveTo(20.408f, 7.888f)
                            lineTo(18.843f, 9.025f)
                            lineTo(17.923f, 7.758f)
                            lineTo(19.488f, 6.621f)
                            lineTo(20.408f, 7.888f)
                            close()
                        }
                    }
                }.build()

        return _DressUpLine!!
    }

@Suppress("ObjectPropertyName")
private var _DressUpLine: ImageVector? = null
