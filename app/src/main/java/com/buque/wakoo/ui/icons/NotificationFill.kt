package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.NotificationFill: ImageVector
    get() {
        val current = _notificationFill
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.NotificationFill",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // M22 20 H2 v-2 h1 v-6.97 A9 9 0 0 1 12 2 c4.97 0 9 4.04 9 9.03 V18 h1z M9.5 21 h5 a2.5 2.5 0 0 1 -5 0
                path(
                    fill =
                        Brush.radialGradient(
                            0.0f to Color(0xFFFFFAE0),
                            1.0f to Color(0xFFFFC02E),
                            center = Offset.Zero,
                            radius = 1.0f,
                        ),
                ) {
                    // M 22 20
                    moveTo(x = 22.0f, y = 20.0f)
                    // H 2
                    horizontalLineTo(x = 2.0f)
                    // v -2
                    verticalLineToRelative(dy = -2.0f)
                    // h 1
                    horizontalLineToRelative(dx = 1.0f)
                    // v -6.97
                    verticalLineToRelative(dy = -6.97f)
                    // A 9 9 0 0 1 12 2
                    arcTo(
                        horizontalEllipseRadius = 9.0f,
                        verticalEllipseRadius = 9.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 12.0f,
                        y1 = 2.0f,
                    )
                    // c 4.97 0 9 4.04 9 9.03
                    curveToRelative(
                        dx1 = 4.97f,
                        dy1 = 0.0f,
                        dx2 = 9.0f,
                        dy2 = 4.04f,
                        dx3 = 9.0f,
                        dy3 = 9.03f,
                    )
                    // V 18
                    verticalLineTo(y = 18.0f)
                    // h 1z
                    horizontalLineToRelative(dx = 1.0f)
                    close()
                    // M 9.5 21
                    moveTo(x = 9.5f, y = 21.0f)
                    // h 5
                    horizontalLineToRelative(dx = 5.0f)
                    // a 2.5 2.5 0 0 1 -5 0
                    arcToRelative(
                        a = 2.5f,
                        b = 2.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -5.0f,
                        dy1 = 0.0f,
                    )
                }
            }.build()
            .also { _notificationFill = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.NotificationFill,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _notificationFill: ImageVector? = null
