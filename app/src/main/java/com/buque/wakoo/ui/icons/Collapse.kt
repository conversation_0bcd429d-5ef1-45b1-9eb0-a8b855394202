package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Collapse: ImageVector
    get() {
        val current = _collapse
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.Collapse",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // M15 4 h-2 v7 h7 V9 h-3.59 l4.3 -4.3 -1.42 -1.4 L15 7.58z M4 15 h3.59 l-4.3 4.3 1.42 1.4 L9 16.42 V20 h2 v-7 H4z
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 15 4
                    moveTo(x = 15.0f, y = 4.0f)
                    // h -2
                    horizontalLineToRelative(dx = -2.0f)
                    // v 7
                    verticalLineToRelative(dy = 7.0f)
                    // h 7
                    horizontalLineToRelative(dx = 7.0f)
                    // V 9
                    verticalLineTo(y = 9.0f)
                    // h -3.59
                    horizontalLineToRelative(dx = -3.59f)
                    // l 4.3 -4.3
                    lineToRelative(dx = 4.3f, dy = -4.3f)
                    // l -1.42 -1.4
                    lineToRelative(dx = -1.42f, dy = -1.4f)
                    // L 15 7.58z
                    lineTo(x = 15.0f, y = 7.58f)
                    close()
                    // M 4 15
                    moveTo(x = 4.0f, y = 15.0f)
                    // h 3.59
                    horizontalLineToRelative(dx = 3.59f)
                    // l -4.3 4.3
                    lineToRelative(dx = -4.3f, dy = 4.3f)
                    // l 1.42 1.4
                    lineToRelative(dx = 1.42f, dy = 1.4f)
                    // L 9 16.42
                    lineTo(x = 9.0f, y = 16.42f)
                    // V 20
                    verticalLineTo(y = 20.0f)
                    // h 2
                    horizontalLineToRelative(dx = 2.0f)
                    // v -7
                    verticalLineToRelative(dy = -7.0f)
                    // H 4z
                    horizontalLineTo(x = 4.0f)
                    close()
                }
            }.build()
            .also { _collapse = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Collapse,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _collapse: ImageVector? = null
