package com.buque.wakoo.ui.widget.media.data

import android.content.ContentResolver
import android.content.ContentUris
import android.content.Context
import android.database.ContentObserver
import android.database.Cursor
import android.graphics.BitmapFactory
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import com.buque.wakoo.ui.widget.media.data.common.Album
import com.buque.wakoo.ui.widget.media.data.common.ImageItem
import com.buque.wakoo.ui.widget.media.data.common.MediaChanges
import com.buque.wakoo.ui.widget.media.data.common.MediaItem
import com.buque.wakoo.ui.widget.media.data.common.MediaRepository
import com.buque.wakoo.ui.widget.media.data.common.MediaType
import com.buque.wakoo.ui.widget.media.data.common.VideoItem
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * MediaRepository 在 Android 平台的具体实现。
 * 使用 ContentResolver 与 MediaStore API 交互，以获取设备上的媒体文件。
 *
 * @param context ApplicationContext，用于获取 ContentResolver。
 */
class AndroidMediaRepository(
    private val context: Context,
    private val scope: CoroutineScope,
) : MediaRepository {
    private val contentResolver by lazy { context.contentResolver }

    // 回调与观察者
    private var observerCallback: ((MediaChanges?) -> Unit)? = null
    private var contentObserver: ContentObserver? = null

    // 用于增量更新比对的缓存
    @Volatile
    private var cachedMediaIds: Set<String>? = null
    private var diffJob: Job? = null

    /**
     * 异步获取相册列表。
     *
     * 实现策略：
     * 1. 查询所有符合条件的媒体（图片/视频），但只获取关键的 Bucket 信息和 ID。
     * 2. 在内存中对结果按 Bucket ID 进行分组。
     * 3. 构造 Album 对象，并为每个相册找到最新的媒体作为封面。
     * 4. 手动创建一个 "所有媒体" 的相册。
     */
    override suspend fun getAlbums(mediaType: MediaType): Result<List<Album>> =
        withContext(Dispatchers.IO) {
            runCatching {
                val albums = mutableMapOf<String, Album>()
                var allMediaCount = 0
                var allMediaCoverUri: Uri? = null
                var latestDate = 0L

                val (uri, selection, selectionArgs) = buildQueryParameters(null, mediaType)
                val projection =
                    arrayOf(
                        MediaStore.MediaColumns._ID,
                        MediaStore.MediaColumns.BUCKET_ID,
                        MediaStore.MediaColumns.BUCKET_DISPLAY_NAME,
                        MediaStore.MediaColumns.DATE_ADDED,
                    )
                val sortOrder = "${MediaStore.MediaColumns.DATE_ADDED} DESC"

                contentResolver.query(uri, projection, selection, selectionArgs, sortOrder)?.use { cursor ->
                    val idCol = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns._ID)
                    val bucketIdCol = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.BUCKET_ID)
                    val bucketNameCol = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.BUCKET_DISPLAY_NAME)
                    val dateAddedCol = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DATE_ADDED)

                    while (cursor.moveToNext()) {
                        allMediaCount++
                        val id = cursor.getLong(idCol)
                        val dateAdded = cursor.getLong(dateAddedCol)
                        val contentUri = ContentUris.withAppendedId(uri, id)

                        if (dateAdded > latestDate) {
                            latestDate = dateAdded
                            allMediaCoverUri = contentUri
                        }

                        val bucketId = cursor.getString(bucketIdCol)
                        val album = albums[bucketId]
                        if (album == null) {
                            val bucketName = cursor.getString(bucketNameCol)
                            albums[bucketId] = Album(bucketId, bucketName, contentUri.toString(), 1)
                        } else {
                            albums[bucketId] = album.copy(count = album.count + 1)
                        }
                    }
                }

                val resultList = mutableListOf<Album>()
                if (allMediaCount > 0) {
                    resultList.add(
                        Album(
                            MediaRepository.Companion.ALL_MEDIA_ALBUM_ID,
                            "所有媒体",
                            allMediaCoverUri?.toString(),
                            allMediaCount,
                        ),
                    )
                }
                resultList.addAll(albums.values.sortedByDescending { it.count })
                resultList
            }
        }

    /**
     * 异步分页获取媒体列表。
     *
     * 实现策略：
     * 使用 Android 10 (API 29) 及以上版本提供的分页查询参数（QUERY_ARG_LIMIT, QUERY_ARG_OFFSET），
     * 这是最高效的分页方式，避免了手动移动 cursor 的开销。
     * 对于 Android 9 (API 28)，我们仍然使用这个 API，因为它是向后兼容的。
     */
    override suspend fun getMedia(
        page: Int,
        pageSize: Int,
        albumId: String?,
        mediaType: MediaType,
    ): Result<List<MediaItem>> =
        withContext(Dispatchers.IO) {
            runCatching {
                val (uri, selection, selectionArgs) = buildQueryParameters(albumId, mediaType)

                // 使用 Bundle 来设置分页参数，这是现代且推荐的方式
                val queryArgs =
                    Bundle().apply {
                        // 排序
                        putString(
                            ContentResolver.QUERY_ARG_SQL_SORT_ORDER,
                            "${MediaStore.MediaColumns.DATE_ADDED} DESC",
                        )
                        // 分页
                        putInt(ContentResolver.QUERY_ARG_LIMIT, pageSize)
                        putInt(ContentResolver.QUERY_ARG_OFFSET, page * pageSize)
                        // 条件
                        putString(ContentResolver.QUERY_ARG_SQL_SELECTION, selection)
                        putStringArray(ContentResolver.QUERY_ARG_SQL_SELECTION_ARGS, selectionArgs)
                    }

                // 使用 ContentResolver.query(Uri, String[], Bundle, CancellationSignal)
                // 最后的 queryMedia 辅助方法可以被复用，只需传入合适的参数
                queryMediaWithBundle(uri, queryArgs)
            }
        }

    /**
     * 高效获取所有媒体ID。
     */
    override suspend fun getAllMediaIds(
        albumId: String?,
        mediaType: MediaType,
    ): Result<List<String>> =
        withContext(Dispatchers.IO) {
            runCatching {
                val idList = mutableListOf<String>()
                val (uri, selection, selectionArgs) = buildQueryParameters(albumId, mediaType)
                val projection = arrayOf(MediaStore.MediaColumns._ID)
                val sortOrder = "${MediaStore.MediaColumns.DATE_ADDED} DESC"

                contentResolver.query(uri, projection, selection, selectionArgs, sortOrder)?.use { cursor ->
                    val idCol = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns._ID)
                    while (cursor.moveToNext()) {
                        idList.add(cursor.getLong(idCol).toString())
                    }
                }
                idList
            }
        }

    /**
     * 根据ID批量获取媒体详情。
     */
    override suspend fun getMediaByIds(ids: List<String>): Result<List<MediaItem>> =
        withContext(Dispatchers.IO) {
            if (ids.isEmpty()) return@withContext Result.success(emptyList())
            runCatching {
                val placeholder = ids.joinToString(",") { "?" }
                val selection = "${MediaStore.MediaColumns._ID} IN ($placeholder)"
                val uri = MediaStore.Files.getContentUri("external")
                queryMedia(uri, selection, ids.toTypedArray())
            }
        }

    override fun registerObserver(observer: (MediaChanges?) -> Unit) {
        // 如果已存在，先注销，避免重复注册
        unregisterObserver()
        this.observerCallback = observer

        scope.launch(Dispatchers.IO) {
            cachedMediaIds = getAllMediaIds(null, MediaType.ALL).getOrNull()?.toSet()
        }

        contentObserver =
            object : ContentObserver(Handler(Looper.getMainLooper())) {
                override fun onChange(selfChange: Boolean) {
                    performDiffCheck()
                }
            }.also {
                contentResolver.registerContentObserver(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, true, it)
                contentResolver.registerContentObserver(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, true, it)
            }
    }

    /**
     * 注销媒体库变化观察者。
     */
    override fun unregisterObserver() {
        contentObserver?.let {
            contentResolver.unregisterContentObserver(it)
            contentObserver = null
            observerCallback = null
        }
    }

    /**
     * 执行增量比对的核心逻辑。
     */
    private fun performDiffCheck() {
        if (diffJob?.isActive == true) return

        diffJob =
            scope.launch(Dispatchers.IO) {
                val oldIds = cachedMediaIds ?: getAllMediaIds(null, MediaType.ALL).getOrNull()?.toSet() ?: return@launch
                val newIdsResult = getAllMediaIds(null, MediaType.ALL)

                if (newIdsResult.isFailure) {
                    withContext(Dispatchers.Main) { observerCallback?.invoke(null) } // 无法比对，建议全局刷新
                    return@launch
                }

                val newIds = newIdsResult.getOrThrow().toSet()
                if (oldIds == newIds) return@launch

                val removedIds = (oldIds - newIds).toList()
                val insertedIds = (newIds - oldIds).toList()
                cachedMediaIds = newIds

                if (removedIds.isEmpty() && insertedIds.isEmpty()) return@launch

                val changes = MediaChanges(removedIds, insertedIds, emptyList())
                withContext(Dispatchers.Main) {
                    observerCallback?.invoke(changes)
                }
            }
    }

    /**
     * 构建查询所需的URI, selection和selectionArgs。
     */
    private fun buildQueryParameters(
        albumId: String?,
        mediaType: MediaType,
    ): Triple<Uri, String?, Array<String>?> {
        val selectionClauses = mutableListOf<String>()
        val selectionArgs = mutableListOf<String>()
        val uri: Uri

        when (mediaType) {
            MediaType.IMAGE -> uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
            MediaType.VIDEO -> uri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
            MediaType.ALL -> {
                uri = MediaStore.Files.getContentUri("external")
                selectionClauses.add("${MediaStore.Files.FileColumns.MEDIA_TYPE} IN (?,?)")
                selectionArgs.add(
                    MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE
                        .toString(),
                )
                selectionArgs.add(
                    MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO
                        .toString(),
                )
            }
        }

        if (albumId != null && albumId != MediaRepository.Companion.ALL_MEDIA_ALBUM_ID) {
            selectionClauses.add("${MediaStore.MediaColumns.BUCKET_ID} = ?")
            selectionArgs.add(albumId)
        }

        val selection = if (selectionClauses.isNotEmpty()) selectionClauses.joinToString(" AND ") else null
        val args = if (selectionArgs.isNotEmpty()) selectionArgs.toTypedArray() else null
        return Triple(uri, selection, args)
    }

    // 需要一个使用 Bundle 的 queryMedia 辅助方法
    private fun queryMediaWithBundle(
        uri: Uri,
        queryArgs: Bundle,
    ): List<MediaItem> {
        val mediaList = mutableListOf<MediaItem>()
        val projection =
            arrayOf(
                MediaStore.MediaColumns._ID,
                MediaStore.MediaColumns.BUCKET_ID,
                MediaStore.MediaColumns.DISPLAY_NAME,
                MediaStore.MediaColumns.DATE_ADDED,
                MediaStore.MediaColumns.SIZE,
                MediaStore.MediaColumns.WIDTH,
                MediaStore.MediaColumns.HEIGHT,
                MediaStore.MediaColumns.MIME_TYPE,
                MediaStore.Video.Media.DURATION, // 视频特有
            )

        contentResolver.query(uri, projection, queryArgs, null)?.use { cursor ->
            parserCursor(cursor, mediaList)
        }
        return mediaList
    }

    /**
     * 执行实际的媒体查询和模型转换。
     */
    private fun queryMedia(
        uri: Uri,
        selection: String?,
        selectionArgs: Array<String>?,
    ): List<MediaItem> {
        val mediaList = mutableListOf<MediaItem>()
        val projection =
            arrayOf(
                MediaStore.MediaColumns._ID,
                MediaStore.MediaColumns.BUCKET_ID,
                MediaStore.MediaColumns.DISPLAY_NAME,
                MediaStore.MediaColumns.DATE_ADDED,
                MediaStore.MediaColumns.SIZE,
                MediaStore.MediaColumns.WIDTH,
                MediaStore.MediaColumns.HEIGHT,
                MediaStore.MediaColumns.MIME_TYPE,
                MediaStore.Video.Media.DURATION,
            )
        val sortOrder = "${MediaStore.MediaColumns.DATE_ADDED} DESC"

        contentResolver.query(uri, projection, selection, selectionArgs, sortOrder)?.use { cursor ->
            parserCursor(cursor, mediaList)
        }
        return mediaList
    }

    private fun parserCursor(
        cursor: Cursor,
        mediaList: MutableList<MediaItem>,
    ) {
        val idCol = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns._ID)
        val bucketIdCol = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.BUCKET_ID)
        val nameCol = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DISPLAY_NAME)
        val dateCol = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DATE_ADDED)
        val sizeCol = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.SIZE)
        val widthCol = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.WIDTH)
        val heightCol = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.HEIGHT)
        val mimeCol = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.MIME_TYPE)
        val durationCol = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DURATION)

        while (cursor.moveToNext()) {
            var width = cursor.getInt(widthCol)
            var height = cursor.getInt(heightCol)
            val mimeType = cursor.getString(mimeCol)

            val id = cursor.getLong(idCol)

            val contentUri =
                ContentUris.withAppendedId(
                    if (mimeType.startsWith("video")) {
                        MediaStore.Video.Media.EXTERNAL_CONTENT_URI
                    } else {
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                    },
                    id,
                )

            if (width == 0 || height == 0) {
                val dimensions =
                    if (mimeType.startsWith("video/")) {
                        getVideoDimensions(context, contentUri)
                    } else {
                        getImageDimensions(context, contentUri)
                    }
                width = dimensions.first
                height = dimensions.second
            }

            val item =
                if (mimeType.startsWith("video")) {
                    var duration = cursor.getLong(durationCol)
                    if (duration <= 0) {
                        duration = getVideoDuration(context, contentUri)
                    }
                    VideoItem(
                        id.toString(),
                        cursor.getString(bucketIdCol),
                        contentUri.toString(),
                        cursor.getString(nameCol),
                        cursor.getLong(sizeCol),
                        cursor.getLong(dateCol),
                        width,
                        height,
                        mimeType,
                        duration,
                    )
                } else {
                    ImageItem(
                        id.toString(),
                        cursor.getString(bucketIdCol),
                        contentUri.toString(),
                        cursor.getString(nameCol),
                        cursor.getLong(sizeCol),
                        cursor.getLong(dateCol),
                        width,
                        height,
                        mimeType,
                    )
                }
            mediaList.add(item)
        }
    }

    companion object {
        /**
         * 使用 MediaMetadataRetriever 获取视频的尺寸（宽度和高度）。
         * **此函数已做向后兼容处理，可在 API 28 上运行。**
         */
        fun getVideoDimensions(
            context: Context,
            uri: Uri,
        ): Pair<Int, Int> {
            val retriever = MediaMetadataRetriever()
            try {
                retriever.setDataSource(context, uri)
                val width =
                    retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)?.toIntOrNull() ?: 0
                val height =
                    retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)?.toIntOrNull() ?: 0
                return Pair(width, height)
            } catch (e: Exception) {
                e.printStackTrace()
                return Pair(0, 0)
            } finally {
                // 关键的兼容性处理：
                // 在 API 29 (Android 10) 之前，MediaMetadataRetriever 没有实现 Closeable 接口，
                // 不能使用 `use` 块。必须手动在 `finally` 中调用 `release()`。
                // 在 API 29 及以上，推荐使用 `close()`。
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    retriever.close()
                } else {
                    @Suppress("DEPRECATION") // 明确告知编译器这是有意的版本兼容代码
                    retriever.release()
                }
            }
        }

        /**
         * 使用 MediaMetadataRetriever 获取视频的时长。
         * **此函数已做向后兼容处理，可在 API 28 上运行。**
         */
        fun getVideoDuration(
            context: Context,
            uri: Uri,
        ): Long {
            val retriever = MediaMetadataRetriever()
            try {
                retriever.setDataSource(context, uri)
                return retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLongOrNull() ?: 0L
            } catch (e: Exception) {
                e.printStackTrace()
                return 0L
            } finally {
                // 同样，手动释放资源以兼容旧版本。
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    retriever.close()
                } else {
                    @Suppress("DEPRECATION")
                    retriever.release()
                }
            }
        }

        /**
         * 使用 BitmapFactory 获取图片的尺寸，这是一种高效且内存安全的方式。
         */
        fun getImageDimensions(
            context: Context,
            uri: Uri,
        ): Pair<Int, Int> =
            try {
                // InputStream 实现了 Closeable，所以可以安全地使用 `use` 块。
                context.contentResolver.openInputStream(uri)?.use { inputStream ->
                    val options =
                        BitmapFactory.Options().apply {
                            // 关键！设置为 true 表示只解码图片的边界信息（尺寸），
                            // 而不把整个位图加载到内存中，从而避免 OOM。
                            inJustDecodeBounds = true
                        }
                    BitmapFactory.decodeStream(inputStream, null, options)
                    Pair(options.outWidth, options.outHeight)
                } ?: Pair(0, 0) // 如果无法打开输入流，返回 (0,0)
            } catch (e: Exception) {
                e.printStackTrace()
                Pair(0, 0)
            }
    }
}
