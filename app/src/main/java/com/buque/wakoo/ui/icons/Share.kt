package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Share: ImageVector
    get() {
        val current = _share
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.Share",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // M11 2.05 v2.01 A8 8 0 1 0 19.94 13 h2.01 A10 10 0 1 1 11 2.05 m9 3.36 -8 8 L10.59 12 l8 -8 H14 V2 h8 v8 h-2z
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 11 2.05
                    moveTo(x = 11.0f, y = 2.05f)
                    // v 2.01
                    verticalLineToRelative(dy = 2.01f)
                    // A 8 8 0 1 0 19.94 13
                    arcTo(
                        horizontalEllipseRadius = 8.0f,
                        verticalEllipseRadius = 8.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        x1 = 19.94f,
                        y1 = 13.0f,
                    )
                    // h 2.01
                    horizontalLineToRelative(dx = 2.01f)
                    // A 10 10 0 1 1 11 2.05
                    arcTo(
                        horizontalEllipseRadius = 10.0f,
                        verticalEllipseRadius = 10.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = true,
                        x1 = 11.0f,
                        y1 = 2.05f,
                    )
                    // m 9 3.36
                    moveToRelative(dx = 9.0f, dy = 3.36f)
                    // l -8 8
                    lineToRelative(dx = -8.0f, dy = 8.0f)
                    // L 10.59 12
                    lineTo(x = 10.59f, y = 12.0f)
                    // l 8 -8
                    lineToRelative(dx = 8.0f, dy = -8.0f)
                    // H 14
                    horizontalLineTo(x = 14.0f)
                    // V 2
                    verticalLineTo(y = 2.0f)
                    // h 8
                    horizontalLineToRelative(dx = 8.0f)
                    // v 8
                    verticalLineToRelative(dy = 8.0f)
                    // h -2z
                    horizontalLineToRelative(dx = -2.0f)
                    close()
                }
            }.build()
            .also { _share = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Share,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _share: ImageVector? = null
