# WheelPicker - Jetpack Compose 滚轮选择器

一个功能强大、高度可定制、性能稳定的Jetpack Compose通用滚轮选择器组件，模仿iOS原生的滚轮选择器效果。

## 特性

### 🎨 3D视觉效果
- **滚轮效果**: 具有3D透视感的滚轮效果
- **近大远小**: 位于中心的item为原始大小，离中心越远尺寸越小
- **透明度渐变**: 中心item完全不透明，离中心越远透明度越低
- **微曲旋转**: 离中心越远的item有轻微的rotationX变换，模拟滚轮弧形曲面
- **中心高亮**: 通过分割线明确标识选中的中心区域

### ⚙️ 高度可定制
- **数据源**: 支持任意类型的List数据源
- **可见项数**: 可配置同时可见的item数量（必须为奇数）
- **样式定制**: 项高度、文本样式、分割线颜色等UI元素可配置
- **内容定制**: 提供itemContent插槽，完全自定义每个item的显示内容

### 🔄 交互与状态
- **惯性滚动**: 平滑的、带有惯性的滚动体验
- **自动吸附**: 滚动停止后自动吸附到最近的item
- **滚动停止确认**: 只有在完全静止后才确认用户选择
- **无限循环**: 支持无限循环滚动模式

### 📊 状态管理
- **WheelPickerState**: 健壮的状态管理类
- **snappedIndex**: 获取滚动完全停止后最终选中的数据索引（最重要）
- **currentIndex**: 获取当前实时的索引（滚动过程中会实时变化）
- **isScrollInProgress**: 判断滚轮当前是否正在滚动
- **scrollToItem**: 立即定位到指定索引（无动画）
- **animateScrollToItem**: 平滑滚动到指定索引（有动画）

### 🔗 多级联动
- 支持多个WheelPicker水平并排排列
- 实现滚轮之间的联动效果
- 提供完整的日期选择器示例（年、月、日三级联动）

## 使用方法

### 基础用法

```kotlin
@Composable
fun BasicExample() {
    val items = listOf("苹果", "香蕉", "橙子", "葡萄", "草莓")
    val state = rememberWheelPickerState(
        initialIndex = 0,
        itemCount = items.size
    )
    var selectedItem by remember { mutableStateOf("") }

    // 监听最终选中项变化（只在滚动停止后触发）
    LaunchedEffect(state.snappedIndex) {
        selectedItem = items.getOrNull(state.snappedIndex) ?: ""
    }

    WheelPicker(
        items = items,
        state = state,
        modifier = Modifier.fillMaxWidth()
    )

    // selectedItem 现在包含最终选中的项
}
```

### 无限循环模式

```kotlin
@Composable
fun InfiniteExample() {
    val numbers = (0..99).toList()
    val state = rememberWheelPickerState(
        initialIndex = 0,
        itemCount = numbers.size,
        isInfinite = true
    )
    
    WheelPicker(
        items = numbers,
        state = state,
        isInfinite = true,
        itemContent = { number, isSelected ->
            Text(
                text = String.format("%02d", number),
                color = if (isSelected) Color.Red else Color.Gray
            )
        }
    )
}
```

### 日期选择器

```kotlin
@Composable
fun DatePickerExample() {
    var selectedDate by remember { mutableStateOf(Clock.System.todayIn(TimeZone.currentSystemDefault())) }

    // 基础日期选择器
    DateWheelPicker(
        selectedDate = selectedDate,
        onDateChanged = { selectedDate = it },
        yearRange = (2020..2030)
    )

    // 限制到今天的日期选择器
    DateWheelPicker(
        selectedDate = selectedDate,
        onDateChanged = { selectedDate = it },
        yearRange = (2020..2030),
        enabledDateRange = WheelPickerUtils.createDateRangeToToday(2020..2030)
    )

    // 带循环滚动的日期选择器
    DateWheelPicker(
        selectedDate = selectedDate,
        onDateChanged = { selectedDate = it },
        yearRange = (2020..2030),
        isMonthInfinite = true,
        isDayInfinite = true  // 固定显示31天，不存在的日期变灰
    )
}
```

### 时间选择器

```kotlin
@Composable
fun TimePickerExample() {
    var hour by remember { mutableStateOf(12) }
    var minute by remember { mutableStateOf(0) }
    var second by remember { mutableStateOf(0) }
    
    TimeWheelPicker(
        selectedHour = hour,
        selectedMinute = minute,
        selectedSecond = second,
        onTimeChanged = { h, m, s ->
            hour = h
            minute = m
            second = s
        },
        is24Hour = true,
        showSeconds = true
    )
}
```

### 可用性检查

```kotlin
@Composable
fun EnabledExample() {
    val numbers = (0..20).toList()
    val state = rememberWheelPickerState(
        initialIndex = 0,
        itemCount = numbers.size,
        isItemEnabled = { index ->
            val number = numbers.getOrNull(index) ?: 0
            number % 2 == 0 // 只有偶数可选
        }
    )

    WheelPicker(
        items = numbers,
        state = state,
        itemContent = { number, isSelected, isEnabled ->
            Text(
                text = number.toString(),
                color = when {
                    !isEnabled -> Color.Gray.copy(alpha = 0.3f)
                    isSelected -> Color.Red
                    else -> Color.Black
                }
            )
        }
    )
}
```

### 自定义样式

```kotlin
@Composable
fun CustomStyleExample() {
    val items = listOf("小", "中", "大", "特大", "超大")
    val state = rememberWheelPickerState(
        initialIndex = 2,
        itemCount = items.size
    )

    WheelPicker(
        items = items,
        state = state,
        visibleItemsCount = 3,
        itemHeight = 60.dp,
        dividerConfig = WheelPickerDefaults.DividerConfig(
            enabled = true,
            color = Color.Red.copy(alpha = 0.5f),
            thickness = 2.dp
        ),
        effect3DConfig = WheelPickerDefaults.Effect3DConfig(
            enabled = true,
            maxScale = 1.2f,
            minScale = 0.7f,
            maxRotationX = 0f,
            minRotationX = -20f
        )
    )
}
```

## API 参考

### WheelPicker

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| items | List<T> | - | 数据源列表 |
| state | WheelPickerState | - | 滚轮选择器状态 |
| modifier | Modifier | Modifier | 修饰符 |
| visibleItemsCount | Int | 5 | 可见项数量（必须为奇数） |
| itemHeight | Dp | 48.dp | 每个项的高度 |
| itemTextStyle | TextStyle | - | 项文本样式 |
| selectedTextColor | Color | - | 选中项文本颜色 |
| unselectedTextColor | Color | - | 未选中项文本颜色 |
| dividerConfig | DividerConfig | - | 分割线配置 |
| effect3DConfig | Effect3DConfig | - | 3D效果配置 |
| isInfinite | Boolean | false | 是否启用无限循环 |
| itemContent | @Composable | - | 自定义项内容 |

### WheelPickerState

| 属性/方法 | 类型 | 描述 |
|-----------|------|------|
| snappedIndex | Int | 最终选中的数据索引（停止滚动后） |
| currentIndex | Int | 当前实时的数据索引（滚动过程中） |
| isScrollInProgress | Boolean | 是否正在滚动 |
| scrollToItem(index) | suspend fun | 立即滚动到指定索引 |
| animateScrollToItem(index) | suspend fun | 平滑滚动到指定索引 |

## 注意事项

1. **visibleItemsCount** 必须为奇数
2. **items** 不能为空
3. 在无限循环模式下，组件会自动处理索引映射
4. **重要**：使用 `snappedIndex` 监听最终选择，使用 `currentIndex` 获取实时索引
5. **重要**：`LaunchedEffect(state.snappedIndex)` 只在滚动停止后触发，无需额外判断 `isScrollInProgress`
6. 多级联动时，注意处理数据更新和索引调整
7. 避免直接访问 `layoutInfo`，组件内部已优化重组性能

## 示例

完整的使用示例请参考 `WheelPickerExamples.kt` 文件。
