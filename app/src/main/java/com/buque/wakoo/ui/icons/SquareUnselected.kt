package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.SquareUnselected: ImageVector
    get() {
        val current = _SquareUnselected
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.SquareUnselected",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // M12 10 v5 m-3 -3.5 v2 m6 -2 v2
                path(
                    stroke = SolidColor(Color(0xFFB6B6B6)),
                    strokeLineCap = StrokeCap.Round,
                    strokeLineWidth = 1.6f,
                ) {
                    // M 12 10
                    moveTo(x = 12.0f, y = 10.0f)
                    // v 5
                    verticalLineToRelative(dy = 5.0f)
                    // m -3 -3.5
                    moveToRelative(dx = -3.0f, dy = -3.5f)
                    // v 2
                    verticalLineToRelative(dy = 2.0f)
                    // m 6 -2
                    moveToRelative(dx = 6.0f, dy = -2.0f)
                    // v 2
                    verticalLineToRelative(dy = 2.0f)
                }
                // M19 21 H5 a1 1 0 0 1 -1 -1 v-9 H1 l10.33 -9.39 a1 1 0 0 1 1.34 0 L23 11 h-3 v9 a1 1 0 0 1 -1 1 M6 19 h12 V9.16 L12 3.7 6 9.16z
                path(
                    fill = SolidColor(Color(0xFFB6B6B6)),
                ) {
                    // M 19 21
                    moveTo(x = 19.0f, y = 21.0f)
                    // H 5
                    horizontalLineTo(x = 5.0f)
                    // a 1 1 0 0 1 -1 -1
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.0f,
                        dy1 = -1.0f,
                    )
                    // v -9
                    verticalLineToRelative(dy = -9.0f)
                    // H 1
                    horizontalLineTo(x = 1.0f)
                    // l 10.33 -9.39
                    lineToRelative(dx = 10.33f, dy = -9.39f)
                    // a 1 1 0 0 1 1.34 0
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.34f,
                        dy1 = 0.0f,
                    )
                    // L 23 11
                    lineTo(x = 23.0f, y = 11.0f)
                    // h -3
                    horizontalLineToRelative(dx = -3.0f)
                    // v 9
                    verticalLineToRelative(dy = 9.0f)
                    // a 1 1 0 0 1 -1 1
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.0f,
                        dy1 = 1.0f,
                    )
                    // M 6 19
                    moveTo(x = 6.0f, y = 19.0f)
                    // h 12
                    horizontalLineToRelative(dx = 12.0f)
                    // V 9.16
                    verticalLineTo(y = 9.16f)
                    // L 12 3.7
                    lineTo(x = 12.0f, y = 3.7f)
                    // L 6 9.16z
                    lineTo(x = 6.0f, y = 9.16f)
                    close()
                }
            }.build()
            .also { _SquareUnselected = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.SquareUnselected,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _SquareUnselected: ImageVector? = null
