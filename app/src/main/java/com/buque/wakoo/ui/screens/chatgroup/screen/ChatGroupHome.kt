package com.buque.wakoo.ui.screens.chatgroup.screen

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.platform.LocalDensity
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.utils.WatchMessageEventEffect
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.navigation.ChatGroupRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.screens.chatgroup.chat.ChatGroupPage
import com.buque.wakoo.ui.widget.gift.GiftViewModel
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.requireData
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupSettingsViewModel
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupViewModel

@Composable
fun ChatGroupHome(viewModel: ChatGroupSettingsViewModel) {
    val nav = LocalAppNavController.current
    val chatGroupId = viewModel.groupId
    val st by viewModel.state
    val lm = LocalLoadingManager.current
    val scope = rememberCoroutineScope()
    val density = LocalDensity.current
    if (st is CState.Success) {
        val chatGroup = st.requireData
        WatchMessageEventEffect(
            object : IMCompatListener {
                override fun onRecvNewCustomMessage(
                    message: UCCustomMessage,
                    offline: Boolean,
                ) {
                    LogUtils.d("rec chatgroup event:\n${message.customJson}")
                }
            },
        )

        val imId = chatGroup.imId
        val chatViewModel =
            viewModel<ChatGroupViewModel>(initializer = {
                ChatGroupViewModel(imId, density)
            })

        val giftViewModel =
            viewModel<GiftViewModel>(initializer = {
                GiftViewModel(chatGroupId, ConversationType.GROUP, imId)
            })

        ChatGroupPage(chatGroup, chatViewModel, giftViewModel, onChangeBuilt = { newBuilt ->
            lm.show(scope) {
                viewModel.update(bulletin = newBuilt)
            }
        }, onMore = {
            nav.push(ChatGroupRoute.ChatGroupDetail(groupId = chatGroupId))
        })
    }
}
