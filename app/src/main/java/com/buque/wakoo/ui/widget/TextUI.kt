package com.buque.wakoo.ui.widget

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.ui.theme.MI_SANS

/**
 * 实现了倾斜和渐变效果的文字组件
 */
@Composable
fun SkewedGradientText(
    text: String,
    enableBrush: Boolean,
    modifier: Modifier = Modifier,
    fontSize: TextUnit = TextUnit.Unspecified,
) {
    val style =
        if (enableBrush) {
            TextStyle(
                brush =
                    Brush.horizontalGradient(
                        colors =
                            listOf(
                                Color(0xFFA3FF2C), // 黄绿色 (LimeGreen)
                                Color(0xFF31FFA1), // 青色
                            ),
                    ),
                // 2. 将渐变画刷应用到文字样式
                fontSize = fontSize,
                fontStyle = FontStyle.Italic,
                fontFamily = FontFamily.MI_SANS,
            )
        } else {
            TextStyle(
                color = Color(0xFF111111), // 2. 将渐变画刷应用到文字样式
                fontSize = 20.sp,
                fontStyle = FontStyle.Italic,
                fontFamily = FontFamily.MI_SANS,
            )
        }

    Text(
        text = text,
        style = style,
        modifier =
            modifier
                // 3. 使用 graphicsLayer 实现倾斜效果
                .graphicsLayer {
                    rotationY = -10f
                    translationX = -10f
                    rotationZ = -7f
                    cameraDistance = 2.8f
                },
    )
}

@Preview(showBackground = true, backgroundColor = 0xFFFFFFFF)
@Composable
private fun FollowButtonPreview() {
    Box(modifier = Modifier.padding(20.dp)) {
        SkewedGradientText("关注TA", true)
    }
}
