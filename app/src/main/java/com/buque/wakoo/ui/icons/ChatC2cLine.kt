package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.ChatC2cLine: ImageVector
    get() {
        val current = _chatC2cLine
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.ChatC2cLine",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // M7.3 20.82 2 22 l1.18 -5.3 a10 10 0 1 1 4.12 4.12 m.28 -2.1 .65 .34 a8 8 0 1 0 -3.3 -3.3 l.36 .66 -.66 2.95z M7 12 h2 a3 3 0 1 0 6 0 h2 a5 5 0 0 1 -10 0
                path(
                    fill = SolidColor(Color(0xFFFFFFFF)),
                ) {
                    // M 7.3 20.82
                    moveTo(x = 7.3f, y = 20.82f)
                    // L 2 22
                    lineTo(x = 2.0f, y = 22.0f)
                    // l 1.18 -5.3
                    lineToRelative(dx = 1.18f, dy = -5.3f)
                    // a 10 10 0 1 1 4.12 4.12
                    arcToRelative(
                        a = 10.0f,
                        b = 10.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = true,
                        dx1 = 4.12f,
                        dy1 = 4.12f,
                    )
                    // m 0.28 -2.1
                    moveToRelative(dx = 0.28f, dy = -2.1f)
                    // l 0.65 0.34
                    lineToRelative(dx = 0.65f, dy = 0.34f)
                    // a 8 8 0 1 0 -3.3 -3.3
                    arcToRelative(
                        a = 8.0f,
                        b = 8.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        dx1 = -3.3f,
                        dy1 = -3.3f,
                    )
                    // l 0.36 0.66
                    lineToRelative(dx = 0.36f, dy = 0.66f)
                    // l -0.66 2.95z
                    lineToRelative(dx = -0.66f, dy = 2.95f)
                    close()
                    // M 7 12
                    moveTo(x = 7.0f, y = 12.0f)
                    // h 2
                    horizontalLineToRelative(dx = 2.0f)
                    // a 3 3 0 1 0 6 0
                    arcToRelative(
                        a = 3.0f,
                        b = 3.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        dx1 = 6.0f,
                        dy1 = 0.0f,
                    )
                    // h 2
                    horizontalLineToRelative(dx = 2.0f)
                    // a 5 5 0 0 1 -10 0
                    arcToRelative(
                        a = 5.0f,
                        b = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -10.0f,
                        dy1 = 0.0f,
                    )
                }
            }.build()
            .also { _chatC2cLine = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.ChatC2cLine,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _chatC2cLine: ImageVector? = null
