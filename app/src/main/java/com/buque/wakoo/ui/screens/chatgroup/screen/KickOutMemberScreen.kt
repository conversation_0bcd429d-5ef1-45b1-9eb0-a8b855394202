package com.buque.wakoo.ui.screens.chatgroup.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.bean.BasicUser
import com.buque.wakoo.bean.User
import com.buque.wakoo.bean.chatgroup.ChatGroupMember
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.ext.toastWhenError
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.Search
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.UserListItem
import com.buque.wakoo.ui.widget.state.StateListPaginateLayout
import com.buque.wakoo.viewmodel.chatgroup.SearchMemberViewModel
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupMemberListViewModel
import kotlinx.coroutines.delay

@Composable
fun KickOutMemberScreen(
    groupId: String,
    targetRoles: String,
) {
    val viewModel =
        viewModel<ChatGroupMemberListViewModel>(
            factory =
                viewModelFactory {
                    initializer { ChatGroupMemberListViewModel(groupId, targetRoles) }
                },
        )

    val searchViewModel =
        viewModel<SearchMemberViewModel>(
            factory =
                viewModelFactory {
                    initializer {
                        SearchMemberViewModel(groupId)
                    }
                },
        )
    var keywords by remember {
        mutableStateOf("")
    }

    var searchContent by remember {
        mutableStateOf("")
    }
    LaunchedEffect(searchContent) {
        delay(800L)
        keywords = searchContent
        searchViewModel.keywords = keywords
    }

    val scope = rememberCoroutineScope()
    val lm = LocalLoadingManager.current
    TitleScreenScaffold("成员管理") { pd ->
        Column(modifier = Modifier.padding(pd)) {
            SearchBar(searchContent, {
                if (it.length < 8) {
                    searchContent = it
                }
            }, placeholder = "请输入ID", modifier = Modifier.padding(16.dp, 8.dp))
            if (keywords.isNotEmpty()) {
                StateListPaginateLayout<Int, ChatGroupMember, SearchMemberViewModel>(
                    viewModel = searchViewModel,
                ) { _, list ->
                    LazyColumn {
                        items(list) { item ->
                            KickOutMemberItem(item.user, onKickOut = {
                                lm.show(scope) {
                                    searchViewModel
                                        .kickOutMember(item)
                                        .onSuccess {
                                            viewModel.remove(item)
                                            showToast("已成功踢出")
                                        }.toastWhenError()
                                }
                            })
                        }
                    }
                }
            } else {
                StateListPaginateLayout<Int, ChatGroupMember, ChatGroupMemberListViewModel>(viewModel = viewModel) { _, list ->
                    LazyColumn {
                        items(list) { item ->
                            KickOutMemberItem(item.user, onKickOut = {
                                lm.show(scope) {
                                    viewModel
                                        .kickOutMember(item)
                                        .onSuccess {
                                            showToast("已成功踢出")
                                        }.toastWhenError()
                                }
                            })
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun KickOutMemberItem(
    user: User,
    onKickOut: OnAction = {},
) {
    UserListItem(
        user,
        modifier = Modifier.padding(16.dp, 12.dp),
        centerContent = {
            Column {
                Text(
                    text = user.name,
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color(0xFF111111),
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
            }
        },
        endContent = {
            SolidButton(
                "踢出群组",
                height = 32.dp,
                minWidth = 64.dp,
                fontSize = 14.sp,
                onClick = onKickOut,
            )
        },
    )
}

@Composable
fun SearchBar(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    placeholder: String = "",
    onSave: OnAction = {},
) {
    Row(
        modifier =
            modifier
                .fillMaxWidth()
                .height(40.dp)
                .background(Color(0xFFE9EAEF), RoundedCornerShape(50))
                .padding(horizontal = 18.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Image(WakooIcons.Search, contentDescription = "search")
        BasicTextField(
            value,
            onValueChange,
            modifier = Modifier.padding(start = 8.dp),
            decorationBox = {
                Box(Modifier.fillMaxSize(), contentAlignment = Alignment.CenterStart) {
                    it.invoke()
                    if (value.isEmpty()) {
                        Text(
                            placeholder,
                            fontSize = 14.sp,
                            color = Color(0xFF999999),
                        )
                    }
                }
            },
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Search),
            keyboardActions = KeyboardActions(onDone = { onSave }),
        )
    }
}

@Preview
@Composable
fun KickOutMemberItemPreview(modifier: Modifier = Modifier) {
    WakooTheme {
        Column {
            SearchBar("xxx", {}, placeholder = "请输入ID")
            KickOutMemberItem(BasicUser.sampleGirl)
        }
    }
}
