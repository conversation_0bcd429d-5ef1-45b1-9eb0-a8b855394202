package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.EvenOdd
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

// 正确的 Favorite ImageVector 实现
val WakooIcons.Favorite: ImageVector
    get() {
        if (_Favorite != null) return _Favorite!!
        _Favorite =
            ImageVector
                .Builder(
                    name = "WakooIcons.Favorite", // 推荐使用对象名+属性名的方式命名
                    defaultWidth = 16.0.dp,
                    defaultHeight = 16.0.dp,
                    viewportWidth = 16.0f,
                    viewportHeight = 16.0f,
                ).path(
                    fill = SolidColor(Color(0xFFB6B6B6)),
                    // 关键点：使用 EvenOdd 填充规则来创建 "空心" 效果
                    pathFillType = EvenOdd,
                ) {
                    // Path Data from: M8.00032 12.174L...Z
                    moveTo(8.00032f, 12.174f)
                    lineTo(3.29802f, 14.8061f)
                    lineTo(4.34824f, 9.52058f)
                    lineTo(0.391846f, 5.86185f)
                    lineTo(5.74321f, 5.22736f)
                    lineTo(8.00032f, 0.333984f)
                    lineTo(10.2574f, 5.22736f)
                    lineTo(15.6087f, 5.86185f)
                    lineTo(11.6524f, 9.52058f)
                    lineTo(12.7026f, 14.8061f)
                    lineTo(8.00032f, 12.174f)
                    close()
                    // Path Data from: M8.00032 10.646L...Z
                    moveTo(8.00032f, 10.646f)
                    lineTo(10.8315f, 12.2307f)
                    lineTo(10.1991f, 9.04845f)
                    lineTo(12.5812f, 6.84558f)
                    lineTo(9.35925f, 6.46354f)
                    lineTo(8.00032f, 3.51733f)
                    lineTo(6.64135f, 6.46354f)
                    lineTo(3.41939f, 6.84558f)
                    lineTo(5.80146f, 9.04845f)
                    lineTo(5.16914f, 12.2307f)
                    lineTo(8.00032f, 10.646f)
                    close()
                }.build()
        return _Favorite!!
    }

@Suppress("ObjectPropertyName")
private var _Favorite: ImageVector? = null

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Favorite,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((16.0).dp)
                        .height((16.0).dp),
            )
        }
    }
}
