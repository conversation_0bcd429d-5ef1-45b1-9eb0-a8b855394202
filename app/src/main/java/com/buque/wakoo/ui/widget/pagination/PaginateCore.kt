package com.buque.wakoo.ui.widget.pagination

import android.os.SystemClock
import androidx.compose.runtime.Stable
import kotlinx.coroutines.delay

/**
 * 分页加载的结果
 */
sealed class LoadResult<Key : Any> {
    /**
     * 加载成功
     * @param pageKey 下一页的键，如果为null表示已经到达末尾
     */
    data class Page<Key : Any>(
        val pageKey: Key?,
    ) : LoadResult<Key>()

    /**
     * 加载失败
     */
    data class Error<Key : Any>(
        val throwable: Throwable,
    ) : LoadResult<Key>()

    val isSuccess: Boolean
        get() = this is Page<*>
}

/**
 * 分页加载状态
 */
sealed class LoadState {
    /**
     * 空闲状态，可以加载
     */
    data object Idle : LoadState()

    /**
     * 加载中状态
     */
    data class Loading<Key : Any>(
        val key: Key?,
    ) : LoadState()

    /**
     * 加载失败状态
     */
    data class Failure<Key : Any>(
        val key: Key?,
        val throwable: Throwable,
    ) : LoadState()

    /**
     * 加载结束状态，没有更多数据
     */
    data object End : LoadState()

    val isLoading: Boolean
        get() = this is Loading<*>

    val isFailure: Boolean
        get() = this is Failure<*>

    val canLoad: Boolean
        get() = this is Idle

    val isEnd: Boolean
        get() = this is End
}

/**
 * 分页加载作用域
 */
data class PagingScope<Key : Any>(
    val key: Key?,
    val next: Boolean,
)

/**
 * 分页操作的抽象类
 */
sealed class PaginateAction {
    data object Load : PaginateAction()

    data object Cancel : PaginateAction()

    data object Nothing : PaginateAction()
}

/**
 * 分页加载的核心工具类
 */
@Stable
object PaginateUtils {
    /**
     * 处理加载结果，更新状态
     */
    suspend fun <Key : Any> processLoadResult(
        result: LoadResult<Key>,
        key: Key?,
        updateKey: (Key?) -> Unit,
        updateState: (LoadState) -> Unit,
        checkMaxReached: () -> Boolean = { false },
    ) {
        // 确保数据分页加载状态变化不在同一次组合中
        val start = SystemClock.elapsedRealtime()
        delay(16 - (SystemClock.elapsedRealtime() - start).coerceAtMost(16))

        val loadState =
            if (result is LoadResult.Page) {
                updateKey(result.pageKey)
                if (result.pageKey == null || checkMaxReached()) {
                    LoadState.End
                } else {
                    LoadState.Idle
                }
            } else {
                result as LoadResult.Error
                LoadState.Failure(key, result.throwable)
            }

        updateState(loadState)
    }
}