package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.RoomNotice: ImageVector
    get() {
        val current = _roomNotice
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.RoomNotice",
                defaultWidth = 12.0.dp,
                defaultHeight = 12.0.dp,
                viewportWidth = 12.0f,
                viewportHeight = 12.0f,
            ).apply {
                // M1.4 10.35 V3.7 a.25 .25 0 0 1 .25 -.25 h8.7 A.25 .25 0 0 1 10.6 3.7 v6.66 a.25 .25 0 0 1 -.25 .25 h-8.7 a.25 .25 0 0 1 -.25 -.25Z
                path(
                    stroke = SolidColor(Color(0xFFFFFFFF)),
                    strokeLineWidth = 1.0f,
                ) {
                    // M 1.4 10.35
                    moveTo(x = 1.4f, y = 10.35f)
                    // V 3.7
                    verticalLineTo(y = 3.7f)
                    // a 0.25 0.25 0 0 1 0.25 -0.25
                    arcToRelative(
                        a = 0.25f,
                        b = 0.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.25f,
                        dy1 = -0.25f,
                    )
                    // h 8.7
                    horizontalLineToRelative(dx = 8.7f)
                    // A 0.25 0.25 0 0 1 10.6 3.7
                    arcTo(
                        horizontalEllipseRadius = 0.25f,
                        verticalEllipseRadius = 0.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 10.6f,
                        y1 = 3.7f,
                    )
                    // v 6.66
                    verticalLineToRelative(dy = 6.66f)
                    // a 0.25 0.25 0 0 1 -0.25 0.25
                    arcToRelative(
                        a = 0.25f,
                        b = 0.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -0.25f,
                        dy1 = 0.25f,
                    )
                    // h -8.7
                    horizontalLineToRelative(dx = -8.7f)
                    // a 0.25 0.25 0 0 1 -0.25 -0.25z
                    arcToRelative(
                        a = 0.25f,
                        b = 0.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -0.25f,
                        dy1 = -0.25f,
                    )
                    close()
                }
                // M5.88 1.57 3.95 3.44 h4.1 L6.23 1.58 a.25 .25 0 0 0 -.35 0Z
                path(
                    stroke = SolidColor(Color(0xFFFFFFFF)),
                    strokeLineWidth = 1.0f,
                ) {
                    // M 5.88 1.57
                    moveTo(x = 5.88f, y = 1.57f)
                    // L 3.95 3.44
                    lineTo(x = 3.95f, y = 3.44f)
                    // h 4.1
                    horizontalLineToRelative(dx = 4.1f)
                    // L 6.23 1.58
                    lineTo(x = 6.23f, y = 1.58f)
                    // a 0.25 0.25 0 0 0 -0.35 0z
                    arcToRelative(
                        a = 0.25f,
                        b = 0.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -0.35f,
                        dy1 = 0.0f,
                    )
                    close()
                }
                // M2.7 6.51 H3.9 V7.71 H2.7z
                path(
                    fill = SolidColor(Color(0xFFFFFFFF)),
                ) {
                    // M 2.7 6.51
                    moveTo(x = 2.7f, y = 6.51f)
                    // H 3.9
                    horizontalLineTo(x = 3.9f)
                    // V 7.71
                    verticalLineTo(y = 7.71f)
                    // H 2.7z
                    horizontalLineTo(x = 2.7f)
                    close()
                }
                // M5.4 6.51 H6.6 V7.71 H5.4z
                path(
                    fill = SolidColor(Color(0xFFFFFFFF)),
                ) {
                    // M 5.4 6.51
                    moveTo(x = 5.4f, y = 6.51f)
                    // H 6.6
                    horizontalLineTo(x = 6.6f)
                    // V 7.71
                    verticalLineTo(y = 7.71f)
                    // H 5.4z
                    horizontalLineTo(x = 5.4f)
                    close()
                }
                // M8.1 6.51 H9.3 V7.71 H8.1z
                path(
                    fill = SolidColor(Color(0xFFFFFFFF)),
                ) {
                    // M 8.1 6.51
                    moveTo(x = 8.1f, y = 6.51f)
                    // H 9.3
                    horizontalLineTo(x = 9.3f)
                    // V 7.71
                    verticalLineTo(y = 7.71f)
                    // H 8.1z
                    horizontalLineTo(x = 8.1f)
                    close()
                }
            }.build()
            .also { _roomNotice = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.RoomNotice,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((12.0).dp)
                        .height((12.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _roomNotice: ImageVector? = null
