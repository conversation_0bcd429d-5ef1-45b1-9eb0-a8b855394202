package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.DownMic: ImageVector
    get() {
        val current = _downMic
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.DownMic",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // M18.99 4.34 A4 4 0 0 1 20 7 v2.13 a4 4 0 0 1 1 7.33 V21 h-2 v-1 H5 v1 H3 v-.67 L5.33 18 H19 v-2.17 a1 1 0 0 1 .67 -.94 A2 2 0 1 0 17 13 v3 h-2 v-1 H9 v1 H7.33 l3 -3 H15 a4 4 0 0 1 3 -3.87 V7 q-.01 -.72 -.43 -1.24z M14.68 3 l-2 2 H8 a2 2 0 0 0 -2 2 v2.13 A4 4 0 0 1 7.66 10 l-1.42 1.42 a2 2 0 0 0 -2.8 2.8 L2 15.67 A3.98 3.98 0 0 1 4 9.13 V7 a4 4 0 0 1 4 -4z
                path(
                    fill = SolidColor(Color(0xFFFFFFFF)),
                ) {
                    // M 18.99 4.34
                    moveTo(x = 18.99f, y = 4.34f)
                    // A 4 4 0 0 1 20 7
                    arcTo(
                        horizontalEllipseRadius = 4.0f,
                        verticalEllipseRadius = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 20.0f,
                        y1 = 7.0f,
                    )
                    // v 2.13
                    verticalLineToRelative(dy = 2.13f)
                    // a 4 4 0 0 1 1 7.33
                    arcToRelative(
                        a = 4.0f,
                        b = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.0f,
                        dy1 = 7.33f,
                    )
                    // V 21
                    verticalLineTo(y = 21.0f)
                    // h -2
                    horizontalLineToRelative(dx = -2.0f)
                    // v -1
                    verticalLineToRelative(dy = -1.0f)
                    // H 5
                    horizontalLineTo(x = 5.0f)
                    // v 1
                    verticalLineToRelative(dy = 1.0f)
                    // H 3
                    horizontalLineTo(x = 3.0f)
                    // v -0.67
                    verticalLineToRelative(dy = -0.67f)
                    // L 5.33 18
                    lineTo(x = 5.33f, y = 18.0f)
                    // H 19
                    horizontalLineTo(x = 19.0f)
                    // v -2.17
                    verticalLineToRelative(dy = -2.17f)
                    // a 1 1 0 0 1 0.67 -0.94
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.67f,
                        dy1 = -0.94f,
                    )
                    // A 2 2 0 1 0 17 13
                    arcTo(
                        horizontalEllipseRadius = 2.0f,
                        verticalEllipseRadius = 2.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        x1 = 17.0f,
                        y1 = 13.0f,
                    )
                    // v 3
                    verticalLineToRelative(dy = 3.0f)
                    // h -2
                    horizontalLineToRelative(dx = -2.0f)
                    // v -1
                    verticalLineToRelative(dy = -1.0f)
                    // H 9
                    horizontalLineTo(x = 9.0f)
                    // v 1
                    verticalLineToRelative(dy = 1.0f)
                    // H 7.33
                    horizontalLineTo(x = 7.33f)
                    // l 3 -3
                    lineToRelative(dx = 3.0f, dy = -3.0f)
                    // H 15
                    horizontalLineTo(x = 15.0f)
                    // a 4 4 0 0 1 3 -3.87
                    arcToRelative(
                        a = 4.0f,
                        b = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 3.0f,
                        dy1 = -3.87f,
                    )
                    // V 7
                    verticalLineTo(y = 7.0f)
                    // q -0.01 -0.72 -0.43 -1.24z
                    quadToRelative(
                        dx1 = -0.01f,
                        dy1 = -0.72f,
                        dx2 = -0.43f,
                        dy2 = -1.24f,
                    )
                    close()
                    // M 14.68 3
                    moveTo(x = 14.68f, y = 3.0f)
                    // l -2 2
                    lineToRelative(dx = -2.0f, dy = 2.0f)
                    // H 8
                    horizontalLineTo(x = 8.0f)
                    // a 2 2 0 0 0 -2 2
                    arcToRelative(
                        a = 2.0f,
                        b = 2.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -2.0f,
                        dy1 = 2.0f,
                    )
                    // v 2.13
                    verticalLineToRelative(dy = 2.13f)
                    // A 4 4 0 0 1 7.66 10
                    arcTo(
                        horizontalEllipseRadius = 4.0f,
                        verticalEllipseRadius = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 7.66f,
                        y1 = 10.0f,
                    )
                    // l -1.42 1.42
                    lineToRelative(dx = -1.42f, dy = 1.42f)
                    // a 2 2 0 0 0 -2.8 2.8
                    arcToRelative(
                        a = 2.0f,
                        b = 2.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -2.8f,
                        dy1 = 2.8f,
                    )
                    // L 2 15.67
                    lineTo(x = 2.0f, y = 15.67f)
                    // A 3.98 3.98 0 0 1 4 9.13
                    arcTo(
                        horizontalEllipseRadius = 3.98f,
                        verticalEllipseRadius = 3.98f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 4.0f,
                        y1 = 9.13f,
                    )
                    // V 7
                    verticalLineTo(y = 7.0f)
                    // a 4 4 0 0 1 4 -4z
                    arcToRelative(
                        a = 4.0f,
                        b = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 4.0f,
                        dy1 = -4.0f,
                    )
                    close()
                }
                // M17.92 1.17 H19.92 V26.17 H17.92z
                path(
                    fill = SolidColor(Color(0xFFFFFFFF)),
                ) {
                    // M 17.92 1.1700001
                    moveTo(x = 17.92f, y = 1.1700001f)
                    // L 19.334213 2.5842133
                    lineTo(x = 19.334213f, y = 2.5842133f)
                    // L 1.6565442 20.26188
                    lineTo(x = 1.6565442f, y = 20.26188f)
                    // L 0.24233103 18.84767z
                    lineTo(x = 0.24233103f, y = 18.84767f)
                    close()
                }
            }.build()
            .also { _downMic = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.DownMic,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _downMic: ImageVector? = null
