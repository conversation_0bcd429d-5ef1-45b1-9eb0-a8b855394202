package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.UserFemale: ImageVector
    get() {
        val current = _userFemale
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.UserFemale",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // M8.47 14.11 a7.5 7.5 0 1 1 1.42 1.42 l-1.46 1.46 3.53 3.53 -1.41 1.42 L7 18.4 l-2.83 2.83 -1.41 -1.41 2.83 -2.83 -3.54 -3.54 1.42 -1.41 L7 15.57z m2.08 -.66 a5.5 5.5 0 1 0 7.78 -7.78 5.5 5.5 0 0 0 -7.78 7.78
                path(
                    fill = SolidColor(Color(0xFFFF6DCC)),
                ) {
                    // M 8.47 14.11
                    moveTo(x = 8.47f, y = 14.11f)
                    // a 7.5 7.5 0 1 1 1.42 1.42
                    arcToRelative(
                        a = 7.5f,
                        b = 7.5f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = true,
                        dx1 = 1.42f,
                        dy1 = 1.42f,
                    )
                    // l -1.46 1.46
                    lineToRelative(dx = -1.46f, dy = 1.46f)
                    // l 3.53 3.53
                    lineToRelative(dx = 3.53f, dy = 3.53f)
                    // l -1.41 1.42
                    lineToRelative(dx = -1.41f, dy = 1.42f)
                    // L 7 18.4
                    lineTo(x = 7.0f, y = 18.4f)
                    // l -2.83 2.83
                    lineToRelative(dx = -2.83f, dy = 2.83f)
                    // l -1.41 -1.41
                    lineToRelative(dx = -1.41f, dy = -1.41f)
                    // l 2.83 -2.83
                    lineToRelative(dx = 2.83f, dy = -2.83f)
                    // l -3.54 -3.54
                    lineToRelative(dx = -3.54f, dy = -3.54f)
                    // l 1.42 -1.41
                    lineToRelative(dx = 1.42f, dy = -1.41f)
                    // L 7 15.57z
                    lineTo(x = 7.0f, y = 15.57f)
                    close()
                    // m 2.08 -0.66
                    moveToRelative(dx = 2.08f, dy = -0.66f)
                    // a 5.5 5.5 0 1 0 7.78 -7.78
                    arcToRelative(
                        a = 5.5f,
                        b = 5.5f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        dx1 = 7.78f,
                        dy1 = -7.78f,
                    )
                    // a 5.5 5.5 0 0 0 -7.78 7.78
                    arcToRelative(
                        a = 5.5f,
                        b = 5.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -7.78f,
                        dy1 = 7.78f,
                    )
                }
            }.build()
            .also { _userFemale = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.UserFemale,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _userFemale: ImageVector? = null
