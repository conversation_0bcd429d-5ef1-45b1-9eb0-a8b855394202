package com.buque.wakoo.ui.screens.chatgroup.panel

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.emptyAction
import com.buque.wakoo.bean.IconLabel
import com.buque.wakoo.bean.LocalSelfUserProvider
import com.buque.wakoo.bean.User
import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.bean.toBasic
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.icons.AddRelations
import com.buque.wakoo.ui.icons.At
import com.buque.wakoo.ui.icons.ChatC2c
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.RoomRole
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.VipCrownTag
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.viewmodel.UserProfileViewModel

// @TA
private const val MENU_TYPE_AT_USER = 1

// 私信
private const val MENU_TYPE_C2C_MESSAGE = 2

// 关注
private const val MENU_TYPE_FOLLOWED = 3

// 更多
private const val MENU_TYPE_MORE = 4

@Composable
fun DialogScope.ChatGroupUserInfoPanel(
    user: User,
    modifier: Modifier = Modifier,
    vm: UserProfileViewModel = viewModel(),
    onSendGift: OnAction = {},
    onAtClick: OnAction = {},
    onChat: OnAction = emptyAction
) {
    val rootNavController = LocalAppNavController.root
    val targetUser = user

    val state by vm.state

    LaunchedEffect(user) {
        vm.preLoad(user)
        vm.requestUserInfo(user.id)
    }
    Box(
        modifier = modifier.fillMaxWidth(),
    ) {
        Spacer(
            modifier =
                Modifier
                    .padding(top = 28.dp)
                    .matchParentSize()
                    .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                    .background(WakooWhite)
                    .background(
                        brush =
                            Brush.verticalGradient(
                                0f to Color(0xFFD6FFD7),
                                0.3f to Color(0x00D6FFD7),
                            ),
                    ),
        )
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
            ) {
                if (!user.isSelf) {
                    Text(
                        text = "举报",
                        style = MaterialTheme.typography.labelLarge,
                        color = WakooSecondarySelected,
                        modifier =
                            Modifier
                                .align(Alignment.CenterStart)
                                .padding(top = 10.dp)
                                .clickable {
                                    rootNavController.push(Route.Report(1, user.id))
                                },
                    )
                }

                AvatarNetworkImage(
                    user = targetUser,
                    size = 80.dp,
                    modifier =
                        Modifier
                            .align(Alignment.Center)
                            .border(1.5.dp, color = WakooWhite, CircleShape),
                )

                Text(
                    text = "查看主页",
                    style = MaterialTheme.typography.labelLarge,
                    color = WakooSecondarySelected,
                    modifier =
                        Modifier
                            .align(Alignment.CenterEnd)
                            .padding(top = 10.dp)
                            .clickable {
                                dismiss()
                                rootNavController.push(Route.UserProfile(targetUser.toBasic()))
                            },
                )
            }

            SizeHeight(16.dp)

            Row(
                modifier = Modifier.padding(horizontal = 20.dp),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = targetUser.name,
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color(0xFF111111),
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    textAlign = TextAlign.Center,
                )

                SizeWidth(4.dp)

                GenderAgeTag(user = targetUser)
            }

            SizeHeight(8.dp)

            FlowRow(
                modifier = Modifier.padding(horizontal = 20.dp),
                horizontalArrangement = Arrangement.spacedBy(2.dp, Alignment.CenterHorizontally),
                verticalArrangement = Arrangement.spacedBy(2.dp, Alignment.CenterVertically),
            ) {
                if (user.isVip) {
                    VipCrownTag()
                }
                if (user is UserInfo) {
                    user.extra.medalList?.forEach {
                        NetworkImage(
                            data = it.icon,
                            modifier = Modifier.size(it.width.dp, it.height.dp),
                        )
                    }
                }
            }

            SizeHeight(30.dp)

            if (user.isSelf) {
                return
            }

            GradientButton(
                text = "送礼物",
                onClick = onSendGift,
                minWidth = 215.dp,
                height = 40.dp,
            )

            SizeHeight(15.dp)

            HorizontalDivider(thickness = 0.5.dp, color = Color(0xFFE5E5E5))

            val buttonItems by remember(state) {
                derivedStateOf {
                    buildList {
                        add(IconLabel(MENU_TYPE_AT_USER, WakooIcons.At, "TA"))
                        if (state?.extra?.isFollowed == true) {
                            add(IconLabel(MENU_TYPE_FOLLOWED, WakooIcons.AddRelations, "已关注", null, true))
                        } else {
                            add(IconLabel(MENU_TYPE_FOLLOWED, WakooIcons.AddRelations, "关注"))
                        }
                        add(IconLabel(MENU_TYPE_C2C_MESSAGE, WakooIcons.ChatC2c, "私信"))
                    }
                }
            }

            val onClick = { item: IconLabel<*> ->
                when (item.id) {
                    MENU_TYPE_AT_USER -> {
                        onAtClick()
                        dismiss()
                    }

                    MENU_TYPE_FOLLOWED -> {
                        vm.toggleFollowState(user.id)
                    }

                    MENU_TYPE_C2C_MESSAGE -> {
                        onChat()
                        dismiss()
                    }

                    MENU_TYPE_MORE -> {
                        dismiss()
                    }
                }
            }

            Row(
                modifier =
                    Modifier
                        .padding(horizontal = 8.dp)
                        .height(IntrinsicSize.Min),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                buttonItems.forEachIndexed { index: Int, item: IconLabel<ImageVector> ->
                    if (index > 0) {
                        VerticalDivider(
                            thickness = 0.5.dp,
                            color = Color(0xFFE5E5E5),
                            modifier = Modifier.fillMaxHeight(0.6f),
                        )
                    }
                    Row(
                        modifier =
                            Modifier
                                .weight(1f)
                                .noEffectClick(onClick = {
                                    if (item.iconVisible) {
                                        onClick(item)
                                    }
                                })
                                .padding(vertical = 16.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center,
                    ) {
                        if (item.iconVisible) {
                            Icon(
                                imageVector = item.icon,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp),
                                tint = item.iconTint ?: WakooSecondarySelected,
                            )
                            SizeWidth(5.dp)
                        }
                        Text(
                            text = item.label,
                            style = MaterialTheme.typography.labelLarge,
                            color = if (item.iconVisible) WakooSecondarySelected else WakooGrayText,
                            fontWeight = FontWeight.Medium,
                        )
                    }
                }
            }

            SizeHeight(25.dp)
        }
    }
}

@Composable
fun DialogScope.ChatGroupManagePanel(
    user: User,
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {

    val selfRole by roomInfoState.rememberRoomRoleState(LocalSelfUserProvider.currentId)
    if (selfRole == RoomRole.Member) {
        // 失去管理员权限
        LaunchedEffect(Unit) {
            dismiss()
        }
    }

    roomInfoState.trackRoomMember(user)

    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .background(
                    color = WakooWhite,
                    shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp),
                )
                .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        val targetRole by roomInfoState.rememberRoomRoleState(user.id)
        val targetInBlack by roomInfoState.rememberInBlackListState(user.id)

        if (selfRole == RoomRole.Owner) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .clickable {
                            dismiss()
                            roomInfoState.sendEvent(RoomEvent.SetAdminEvent(user.id, targetRole == RoomRole.Member))
                        }
                        .padding(16.dp),
                contentAlignment = Alignment.Center,
            ) {
                Text(
                    text = if (targetRole == RoomRole.Member) "设置为房间管理员" else "取消房间管理员",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Normal,
                    color = if (targetRole == RoomRole.Member) WakooSecondarySelected else Color(0xFFF53F3F),
                )
            }

            HorizontalDivider(
                color = Color(0xFFE5E5E5),
                thickness = 0.5.dp,
            )
        }
        val isFollowed by remember(user.id) {
            derivedStateOf {
                (roomInfoState.requireRoomUser(user) as? UserInfo)?.extra?.isFollowed ?: true // 不是UserInfo就认为关注了
            }
        }

        if (!isFollowed) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .clickable {
                            dismiss()
                            roomInfoState.sendEvent(RoomEvent.FollowUser(user.id))
                        }
                        .padding(16.dp),
                contentAlignment = Alignment.Center,
            ) {
                Text(
                    text = "关注",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Normal,
                    color = WakooSecondarySelected,
                )
            }

            HorizontalDivider(
                color = Color(0xFFE5E5E5),
                thickness = 0.5.dp,
            )
        }

        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .clickable {
                        dismiss()
                        roomInfoState.sendEvent(RoomEvent.SetBlackEvent(user.id, !targetInBlack))
                    }
                    .padding(16.dp),
            contentAlignment = Alignment.Center,
        ) {
            Text(
                text = if (!targetInBlack) "加入黑名单" else "取消拉黑",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Normal,
                color = if (!targetInBlack) WakooSecondarySelected else Color(0xFFF53F3F),
            )
        }

        HorizontalDivider(
            color = Color(0xFFF2F3F5),
            thickness = 8.dp,
        )

        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .clickable {
                        dismiss()
                    }
                    .padding(16.dp),
            contentAlignment = Alignment.Center,
        ) {
            Text(
                text = "取消",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Normal,
                color = WakooSecondarySelected,
            )
        }

        SizeHeight(15.dp)
    }
}
