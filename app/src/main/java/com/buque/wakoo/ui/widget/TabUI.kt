package com.buque.wakoo.ui.widget

import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.PressInteraction
import androidx.compose.foundation.pager.PagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import kotlin.math.abs

@Composable
fun rememberSelectionFractions(pagerState: PagerState) =
    remember(
        pagerState,
        pagerState.pageCount,
    ) {
        derivedStateOf {
            val currentPage = pagerState.currentPage
            val offset = pagerState.currentPageOffsetFraction
            val targetPage = pagerState.targetPage

            // 一次性计算出所有 Tab 的选中进度并存入 Map
            (0 until pagerState.pageCount).associateWith { index ->
                when (index) {
                    currentPage -> 1f - abs(offset)
                    targetPage -> abs(offset)
                    else -> 0f
                }
            }
        }
    }

// 自定义一个不产生任何视觉反馈的 InteractionSource
class NoIndicationInteractionSource : MutableInteractionSource {
    private val interactionSource = MutableInteractionSource()

    override val interactions = interactionSource.interactions

    override suspend fun emit(interaction: androidx.compose.foundation.interaction.Interaction) {
        // 不做任何处理，不发出任何会引起视觉反馈的 Interaction
        // 例如，我们可以过滤掉 PressInteraction 和 HoverInteraction
        if (interaction is PressInteraction.Press || interaction is PressInteraction.Release || interaction is PressInteraction.Cancel) {
            // 不处理 PressInteraction，阻止涟漪效果
        } else {
            interactionSource.emit(interaction) // 转发其他类型的交互，如果需要的话
        }
    }

    override fun tryEmit(interaction: androidx.compose.foundation.interaction.Interaction): Boolean {
        if (interaction is PressInteraction.Press || interaction is PressInteraction.Release || interaction is PressInteraction.Cancel) {
            return true // 假装处理了，但实际没做
        }
        return interactionSource.tryEmit(interaction)
    }
}


