package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Retry: ImageVector
    get() {
        val current = _Retry
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.Retry",
                defaultWidth = 28.0.dp,
                defaultHeight = 28.0.dp,
                viewportWidth = 28.0f,
                viewportHeight = 28.0f,
            ).apply {
                // M2.33 14 a11.67 11.67 0 0 1 21 -7 V4.08 h2.34 v7 h-7 V8.75 h3.05 A9.32 9.32 0 1 0 23.33 14 h2.34 a11.67 11.67 0 0 1 -23.34 0
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 2.33 14
                    moveTo(x = 2.33f, y = 14.0f)
                    // a 11.67 11.67 0 0 1 21 -7
                    arcToRelative(
                        a = 11.67f,
                        b = 11.67f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 21.0f,
                        dy1 = -7.0f,
                    )
                    // V 4.08
                    verticalLineTo(y = 4.08f)
                    // h 2.34
                    horizontalLineToRelative(dx = 2.34f)
                    // v 7
                    verticalLineToRelative(dy = 7.0f)
                    // h -7
                    horizontalLineToRelative(dx = -7.0f)
                    // V 8.75
                    verticalLineTo(y = 8.75f)
                    // h 3.05
                    horizontalLineToRelative(dx = 3.05f)
                    // A 9.32 9.32 0 1 0 23.33 14
                    arcTo(
                        horizontalEllipseRadius = 9.32f,
                        verticalEllipseRadius = 9.32f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        x1 = 23.33f,
                        y1 = 14.0f,
                    )
                    // h 2.34
                    horizontalLineToRelative(dx = 2.34f)
                    // a 11.67 11.67 0 0 1 -23.34 0
                    arcToRelative(
                        a = 11.67f,
                        b = 11.67f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -23.34f,
                        dy1 = 0.0f,
                    )
                }
            }.build()
            .also { _Retry = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Retry,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((28.0).dp)
                        .height((28.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _Retry: ImageVector? = null
