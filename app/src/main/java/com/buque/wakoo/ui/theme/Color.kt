package com.buque.wakoo.ui.theme

import androidx.compose.ui.graphics.Color

// Wakoo 核心品牌色
val WakooGreen = Color(0xFF66FE6B) // 主按钮、高亮、图标等的鲜绿色
val WakooBlack = Color(0xFF000000) // 主要文字、图标颜色
val WakooWhite = Color(0xFFFFFFFF) // 主要背景色

// 辅助色
val WakooText = Color(0xFF111111) // 主要文字
val WakooGrayText = Color(0xFF999999) // 次要文字，如时间戳、提示信息
val WakooSecondaryText = Color(0xFFB6B6B6) // 次要文字，如时间戳、提示信息
val WakooLightGrayBg = Color(0xFFE9EAEF) // 输入框、部分模块的浅灰色背景
val WakooDarkGray = Color(0xFF333333) // 用于一些深色元素或文字
val WakooRed = Color(0xFFFF4D4D) // 用于删除、错误等警告状态
val WakooSelected = Color(0xFF000000) // 主要tab
val WakooUnSelected = Color(0xFFC8CCD7) // 主要tab
val WakooSecondarySelected = Color(0xFF111111) // 次要tab
val WakooSecondaryUnSelected = Color(0xFF999999) // 次要tab

// 渐变色 (设计稿中多处使用)
val GradientGreenStart = Color(0xFFD4FFEC)
val GradientBlueEnd = Color(0xFFC7EEFF)
