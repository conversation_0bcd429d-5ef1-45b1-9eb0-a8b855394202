package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.RoomSettings: ImageVector
    get() {
        val current = _roomSettings
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.RoomSettings",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    moveTo(19.0f, 21.0001f)
                    horizontalLineTo(5.0f)
                    curveTo(4.4477f, 21.0001f, 4.0f, 20.5524f, 4.0f, 20.0001f)
                    verticalLineTo(11.0001f)
                    horizontalLineTo(1.0f)
                    lineTo(11.3273f, 1.6116f)
                    curveTo(11.7087f, 1.2649f, 12.2913f, 1.2649f, 12.6727f, 1.6116f)
                    lineTo(23.0f, 11.0001f)
                    horizontalLineTo(20.0f)
                    verticalLineTo(20.0001f)
                    curveTo(20.0f, 20.5524f, 19.5523f, 21.0001f, 19.0f, 21.0001f)
                    close()
                    moveTo(6.0f, 19.0001f)
                    horizontalLineTo(18.0f)
                    verticalLineTo(9.1576f)
                    lineTo(12.0f, 3.703f)
                    lineTo(6.0f, 9.1576f)
                    verticalLineTo(19.0001f)
                    close()
                    moveTo(8.5912f, 13.809f)
                    curveTo(8.5294f, 13.5487f, 8.4967f, 13.2771f, 8.4967f, 12.9979f)
                    curveTo(8.4967f, 12.7187f, 8.5294f, 12.4472f, 8.5912f, 12.1869f)
                    lineTo(7.6000f, 11.6146f)
                    lineTo(8.5995f, 9.8835f)
                    lineTo(9.5914f, 10.4561f)
                    curveTo(9.9843f, 10.0844f, 10.4633f, 9.8029f, 10.9954f, 9.6445f)
                    verticalLineTo(8.5001f)
                    horizontalLineTo(12.9945f)
                    verticalLineTo(9.6445f)
                    curveTo(13.5266f, 9.8029f, 14.0056f, 10.0844f, 14.3985f, 10.456f)
                    lineTo(15.3904f, 9.8834f)
                    lineTo(16.39f, 11.6145f)
                    lineTo(15.3987f, 12.1868f)
                    curveTo(15.4605f, 12.4471f, 15.4932f, 12.7187f, 15.4932f, 12.9979f)
                    curveTo(15.4932f, 13.2771f, 15.4605f, 13.5486f, 15.3988f, 13.8089f)
                    lineTo(16.39f, 14.3811f)
                    lineTo(15.3905f, 16.1123f)
                    lineTo(14.3986f, 15.5397f)
                    curveTo(14.0057f, 15.9113f, 13.5267f, 16.1929f, 12.9946f, 16.3513f)
                    verticalLineTo(17.4957f)
                    horizontalLineTo(10.9955f)
                    verticalLineTo(16.3513f)
                    curveTo(10.4634f, 16.193f, 9.9844f, 15.9115f, 9.5914f, 15.5398f)
                    lineTo(8.5996f, 16.1124f)
                    lineTo(7.6f, 14.3812f)
                    lineTo(8.5912f, 13.809f)
                    close()
                    moveTo(11.995f, 14.4972f)
                    curveTo(12.823f, 14.4972f, 13.4942f, 13.8259f, 13.4942f, 12.9979f)
                    curveTo(13.4942f, 12.1699f, 12.823f, 11.4986f, 11.995f, 11.4986f)
                    curveTo(11.1669f, 11.4986f, 10.4957f, 12.1699f, 10.4957f, 12.9979f)
                    curveTo(10.4957f, 13.8259f, 11.1669f, 14.4972f, 11.995f, 14.4972f)
                    close()
                }
            }.build()
            .also { _roomSettings = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.RoomSettings,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _roomSettings: ImageVector? = null
