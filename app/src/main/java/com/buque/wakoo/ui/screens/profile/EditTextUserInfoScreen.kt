package com.buque.wakoo.ui.screens.profile

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.InputEdit
import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.ext.typeRootPopInvoker
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults
import com.buque.wakoo.viewmodel.EditUserInfoViewModel
import kotlinx.coroutines.launch

/**
 * 修改昵称页面
 * 用户可以修改自己的昵称
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditTextUserInfoScreen(edit: InputEdit) {
    var content by rememberSaveable { mutableStateOf(edit.originText) }

    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current
    val rootPopInvoker = typeRootPopInvoker<Route.EditTextUserInfo>()

    val viewModel = viewModel<EditUserInfoViewModel>()
    val scope = rememberCoroutineScope()

    fun onSave() {
        focusManager.clearFocus()
        if (content == edit.originText) {
            rootPopInvoker()
        } else {
            scope.launch {
                viewModel.updateNickName(content)
                rootPopInvoker()
            }
        }
    }

    SegColorTitleScreenScaffold(
        title = edit.title,
        actions = {
            WakooTitleBarDefaults.TextButtonAction(
                text = "保存",
                onClick = ::onSave,
                enabled = content.isNotEmpty(),
            )
        },
    ) { paddingValues ->
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(horizontal = 16.dp),
        ) {
            SizeHeight(24.dp)

            // 提示文字
            if (edit.inputTip != null) {
                Text(
                    text = edit.inputTip,
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF86909C),
                )
                SizeHeight(16.dp)
            }

            AppTextField(
                value = content,
                onValueChange = {
                    content = it
                },
                modifier =
                    Modifier
                        .focusRequester(focusRequester)
                        .fillMaxWidth()
                        .height(68.dp),
                maxLength = 9,
                showLengthTip = false,
                keyboardOptions =
                    KeyboardOptions(
                        keyboardType = InputEdit.toKeyboardType(edit),
                        imeAction = ImeAction.Done,
                    ),
                keyboardActions =
                    KeyboardActions(
                        onDone = {
                            onSave()
                        },
                    ),
            )

            LaunchedEffect(Unit) {
                focusRequester.requestFocus() // 请求焦点
                keyboardController?.show() // 请求显示键盘
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun EditNicknamePagePreview() {
    WakooTheme {
        EditTextUserInfoScreen(InputEdit.createNickNameEdit(UserInfo.previewBoy.name))
    }
}
