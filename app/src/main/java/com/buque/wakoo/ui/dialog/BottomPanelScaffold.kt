package com.buque.wakoo.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.icons.ArrowLeft
import com.buque.wakoo.ui.icons.Close
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults

@Composable
fun DialogScope.BottomPanelScaffold(
    title: String,
    useClose: Boolean,
    modifier: Modifier = Modifier,
    backgroundColor: Color = Color(0xFFF7F7F7),
    contentPadding: Dp = 16.dp,
    bottomPadding: Dp = 36.dp,
    onBack: () -> Unit = {},
    content: @Composable ColumnScope.() -> Unit,
) {
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .background(
                    color = backgroundColor,
                    shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp),
                ).navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(48.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            WakooTitleBarDefaults.IconButtonAction(
                imageVector = WakooIcons.ArrowLeft,
                onClick = {
                    onBack()
                    dismiss()
                },
                modifier = Modifier.alpha(if (useClose) 0f else 1f),
                enabled = !useClose,
            )

            Text(
                text = title,
                modifier =
                    Modifier
                        .padding(horizontal = 16.dp)
                        .weight(1f)
                        .basicMarquee(),
                style = MaterialTheme.typography.titleSmall,
                textAlign = TextAlign.Center,
                maxLines = 1,
            )

            WakooTitleBarDefaults.IconButtonAction(
                imageVector = WakooIcons.Close,
                onClick = {
                    dismiss()
                },
                modifier = Modifier.alpha(if (useClose) 1f else 0f),
                enabled = useClose,
            )
        }

        Column(
            modifier =
                Modifier
                    .padding(horizontal = contentPadding)
                    .fillMaxWidth(),
        ) {
            SizeHeight(16.dp)

            content()
        }

        SizeHeight(bottomPadding)
    }
}
