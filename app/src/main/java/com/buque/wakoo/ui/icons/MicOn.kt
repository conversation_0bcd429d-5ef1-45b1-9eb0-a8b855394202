package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.MicOn: ImageVector
    get() {
        val current = _MicOn
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.MicOn",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // M12 3 a3 3 0 0 0 -3 3 v4 a3 3 0 1 0 6 0 V6 a3 3 0 0 0 -3 -3 m0 -2 a5 5 0 0 1 5 5 v4 a5 5 0 0 1 -10 0 V6 a5 5 0 0 1 5 -5 M3.05 11 h2.02 a7 7 0 0 0 13.86 0 h2.01 A9 9 0 0 1 13 18.95 V23 h-2 v-4.05 A9 9 0 0 1 3.05 11
                path(
                    fill = SolidColor(Color(0xFFFFFFFF)),
                ) {
                    // M 12 3
                    moveTo(x = 12.0f, y = 3.0f)
                    // a 3 3 0 0 0 -3 3
                    arcToRelative(
                        a = 3.0f,
                        b = 3.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -3.0f,
                        dy1 = 3.0f,
                    )
                    // v 4
                    verticalLineToRelative(dy = 4.0f)
                    // a 3 3 0 1 0 6 0
                    arcToRelative(
                        a = 3.0f,
                        b = 3.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        dx1 = 6.0f,
                        dy1 = 0.0f,
                    )
                    // V 6
                    verticalLineTo(y = 6.0f)
                    // a 3 3 0 0 0 -3 -3
                    arcToRelative(
                        a = 3.0f,
                        b = 3.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -3.0f,
                        dy1 = -3.0f,
                    )
                    // m 0 -2
                    moveToRelative(dx = 0.0f, dy = -2.0f)
                    // a 5 5 0 0 1 5 5
                    arcToRelative(
                        a = 5.0f,
                        b = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 5.0f,
                        dy1 = 5.0f,
                    )
                    // v 4
                    verticalLineToRelative(dy = 4.0f)
                    // a 5 5 0 0 1 -10 0
                    arcToRelative(
                        a = 5.0f,
                        b = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -10.0f,
                        dy1 = 0.0f,
                    )
                    // V 6
                    verticalLineTo(y = 6.0f)
                    // a 5 5 0 0 1 5 -5
                    arcToRelative(
                        a = 5.0f,
                        b = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 5.0f,
                        dy1 = -5.0f,
                    )
                    // M 3.05 11
                    moveTo(x = 3.05f, y = 11.0f)
                    // h 2.02
                    horizontalLineToRelative(dx = 2.02f)
                    // a 7 7 0 0 0 13.86 0
                    arcToRelative(
                        a = 7.0f,
                        b = 7.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 13.86f,
                        dy1 = 0.0f,
                    )
                    // h 2.01
                    horizontalLineToRelative(dx = 2.01f)
                    // A 9 9 0 0 1 13 18.95
                    arcTo(
                        horizontalEllipseRadius = 9.0f,
                        verticalEllipseRadius = 9.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 13.0f,
                        y1 = 18.95f,
                    )
                    // V 23
                    verticalLineTo(y = 23.0f)
                    // h -2
                    horizontalLineToRelative(dx = -2.0f)
                    // v -4.05
                    verticalLineToRelative(dy = -4.05f)
                    // A 9 9 0 0 1 3.05 11
                    arcTo(
                        horizontalEllipseRadius = 9.0f,
                        verticalEllipseRadius = 9.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 3.05f,
                        y1 = 11.0f,
                    )
                }
            }.build()
            .also { _MicOn = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.MicOn,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _MicOn: ImageVector? = null
