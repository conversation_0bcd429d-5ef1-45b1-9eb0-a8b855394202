package com.buque.wakoo.ui.widget

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.DrawModifier
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.ContentDrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.withTransform
import androidx.compose.ui.graphics.vector.PathParser
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

// --- 为了性能，只解析一次引号的 SVG 路径数据 ---
private val topQuotePath: Path by lazy {
    // 这段路径数据已被手动平移，使其左上角位于 (0,0)
    PathParser()
        .parsePathString(
            "M15.328,9.664 H10.048 C9.856,9.664 9.685,9.600 9.536,9.472 C9.408,9.323 9.344,9.184 9.344,9.056 C9.344,8.907 9.355,8.800 9.376,8.736 L12.416,1.024 C12.672,0.341 13.163,0.000 13.888,0.000 H17.344 C17.536,0.000 17.696,0.075 17.824,0.224 C17.973,0.352 18.048,0.512 18.048,0.704 V0.768 L16.864,8.416 C16.821,8.736 16.651,9.024 16.352,9.280 C16.075,9.536 15.733,9.664 15.328,9.664 Z M6.0,9.664 H0.72 C0.528,9.664 0.357,9.600 0.208,9.472 C0.08,9.323 0.016,9.184 0.016,9.056 C0.016,8.907 0.027,8.800 0.048,8.736 L3.088,1.024 C3.344,0.341 3.835,0.000 4.56,0.000 H8.016 C8.208,0.000 8.368,0.075 8.496,0.224 C8.645,0.352 8.72,0.512 8.72,0.704 V0.768 L6.536,8.416 C6.493,8.736 6.323,9.024 6.024,9.280 C5.747,9.536 5.405,9.664 6.0,9.664 Z",
        ).toPath()
}
private val bottomQuotePath: Path by lazy {
    // 这段路径数据也已被手动平移，使其左上角位于 (0,0)
    PathParser()
        .parsePathString(
            "M2.72,0.000 H8.00 C8.192,0.000 8.363,0.064 8.512,0.192 C8.640,0.341 8.704,0.480 8.704,0.608 C8.704,0.757 8.693,0.864 8.672,0.928 L5.632,8.640 C5.376,9.323 4.885,9.664 4.160,9.664 H0.704 C0.512,9.664 0.352,9.589 0.224,9.440 C0.075,9.312 0.000,9.152 0.000,8.960 V8.896 L1.184,1.248 C1.227,0.928 1.397,0.640 1.696,0.384 C1.973,0.128 2.315,0.000 2.72,0.000 Z M12.064,0.000 H17.344 C17.536,0.000 17.696,0.064 17.824,0.192 C17.973,0.341 18.048,0.480 18.048,0.608 C18.048,0.757 18.037,0.864 18.016,0.928 L14.976,8.640 C14.720,9.323 14.229,9.664 13.504,9.664 H10.048 C9.856,9.664 9.696,9.589 9.568,9.440 C9.419,9.312 9.444,9.152 9.444,8.960 V8.896 L10.628,1.248 C10.671,0.928 10.841,0.640 11.140,0.384 C11.417,0.128 11.759,0.000 12.064,0.000 Z",
        ).toPath()
}

/**
 * 一个高度可配置的 DrawModifier，用于绘制带“切口”和引号的可拉伸圆角边框。
 */
private class QuoteFrameModifier(
    private val color: Color,
    private val strokeWidth: Dp,
    private val cornerRadius: Dp,
    private val quoteWidth: Dp,
    private val topQuoteHorizontalOffset: Dp,
    private val bottomQuoteHorizontalOffset: Dp,
    private val quoteHorizontalPadding: Dp,
) : DrawModifier {
    override fun ContentDrawScope.draw() {
        // 先绘制原始内容 (如背景色, 文字等)
        drawContent()

        // --- 1. 准备绘制参数 (将 Dp 转换为 Px) ---
        val quoteHorizontalPaddingPx = quoteHorizontalPadding.toPx()
        val strokePx = strokeWidth.toPx()
        val radiusPx = cornerRadius.toPx()
        val (width, height) = size

        // --- 2. 计算引号和切口的大小与位置 ---
        val topQuoteBounds = topQuotePath.getBounds()
        val scale = quoteWidth.toPx() / topQuoteBounds.width
        val scaledQuoteWidth = topQuoteBounds.width * scale

        val topCutoutStartX = radiusPx + topQuoteHorizontalOffset.toPx()
        val topCutoutTotalWidth = scaledQuoteWidth

        val bottomCutoutTotalWidth = topCutoutTotalWidth // 保持上下切口大小一致
        val bottomCutoutStartX =
            width - radiusPx - bottomQuoteHorizontalOffset.toPx() - bottomCutoutTotalWidth - quoteHorizontalPaddingPx * 2

        // --- 3. 构建带切口的边框路径 ---
        val borderPath =
            Path().apply {
                // 从左上角切口的右边开始
                moveTo(
                    topCutoutStartX + topCutoutTotalWidth + quoteHorizontalPaddingPx * 2,
                    strokePx / 2f,
                )

                // 上边线到右上角
                lineTo(width - radiusPx, strokePx / 2)
                // 右上圆弧 (修正了Rect定义，确保平滑)
                arcTo(
                    Rect(
                        width - 2 * radiusPx - strokePx / 2,
                        strokePx / 2,
                        width - strokePx / 2,
                        2 * radiusPx + strokePx / 2,
                    ),
                    270f,
                    90f,
                    false,
                )

                // 右边线
                lineTo(width - strokePx / 2, height - radiusPx)
                // 右下圆弧
                arcTo(
                    Rect(
                        width - 2 * radiusPx - strokePx / 2,
                        height - 2 * radiusPx - strokePx / 2,
                        width - strokePx / 2,
                        height - strokePx / 2,
                    ),
                    0f,
                    90f,
                    false,
                )

                // 下边线到右下角切口
                lineTo(
                    bottomCutoutStartX + bottomCutoutTotalWidth + quoteHorizontalPaddingPx * 2,
                    height - strokePx / 2,
                )

                // 跳过右下角切口，从切口左边继续
                moveTo(bottomCutoutStartX, height - strokePx / 2f)

                // 下边线到左下角
                lineTo(radiusPx, height - strokePx / 2)
                // 左下圆弧
                arcTo(
                    Rect(
                        strokePx / 2,
                        height - 2 * radiusPx - strokePx / 2,
                        2 * radiusPx + strokePx / 2,
                        height - strokePx / 2,
                    ),
                    90f,
                    90f,
                    false,
                )

                // 左边线
                lineTo(strokePx / 2, radiusPx)
                // 左上圆弧
                arcTo(
                    Rect(
                        strokePx / 2,
                        strokePx / 2,
                        2 * radiusPx + strokePx / 2,
                        2 * radiusPx + strokePx / 2,
                    ),
                    180f,
                    90f,
                    false,
                )

                // 上边线到左上角切口
                lineTo(topCutoutStartX, strokePx / 2)
            }

        // --- 4. 绘制路径 ---
        drawPath(borderPath, color, style = Stroke(width = strokePx))

        // --- 5. 绘制经过缩放和定位的引号 ---
        drawQuotes(
            width,
            height,
            strokePx,
            scale,
            topCutoutStartX + quoteHorizontalPaddingPx,
            bottomCutoutStartX + quoteHorizontalPaddingPx,
        )
    }

    private fun ContentDrawScope.drawQuotes(
        width: Float,
        height: Float,
        strokePx: Float,
        scale: Float,
        topCutoutStartX: Float,
        bottomCutoutStartX: Float,
    ) {
        val topQuoteBounds = topQuotePath.getBounds()
        val scaledQuoteHeight = topQuoteBounds.height * scale

        // 绘制上引号
        withTransform({
            translate(
                left = topCutoutStartX,
                top = (strokePx / 2) - (scaledQuoteHeight / 2), // 垂直居中于上边框
            )
            scale(scale, scale, pivot = topQuoteBounds.topLeft)
        }) {
            drawPath(path = topQuotePath, color = color)
        }

        // 绘制下引号
        withTransform({
            translate(
                left = bottomCutoutStartX,
                top = height - (strokePx / 2) - (scaledQuoteHeight / 2), // 垂直居中于下边框
            )
            scale(scale, scale, pivot = topQuoteBounds.topLeft) // 使用相同的缩放和锚点
            translate(-bottomQuotePath.getBounds().left, -bottomQuotePath.getBounds().top)
        }) {
            drawPath(path = bottomQuotePath, color = color)
        }
    }
}

/**
 * 将复杂的“切口”边框（包含引号和圆角）应用到任何Composable上的Modifier。
 *
 * @param color 边框和引号的颜色。
 * @param strokeWidth 边框的粗细。
 * @param cornerRadius 边框的圆角半径。
 * @param quoteWidth 引号的宽度，高度将按比例缩放。
 * @param topQuoteHorizontalOffset 左上角引号所在的“切口”距离组件左边缘的水平距离。
 * @param bottomQuoteHorizontalOffset 右下角引号所在的“切口”距离组件右边缘的水平距离。
 */
fun Modifier.quoteFrame(
    color: Color = Color.Black,
    strokeWidth: Dp = 2.5.dp, // 匹配设计图的粗细
    cornerRadius: Dp = 22.dp,
    quoteWidth: Dp = 18.dp, // 匹配设计图的大小
    topQuoteHorizontalOffset: Dp = 5.dp, // 匹配设计图的位置
    bottomQuoteHorizontalOffset: Dp = 5.dp,
    quoteHorizontalPadding: Dp = 5.dp,
): Modifier =
    this.then(
        QuoteFrameModifier(
            color,
            strokeWidth,
            cornerRadius,
            quoteWidth,
            topQuoteHorizontalOffset,
            bottomQuoteHorizontalOffset,
            quoteHorizontalPadding,
        ),
    )

/**
 * 用于展示最终效果的组件
 */
@Composable
private fun QuoteBorderBox(
    text: String,
    modifier: Modifier = Modifier,
    // 将所有配置参数暴露出来，方便预览和使用
    frameColor: Color = Color.Black,
    frameStrokeWidth: Dp = 2.5.dp,
    cornerRadius: Dp = 22.dp,
    quoteWidth: Dp = 18.dp,
    topLeftQuoteOffset: Dp = 5.dp,
    bottomRightQuoteOffset: Dp = 5.dp,
    quoteHorizontalPadding: Dp = 5.dp,
) {
    Box(
        modifier =
            modifier
                .fillMaxWidth()
                .quoteFrame(
                    color = frameColor,
                    strokeWidth = frameStrokeWidth,
                    cornerRadius = cornerRadius,
                    quoteWidth = quoteWidth,
                    topQuoteHorizontalOffset = topLeftQuoteOffset,
                    bottomQuoteHorizontalOffset = bottomRightQuoteOffset,
                    quoteHorizontalPadding = quoteHorizontalPadding,
                ).padding(horizontal = 35.dp, vertical = 35.dp),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = text,
            color = Color.DarkGray,
            fontSize = 18.sp,
            lineHeight = 30.sp,
        )
    }
}

/**
 * 预览函数，100% 还原设计稿并展示其强大的配置能力
 */
@Preview(showBackground = true, backgroundColor = 0xFFF0F4F8)
@Composable
private fun FinalQuoteCardPreview() {
    val shortText = "有時話不多，是因為想得太多。\n喜歡安靜的對話，也期待有人能讀懂沉默裡的內心。"
    val longText =
        "有時話不多，是因為想得太多。\n喜歡安靜的對話，也期待有人能讀懂沉默裡的內心。有時話不多，是因為想得太多。喜歡安ंधी的對話，也期待有人能讀懂沉默裡的內心。"

    Column(
        modifier = Modifier.padding(24.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp),
    ) {
        // --- 默认样式 (已微调参数以匹配设计图) ---
        QuoteBorderBox(text = shortText)

        // --- 完全自定义样式，展示配置能力 ---
        QuoteBorderBox(
            text = longText,
            frameColor = Color(0xFF4A148C), // 深紫色
            frameStrokeWidth = 4.dp, // 更粗的边框
            quoteWidth = 40.dp, // 更大的引号
            topLeftQuoteOffset = 20.dp, // 引号更靠里
            bottomRightQuoteOffset = 20.dp,
        )
    }
}
