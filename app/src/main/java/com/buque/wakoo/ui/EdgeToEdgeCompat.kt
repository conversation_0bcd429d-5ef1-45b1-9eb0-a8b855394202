package com.buque.wakoo.ui

import android.graphics.Color
import android.os.Build
import androidx.activity.ComponentActivity
import androidx.activity.SystemBarStyle
import androidx.activity.enableEdgeToEdge

private val DefaultLightScrim = Color.argb(0xe6, 0xFF, 0xFF, 0xFF)

private val DefaultDarkScrim = Color.argb(0x80, 0x1b, 0x1b, 0x1b)

fun ComponentActivity.setEdgeToEdgeConfig() {
    enableEdgeToEdge(
        statusBarStyle =
            SystemBarStyle.auto(Color.TRANSPARENT, Color.TRANSPARENT) {
                false
            },
        navigationBarStyle =
            SystemBarStyle.auto(DefaultLightScrim, DefaultDarkScrim) {
                false
            },
    )
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        // Force the 3-button navigation bar to be transparent
        // See: https://developer.android.com/develop/ui/views/layout/edge-to-edge#create-transparent
        window.isNavigationBarContrastEnforced = false
    }
}
