package com.buque.wakoo.ui.widget

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.InfiniteRepeatableSpec
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

// 动画状态枚举，与之前相同
enum class RotationState {
    STOPPED, // 停止，回到原点
    PAUSED, // 暂停，保留当前位置
    PLAYING, // 播放中
}

/**
 * 一个可旋转的组件包装器。
 *
 * @param rotationState 当前动画的状态，由外部控制。
 * @param onRotationStateChange 当内部需要改变状态时（如暂停或停止时），向外部回调新状态。
 * @param animationDurationMillis 单次360度旋转的持续时间（毫秒）。
 * @param content 要旋转的 Composable 内容。
 */
@Composable
inline fun Rotatable(
    rotationState: RotationState,
    onRotationStateChange: (RotationState) -> Unit = {},
    animationDurationMillis: Int = 2000,
    content: @Composable () -> Unit,
) {
    val rotationAngle = remember { Animatable(0f) }
    var pausedAngle by remember { mutableFloatStateOf(0f) }

    LaunchedEffect(rotationState) {
        when (rotationState) {
            RotationState.PLAYING -> {
                launch {
                    rotationAngle.animateTo(
                        targetValue = rotationAngle.value + 360f,
                        animationSpec =
                            InfiniteRepeatableSpec(
                                animation = tween(durationMillis = animationDurationMillis, easing = LinearEasing),
                                repeatMode = RepeatMode.Restart,
                            ),
                    )
                }
            }

            RotationState.PAUSED -> {
                this.cancel()
                pausedAngle = rotationAngle.value % 360
            }

            RotationState.STOPPED -> {
                this.cancel()
                rotationAngle.snapTo(0f)
                pausedAngle = 0f
            }
        }
    }

    // 当从 PAUSED 状态切换到 PLAYING 时，需要先snap到暂停时的角度
    LaunchedEffect(rotationState) {
        if (rotationState == RotationState.PLAYING && rotationAngle.value % 360 != pausedAngle) {
            // 只有当当前角度不等于暂停角度时才snap，避免不必要的瞬间跳动
            rotationAngle.snapTo(pausedAngle)
        }
    }

    Box(
        modifier =
            Modifier.graphicsLayer {
                rotationZ = rotationAngle.value
            },
    ) {
        content() // 渲染传入的内容
    }
}
