package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.More: ImageVector
    get() {
        val current = _more
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.More",
                defaultWidth = 20.0.dp,
                defaultHeight = 20.0.dp,
                viewportWidth = 20.0f,
                viewportHeight = 20.0f,
            ).apply {
                // M3.75 8.75 A1.25 1.25 0 0 0 2.5 10 a1.25 1.25 0 0 0 1.25 1.25 A1.25 1.25 0 0 0 5 10 a1.25 1.25 0 0 0 -1.25 -1.25 m12.5 0 A1.25 1.25 0 0 0 15 10 a1.25 1.25 0 0 0 1.25 1.25 A1.25 1.25 0 0 0 17.5 10 a1.25 1.25 0 0 0 -1.25 -1.25 m-6.25 0 A1.25 1.25 0 0 0 8.75 10 1.25 1.25 0 0 0 10 11.25 1.25 1.25 0 0 0 11.25 10 1.25 1.25 0 0 0 10 8.75
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 3.75 8.75
                    moveTo(x = 3.75f, y = 8.75f)
                    // A 1.25 1.25 0 0 0 2.5 10
                    arcTo(
                        horizontalEllipseRadius = 1.25f,
                        verticalEllipseRadius = 1.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        x1 = 2.5f,
                        y1 = 10.0f,
                    )
                    // a 1.25 1.25 0 0 0 1.25 1.25
                    arcToRelative(
                        a = 1.25f,
                        b = 1.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 1.25f,
                        dy1 = 1.25f,
                    )
                    // A 1.25 1.25 0 0 0 5 10
                    arcTo(
                        horizontalEllipseRadius = 1.25f,
                        verticalEllipseRadius = 1.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        x1 = 5.0f,
                        y1 = 10.0f,
                    )
                    // a 1.25 1.25 0 0 0 -1.25 -1.25
                    arcToRelative(
                        a = 1.25f,
                        b = 1.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -1.25f,
                        dy1 = -1.25f,
                    )
                    // m 12.5 0
                    moveToRelative(dx = 12.5f, dy = 0.0f)
                    // A 1.25 1.25 0 0 0 15 10
                    arcTo(
                        horizontalEllipseRadius = 1.25f,
                        verticalEllipseRadius = 1.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        x1 = 15.0f,
                        y1 = 10.0f,
                    )
                    // a 1.25 1.25 0 0 0 1.25 1.25
                    arcToRelative(
                        a = 1.25f,
                        b = 1.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 1.25f,
                        dy1 = 1.25f,
                    )
                    // A 1.25 1.25 0 0 0 17.5 10
                    arcTo(
                        horizontalEllipseRadius = 1.25f,
                        verticalEllipseRadius = 1.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        x1 = 17.5f,
                        y1 = 10.0f,
                    )
                    // a 1.25 1.25 0 0 0 -1.25 -1.25
                    arcToRelative(
                        a = 1.25f,
                        b = 1.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -1.25f,
                        dy1 = -1.25f,
                    )
                    // m -6.25 0
                    moveToRelative(dx = -6.25f, dy = 0.0f)
                    // A 1.25 1.25 0 0 0 8.75 10
                    arcTo(
                        horizontalEllipseRadius = 1.25f,
                        verticalEllipseRadius = 1.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        x1 = 8.75f,
                        y1 = 10.0f,
                    )
                    // A 1.25 1.25 0 0 0 10 11.25
                    arcTo(
                        horizontalEllipseRadius = 1.25f,
                        verticalEllipseRadius = 1.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        x1 = 10.0f,
                        y1 = 11.25f,
                    )
                    // A 1.25 1.25 0 0 0 11.25 10
                    arcTo(
                        horizontalEllipseRadius = 1.25f,
                        verticalEllipseRadius = 1.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        x1 = 11.25f,
                        y1 = 10.0f,
                    )
                    // A 1.25 1.25 0 0 0 10 8.75
                    arcTo(
                        horizontalEllipseRadius = 1.25f,
                        verticalEllipseRadius = 1.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        x1 = 10.0f,
                        y1 = 8.75f,
                    )
                }
            }.build()
            .also { _more = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.More,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((20.0).dp)
                        .height((20.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _more: ImageVector? = null
