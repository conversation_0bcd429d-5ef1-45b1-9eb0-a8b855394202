package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.TabIndicator: ImageVector
    get() {
        val current = _tabIndicator
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.TabIndicator",
                defaultWidth = 54.0.dp,
                defaultHeight = 12.0.dp,
                viewportWidth = 54.0f,
                viewportHeight = 12.0f,
            ).apply {
                group(
                    // M 0 0 H 54 V 12 H 0z
                    clipPathData =
                        PathData {
                            // M 0 0
                            moveTo(x = 0.0f, y = 0.0f)
                            // H 54
                            horizontalLineTo(x = 54.0f)
                            // V 12
                            verticalLineTo(y = 12.0f)
                            // H 0z
                            horizontalLineTo(x = 0.0f)
                            close()
                        },
                ) {
                    // M2.05 10.42 C4.69 7.77 6.33 4.27 9.64 2.3 c3.45 -2.05 5.71 .77 8.49 2.4 1.74 1.02 3.32 2.2 5.13 3.11 2.33 1.17 3.62 -.3 5.5 -1.77 1.94 -1.52 2.9 -3.07 5.56 -2.55 2.81 .55 5.2 1.65 7.55 3.28 1.76 1.22 3.52 .08 5.3 -.78 1.16 -.56 1.2 -.56 2.44 -1.44 1.25 -.87 2.3 -2.2 2.3 -2.2
                    path(
                        stroke =
                            Brush.linearGradient(
                                0.0f to Color(0xFF97FF4F),
                                1.0f to Color(0xFF3AFF98),
                                start = Offset(x = 2.05f, y = 6.0f),
                                end = Offset(x = 51.95f, y = 6.0f),
                            ),
                        strokeLineCap = StrokeCap.Round,
                        strokeLineWidth = 3.0f,
                    ) {
                        // M 2.05 10.42
                        moveTo(x = 2.05f, y = 10.42f)
                        // C 4.69 7.77 6.33 4.27 9.64 2.3
                        curveTo(
                            x1 = 4.69f,
                            y1 = 7.77f,
                            x2 = 6.33f,
                            y2 = 4.27f,
                            x3 = 9.64f,
                            y3 = 2.3f,
                        )
                        // c 3.45 -2.05 5.71 0.77 8.49 2.4
                        curveToRelative(
                            dx1 = 3.45f,
                            dy1 = -2.05f,
                            dx2 = 5.71f,
                            dy2 = 0.77f,
                            dx3 = 8.49f,
                            dy3 = 2.4f,
                        )
                        // c 1.74 1.02 3.32 2.2 5.13 3.11
                        curveToRelative(
                            dx1 = 1.74f,
                            dy1 = 1.02f,
                            dx2 = 3.32f,
                            dy2 = 2.2f,
                            dx3 = 5.13f,
                            dy3 = 3.11f,
                        )
                        // c 2.33 1.17 3.62 -0.3 5.5 -1.77
                        curveToRelative(
                            dx1 = 2.33f,
                            dy1 = 1.17f,
                            dx2 = 3.62f,
                            dy2 = -0.3f,
                            dx3 = 5.5f,
                            dy3 = -1.77f,
                        )
                        // c 1.94 -1.52 2.9 -3.07 5.56 -2.55
                        curveToRelative(
                            dx1 = 1.94f,
                            dy1 = -1.52f,
                            dx2 = 2.9f,
                            dy2 = -3.07f,
                            dx3 = 5.56f,
                            dy3 = -2.55f,
                        )
                        // c 2.81 0.55 5.2 1.65 7.55 3.28
                        curveToRelative(
                            dx1 = 2.81f,
                            dy1 = 0.55f,
                            dx2 = 5.2f,
                            dy2 = 1.65f,
                            dx3 = 7.55f,
                            dy3 = 3.28f,
                        )
                        // c 1.76 1.22 3.52 0.08 5.3 -0.78
                        curveToRelative(
                            dx1 = 1.76f,
                            dy1 = 1.22f,
                            dx2 = 3.52f,
                            dy2 = 0.08f,
                            dx3 = 5.3f,
                            dy3 = -0.78f,
                        )
                        // c 1.16 -0.56 1.2 -0.56 2.44 -1.44
                        curveToRelative(
                            dx1 = 1.16f,
                            dy1 = -0.56f,
                            dx2 = 1.2f,
                            dy2 = -0.56f,
                            dx3 = 2.44f,
                            dy3 = -1.44f,
                        )
                        // c 1.25 -0.87 2.3 -2.2 2.3 -2.2
                        curveToRelative(
                            dx1 = 1.25f,
                            dy1 = -0.87f,
                            dx2 = 2.3f,
                            dy2 = -2.2f,
                            dx3 = 2.3f,
                            dy3 = -2.2f,
                        )
                    }
                }
            }.build()
            .also { _tabIndicator = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.TabIndicator,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((54.0).dp)
                        .height((12.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _tabIndicator: ImageVector? = null
