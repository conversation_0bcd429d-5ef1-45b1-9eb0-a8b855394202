package com.buque.wakoo.ui.screens.messages.notification

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.ui.theme.WakooBlack
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooRed
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.Weight

/**
 * 通知/消息页面
 */
@Composable
fun NotificationPage(onBackClick: () -> Unit = {}) {
    // 当前选中的标签
    var selectedTab by remember { mutableStateOf(NotificationTab.DIRECT_MESSAGE) }

    val messages =
        remember {
            listOf(
                NotificationModel(
                    id = "1",
                    avatar = R.drawable.ic_app_logo,
                    name = "Wakoo官方通知",
                    message = "我在这里等了好久，你终于出现啦啦啦啦啦啦啦啦啦...",
                    time = "1分钟前",
                    unreadCount = 99,
                    isOfficial = true,
                ),
                NotificationModel(
                    id = "2",
                    avatar = R.drawable.ic_app_logo,
                    name = "幼儿园搬花",
                    message = "我在这里等了好久，你终于出现啦！",
                    time = "1分钟前",
                    unreadCount = 2,
                ),
                NotificationModel(
                    id = "3",
                    avatar = R.drawable.ic_app_logo,
                    name = "幼儿园搬花",
                    message = "我在这里等了好久，你终于出现啦！",
                    time = "1分钟前",
                ),
                NotificationModel(
                    id = "4",
                    avatar = R.drawable.ic_app_logo,
                    name = "幼儿园搬花",
                    message = "我在这里等了好久，你终于出现啦！",
                    time = "1分钟前",
                ),
                NotificationModel(
                    id = "5",
                    avatar = R.drawable.ic_app_logo,
                    name = "幼儿园搬花",
                    message = "我在这里等了好久，你终于出现啦！",
                    time = "1分钟前",
                ),
            )
        }

    val notifications =
        remember {
            listOf(
                NotificationModel(
                    id = "6",
                    avatar = R.drawable.ic_app_logo,
                    name = "Wakoo官方通知",
                    message = "系统通知：您的账号已激活！",
                    time = "1分钟前",
                    isOfficial = true,
                ),
                NotificationModel(
                    id = "7",
                    avatar = R.drawable.ic_app_logo,
                    name = "幼儿园搬花",
                    message = "关注了你",
                    time = "10分钟前",
                ),
                NotificationModel(
                    id = "8",
                    avatar = R.drawable.ic_app_logo,
                    name = "幼儿园搬花",
                    message = "喜欢了你的作品",
                    time = "1小时前",
                ),
            )
        }

    Scaffold(
        topBar = {
            Column {
                // 顶部导航栏
                Row(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(44.dp)
                            .background(Color.White)
                            .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
//                    // 返回按钮
//                    Image(
//                        imageVector = WakooIcons.Back,
//                        contentDescription = "Back",
//                        modifier = Modifier
//                            .size(24.dp)
//                            .clickable { onBackClick() }
//                    )

                    Weight()
                }

                // 标签栏
                Row(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(44.dp)
                            .background(Color.White)
                            .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Weight()

                    // 私信标签
                    TabItem(
                        title = "私信",
                        isSelected = selectedTab == NotificationTab.DIRECT_MESSAGE,
                        badgeCount = if (selectedTab != NotificationTab.DIRECT_MESSAGE) 2 else null,
                        onClick = { selectedTab = NotificationTab.DIRECT_MESSAGE },
                    )

                    SizeWidth(32.dp)

                    // 通知标签
                    TabItem(
                        title = "通知",
                        isSelected = selectedTab == NotificationTab.NOTIFICATION,
                        badgeCount = if (selectedTab != NotificationTab.NOTIFICATION) 99 else null,
                        onClick = { selectedTab = NotificationTab.NOTIFICATION },
                    )

                    Weight()
                }

                HorizontalDivider(
                    color = Color(0xFFEEEEEE),
                    thickness = 1.dp,
                )
            }
        },
    ) { paddingValues ->
        // 消息列表
        LazyColumn(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .background(Color.White),
        ) {
            val dataList =
                if (selectedTab == NotificationTab.DIRECT_MESSAGE) messages else notifications

            items(dataList) { item ->
                NotificationListItem(
                    avatar = item.avatar,
                    name = item.name,
                    message = item.message,
                    time = item.time,
                    unreadCount = item.unreadCount,
                    isOfficial = item.isOfficial,
                )

                HorizontalDivider(
                    modifier = Modifier.padding(horizontal = 16.dp),
                    color = Color(0xFFEEEEEE),
                    thickness = 0.5.dp,
                )
            }
        }
    }
}

/**
 * 标签项组件
 */
@Composable
private fun TabItem(
    title: String,
    isSelected: Boolean,
    badgeCount: Int? = null,
    onClick: () -> Unit,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.clickable(onClick = onClick),
    ) {
        Box {
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
                color = if (isSelected) WakooBlack else WakooGrayText,
                modifier = Modifier.padding(vertical = 13.dp),
            )

            // 未读消息徽标
            badgeCount?.let {
                Box(
                    modifier =
                        Modifier
                            .align(Alignment.TopEnd)
                            .padding(start = 4.dp)
                            .size(16.dp)
                            .background(WakooRed, CircleShape),
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = if (it > 99) "99+" else it.toString(),
                        color = Color.White,
                        fontSize = 11.sp,
                        fontWeight = FontWeight.Medium,
                    )
                }
            }
        }

        // 选中指示器
        if (isSelected) {
            Box(
                modifier =
                    Modifier
                        .width(12.dp)
                        .height(3.dp)
                        .background(WakooBlack),
            )
        }
    }
}

/**
 * 通知/消息标签
 */
enum class NotificationTab {
    DIRECT_MESSAGE, // 私信
    NOTIFICATION, // 通知
}

/**
 * 通知/消息数据模型
 */
data class NotificationModel(
    val id: String,
    val avatar: Int,
    val name: String,
    val message: String,
    val time: String,
    val unreadCount: Int? = null,
    val isOfficial: Boolean = false,
)

@Preview(showBackground = true)
@Composable
private fun NotificationPagePreview() {
    WakooTheme {
        NotificationPage()
    }
}
