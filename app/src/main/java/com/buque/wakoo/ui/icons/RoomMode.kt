package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.RoomMode: ImageVector
    get() {
        val current = _roomMode
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.RoomMode",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    moveTo(4.0f, 3.0f)
                    curveTo(3.4477f, 3.0f, 3.0f, 3.4477f, 3.0f, 4.0f)
                    verticalLineTo(10.0f)
                    curveTo(3.0f, 10.5523f, 3.4477f, 11.0f, 4.0f, 11.0f)
                    horizontalLineTo(10.0f)
                    curveTo(10.5523f, 11.0f, 11.0f, 10.5523f, 11.0f, 10.0f)
                    verticalLineTo(4.0f)
                    curveTo(11.0f, 3.4477f, 10.5523f, 3.0f, 10.0f, 3.0f)
                    horizontalLineTo(4.0f)
                    close()
                    moveTo(4.0f, 13.0f)
                    curveTo(3.4477f, 13.0f, 3.0f, 13.4477f, 3.0f, 14.0f)
                    verticalLineTo(20.0f)
                    curveTo(3.0f, 20.5523f, 3.4477f, 21.0f, 4.0f, 21.0f)
                    horizontalLineTo(10.0f)
                    curveTo(10.5523f, 21.0f, 11.0f, 20.5523f, 11.0f, 20.0f)
                    verticalLineTo(14.0f)
                    curveTo(11.0f, 13.4477f, 10.5523f, 13.0f, 10.0f, 13.0f)
                    horizontalLineTo(4.0f)
                    close()
                    moveTo(14.0f, 13.0f)
                    curveTo(13.4477f, 13.0f, 13.0f, 13.4477f, 13.0f, 14.0f)
                    verticalLineTo(20.0f)
                    curveTo(13.0f, 20.5523f, 13.4477f, 21.0f, 14.0f, 21.0f)
                    horizontalLineTo(20.0f)
                    curveTo(20.5523f, 21.0f, 21.0f, 20.5523f, 21.0f, 20.0f)
                    verticalLineTo(14.0f)
                    curveTo(21.0f, 13.4477f, 20.5523f, 13.0f, 20.0f, 13.0f)
                    horizontalLineTo(14.0f)
                    close()
                    moveTo(15.0f, 19.0f)
                    verticalLineTo(15.0f)
                    horizontalLineTo(19.0f)
                    verticalLineTo(19.0f)
                    horizontalLineTo(15.0f)
                    close()
                    moveTo(5.0f, 9.0f)
                    verticalLineTo(5.0f)
                    horizontalLineTo(9.0f)
                    verticalLineTo(9.0f)
                    horizontalLineTo(5.0f)
                    close()
                    moveTo(5.0f, 19.0f)
                    verticalLineTo(15.0f)
                    horizontalLineTo(9.0f)
                    verticalLineTo(19.0f)
                    horizontalLineTo(5.0f)
                    close()
                    moveTo(16.0f, 11.0f)
                    verticalLineTo(8.0f)
                    horizontalLineTo(13.0f)
                    verticalLineTo(6.0f)
                    horizontalLineTo(16.0f)
                    verticalLineTo(3.0f)
                    horizontalLineTo(18.0f)
                    verticalLineTo(6.0f)
                    horizontalLineTo(21.0f)
                    verticalLineTo(8.0f)
                    horizontalLineTo(18.0f)
                    verticalLineTo(11.0f)
                    horizontalLineTo(16.0f)
                    close()
                }
            }.build()
            .also { _roomMode = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.RoomMode,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _roomMode: ImageVector? = null
