package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.ChatC2c: ImageVector
    get() {
        val current = _chatC2c
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.ChatC2c",
                defaultWidth = 16.0.dp,
                defaultHeight = 16.0.dp,
                viewportWidth = 16.0f,
                viewportHeight = 16.0f,
            ).apply {
                // m4.86 13.88 -3.53 .79 .79 -3.53 a6.67 6.67 0 1 1 2.74 2.74 M4.66 8 a3.33 3.33 0 0 0 6.67 0 H10 a2 2 0 1 1 -4 0z
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 4.86 13.88
                    moveTo(x = 4.86f, y = 13.88f)
                    // l -3.53 0.79
                    lineToRelative(dx = -3.53f, dy = 0.79f)
                    // l 0.79 -3.53
                    lineToRelative(dx = 0.79f, dy = -3.53f)
                    // a 6.67 6.67 0 1 1 2.74 2.74
                    arcToRelative(
                        a = 6.67f,
                        b = 6.67f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = true,
                        dx1 = 2.74f,
                        dy1 = 2.74f,
                    )
                    // M 4.66 8
                    moveTo(x = 4.66f, y = 8.0f)
                    // a 3.33 3.33 0 0 0 6.67 0
                    arcToRelative(
                        a = 3.33f,
                        b = 3.33f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 6.67f,
                        dy1 = 0.0f,
                    )
                    // H 10
                    horizontalLineTo(x = 10.0f)
                    // a 2 2 0 1 1 -4 0z
                    arcToRelative(
                        a = 2.0f,
                        b = 2.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = true,
                        dx1 = -4.0f,
                        dy1 = 0.0f,
                    )
                    close()
                }
            }.build()
            .also { _chatC2c = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.ChatC2c,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((16.0).dp)
                        .height((16.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _chatC2c: ImageVector? = null
