package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.AddRelations: ImageVector
    get() {
        val current = _addRelations
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.AddRelations",
                defaultWidth = 16.0.dp,
                defaultHeight = 16.0.dp,
                viewportWidth = 16.0f,
                viewportHeight = 16.0f,
            ).apply {
                // M9 7 h5 v2 H9 v5 H7 V9 H2 V7 h5 V2 h2z
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 9 7
                    moveTo(x = 9.0f, y = 7.0f)
                    // h 5
                    horizontalLineToRelative(dx = 5.0f)
                    // v 2
                    verticalLineToRelative(dy = 2.0f)
                    // H 9
                    horizontalLineTo(x = 9.0f)
                    // v 5
                    verticalLineToRelative(dy = 5.0f)
                    // H 7
                    horizontalLineTo(x = 7.0f)
                    // V 9
                    verticalLineTo(y = 9.0f)
                    // H 2
                    horizontalLineTo(x = 2.0f)
                    // V 7
                    verticalLineTo(y = 7.0f)
                    // h 5
                    horizontalLineToRelative(dx = 5.0f)
                    // V 2
                    verticalLineTo(y = 2.0f)
                    // h 2z
                    horizontalLineToRelative(dx = 2.0f)
                    close()
                }
            }.build()
            .also { _addRelations = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.AddRelations,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((16.0).dp)
                        .height((16.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _addRelations: ImageVector? = null
