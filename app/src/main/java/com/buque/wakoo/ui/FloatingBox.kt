package com.buque.wakoo.ui

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.ui.icons.DeleteCircle
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.widget.drag.FloatingLayoutManager
import com.buque.wakoo.ui.widget.drag.FloatingLayoutScope
import com.buque.wakoo.ui.widget.drag.rememberDraggableFloatingState
import com.buque.wakoo.ui.widget.drag.rememberFloatingLayoutManagerState
import com.buque.wakoo.ui.widget.image.NetworkImage
import kotlinx.coroutines.flow.collectLatest

@Composable
@Preview
fun AppFloatingWidgets() {
    val managerState = rememberFloatingLayoutManagerState(collisionSpacing = 10.dp)

    Box(modifier = Modifier.fillMaxSize()) {
        FloatingLayoutManager(state = managerState) {
            LiveRoomFloatingItem()
        }
    }
}

@Composable
fun FloatingLayoutScope.LiveRoomFloatingItem() {
    val liveRoomFloatingState =
        rememberDraggableFloatingState(
            initialAlignment = Alignment.BottomEnd,
            initialOffset = Offset(x = 0f, y = -360f),
            initialIsVisible = false,
            initialIsStickyToEdge = true,
            allowDragOutOfBounds = true,
        )

    LaunchedEffect(liveRoomFloatingState) {
        snapshotFlow {
            LiveRoomManager.isCollapse
        }.collectLatest {
            liveRoomFloatingState.isVisible = it
        }
    }
    DraggableItem(state = liveRoomFloatingState) {
        Box(
            modifier =
                Modifier
                    .size(92.dp, 116.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .clickable {
                        LiveRoomManager.expandCurrentRoom()
                    },
        ) {
            NetworkImage(
                data = LiveRoomManager.liveRoomBackground(),
                modifier = Modifier.fillMaxSize(),
            )
            Icon(
                imageVector = WakooIcons.DeleteCircle,
                contentDescription = "exit live room",
                modifier =
                    Modifier
                        .align(Alignment.TopEnd)
                        .padding(4.dp)
                        .size(16.dp)
                        .clickable(onClick = {
                            LiveRoomManager.exitCurrentRoom()
                        }),
                tint = Color.White,
            )
        }
    }
}
