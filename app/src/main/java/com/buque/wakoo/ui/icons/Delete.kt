package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Delete: ImageVector
    get() {
        val current = _delete
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.Delete",
                defaultWidth = 16.0.dp,
                defaultHeight = 16.0.dp,
                viewportWidth = 16.0f,
                viewportHeight = 16.0f,
            ).apply {
                path(
                    fill = SolidColor(Color(0xFF999999)),
                ) {
                    // Outer shape of the trash can
                    moveTo(11.3333f, 4.00065f)
                    horizontalLineTo(14.6666f)
                    verticalLineTo(5.33398f)
                    horizontalLineTo(13.3333f)
                    verticalLineTo(14.0007f)
                    // C 13.3333 14.3689 13.0348 14.6673 12.6666 14.6673
                    curveTo(
                        x1 = 13.3333f,
                        y1 = 14.3689f,
                        x2 = 13.0348f,
                        y2 = 14.6673f,
                        x3 = 12.6666f,
                        y3 = 14.6673f,
                    )
                    horizontalLineTo(3.33331f)
                    // C 2.96513 14.6673 2.66665 14.3689 2.66665 14.0007
                    curveTo(
                        x1 = 2.96513f,
                        y1 = 14.6673f,
                        x2 = 2.66665f,
                        y2 = 14.3689f,
                        x3 = 2.66665f,
                        y3 = 14.0007f,
                    )
                    verticalLineTo(5.33398f)
                    horizontalLineTo(1.33331f)
                    verticalLineTo(4.00065f)
                    horizontalLineTo(4.66665f)
                    verticalLineTo(2.00065f)
                    // C 4.66665 1.63246 4.96513 1.33398 5.33331 1.33398
                    curveTo(
                        x1 = 4.66665f,
                        y1 = 1.63246f,
                        x2 = 4.96513f,
                        y2 = 1.33398f,
                        x3 = 5.33331f,
                        y3 = 1.33398f,
                    )
                    horizontalLineTo(10.6666f)
                    // C 11.0348 1.33398 11.3333 1.63246 11.3333 2.00065
                    curveTo(
                        x1 = 11.0348f,
                        y1 = 1.33398f,
                        x2 = 11.3333f,
                        y2 = 1.63246f,
                        x3 = 11.3333f,
                        y3 = 2.00065f,
                    )
                    verticalLineTo(4.00065f)
                    close()

                    // Inner rectangle (main body of the trash can)
                    moveTo(12f, 5.33398f)
                    horizontalLineTo(3.99998f)
                    verticalLineTo(13.334f)
                    horizontalLineTo(12f)
                    close()

                    // First vertical line (inside)
                    moveTo(5.99998f, 7.33398f)
                    horizontalLineTo(7.33331f)
                    verticalLineTo(11.334f)
                    horizontalLineTo(5.99998f)
                    close()

                    // Second vertical line (inside)
                    moveTo(8.66665f, 7.33398f)
                    horizontalLineTo(9.99998f)
                    verticalLineTo(11.334f)
                    horizontalLineTo(8.66665f)
                    close()

                    // Top part of the trash can (lid area)
                    moveTo(5.99998f, 2.66732f)
                    verticalLineTo(4.00065f)
                    horizontalLineTo(9.99998f)
                    verticalLineTo(2.66732f)
                    close()
                }
            }.build()
            .also { _delete = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Delete,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((16.0).dp)
                        .height((16.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _delete: ImageVector? = null
