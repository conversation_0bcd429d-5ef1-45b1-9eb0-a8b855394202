package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Trash: ImageVector
    get() {
        if (_trash != null) return _trash!!

        _trash =
            ImageVector
                .Builder(
                    name = "trash",
                    defaultWidth = 24.dp,
                    defaultHeight = 24.dp,
                    viewportWidth = 24f,
                    viewportHeight = 24f,
                ).apply {
                    path(
                        fill = SolidColor(Color(0xFF111111)),
                    ) {
                        moveTo(15.1426f, 2.11133f)
                        curveTo(15.4697f, 2.16371f, 15.7463f, 2.39415f, 15.8535f, 2.71582f)
                        lineTo(16.6484f, 5.09961f)
                        horizontalLineTo(21.9004f)
                        verticalLineTo(6.90039f)
                        horizontalLineTo(20.0479f)
                        lineTo(19.1982f, 21.0537f)
                        curveTo(19.1697f, 21.529f, 18.7759f, 21.9004f, 18.2998f, 21.9004f)
                        horizontalLineTo(5.7002f)
                        curveTo(5.22407f, 21.9004f, 4.83027f, 21.529f, 4.80176f, 21.0537f)
                        lineTo(3.95215f, 6.90039f)
                        horizontalLineTo(2.09961f)
                        verticalLineTo(5.09961f)
                        horizontalLineTo(7.35156f)
                        lineTo(8.14648f, 2.71582f)
                        lineTo(8.20215f, 2.58301f)
                        curveTo(8.3554f, 2.28955f, 8.66116f, 2.09961f, 9f, 2.09961f)
                        horizontalLineTo(15f)
                        lineTo(15.1426f, 2.11133f)
                        close()
                        moveTo(6.54785f, 20.0996f)
                        horizontalLineTo(17.4521f)
                        lineTo(18.2441f, 6.90039f)
                        horizontalLineTo(5.75586f)
                        lineTo(6.54785f, 20.0996f)
                        close()
                        moveTo(9.24805f, 5.09961f)
                        horizontalLineTo(14.752f)
                        lineTo(14.3516f, 3.90039f)
                        horizontalLineTo(9.64844f)
                        lineTo(9.24805f, 5.09961f)
                        close()
                    }
                    path(
                        fill = SolidColor(Color(0xFF111111)),
                    ) {
                        moveTo(13.2451f, 10.1006f)
                        curveTo(14.5781f, 10.1199f, 15.9461f, 11.2011f, 15.9463f, 12.8145f)
                        curveTo(15.9461f, 14.2584f, 15.0316f, 15.3907f, 14.248f, 16.0918f)
                        curveTo(13.8402f, 16.4567f, 13.4227f, 16.7464f, 13.0791f, 16.9482f)
                        curveTo(12.9078f, 17.0489f, 12.7449f, 17.1336f, 12.6025f, 17.1963f)
                        curveTo(12.5321f, 17.2273f, 12.4555f, 17.2579f, 12.3789f, 17.2822f)
                        curveTo(12.3248f, 17.2994f, 12.187f, 17.3418f, 12.0234f, 17.3418f)
                        curveTo(11.8596f, 17.3418f, 11.721f, 17.2994f, 11.667f, 17.2822f)
                        curveTo(11.5905f, 17.2579f, 11.5146f, 17.2272f, 11.4443f, 17.1963f)
                        curveTo(11.302f, 17.1336f, 11.1392f, 17.0489f, 10.9678f, 16.9482f)
                        curveTo(10.6242f, 16.7464f, 10.2058f, 16.4568f, 9.79785f, 16.0918f)
                        curveTo(9.01439f, 15.3907f, 8.09977f, 14.2582f, 8.09961f, 12.8145f)
                        curveTo(8.09976f, 11.2118f, 9.45887f, 10.1008f, 10.8135f, 10.1006f)
                        curveTo(11.1951f, 10.1006f, 11.6249f, 10.1812f, 12.0195f, 10.4033f)
                        curveTo(12.4126f, 10.1787f, 12.8455f, 10.0949f, 13.2451f, 10.1006f)
                        close()
                        moveTo(13.2197f, 11.9004f)
                        curveTo(13.0996f, 11.8987f, 13.0099f, 11.9191f, 12.9463f, 11.9482f)
                        curveTo(12.8893f, 11.9744f, 12.8304f, 12.0177f, 12.7725f, 12.1045f)
                        curveTo(12.6056f, 12.3547f, 12.3242f, 12.5048f, 12.0234f, 12.5049f)
                        curveTo(11.7226f, 12.5049f, 11.4414f, 12.3547f, 11.2744f, 12.1045f)
                        curveTo(11.2201f, 12.023f, 11.1624f, 11.9797f, 11.1016f, 11.9521f)
                        curveTo(11.0333f, 11.9213f, 10.9375f, 11.9004f, 10.8135f, 11.9004f)
                        curveTo(10.3546f, 11.9007f, 9.90054f, 12.3015f, 9.90039f, 12.8145f)
                        curveTo(9.90055f, 13.4862f, 10.3459f, 14.1674f, 10.998f, 14.751f)
                        curveTo(11.308f, 15.0283f, 11.6274f, 15.2488f, 11.8789f, 15.3965f)
                        curveTo(11.9314f, 15.4273f, 11.9798f, 15.4539f, 12.0225f, 15.4766f)
                        curveTo(12.0654f, 15.4538f, 12.1141f, 15.4276f, 12.167f, 15.3965f)
                        curveTo(12.4185f, 15.2488f, 12.7379f, 15.0282f, 13.0479f, 14.751f)
                        curveTo(13.7001f, 14.1674f, 14.1463f, 13.4863f, 14.1465f, 12.8145f)
                        curveTo(14.1463f, 12.3119f, 13.7005f, 11.9075f, 13.2197f, 11.9004f)
                        close()
                    }
                }.build()

        return _trash!!
    }

private var _trash: ImageVector? = null
