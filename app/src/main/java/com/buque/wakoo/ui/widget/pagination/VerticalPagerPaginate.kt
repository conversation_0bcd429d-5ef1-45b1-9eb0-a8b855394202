package com.buque.wakoo.ui.widget.pagination

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.pager.PagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.listSaver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter

/**
 * 创建一个垂直分页器的分页状态
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun <Key : Any> rememberVerticalPagerPaginateState(
    initialKey: Key? = null,
    enabled: Boolean = true,
    prefetchBuffer: Int = 3, // 预加载缓冲区大小，表示当前页面前后各预加载几页
): VerticalPagerPaginateState<Key> =
    rememberSaveable(saver = VerticalPagerPaginateState.createSaver()) {
        VerticalPagerPaginateState(
            initialKey = initialKey,
            enabled = enabled,
            prefetchBuffer = prefetchBuffer,
        )
    }

/**
 * 垂直分页器的分页状态管理类，只支持向后分页加载
 */
@Stable
class VerticalPagerPaginateState<Key : Any> internal constructor(
    initialKey: Key? = null,
    enabled: Boolean,
    private val prefetchBuffer: Int = 2,
) {
    // 当前加载状态
    var loadState: LoadState by mutableStateOf(LoadState.Idle)
        private set

    // 是否启用分页加载
    private var _enabled: Boolean by mutableStateOf(enabled)
    val enabled: Boolean get() = _enabled

    // 最大页数，-1表示无限
    private var _maxPage: Int by mutableStateOf(-1)
    val maxPage: Int get() = _maxPage

    // 当前加载键
    private var currentKey: Key? = initialKey

    /**
     * 获取当前分页键
     */
    fun getCurrentKey(): Key? = currentKey

    /**
     * 设置是否启用分页加载
     */
    fun setEnabled(enabled: Boolean) {
        _enabled = enabled
        if (!enabled) {
            loadState = LoadState.End
        } else if (loadState == LoadState.End) {
            loadState = LoadState.Idle
        }
    }

    /**
     * 设置最大页数，用于限制分页数量
     */
    fun setMaxPage(maxPage: Int) {
        _maxPage = maxPage
    }

    /**
     * 重置状态
     */
    fun reset(
        key: Key? = null,
        enabled: Boolean = this.enabled,
        maxPage: Int = this.maxPage,
    ) {
        this.currentKey = key
        setEnabled(enabled)
        setMaxPage(maxPage)
    }

    /**
     * 重试加载
     */
    fun retry(): Boolean {
        if (enabled && loadState.isFailure) {
            loadState = LoadState.Idle
            return true
        }
        return false
    }

    /**
     * 手动触发一次加载
     */
    @OptIn(ExperimentalFoundationApi::class)
    suspend fun loadTriggerOnce(
        pagerState: PagerState,
        key: Key? = currentKey,
        ignoreEnabled: Boolean = false,
        onLoad: suspend (Key?, Int) -> LoadResult<Key>,
    ) {
        if (!ignoreEnabled && !enabled) {
            return
        }
        if (loadState.isLoading) {
            return
        }
        loadPageData(pagerState, key, onLoad)
    }

    /**
     * 将分页状态绑定到PagerState
     */
    @OptIn(ExperimentalFoundationApi::class)
    @Composable
    fun ConnectToState(
        pagerState: PagerState,
        onLoad: suspend (Key?, Int) -> LoadResult<Key>,
    ) {
        if (enabled) {
            LaunchedEffect(pagerState) {
                val triggerLoad by derivedStateOf {
                    val currentPage = pagerState.currentPage
                    val pageCount = pagerState.pageCount
                    // 当接近末尾时触发加载
                    (currentPage >= pageCount - prefetchBuffer) &&
                        (maxPage == -1 || pageCount < maxPage)
                }

                snapshotFlow {
                    if (loadState.canLoad && triggerLoad) {
                        PaginateAction.Load
                    } else if (loadState.isLoading) {
                        PaginateAction.Nothing
                    } else {
                        PaginateAction.Cancel
                    }
                }.filter {
                    it != PaginateAction.Nothing
                }.collectLatest {
                    if (it is PaginateAction.Cancel) {
                        return@collectLatest
                    }

                    loadPageData(pagerState, currentKey, onLoad)
                }
            }
        }
    }

    /**
     * 刷新数据
     */
    @OptIn(ExperimentalFoundationApi::class)
    suspend fun refresh(
        pagerState: PagerState,
        key: Key,
        onLoad: suspend (Key?, Int) -> LoadResult<Key>,
    ) {
        loadPageData(pagerState, key, onLoad)
    }

    @OptIn(ExperimentalFoundationApi::class)
    private suspend fun loadPageData(
        pagerState: PagerState,
        key: Key?,
        onLoad: suspend (Key?, Int) -> LoadResult<Key>,
    ) {
        loadState = LoadState.Loading(key)
        val result = onLoad(key, pagerState.pageCount)

        PaginateUtils.processLoadResult(
            result = result,
            key = key,
            updateKey = { currentKey = it },
            updateState = { loadState = it },
            checkMaxReached = { maxPage != -1 && pagerState.pageCount >= maxPage },
        )
    }

    companion object {
        /**
         * 创建保存器，用于在配置更改时保存状态
         */
        fun <Key : Any> createSaver() =
            listSaver(
                save = {
                    listOf(
                        it.currentKey,
                        it._enabled,
                        it.prefetchBuffer,
                        it._maxPage,
                    )
                },
                restore = {
                    VerticalPagerPaginateState(
                        initialKey = it[0] as? Key?,
                        enabled = it[1] as Boolean,
                        prefetchBuffer = it[2] as Int,
                    ).apply {
                        setMaxPage(it[3] as Int)
                    }
                },
            )
    }
}

/**
 * 使用示例：
 *
 * @OptIn(ExperimentalFoundationApi::class)
 * @Composable
 * fun VerticalPaginationExample() {
 *     val pagerState = rememberPagerState(pageCount = { items.size })
 *     val paginateState = rememberVerticalPagerPaginateState<Int>(initialKey = 1)
 *
 *     paginateState.ConnectToState(pagerState) { key, currentPageCount ->
 *         try {
 *             val nextKey = key?.plus(1) ?: 1
 *             val newItems = loadMoreItems(nextKey)
 *             items.addAll(newItems)
 *             pagerState.pageCount = items.size
 *             LoadResult.Page(nextKey)
 *         } catch (e: Exception) {
 *             LoadResult.Error(e)
 *         }
 *     }
 *
 *     VerticalPager(
 *         state = pagerState
 *     ) { page ->
 *         // 页面内容
 *     }
 * }
 */
