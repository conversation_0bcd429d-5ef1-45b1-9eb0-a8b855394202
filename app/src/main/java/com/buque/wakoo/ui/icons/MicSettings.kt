package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.MicSettings: ImageVector
    get() {
        val current = _MicSettings
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.MicSettings",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                group(
                    clipPathData =
                        PathData {
                            moveTo(x = 0.0f, y = 0.0f)
                            horizontalLineTo(x = 24.0f)
                            verticalLineTo(y = 24.0f)
                            horizontalLineTo(x = 0.0f)
                            close()
                        },
                ) {
                    path(
                        fill = SolidColor(Color(0xFFFFFFFF)),
                    ) {
                        moveTo(7.0f, 6.0f)
                        curveTo(7.0f, 3.2386f, 9.2386f, 1.0f, 12.0f, 1.0f)
                        curveTo(12.9914f, 1.0f, 13.9182f, 1.2896f, 14.6969f, 1.7891f)
                        lineTo(13.617f, 3.4725f)
                        curveTo(13.1509f, 3.1735f, 12.5972f, 3.0f, 12.0f, 3.0f)
                        curveTo(10.3431f, 3.0f, 9.0f, 4.3431f, 9.0f, 6.0f)
                        verticalLineTo(12.0f)
                        curveTo(9.0f, 13.6569f, 10.3431f, 15.0f, 12.0f, 15.0f)
                        curveTo(13.6569f, 15.0f, 15.0f, 13.6569f, 15.0f, 12.0f)
                        verticalLineTo(10.9791f)
                        horizontalLineTo(17.0f)
                        verticalLineTo(12.0f)
                        curveTo(17.0f, 14.7614f, 14.7614f, 17.0f, 12.0f, 17.0f)
                        curveTo(9.2386f, 17.0f, 7.0f, 14.7614f, 7.0f, 12.0f)
                        verticalLineTo(6.0f)
                        close()
                        moveTo(2.1924f, 13.9617f)
                        lineTo(4.1539f, 13.5693f)
                        curveTo(4.8832f, 17.2362f, 8.1189f, 20.0001f, 12.0001f, 20.0001f)
                        curveTo(15.8813f, 20.0001f, 19.1169f, 17.2363f, 19.8462f, 13.5693f)
                        lineTo(21.8078f, 13.9617f)
                        curveTo(20.8961f, 18.5453f, 16.8516f, 22.0001f, 12.0001f, 22.0001f)
                        curveTo(7.1486f, 22.0001f, 3.104f, 18.5453f, 2.1924f, 13.9617f)
                        close()
                    }
                    path(
                        fill = SolidColor(Color(0xFFFFFFFF)),
                    ) {
                        moveTo(20.5508f, 1.0f)
                        verticalLineTo(2.1621f)
                        curveTo(21.0913f, 2.323f, 21.5785f, 2.6089f, 21.9775f, 2.9863f)
                        lineTo(22.9844f, 2.4053f)
                        lineTo(24.0f, 4.1631f)
                        lineTo(22.9932f, 4.7441f)
                        curveTo(23.0559f, 5.0086f, 23.0889f, 5.2848f, 23.0889f, 5.5684f)
                        curveTo(23.0889f, 5.8517f, 23.0558f, 6.1274f, 22.9932f, 6.3916f)
                        lineTo(24.0f, 6.9736f)
                        lineTo(22.9844f, 8.7314f)
                        lineTo(21.9775f, 8.1494f)
                        curveTo(21.5786f, 8.5269f, 21.0921f, 8.8138f, 20.5518f, 8.9746f)
                        verticalLineTo(10.1367f)
                        horizontalLineTo(18.5205f)
                        verticalLineTo(8.9746f)
                        curveTo(17.9802f, 8.8138f, 17.4937f, 8.5278f, 17.0947f, 8.1504f)
                        lineTo(16.0879f, 8.7314f)
                        lineTo(15.0723f, 6.9736f)
                        lineTo(16.0791f, 6.3926f)
                        curveTo(16.0163f, 6.1282f, 15.9834f, 5.852f, 15.9834f, 5.5684f)
                        curveTo(15.9834f, 5.285f, 16.0164f, 5.0093f, 16.0791f, 4.7451f)
                        lineTo(15.0723f, 4.1631f)
                        lineTo(16.0879f, 2.4053f)
                        lineTo(17.0947f, 2.9863f)
                        curveTo(17.4937f, 2.609f, 17.9802f, 2.323f, 18.5205f, 2.1621f)
                        verticalLineTo(1.0f)
                        horizontalLineTo(20.5508f)
                        close()
                        moveTo(19.5361f, 4.0459f)
                        curveTo(18.6952f, 4.0459f, 18.0137f, 4.7274f, 18.0137f, 5.5684f)
                        curveTo(18.0137f, 6.4093f, 18.6952f, 7.0908f, 19.5361f, 7.0908f)
                        curveTo(20.3771f, 7.0908f, 21.0586f, 6.4093f, 21.0586f, 5.5684f)
                        curveTo(21.0586f, 4.7274f, 20.3771f, 4.0459f, 19.5361f, 4.0459f)
                        close()
                    }
                }
            }.build()
            .also { _MicSettings = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.MicSettings,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _MicSettings: ImageVector? = null
