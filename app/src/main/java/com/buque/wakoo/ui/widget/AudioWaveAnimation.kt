package com.buque.wakoo.ui.widget

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.random.Random

// 恢复使用您指定的预定义静态波形。
private val stoppedWaveform =
    listOf(0.4f, 0.6f, 0.9f, 0.5f, 0.7f, 0.3f, 0.5f, 0.8f, 0.6f, 0.4f, 0.7f, 0.9f, 0.5f, 0.6f, 0.4f)

/**
 * 一个模拟音频播放效果的波动动画组件，具有播放/停止控制和优化的视觉效果。
 * 其尺寸完全由传入的 Modifier 控制。
 *
 * @param modifier The modifier to be applied to the component, determining its size.
 * @param isPlaying 控制动画是否正在播放。
 * @param barColor 波动矩形的颜色。
 * @param barWidth 每个波动矩形的固定宽度。
 * @param barSpacing 波动矩形之间的固定间隔。
 * @param minHeightFraction 矩形动画高度的最小比例（相对于组件总高度），值在 0.0 到 1.0 之间。
 * @param maxHeightFraction 矩形动画高度的最大比例（相对于组件总高度），值在 0.0 到 1.0 之间。
 */
@Composable
fun AudioWaveAnimation(
    isPlaying: Boolean,
    modifier: Modifier = Modifier,
    barColor: Color = Color.Black,
    barWidth: Dp = 3.dp,
    barSpacing: Dp = 3.dp,
    minHeightFraction: Float = 0.1f,
    maxHeightFraction: Float = 0.9f,
) {
    val validMinHeight = minHeightFraction.coerceIn(0f, 1f)
    val validMaxHeight = maxHeightFraction.coerceIn(validMinHeight, 1f)

    BoxWithConstraints(modifier = modifier) {
        val density = LocalDensity.current

        val widthInPx = with(density) { maxWidth.toPx() }
        val barWidthInPx = with(density) { barWidth.toPx() }
        val barSpacingInPx = with(density) { barSpacing.toPx() }

        val numberOfBars =
            ((widthInPx + barSpacingInPx) / (barWidthInPx + barSpacingInPx))
                .toInt()
                .coerceAtLeast(0)

        // Animatable的初始值直接使用静态波形的值，通过取模运算来循环图案。
        val heightFractions =
            remember(numberOfBars) {
                List(numberOfBars) { index ->
                    val targetHeight =
                        if (stoppedWaveform.isNotEmpty()) {
                            stoppedWaveform[index % stoppedWaveform.size]
                        } else {
                            validMinHeight
                        }
                    Animatable(targetHeight.coerceIn(validMinHeight, validMaxHeight))
                }
            }

        LaunchedEffect(isPlaying, numberOfBars) {
            coroutineScope {
                if (isPlaying) {
                    while (true) {
                        heightFractions.forEachIndexed { index, animatable ->
                            launch {
                                delay(index * 25L)
                                val targetHeight =
                                    Random.nextFloat().coerceIn(validMinHeight, validMaxHeight)
                                animatable.animateTo(
                                    targetValue = targetHeight,
                                    animationSpec =
                                        tween(
                                            durationMillis = Random.nextInt(300, 800),
                                            easing = LinearEasing,
                                        ),
                                )
                            }
                        }
                        delay(Random.nextLong(150, 400))
                    }
                } else {
                    // 停止时，平滑过渡到预设的静态波形，同样使用取模运算
                    heightFractions.forEachIndexed { index, animatable ->
                        launch {
                            val targetHeight =
                                if (stoppedWaveform.isNotEmpty()) {
                                    stoppedWaveform[index % stoppedWaveform.size]
                                } else {
                                    validMinHeight
                                }
                            animatable.animateTo(
                                targetValue = targetHeight.coerceIn(validMinHeight, validMaxHeight),
                                animationSpec = tween(durationMillis = 450, easing = LinearEasing),
                            )
                        }
                    }
                }
            }
        }

        Canvas(modifier = Modifier.fillMaxSize()) {
            val canvasHeight = size.height
            val canvasWidth = size.width
            val availableWidth = (numberOfBars * (barWidthInPx + barSpacingInPx)) - barSpacingInPx
            val startX = (canvasWidth - availableWidth) / 2

            for (i in 0 until numberOfBars) {
                val xOffset = startX + i * (barWidthInPx + barSpacingInPx)
                val barHeight = canvasHeight * heightFractions[i].value
                val yOffset = (canvasHeight - barHeight) / 2

                drawRect(
                    color = barColor,
                    topLeft = Offset(x = xOffset, y = yOffset),
                    size = Size(width = barWidthInPx, height = barHeight),
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun AudioWavePreview() {
    Box(
        modifier =
            Modifier
                .fillMaxSize()
                .background(Color(0xFFEAFCF9)),
        // 使用一个柔和的背景色
        contentAlignment = Alignment.Center,
    ) {
        // 使用 AudioWaveAnimation 组件
        AudioWaveAnimation(
            isPlaying = true,
            modifier = Modifier.size(250.dp, 30.dp),
            barWidth = 4.dp,
            barSpacing = 3.dp,
            barColor = Color(0xFF1DB954), // 类似 Spotify 的绿色
            minHeightFraction = 0.2f,
            maxHeightFraction = 0.8f,
        )
    }
}
