package com.buque.wakoo.ui.widget.media.manager

import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.referentialEqualityPolicy
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.IntSize
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.Timeline
import androidx.media3.common.VideoSize
import kotlinx.serialization.Serializable

// 定义媒体类型
@Serializable
enum class MediaType {
    VIDEO,
    AUDIO,
    LIVE_AUDIO,
}

/**
 *
 * @param tag 是一个真正意义上的唯一标识, 一般是url加上使用环境拼接而成
 *        因为考虑到同一个url可能会同时存在不同环境，使用唯一tag可以更精确控制，特别是停止播放
 */
@Stable
@Serializable
data class PlayMediaItem(
    val mediaType: MediaType, // 媒体类型，目前有3种
    val url: String, // 需要播放资源的地址，可以是远程url，也可以是uriString
    val tag: String, // tag是一个真正意义上的唯一标识(建议使用)，一般是url加上使用环境拼接而成（因为考虑到同一个url可能会同时存在不同环境，使用唯一tag可以更精确控制，特别是停止播放）
) {
    companion object {
        fun url(
            url: String,
            mediaType: MediaType,
            tag: String = url,
        ): PlayMediaItem =
            PlayMediaItem(
                mediaType = mediaType,
                url = url,
                tag = tag,
            )

        fun prefixTagUrl(
            url: String,
            mediaType: MediaType,
            prefix: String,
        ): PlayMediaItem =
            PlayMediaItem(
                mediaType = mediaType,
                url = url,
                tag = "$prefix-$url",
            )

        fun audio(
            url: String,
            tag: String = url,
        ): PlayMediaItem =
            PlayMediaItem(
                mediaType = MediaType.AUDIO,
                url = url,
                tag = tag,
            )

        fun prefixTagAudio(
            url: String,
            prefix: String,
        ): PlayMediaItem =
            PlayMediaItem(
                mediaType = MediaType.AUDIO,
                url = url,
                tag = "$prefix-$url",
            )

        fun video(
            url: String,
            tag: String = url,
        ): PlayMediaItem =
            PlayMediaItem(
                mediaType = MediaType.VIDEO,
                url = url,
                tag = tag,
            )

        fun prefixTagVideo(
            url: String,
            prefix: String,
        ): PlayMediaItem =
            PlayMediaItem(
                mediaType = MediaType.VIDEO,
                url = url,
                tag = "$prefix-$url",
            )

        fun liveAudio(
            url: String,
            tag: String = url,
        ): PlayMediaItem =
            PlayMediaItem(
                mediaType = MediaType.LIVE_AUDIO,
                url = url,
                tag = tag,
            )

        fun prefixTagLiveAudio(
            url: String,
            prefix: String,
        ): PlayMediaItem =
            PlayMediaItem(
                mediaType = MediaType.LIVE_AUDIO,
                url = url,
                tag = "$prefix-$url",
            )
    }

    fun toExoPlayerMediaItem(): MediaItem =
        MediaItem
            .Builder()
            .setUri(url)
            .setMediaId(tag)
            .run {
                if (mediaType == MediaType.LIVE_AUDIO) {
                    setLiveConfiguration(
                        MediaItem.LiveConfiguration.Builder().build(),
                    )
                } else {
                    this
                }
            }.build()
}

val PlayMediaState.canShowVideo
    get() =
        when (status) {
            is PlayerStatus.Idle.Initial -> true
            is PlayerStatus.Play -> true
            is PlayerStatus.Pause -> true
            PlayerStatus.Idle.Evicted -> false
        }

val PlayMediaState.canShowStill
    get() =
        videoSize == IntSize.Zero ||
            !renderedFirstFrame ||
            when (status) {
                is PlayerStatus.Idle -> true
                is PlayerStatus.Pause -> false
                PlayerStatus.Play.Requested -> true
                PlayerStatus.Play.Confirmed -> false
            }

@Stable
class PlayMediaState(
    val playItem: PlayMediaItem,
) {
    val url: String get() = playItem.url
    val tag: String get() = playItem.tag
    var alignment by mutableStateOf(Alignment.Center)
    var contentScale by mutableStateOf(ContentScale.Crop)
    var status by mutableStateOf<PlayerStatus>(PlayerStatus.Idle.Initial)
    var renderedFirstFrame by mutableStateOf(false)
    var videoSize by mutableStateOf(IntSize.Zero)
    var playerPosition by mutableLongStateOf(0L)

    // 表示未获取到
    var duration by mutableLongStateOf(-1L)
    var player by mutableStateOf<Player?>(
        value = null,
        policy = referentialEqualityPolicy(),
    )

    var videoStill by mutableStateOf<ImageBitmap?>(
        value = null,
        policy = referentialEqualityPolicy(),
    )

    var isActuallyPlaying by mutableStateOf(false)

    var isBuffering by mutableStateOf(false)

    val isPlaybackRequested
        get() = status is PlayerStatus.Play

    val isConfirmedPlaying
        get() = status is PlayerStatus.Play.Confirmed

    internal val playerListener =
        object : Player.Listener {
            override fun onVideoSizeChanged(size: VideoSize) {
                updateVideoSize(size)
            }

            override fun onMediaItemTransition(
                mediaItem: MediaItem?,
                reason: Int,
            ) {
                if (mediaItem?.mediaId == playItem.tag && reason == Player.MEDIA_ITEM_TRANSITION_REASON_REPEAT) {
                    if (playItem.mediaType == MediaType.AUDIO) {
                        player?.pause()
                        if (status !is PlayerStatus.Idle.Initial) {
                            status = PlayerStatus.Pause.Requested
                        }
                    }
                }
            }

            override fun onTimelineChanged(
                timeline: Timeline,
                reason: Int,
            ) {
                // 当媒体信息（包括时长）可用或发生变化时，这个回调会被触发
                if (!timeline.isEmpty) {
                    val newDuration =
                        player?.let {
                            val window = Timeline.Window()
                            // 我们通常关心当前播放窗口的信息，所以使用 player.currentMediaItemIndex
                            it.currentTimeline.getWindow(it.currentMediaItemIndex, window)
                            window.durationMs // window.durationMs 包含了当前媒体项的时长（毫秒）
                        }
                    // C.TIME_UNSET 表示时长未知（例如，直播流）
                    if (newDuration != null && newDuration != C.TIME_UNSET) {
                        duration = newDuration
                    }
                }
            }

            override fun onPlayWhenReadyChanged(
                playWhenReady: Boolean,
                reason: Int,
            ) {
                status =
                    when {
                        playWhenReady && player?.playbackState == Player.STATE_READY -> PlayerStatus.Play.Confirmed
                        playWhenReady -> PlayerStatus.Play.Requested
                        status == PlayerStatus.Idle.Initial -> status
                        else -> PlayerStatus.Pause.Confirmed
                    }
                player?.videoSize?.let(::updateVideoSize)
            }

            override fun onPlaybackStateChanged(playbackState: Int) {
                isBuffering = (playbackState == Player.STATE_BUFFERING)

                if (playbackState == Player.STATE_READY) {
                    if (status is PlayerStatus.Play) {
                        player?.play()
                    } else if (status is PlayerStatus.Pause.Requested) {
                        status = PlayerStatus.Pause.Confirmed
                    }
                }
//                status =
//                    when {
//                        playbackState == Player.STATE_READY && player?.playWhenReady == true ->
//                            PlayerStatus.Play.Confirmed.also {
//                                player?.play()
//                            }
//                        player?.playWhenReady == true -> PlayerStatus.Play.Requested
//                        else -> status
//                    }
                player?.videoSize?.let(::updateVideoSize)

                player?.duration?.let { newDuration ->
                    if (newDuration != C.TIME_UNSET) {
                        duration = newDuration
                    }
                }
            }

            override fun onRenderedFirstFrame() {
                renderedFirstFrame = true
                player?.videoSize?.let(::updateVideoSize)
            }

            override fun onIsPlayingChanged(isPlaying: Boolean) {
                isActuallyPlaying = isPlaying
            }
        }

    private fun updateVideoSize(size: VideoSize) {
        videoSize =
            when (val intSize = size.toIntSize()) {
                IntSize.Zero -> videoSize
                else -> intSize
            }
    }
}

private fun VideoSize.toIntSize() = IntSize(width, height)
