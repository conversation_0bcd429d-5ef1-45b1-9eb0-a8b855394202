package com.buque.wakoo.ui.screens.vip

import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.consts.Contracts
import com.buque.wakoo.consts.Pay
import com.buque.wakoo.core.pay.AppPayCoreKit
import com.buque.wakoo.core.pay.PayRequest
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.network.api.bean.ActivateOption
import com.buque.wakoo.network.api.bean.VipInfo
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.WakooTitleBar
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.utils.DateTimeUtils
import com.buque.wakoo.utils.toCState
import com.buque.wakoo.viewmodel.WalletEvent
import com.buque.wakoo.viewmodel.WalletViewModel
import kotlinx.coroutines.launch

@Composable
fun MemberScreen(onOpenWeb: (String) -> Unit = {}) {
    val viewModel = viewModel<WalletViewModel>()
    LaunchedEffect(viewModel) {
        viewModel.sendEvent(WalletEvent.FetchVipConfig)
    }
    val state by viewModel.vipConfigState.collectAsState()
    val cState = state.toCState()
    val self by AccountManager.userStateFlow.collectAsState()
    self?.let { MemberPageContent(cState, it, onOpenWeb) }
}

@Composable
fun MemberPageContent(state: CState<VipInfo>, self: UserInfo, onOpenWeb: (String) -> Unit = {}) {
    val context = LocalContext.current
    val act = LocalActivity.current
    val loadingMgr = LocalLoadingManager.current
    val loading by AppPayCoreKit.loading
    if (!loading) {
        loadingMgr.dismiss()
    }
    val scope = rememberCoroutineScope()
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF5F7F9))
            .navigationBarsPadding()
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(200.dp)
                .background(Brush.verticalGradient(listOf(Color(0xFFFFFBBD), Color.Transparent)))
        )
        val scrollState = rememberScrollState()
        var selectedGood by remember {
            mutableStateOf<ActivateOption?>(null)
        }
        var isMember by remember { mutableStateOf(false) }
        Column(modifier = Modifier.fillMaxSize()) {
            WakooTitleBar(stringResource(R.string.active_member))
            CStateLayout(state) { vipInfo ->
                LaunchedEffect(vipInfo.isVip) {
                    isMember = vipInfo.isVip
                }
                val goods = vipInfo.activateOption
                LaunchedEffect(goods) {
                    if (selectedGood == null) {
                        selectedGood = goods.firstOrNull()
                    }
                }
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(scrollState)
                        .padding(horizontal = 16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    SizeHeight(16.dp)
                    val inactiveMember = stringResource(R.string.member_inactive)
                    val expireDate = remember(vipInfo) {
                        if (vipInfo.isVip) {
                            val date = DateTimeUtils.formatDate(vipInfo.expireTimestamp * 1000, "yyyy-MM-dd")
                            context.getString(R.string.format_expire, date)
                        } else {
                            inactiveMember
                        }
                    }
                    UserVipCard(self.name, expireDate = expireDate, avatar = self.avatar)
                    SizeHeight(20.dp)
                    if (goods.size < 4) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = if (goods.size == 2) Arrangement.spacedBy(12.dp) else Arrangement.SpaceBetween
                        ) {
                            goods.forEach {
                                GoodsItem(it.equity, "${it.currencyMark}${it.currencyNumber}", it == selectedGood) {
                                    selectedGood = it
                                }
                            }
                        }
                    } else {
                        LazyRow(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            items(goods) {
                                GoodsItem(it.equity, "${it.currencyMark}${it.currencyNumber}", it == selectedGood) {
                                    selectedGood = it
                                }
                            }
                        }
                    }
                    SizeHeight(20.dp)
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color.White, RoundedCornerShape(12.dp))
                            .padding(16.dp, 20.dp),
                        verticalArrangement = Arrangement.spacedBy(24.dp)
                    ) {
                        Row(modifier = Modifier.fillMaxWidth()) {
                            NetworkImage(
                                vipInfo.memberRightsTitleIcon,
                                contentScale = ContentScale.Inside,
                                modifier = Modifier
                                    .height(16.dp)
                                    .widthIn(max = 92.dp)
                            )
                        }
                        vipInfo.memberRightsInfos.forEach { rightItem ->
                            VipBenefitItem(
                                icon = {
                                    NetworkImage(rightItem.icon, modifier = Modifier.size(48.dp))
                                },
                                title = rightItem.name,
                                description = rightItem.prompt,
                            )
                        }
                    }
                    SizeHeight(120.dp)
                }
            }
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            SizeHeight(16.dp)
            BottomButton(
                stringResource(if (isMember) R.string.add_member else R.string.active_member), modifier = Modifier
                    .width(320.dp)
                    .shadow(2.dp, RoundedCornerShape(50))
                    .clickable {
                        loadingMgr.show(scope) {
                            val sel = selectedGood
                            if (sel != null) {
                                act?.let { a ->
                                    scope.launch {
                                        AppPayCoreKit.buy(
                                            a,
                                            PayRequest(
                                                sel.fkType,
                                                sel.productId,
                                                sel.fkLink,
                                                orderType = Pay.ORDER_TYPE_MEMBERSHIP
                                            )
                                        )
                                    }
                                }
                            }
                        }
                    })
            SizeHeight(16.dp)
            // 协议文本
            Text(
                text = stringResource(R.string.desc_active_member),
                fontSize = 12.sp,
                color = Color(0xFFB6B6B6),
                textAlign = TextAlign.Center,
                modifier = Modifier.clickable {
                    onOpenWeb(Contracts.member)
                }
            )
            SizeHeight(16.dp)
        }
    }
}

/**
 * VIP开通页面
 * 展示VIP会员权益和价格选项
 */
@Composable
fun VipPage(
    onBackClick: () -> Unit = {},
    onPurchaseClick: (planType: VipPlanType) -> Unit = { _ -> },
    onRestorePurchaseClick: () -> Unit = {},
    userName: String = "幼儿园搬花",
    vipExpireDate: String = "2025.06.08",
) {
    var selectedPlan by remember { mutableStateOf(VipPlanType.MONTHLY) }
    Box(
        modifier =
            Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F7F9)),
    ) {
        // 背景渐变
        BackgroundGradient()

        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
        ) {
            // 状态栏占位
            Spacer(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(44.dp)
                        .statusBarsPadding(),
            )

            // 导航栏
            TopNavigationBar(onBackClick = onBackClick)

            SizeHeight(16.dp)

            // 用户信息卡片
            UserVipCard(
                userName = userName,
                expireDate = vipExpireDate,
                modifier = Modifier.padding(horizontal = 16.dp),
            )

            SizeHeight(20.dp)

            // VIP权益卡片
            VipBenefitsCard(
                modifier = Modifier.padding(horizontal = 16.dp),
            )

            SizeHeight(20.dp)

            // 价格选择
            PricingSection(
                selectedPlan = selectedPlan,
                onPlanSelected = { selectedPlan = it },
                modifier = Modifier.padding(horizontal = 16.dp),
            )

            SizeHeight(100.dp)
        }

        // 底部操作区域
        BottomActionArea(
            selectedPlan = selectedPlan,
            onPurchaseClick = { onPurchaseClick(selectedPlan) },
            onRestorePurchaseClick = onRestorePurchaseClick,
            modifier = Modifier.align(Alignment.BottomCenter),
        )
    }
}

/**
 * VIP套餐类型
 */
enum class VipPlanType(
    val displayName: String,
    val price: String,
) {
    WEEKLY("周会员", "$0.99"),
    MONTHLY("月会员", "$24.99"),
    QUARTERLY("三个月", "$44.99"),
}

/**
 * 背景渐变
 */
@Composable
private fun BackgroundGradient() {
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .height(200.dp)
                .background(
                    brush =
                        Brush.verticalGradient(
                            colors =
                                listOf(
                                    Color.White,
                                    Color(0xFFFFFBBD),
                                ),
                        ),
                ),
    )
}

/**
 * 顶部导航栏
 */
@Composable
private fun TopNavigationBar(onBackClick: () -> Unit) {
    Row(
        modifier =
            Modifier
                .fillMaxWidth()
                .height(44.dp)
                .background(Color.White)
                .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        IconButton(
            onClick = onBackClick,
            modifier = Modifier.size(24.dp),
        ) {
//            Icon(
//                imageVector = WakooIcons.ArrowLeftNav,
//                contentDescription = "返回",
//                tint = Color(0xFF111111)
//            )
        }

        Weight()

        Text(
            text = "开通VIP",
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF111111),
        )

        Weight()

        // 占位，保持标题居中
        Spacer(modifier = Modifier.size(24.dp))
    }
}

/**
 * 用户VIP信息卡片
 */
@Composable
internal fun UserVipCard(
    userName: String,
    expireDate: String,
    modifier: Modifier = Modifier,
    avatar: String? = null,
) {
    Box(
        modifier =
            modifier
                .fillMaxWidth()
                .height(120.dp)
                .background(
                    brush =
                        Brush.verticalGradient(
                            colors =
                                listOf(
                                    Color(0xFFFFFBD3),
                                    Color(0xFFFFE897),
                                ),
                        ),
                    shape = RoundedCornerShape(24.dp),
                ),
    ) {
        Row(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(24.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 用户头像
            if (avatar == null) {
                Image(
                    painter = painterResource(id = R.drawable.ic_app_logo),
                    contentDescription = "用户头像",
                    modifier =
                        Modifier
                            .size(56.dp)
                            .clip(CircleShape),
                    contentScale = ContentScale.Crop,
                )
            } else {
                NetworkImage(
                    avatar,
                    modifier = Modifier
                        .size(56.dp)
                        .clip(CircleShape),
                    contentScale = ContentScale.Crop
                )
            }

            SizeWidth(16.dp)

            // 用户信息
            Column(
                modifier = Modifier.weight(1f),
            ) {
                Text(
                    text = userName,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF674E0F),
                )

                SizeHeight(12.dp)

                Text(
                    text = expireDate,
                    fontSize = 12.sp,
                    color = Color(0xFF674E0F),
                )
            }

            // 皇冠图标
            Box(
                modifier = Modifier.size(72.dp),
            ) {
                // 背景皇冠（模糊效果）
                Image(
                    painter = painterResource(id = R.drawable.ic_crown),
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Fit,
                )

                // 主皇冠
                Image(
                    painter = painterResource(id = R.drawable.ic_crown),
                    contentDescription = "VIP皇冠",
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Fit,
                )
            }
        }
    }
}

/**
 * VIP权益卡片
 */
@Composable
internal fun VipBenefitsCard(modifier: Modifier = Modifier) {
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .background(
                    color = Color.White,
                    shape = RoundedCornerShape(12.dp),
                )
                .padding(16.dp),
    ) {
        // 标题
        Text(
            text = "4项会员权益",
            fontSize = 16.sp,
            fontWeight = FontWeight.Black,
            color = Color(0xFF674E0F),
        )

        SizeHeight(24.dp)

        // 权益列表
        Column(
            verticalArrangement = Arrangement.spacedBy(24.dp),
        ) {
            VipBenefitItem(
                icon = { VipCrownIconWithBg() },
                title = "尊贵VIP勋章",
                description = "点亮身份标识，尊贵一眼可见",
            )

            VipBenefitItem(
                icon = { DiamondIconWithBg() },
                title = "额外加赠xxx钻石",
                description = "会员专享赠送xxx钻石福利，开通即得",
            )

            VipBenefitItem(
                icon = { VipIconWithBg() },
                title = "会员限定声音背景",
                description = "解锁专属声音主题，个性由你定义",
            )

            VipBenefitItem(
                icon = { CustomerServiceIconWithBg() },
                title = "优先客服",
                description = "专属通道，快速响应更安心",
            )
        }
    }
}

/**
 * 单个权益项目
 */
@Composable
private fun VipBenefitItem(
    icon: @Composable () -> Unit,
    title: String,
    description: String,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
    ) {
        icon()

        SizeWidth(12.dp)

        Column(
            modifier = Modifier.weight(1f),
        ) {
            Text(
                text = title,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF674E0F),
            )

            SizeHeight(8.dp)

            Text(
                text = description,
                fontSize = 12.sp,
                color = Color(0xFF674E0F),
            )
        }
    }
}

/**
 * 带背景的VIP皇冠图标
 */
@Composable
private fun VipCrownIconWithBg() {
    Box(
        modifier =
            Modifier
                .size(48.dp)
                .background(
                    color = Color(0xFFFFFDE4),
                    shape = CircleShape,
                ),
        contentAlignment = Alignment.Center,
    ) {
//        Icon(
//            imageVector = WakooIcons.VipCrownIcon,
//            contentDescription = null,
//            modifier = Modifier.size(32.dp),
//            tint = Color.Unspecified
//        )
    }
}

/**
 * 带背景的钻石图标
 */
@Composable
private fun DiamondIconWithBg() {
    Box(
        modifier =
            Modifier
                .size(48.dp)
                .background(
                    color = Color(0xFFFFFDE4),
                    shape = CircleShape,
                ),
        contentAlignment = Alignment.Center,
    ) {
//        Icon(
//            imageVector = WakooIcons.DiamondMain,
//            contentDescription = null,
//            modifier = Modifier.size(34.dp),
//            tint = Color.Unspecified
//        )
    }
}

/**
 * 带背景的VIP图标
 */
@Composable
private fun VipIconWithBg() {
    Box(
        modifier =
            Modifier
                .size(48.dp)
                .background(
                    color = Color(0xFFFFFDE4),
                    shape = CircleShape,
                ),
        contentAlignment = Alignment.Center,
    ) {
//        Icon(
//            imageVector = WakooIcons.VipIcon,
//            contentDescription = null,
//            modifier = Modifier.size(32.dp),
//            tint = Color.Unspecified
//        )
    }
}

/**
 * 带背景的客服图标
 */
@Composable
private fun CustomerServiceIconWithBg() {
    Box(
        modifier =
            Modifier
                .size(48.dp)
                .background(
                    color = Color(0xFFFFFDE4),
                    shape = CircleShape,
                ),
        contentAlignment = Alignment.Center,
    ) {
//        Icon(
//            imageVector = WakooIcons.CustomerServiceIcon,
//            contentDescription = null,
//            modifier = Modifier.size(32.dp),
//            tint = Color.Unspecified
//        )
    }
}

/**
 * 价格选择区域
 */
@Composable
internal fun PricingSection(
    selectedPlan: VipPlanType,
    onPlanSelected: (VipPlanType) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
    ) {
        VipPlanType.values().forEach { plan ->
            PricingCard(
                plan = plan,
                isSelected = selectedPlan == plan,
                onClick = { onPlanSelected(plan) },
                modifier = Modifier.weight(1f),
            )
        }
    }
}

/**
 * 单个价格卡片
 */
@Composable
private fun PricingCard(
    plan: VipPlanType,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val backgroundColor = if (isSelected) Color.White else Color(0xFFFFFCDB)
    val borderColor = if (isSelected) Color(0xFFF5F7F9) else Color(0xFFFFBF48)
    val textColor = if (isSelected) Color(0xFF999999) else Color(0xFF674E0F)

    Column(
        modifier =
            modifier
                .height(120.dp)
                .background(
                    color = backgroundColor,
                    shape = RoundedCornerShape(12.dp),
                )
                .border(
                    width = if (isSelected) 0.5.dp else 2.dp,
                    color = borderColor,
                    shape = RoundedCornerShape(12.dp),
                )
                .clickable { onClick() }
                .padding(vertical = 40.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(12.dp),
    ) {
        Text(
            text = plan.displayName,
            fontSize = 16.sp,
            fontWeight = FontWeight.Black,
            color = textColor,
        )

        Text(
            text = plan.price,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            color = textColor,
            textAlign = TextAlign.Center,
        )
    }
}

@Composable
fun GoodsItem(name: String, price: String, isSelected: Boolean, onClick: () -> Unit) {
    val backgroundColor = if (isSelected) Color(0xFFFFFCDB) else Color.White
    val borderColor = if (isSelected) Color(0xFFFFBF48) else Color.White
    val textColor = if (isSelected) Color(0xFF674E0F) else Color(0xFF999999)

    Column(
        modifier =
            Modifier
                .width(106.dp)
                .height(120.dp)
                .clip(RoundedCornerShape(12.dp))
                .clickable(onClick = onClick)
                .background(
                    color = backgroundColor,
                    shape = RoundedCornerShape(12.dp),
                )
                .border(
                    width = 1.dp,
                    color = borderColor,
                    shape = RoundedCornerShape(12.dp),
                ),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        Text(
            text = name,
            fontSize = 16.sp,
            fontWeight = FontWeight.Black,
            color = textColor,
        )
        SizeHeight(12.dp)
        Text(
            text = price,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            color = textColor,
            textAlign = TextAlign.Center,
        )
    }

}

@Composable
fun BottomButton(text: String, modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .height(44.dp)
            .clip(RoundedCornerShape(50))
            then (modifier)
            .background(
                Brush.horizontalGradient(listOf(Color(0xFFFFF799), Color(0xFFFFE072))), RoundedCornerShape(50)
            ),
        contentAlignment = Alignment.Center
    ) {
        Text(text, color = Color(0xFF674E0F), fontSize = 16.sp)
    }
}

@Preview
@Composable
private fun ButtonPreview() {
    BottomButton("立即开通", modifier = Modifier.width(320.dp))
}

/**
 * 底部操作区域
 */
@Composable
private fun BottomActionArea(
    selectedPlan: VipPlanType,
    onPurchaseClick: () -> Unit,
    onRestorePurchaseClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier =
            modifier
                .fillMaxWidth()
                .height(150.dp)
                .background(
                    brush =
                        Brush.verticalGradient(
                            colors =
                                listOf(
                                    Color.White.copy(alpha = 0f),
                                    Color.White.copy(alpha = 0.6f),
                                    Color.White,
                                    Color.White,
                                ),
                        ),
                ),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 28.dp)
                    .padding(top = 16.dp),
        ) {
            // 购买按钮
            Button(
                onClick = onPurchaseClick,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(44.dp),
                colors =
                    ButtonDefaults.buttonColors(
                        containerColor = Color.Transparent,
                    ),
                shape = RoundedCornerShape(28.dp),
            ) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxSize()
                            .background(
                                brush =
                                    Brush.horizontalGradient(
                                        colors =
                                            listOf(
                                                Color(0xFFA3FF2C),
                                                Color(0xFF31FFA1),
                                                Color(0xFFFFE072),
                                                Color(0xFFFFF799),
                                            ),
                                    ),
                                shape = RoundedCornerShape(28.dp),
                            ),
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = "续费会员",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF674E0F),
                    )
                }
            }

            SizeHeight(16.dp)

            // 恢复购买和协议
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // 恢复购买
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.clickable { onRestorePurchaseClick() },
                ) {
                    Text(
                        text = "恢复购买",
                        fontSize = 12.sp,
                        color = Color(0xFF674E0F),
                    )

//                    Icon(
//                        imageVector = WakooIcons.ArrowRightSmall,
//                        contentDescription = null,
//                        modifier = Modifier.size(12.dp),
//                        tint = Color(0xFF674E0F)
//                    )
                }

                // 协议文本
                Text(
                    text = "开通即代表同意《会员协议》",
                    fontSize = 12.sp,
                    color = Color(0xFFB6B6B6),
                    textAlign = TextAlign.Center,
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun VipPagePreview() {
    WakooTheme {
        VipPage()
    }
}
