package com.buque.wakoo.ui.screens.japan.boost

import com.buque.wakoo.bean.boost.MissionInfo

sealed interface BoostPageWidgetData


data class InputInviteCodeBanner(val banner: String, val text: String) : BoostPageWidgetData

//男-钻石
data class DWidgetData(val ddCount: String) : BoostPageWidgetData

//女-积分
data class PWidgetData(val pCount: String, val showQA: Boolean, val showRecord: Boolean, val excTxt: String, val qaContent: String) :
    BoostPageWidgetData

//女-现金
data class CWidgetData(val cCount: String, val title: String, val buttonText: String, val link: String) : BoostPageWidgetData

data class Distribution(val link: String, val title: String, val content: String, val button: String) : BoostPageWidgetData

//签到
data class SignInWidgetData(val missionSeries: MissionInfo.MissionSeries) : BoostPageWidgetData

//任务
data class MissionWidgetData(val missionInfo: MissionInfo.MissionSeries) : BoostPageWidgetData