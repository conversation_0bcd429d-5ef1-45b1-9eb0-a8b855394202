package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.UserMale: ImageVector
    get() {
        val current = _userMale
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.UserMale",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // M15.05 8.54 18.59 5 H14 V3 h8 v8 h-2 V6.41 l-3.54 3.54 a7.5 7.5 0 1 1 -1.41 -1.41 M10.5 20 a5.5 5.5 0 1 0 0 -11 5.5 5.5 0 0 0 0 11
                path(
                    fill = SolidColor(Color(0xFF6AD4FF)),
                ) {
                    // M 15.05 8.54
                    moveTo(x = 15.05f, y = 8.54f)
                    // L 18.59 5
                    lineTo(x = 18.59f, y = 5.0f)
                    // H 14
                    horizontalLineTo(x = 14.0f)
                    // V 3
                    verticalLineTo(y = 3.0f)
                    // h 8
                    horizontalLineToRelative(dx = 8.0f)
                    // v 8
                    verticalLineToRelative(dy = 8.0f)
                    // h -2
                    horizontalLineToRelative(dx = -2.0f)
                    // V 6.41
                    verticalLineTo(y = 6.41f)
                    // l -3.54 3.54
                    lineToRelative(dx = -3.54f, dy = 3.54f)
                    // a 7.5 7.5 0 1 1 -1.41 -1.41
                    arcToRelative(
                        a = 7.5f,
                        b = 7.5f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = true,
                        dx1 = -1.41f,
                        dy1 = -1.41f,
                    )
                    // M 10.5 20
                    moveTo(x = 10.5f, y = 20.0f)
                    // a 5.5 5.5 0 1 0 0 -11
                    arcToRelative(
                        a = 5.5f,
                        b = 5.5f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        dx1 = 0.0f,
                        dy1 = -11.0f,
                    )
                    // a 5.5 5.5 0 0 0 0 11
                    arcToRelative(
                        a = 5.5f,
                        b = 5.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 0.0f,
                        dy1 = 11.0f,
                    )
                }
            }.build()
            .also { _userMale = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.UserMale,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _userMale: ImageVector? = null
