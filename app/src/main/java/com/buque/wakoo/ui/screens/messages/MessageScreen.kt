package com.buque.wakoo.ui.screens.messages

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Badge
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.bean.toBasic
import com.buque.wakoo.im_business.conversation.AppConversationManger
import com.buque.wakoo.im_business.conversation.C2CConversation
import com.buque.wakoo.im_business.conversation.TribeConversation
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.MessageTab
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.screens.messages.chat.ConversationScreen
import com.buque.wakoo.ui.screens.messages.notification.NotificationInfoScreen
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooSecondaryUnSelected
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults
import kotlinx.coroutines.launch

private val TABS: List<MessageTab> =
    listOf(
        MessageTab.IMMessage, // 关注
        MessageTab.Notification, // 粉丝
    )

@Composable
fun MessageScreen() {
    val pagerState = rememberPagerState(pageCount = { TABS.size })
    val selectedTabIndex = pagerState.currentPage
    val scope = rememberCoroutineScope()
    val controller = LocalAppNavController.current

    SegColorTitleScreenScaffold("", topBar = {
        TopAppBar(
            title = {
                TabRow(
                    selectedTabIndex = selectedTabIndex,
                    modifier =
                        Modifier.padding(
                            start = 30.dp,
                            end = 78.dp,
                        ),
                    divider = {},
                    containerColor = Color.Transparent,
                    indicator = { tabPositions ->
                        if (selectedTabIndex < tabPositions.size) {
                            Box(
                                modifier =
                                    Modifier
                                        .tabIndicatorOffset(tabPositions[selectedTabIndex])
                                        .requiredWidth(12.dp)
                                        .height(3.dp)
                                        .background(
                                            WakooSecondarySelected,
                                            CircleShape,
                                        ),
                            )
                        }
                    },
                ) {
                    TABS.forEachIndexed { index, tab ->
                        Tab(
                            selected = selectedTabIndex == index,
                            selectedContentColor = WakooSecondarySelected,
                            unselectedContentColor = WakooSecondaryUnSelected,
                            onClick = {
                                scope.launch {
                                    pagerState.animateScrollToPage(index)
                                }
                            },
                            content = {
                                Box(
                                    modifier =
                                        Modifier
                                            .padding(bottom = 4.dp),
                                    contentAlignment = Alignment.Center,
                                ) {
                                    tab.TabContent(
                                        selectedTabIndex == index,
                                        modifier = Modifier.padding(horizontal = 18.dp),
                                    )
                                    if (index == 0) {
                                        val unReadCount by AppConversationManger.unReadCountFlow.collectAsStateWithLifecycle()
//                                        val unReadCount = 100
                                        if (unReadCount > 0) {
                                            Badge(modifier = Modifier.align(Alignment.TopEnd)) {
                                                Text(
                                                    buildString {
                                                        append(if (unReadCount > 99) "99+" else unReadCount)
                                                    },
                                                    style =
                                                        TextStyle(
                                                            fontSize = 11.sp,
                                                            lineHeight = 16.sp,
                                                            fontWeight = FontWeight.Medium,
                                                            color = Color.White,
                                                        ),
                                                )
                                            }
                                        }
                                    }
                                }
                            },
                        )
                    }
                }
            },
            navigationIcon = WakooTitleBarDefaults.backIconNavigation(),
        )
    }) { pv ->
        HorizontalPager(pagerState, modifier = Modifier.padding(pv)) { index ->
            val page = TABS[selectedTabIndex]
            when (page) {
                MessageTab.IMMessage -> {
                    ConversationScreen {
                        when (it) {
                            is C2CConversation -> {
                                controller.push(Route.Chat(it.user.toBasic()))
                            }

                            is TribeConversation -> {
                                if (it is TribeConversation.Empty) {
                                    controller.push(Route.ChatGroupSquare)
                                } else if (it is TribeConversation.Instance) {
                                    controller.push(Route.ChatGroup(it.group.id))
                                }
                            }
                        }
                    }
                }

                MessageTab.Notification -> {
                    NotificationInfoScreen()
                }
            }
        }
    }
}
