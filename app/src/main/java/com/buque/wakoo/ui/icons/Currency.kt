package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.CurrencyCny: ImageVector
    get() {
        if (_CurrencyCnyFill != null) {
            return _CurrencyCnyFill!!
        }
        _CurrencyCnyFill = ImageVector.Builder(
            name = "CurrencyCnyFill",
            defaultWidth = 48.dp,
            defaultHeight = 48.dp,
            viewportWidth = 48f,
            viewportHeight = 48f
        ).apply {
            group(
                clipPathData = PathData {
                    moveTo(0f, 0f)
                    horizontalLineToRelative(48f)
                    verticalLineToRelative(48f)
                    horizontalLineToRelative(-48f)
                    close()
                }
            ) {
                path(
                    fill = SolidColor(Color(0xFF1BCD15)),
                    pathFillType = PathFillType.EvenOdd
                ) {
                    moveTo(24f, 4f)
                    curveTo(12.954f, 4f, 4f, 12.954f, 4f, 24f)
                    curveTo(4f, 35.046f, 12.954f, 44f, 24f, 44f)
                    curveTo(35.046f, 44f, 44f, 35.046f, 44f, 24f)
                    curveTo(44f, 12.954f, 35.046f, 4f, 24f, 4f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFE3FFE2))) {
                    moveTo(31.371f, 15.426f)
                    curveTo(31.236f, 15.201f, 31.057f, 15.005f, 30.846f, 14.849f)
                    curveTo(30.635f, 14.693f, 30.395f, 14.58f, 30.14f, 14.516f)
                    curveTo(29.885f, 14.453f, 29.62f, 14.44f, 29.36f, 14.479f)
                    curveTo(29.1f, 14.518f, 28.851f, 14.608f, 28.625f, 14.743f)
                    curveTo(28.4f, 14.879f, 28.204f, 15.058f, 28.048f, 15.269f)
                    lineTo(24f, 20.749f)
                    lineTo(19.952f, 15.269f)
                    curveTo(19.796f, 15.058f, 19.6f, 14.879f, 19.375f, 14.744f)
                    curveTo(19.149f, 14.608f, 18.9f, 14.519f, 18.64f, 14.479f)
                    curveTo(18.38f, 14.441f, 18.115f, 14.453f, 17.86f, 14.516f)
                    curveTo(17.605f, 14.58f, 17.365f, 14.693f, 17.154f, 14.849f)
                    curveTo(16.943f, 15.005f, 16.764f, 15.201f, 16.629f, 15.426f)
                    curveTo(16.493f, 15.651f, 16.403f, 15.901f, 16.365f, 16.161f)
                    curveTo(16.326f, 16.421f, 16.338f, 16.686f, 16.402f, 16.941f)
                    curveTo(16.465f, 17.196f, 16.578f, 17.436f, 16.734f, 17.647f)
                    lineTo(19.952f, 22.001f)
                    horizontalLineTo(18f)
                    curveTo(17.47f, 22.001f, 16.961f, 22.212f, 16.586f, 22.587f)
                    curveTo(16.211f, 22.962f, 16f, 23.471f, 16f, 24.001f)
                    curveTo(16f, 24.531f, 16.211f, 25.04f, 16.586f, 25.415f)
                    curveTo(16.961f, 25.79f, 17.47f, 26.001f, 18f, 26.001f)
                    horizontalLineTo(22f)
                    verticalLineTo(28.001f)
                    horizontalLineTo(18f)
                    curveTo(17.47f, 28.001f, 16.961f, 28.212f, 16.586f, 28.587f)
                    curveTo(16.211f, 28.962f, 16f, 29.471f, 16f, 30.001f)
                    curveTo(16f, 30.531f, 16.211f, 31.04f, 16.586f, 31.415f)
                    curveTo(16.961f, 31.79f, 17.47f, 32.001f, 18f, 32.001f)
                    horizontalLineTo(22f)
                    verticalLineTo(34.001f)
                    curveTo(22f, 34.531f, 22.211f, 35.04f, 22.586f, 35.415f)
                    curveTo(22.961f, 35.79f, 23.47f, 36.001f, 24f, 36.001f)
                    curveTo(24.53f, 36.001f, 25.039f, 35.79f, 25.414f, 35.415f)
                    curveTo(25.789f, 35.04f, 26f, 34.531f, 26f, 34.001f)
                    verticalLineTo(32.001f)
                    horizontalLineTo(30f)
                    curveTo(30.53f, 32.001f, 31.039f, 31.79f, 31.414f, 31.415f)
                    curveTo(31.789f, 31.04f, 32f, 30.531f, 32f, 30.001f)
                    curveTo(32f, 29.471f, 31.789f, 28.962f, 31.414f, 28.587f)
                    curveTo(31.039f, 28.212f, 30.53f, 28.001f, 30f, 28.001f)
                    horizontalLineTo(26f)
                    verticalLineTo(26.001f)
                    horizontalLineTo(30f)
                    curveTo(30.53f, 26.001f, 31.039f, 25.79f, 31.414f, 25.415f)
                    curveTo(31.789f, 25.04f, 32f, 24.531f, 32f, 24.001f)
                    curveTo(32f, 23.471f, 31.789f, 22.962f, 31.414f, 22.587f)
                    curveTo(31.039f, 22.212f, 30.53f, 22.001f, 30f, 22.001f)
                    horizontalLineTo(28.048f)
                    lineTo(31.266f, 17.647f)
                    curveTo(31.422f, 17.436f, 31.535f, 17.196f, 31.599f, 16.941f)
                    curveTo(31.662f, 16.686f, 31.675f, 16.421f, 31.636f, 16.161f)
                    curveTo(31.597f, 15.901f, 31.507f, 15.651f, 31.371f, 15.426f)
                    close()
                }
            }
        }.build()

        return _CurrencyCnyFill!!
    }

@Suppress("ObjectPropertyName")
private var _CurrencyCnyFill: ImageVector? = null
