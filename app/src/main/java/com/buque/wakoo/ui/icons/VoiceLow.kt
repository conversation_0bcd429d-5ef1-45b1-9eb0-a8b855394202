package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.VoiceLow: ImageVector
    get() {
        if (_VoiceLow != null) {
            return _VoiceLow!!
        }
        _VoiceLow =
            ImageVector
                .Builder(
                    name = "VoiceLow",
                    defaultWidth = 20.dp,
                    defaultHeight = 20.dp,
                    viewportWidth = 20f,
                    viewportHeight = 20f,
                ).apply {
                    path(fill = SolidColor(Color(0xFF111111))) {
                        moveTo(5.944f, 12.323f)
                        curveTo(6.249f, 12.018f, 6.491f, 11.656f, 6.657f, 11.257f)
                        curveTo(6.822f, 10.858f, 6.907f, 10.431f, 6.907f, 9.999f)
                        curveTo(6.907f, 9.568f, 6.822f, 9.141f, 6.657f, 8.742f)
                        curveTo(6.491f, 8.343f, 6.249f, 7.981f, 5.944f, 7.676f)
                        lineTo(3.621f, 9.999f)
                        lineTo(5.944f, 12.323f)
                        close()
                    }
                }.build()

        return _VoiceLow!!
    }

@Suppress("ObjectPropertyName")
private var _VoiceLow: ImageVector? = null
