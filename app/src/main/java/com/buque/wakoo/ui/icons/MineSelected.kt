package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.MineSelected: ImageVector
    get() {
        val current = _mineSelected
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.MineSelected",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // M12 22 a10 10 0 1 1 0 -20 10 10 0 0 1 0 20 M7 12 a5 5 0 0 0 10 0 h-2 a3 3 0 1 1 -6 0z
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 12 22
                    moveTo(x = 12.0f, y = 22.0f)
                    // a 10 10 0 1 1 0 -20
                    arcToRelative(
                        a = 10.0f,
                        b = 10.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = -20.0f,
                    )
                    // a 10 10 0 0 1 0 20
                    arcToRelative(
                        a = 10.0f,
                        b = 10.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = 20.0f,
                    )
                    // M 7 12
                    moveTo(x = 7.0f, y = 12.0f)
                    // a 5 5 0 0 0 10 0
                    arcToRelative(
                        a = 5.0f,
                        b = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 10.0f,
                        dy1 = 0.0f,
                    )
                    // h -2
                    horizontalLineToRelative(dx = -2.0f)
                    // a 3 3 0 1 1 -6 0z
                    arcToRelative(
                        a = 3.0f,
                        b = 3.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = true,
                        dx1 = -6.0f,
                        dy1 = 0.0f,
                    )
                    close()
                }
            }.build()
            .also { _mineSelected = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.MineSelected,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _mineSelected: ImageVector? = null
