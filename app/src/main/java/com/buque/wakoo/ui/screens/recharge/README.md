# 充值钻石功能

这个模块实现了完整的钻石充值功能，包括充值页面、充值记录页面和相关的数据模型。

## 文件结构

```
recharge/
├── RechargeScreen.kt        # 主要的充值页面
├── RechargeRecordScreen.kt  # 充值记录页面
├── RechargeDemo.kt          # 演示和集成示例
└── README.md               # 说明文档

bean/
└── RechargeData.kt         # 充值相关数据模型
```

## 核心功能

### 1. 充值页面 (RechargeScreen)

- **钻石余额显示**：渐变卡片展示当前钻石余额
- **充值套餐选择**：6个预设充值套餐，3列2行网格布局
- **套餐选择状态**：选中状态有绿色边框和背景
- **立即充值按钮**：渐变按钮，选择套餐后激活
- **充值协议**：底部协议文字说明

### 2. 充值记录页面 (RechargeRecordScreen)

- **记录列表**：显示所有钻石变动记录
- **记录分类**：充值、消费、赠送、退款等类型
- **时间显示**：格式化的时间显示
- **数量变化**：正数绿色，负数红色
- **空状态**：无记录时的友好提示

### 3. 数据模型

- **RechargePackage**：充值套餐信息
- **DiamondBalance**：用户钻石余额
- **RechargeRecord**：充值记录
- **RechargeOrder**：充值订单

## 设计规范

### 颜色使用

- **主要渐变**：A3FF2C → 31FFA1 (充值按钮)
- **余额卡片渐变**：F3FFD3 → ADFFF1
- **选中状态**：E8FFE9 背景 + 66FE6B 边框
- **钻石绿色**：0B570E
- **背景色**：F5F7F9

### 字体样式

- **页面标题**：18sp, Medium
- **余额数字**：20sp, Black
- **套餐钻石数**：16sp, Black
- **套餐价格**：12sp, Medium
- **按钮文字**：16sp, Medium

### 间距规范

- **页面边距**：16dp
- **卡片圆角**：12dp, 24dp
- **按钮圆角**：28dp
- **套餐间距**：8dp (水平), 12dp (垂直)

## 预设充值套餐

| 钻石数量  | 价格    | 套餐ID              |
|-------|-------|-------------------|
| 300   | ￥30   | package_300       |
| 1980  | ￥198  | package_1980      |
| 5880  | ￥588  | package_5880      |
| 9980  | ￥998  | package_9980 (热门) |
| 19980 | ￥1998 | package_19980     |
| 99980 | ￥9998 | package_99980     |

## 使用方法

### 1. 基本使用

```kotlin
@Composable
fun MyRechargeScreen() {
    RechargeScreen(
        diamondBalance = DiamondBalance(balance = 99999),
        rechargePackages = RechargePackages.defaultPackages,
        onBackClick = { /* 处理返回 */ },
        onRecordClick = { /* 跳转到记录页面 */ },
        onRechargeClick = { package -> /* 处理充值 */ }
    )
}
```

### 2. 集成到导航系统

```kotlin
// 在 Route.kt 中添加路由
@Serializable
data object Recharge : NavKey, RequiresLogin

@Serializable
data object RechargeRecord : NavKey, RequiresLogin

// 在导航处理中添加
when (currentRoute) {
    is Route.Recharge -> {
        RechargeScreen(
            diamondBalance = userDiamondBalance,
            onBackClick = { navController.popBackStack() },
            onRecordClick = { navController.navigate(Route.RechargeRecord) },
            onRechargeClick = { package -> handleRecharge(package) }
        )
    }
    is Route.RechargeRecord -> {
        RechargeRecordScreen(
            records = userRechargeRecords,
            onBackClick = { navController.popBackStack() }
        )
    }
}
```

### 3. 个人中心集成

```kotlin
// 在个人中心添加钻石余额入口
Row(
    modifier = Modifier
        .fillMaxWidth()
        .clickable { navController.navigate(Route.Recharge) }
        .padding(16.dp),
    verticalAlignment = Alignment.CenterVertically
) {
    Image(
        painter = painterResource(id = R.drawable.ic_green_diamond),
        contentDescription = null,
        modifier = Modifier.size(24.dp)
    )
    
    SizeWidth(12.dp)
    
    Column(modifier = Modifier.weight(1f)) {
        Text(text = "钻石余额")
        Text(
            text = userDiamondBalance.formattedBalance,
            color = Color(0xFF0B570E),
            fontWeight = FontWeight.Bold
        )
    }
    
    Text(
        text = "充值",
        color = Color(0xFF66FE6B)
    )
}
```

## 支付集成

### 支付流程

1. **选择套餐**：用户选择充值套餐
2. **创建订单**：调用后端API创建充值订单
3. **调用支付**：集成支付SDK进行支付
4. **支付回调**：处理支付结果
5. **更新余额**：支付成功后更新用户钻石余额
6. **记录保存**：保存充值记录

### 支付处理示例

```kotlin
fun handleRecharge(package: RechargePackage) {
    // 1. 显示加载状态
    showLoading()
    
    // 2. 创建充值订单
    viewModelScope.launch {
        try {
            val order = rechargeRepository.createOrder(package)
            
            // 3. 调用支付
            PaymentManager.pay(
                context = context,
                amount = package.price,
                orderId = order.orderId,
                productName = "${package.diamonds}钻石",
                onSuccess = { payResult ->
                    // 支付成功
                    handlePaymentSuccess(order, payResult)
                },
                onFailure = { error ->
                    // 支付失败
                    handlePaymentFailure(error)
                },
                onCancel = {
                    // 用户取消支付
                    hideLoading()
                }
            )
        } catch (e: Exception) {
            handleError(e)
        }
    }
}

private fun handlePaymentSuccess(order: RechargeOrder, payResult: PayResult) {
    viewModelScope.launch {
        try {
            // 验证支付结果
            val verified = rechargeRepository.verifyPayment(order.orderId, payResult)
            if (verified) {
                // 更新用户钻石余额
                userRepository.updateDiamondBalance(order.diamonds)
                
                // 保存充值记录
                rechargeRepository.saveRecord(
                    RechargeRecord(
                        id = UUID.randomUUID().toString(),
                        description = "充值钻石",
                        diamonds = order.diamonds,
                        timestamp = DateTimeUtils.currentTimeMillis(),
                        type = RechargeRecordType.RECHARGE
                    )
                )
                
                // 显示成功提示
                showSuccessMessage("充值成功！")
            } else {
                showErrorMessage("支付验证失败，请联系客服")
            }
        } catch (e: Exception) {
            showErrorMessage("充值处理失败：${e.message}")
        } finally {
            hideLoading()
        }
    }
}
```

## 图片资源

需要的图片资源：

- `ic_green_diamond.png` - 钻石图标（已存在）
- `ic_green_diamond_large.png` - 大钻石图标（余额卡片用）
- `ic_green_diamond_small.png` - 小钻石图标（套餐卡片用）

图片规格：

- 使用PNG格式
- 3倍图 (xxhdpi)
- 透明背景

## 注意事项

1. **支付安全**：
    - 所有支付操作需要后端验证
    - 订单状态需要实时同步
    - 支付结果需要二次验证

2. **用户体验**：
    - 支付过程中显示加载状态
    - 支付失败时给出明确提示
    - 支付成功后及时更新余额显示

3. **数据一致性**：
    - 充值记录与余额变化保持一致
    - 支持离线状态下的数据同步
    - 异常情况下的数据回滚

4. **合规要求**：
    - 充值协议链接需要指向真实协议页面
    - 支付金额需要符合相关法规
    - 充值记录需要支持导出功能

## 扩展功能

可以考虑添加的功能：

- 充值优惠活动
- 首充双倍奖励
- VIP等级充值折扣
- 充值返利活动
- 钻石消费统计
- 充值限额设置
- 家长控制功能

## 测试

运行演示页面：

```kotlin
// 充值页面预览
@Preview
@Composable
fun RechargeDemoPreview() {
    WakooTheme {
        RechargeDemo()
    }
}

// 充值记录页面预览
@Preview
@Composable
fun RechargeRecordDemoPreview() {
    WakooTheme {
        RechargeRecordDemo()
    }
}
```
