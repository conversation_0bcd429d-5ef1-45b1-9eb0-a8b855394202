package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.Admin: ImageVector
    get() {
        if (_Admin != null) {
            return _Admin!!
        }
        _Admin = ImageVector.Builder(
            name = "Admin",
            defaultWidth = 12.dp,
            defaultHeight = 12.dp,
            viewportWidth = 12f,
            viewportHeight = 12f
        ).apply {
            path(fill = SolidColor(Color(0xFF1A7D1D))) {
                moveTo(1f, 11f)
                curveTo(1f, 8.791f, 2.791f, 7f, 5f, 7f)
                curveTo(7.209f, 7f, 9f, 8.791f, 9f, 11f)
                horizontalLineTo(1f)
                close()
                moveTo(5f, 6.5f)
                curveTo(3.342f, 6.5f, 2f, 5.157f, 2f, 3.5f)
                curveTo(2f, 1.842f, 3.342f, 0.5f, 5f, 0.5f)
                curveTo(6.657f, 0.5f, 8f, 1.842f, 8f, 3.5f)
                curveTo(8f, 5.157f, 6.657f, 6.5f, 5f, 6.5f)
                close()
                moveTo(8.681f, 7.617f)
                curveTo(10.224f, 8.011f, 11.384f, 9.362f, 11.492f, 11f)
                horizontalLineTo(10f)
                curveTo(10f, 9.695f, 9.5f, 8.507f, 8.681f, 7.617f)
                close()
                moveTo(7.67f, 6.478f)
                curveTo(8.486f, 5.746f, 9f, 4.683f, 9f, 3.5f)
                curveTo(9f, 2.791f, 8.816f, 2.126f, 8.492f, 1.548f)
                curveTo(9.638f, 1.777f, 10.5f, 2.787f, 10.5f, 4f)
                curveTo(10.5f, 5.381f, 9.381f, 6.5f, 8f, 6.5f)
                curveTo(7.888f, 6.5f, 7.778f, 6.493f, 7.67f, 6.478f)
                close()
            }
        }.build()

        return _Admin!!
    }

@Suppress("ObjectPropertyName")
private var _Admin: ImageVector? = null
