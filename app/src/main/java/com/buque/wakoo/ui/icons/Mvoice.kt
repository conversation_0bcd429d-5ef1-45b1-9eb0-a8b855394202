package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.MicVoice: ImageVector
    get() {
        val current = _micVoice
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.MicVoice",
                defaultWidth = 32.0.dp,
                defaultHeight = 32.0.dp,
                viewportWidth = 32.0f,
                viewportHeight = 32.0f,
            ).apply {
                group(
                    // M 0 0 H 32 V 32 H 0z
                    clipPathData =
                        PathData {
                            // M 0 0
                            moveTo(x = 0.0f, y = 0.0f)
                            // H 32
                            horizontalLineTo(x = 32.0f)
                            // V 32
                            verticalLineTo(y = 32.0f)
                            // H 0z
                            horizontalLineTo(x = 0.0f)
                            close()
                        },
                ) {
                    // m27.29 10.26 .33 -.76 a5.8 5.8 0 0 1 2.96 -3 l1 -.45 a.7 .7 0 0 0 0 -1.28 l-.95 -.43 a5.8 5.8 0 0 1 -3 -3.1 L27.3 .43 a.68 .68 0 0 0 -1.25 0 L25.7 1.24 a5.8 5.8 0 0 1 -3 3.1 l-.96 .43 a.7 .7 0 0 0 0 1.28 l1.02 .45 a5.8 5.8 0 0 1 2.96 3 l.32 .76 a.67 .67 0 0 0 1.25 0 m-8.1 -3.14 a3.3 3.3 0 0 0 1.5 1.28 l.75 .33 a3 3 0 0 1 1.23 .93 v3.67 a6.67 6.67 0 0 1 -13.34 0 V8 A6.67 6.67 0 0 1 20 2.66 a3.3 3.3 0 0 0 -1.33 2.67 3.3 3.3 0 0 0 .51 1.79 M4.06 14.67 h2.7 a9.34 9.34 0 0 0 18.47 0 h2.69 a12 12 0 0 1 -10.6 10.59 v5.4 h-2.66 v-5.4 a12 12 0 0 1 -10.6 -10.6
                    path(
                        fill = SolidColor(Color(0xFF111111)),
                    ) {
                        // M 27.29 10.26
                        moveTo(x = 27.29f, y = 10.26f)
                        // l 0.33 -0.76
                        lineToRelative(dx = 0.33f, dy = -0.76f)
                        // a 5.8 5.8 0 0 1 2.96 -3
                        arcToRelative(
                            a = 5.8f,
                            b = 5.8f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = true,
                            dx1 = 2.96f,
                            dy1 = -3.0f,
                        )
                        // l 1 -0.45
                        lineToRelative(dx = 1.0f, dy = -0.45f)
                        // a 0.7 0.7 0 0 0 0 -1.28
                        arcToRelative(
                            a = 0.7f,
                            b = 0.7f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = false,
                            dx1 = 0.0f,
                            dy1 = -1.28f,
                        )
                        // l -0.95 -0.43
                        lineToRelative(dx = -0.95f, dy = -0.43f)
                        // a 5.8 5.8 0 0 1 -3 -3.1
                        arcToRelative(
                            a = 5.8f,
                            b = 5.8f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = true,
                            dx1 = -3.0f,
                            dy1 = -3.1f,
                        )
                        // L 27.3 0.43
                        lineTo(x = 27.3f, y = 0.43f)
                        // a 0.68 0.68 0 0 0 -1.25 0
                        arcToRelative(
                            a = 0.68f,
                            b = 0.68f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = false,
                            dx1 = -1.25f,
                            dy1 = 0.0f,
                        )
                        // L 25.7 1.24
                        lineTo(x = 25.7f, y = 1.24f)
                        // a 5.8 5.8 0 0 1 -3 3.1
                        arcToRelative(
                            a = 5.8f,
                            b = 5.8f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = true,
                            dx1 = -3.0f,
                            dy1 = 3.1f,
                        )
                        // l -0.96 0.43
                        lineToRelative(dx = -0.96f, dy = 0.43f)
                        // a 0.7 0.7 0 0 0 0 1.28
                        arcToRelative(
                            a = 0.7f,
                            b = 0.7f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = false,
                            dx1 = 0.0f,
                            dy1 = 1.28f,
                        )
                        // l 1.02 0.45
                        lineToRelative(dx = 1.02f, dy = 0.45f)
                        // a 5.8 5.8 0 0 1 2.96 3
                        arcToRelative(
                            a = 5.8f,
                            b = 5.8f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = true,
                            dx1 = 2.96f,
                            dy1 = 3.0f,
                        )
                        // l 0.32 0.76
                        lineToRelative(dx = 0.32f, dy = 0.76f)
                        // a 0.67 0.67 0 0 0 1.25 0
                        arcToRelative(
                            a = 0.67f,
                            b = 0.67f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = false,
                            dx1 = 1.25f,
                            dy1 = 0.0f,
                        )
                        // m -8.1 -3.14
                        moveToRelative(dx = -8.1f, dy = -3.14f)
                        // a 3.3 3.3 0 0 0 1.5 1.28
                        arcToRelative(
                            a = 3.3f,
                            b = 3.3f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = false,
                            dx1 = 1.5f,
                            dy1 = 1.28f,
                        )
                        // l 0.75 0.33
                        lineToRelative(dx = 0.75f, dy = 0.33f)
                        // a 3 3 0 0 1 1.23 0.93
                        arcToRelative(
                            a = 3.0f,
                            b = 3.0f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = true,
                            dx1 = 1.23f,
                            dy1 = 0.93f,
                        )
                        // v 3.67
                        verticalLineToRelative(dy = 3.67f)
                        // a 6.67 6.67 0 0 1 -13.34 0
                        arcToRelative(
                            a = 6.67f,
                            b = 6.67f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = true,
                            dx1 = -13.34f,
                            dy1 = 0.0f,
                        )
                        // V 8
                        verticalLineTo(y = 8.0f)
                        // A 6.67 6.67 0 0 1 20 2.66
                        arcTo(
                            horizontalEllipseRadius = 6.67f,
                            verticalEllipseRadius = 6.67f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = true,
                            x1 = 20.0f,
                            y1 = 2.66f,
                        )
                        // a 3.3 3.3 0 0 0 -1.33 2.67
                        arcToRelative(
                            a = 3.3f,
                            b = 3.3f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = false,
                            dx1 = -1.33f,
                            dy1 = 2.67f,
                        )
                        // a 3.3 3.3 0 0 0 0.51 1.79
                        arcToRelative(
                            a = 3.3f,
                            b = 3.3f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = false,
                            dx1 = 0.51f,
                            dy1 = 1.79f,
                        )
                        // M 4.06 14.67
                        moveTo(x = 4.06f, y = 14.67f)
                        // h 2.7
                        horizontalLineToRelative(dx = 2.7f)
                        // a 9.34 9.34 0 0 0 18.47 0
                        arcToRelative(
                            a = 9.34f,
                            b = 9.34f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = false,
                            dx1 = 18.47f,
                            dy1 = 0.0f,
                        )
                        // h 2.69
                        horizontalLineToRelative(dx = 2.69f)
                        // a 12 12 0 0 1 -10.6 10.59
                        arcToRelative(
                            a = 12.0f,
                            b = 12.0f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = true,
                            dx1 = -10.6f,
                            dy1 = 10.59f,
                        )
                        // v 5.4
                        verticalLineToRelative(dy = 5.4f)
                        // h -2.66
                        horizontalLineToRelative(dx = -2.66f)
                        // v -5.4
                        verticalLineToRelative(dy = -5.4f)
                        // a 12 12 0 0 1 -10.6 -10.6
                        arcToRelative(
                            a = 12.0f,
                            b = 12.0f,
                            theta = 0.0f,
                            isMoreThanHalf = false,
                            isPositiveArc = true,
                            dx1 = -10.6f,
                            dy1 = -10.6f,
                        )
                    }
                }
            }.build()
            .also { _micVoice = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.MicVoice,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((32.0).dp)
                        .height((32.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _micVoice: ImageVector? = null
