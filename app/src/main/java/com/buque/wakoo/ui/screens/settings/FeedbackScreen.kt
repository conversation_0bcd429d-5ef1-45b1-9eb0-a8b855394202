package com.buque.wakoo.ui.screens.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.buque.wakoo.ext.onRootPopInvoker
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.api.service.SettingsApiService
import com.buque.wakoo.network.executeApiCall
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.ImeButtonScaffold
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SolidButton

/**
 * 反馈与建议页面
 * 用户可以提交反馈内容和联系方式
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FeedbackScreen(onSubmitClick: (feedback: String, contact: String) -> Unit = { _, _ -> }) {
    var feedbackContent by remember { mutableStateOf("") }
    var contactInfo by remember { mutableStateOf("") }

    val userApiService =
        remember {
            ApiClient.createuserApiService<SettingsApiService>()
        }
    val scope = rememberCoroutineScope()
    val rootPopInvoker = onRootPopInvoker

    val loading = LocalLoadingManager.current

    SegColorTitleScreenScaffold(title = "反馈与建议") { paddingValues ->

        ImeButtonScaffold(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
            buttonModifier =
                Modifier
                    .fillMaxWidth()
                    .background(Color(0xFFF7F7F7))
                    .padding(
                        top = 20.dp,
                        bottom = 24.dp,
                    ),
            buttonContent = {
                SolidButton(
                    text = "提交",
                    onClick = {
                        loading.show(scope) {
                            executeApiCall {
                                userApiService.postFeedback(
                                    buildMap {
                                        put("content", feedbackContent)
                                        put("email", contactInfo)
                                    },
                                )
                            }.onSuccess {
                                rootPopInvoker(Route.Feedback)
                            }
                        }
                    },
                    modifier =
                        Modifier
                            .padding(horizontal = 28.dp)
                            .fillMaxWidth(),
                    enabled = feedbackContent.isNotBlank(),
                )
            },
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
            ) {
                SizeHeight(4.dp)

                Text(
                    text = "反馈内容",
                    style = MaterialTheme.typography.labelLarge,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF111111),
                )

                AppTextField(
                    value = feedbackContent,
                    onValueChange = {
                        feedbackContent = it
                    },
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(240.dp),
                    placeholder = "请提出您宝贵的意见或建议反馈。内容将发送官方团队邮箱，我们将用心查看每一条内容，并优化我们的产品",
                    maxLength = 500,
                )

                Text(
                    text = "联系方式",
                    fontWeight = FontWeight.Medium,
                    style = MaterialTheme.typography.labelLarge,
                    color = Color(0xFF111111),
                )

                AppTextField(
                    value = contactInfo,
                    onValueChange = {
                        contactInfo = it
                    },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = "请留下您的邮箱/line号",
                    singleLine = true,
                )

                SizeHeight(16.dp)
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun FeedbackPagePreview() {
    WakooTheme {
        FeedbackScreen()
    }
}
