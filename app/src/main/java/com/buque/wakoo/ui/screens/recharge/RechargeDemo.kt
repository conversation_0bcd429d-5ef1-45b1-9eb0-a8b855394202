package com.buque.wakoo.ui.screens.recharge

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.buque.wakoo.bean.DiamondBalance
import com.buque.wakoo.bean.RechargePackages
import com.buque.wakoo.bean.RechargeRecord
import com.buque.wakoo.bean.RechargeRecordType
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.utils.DateTimeUtils
import kotlinx.coroutines.launch

/**
 * 充值功能演示
 *
 * 这个演示展示了如何使用充值相关的组件。
 * 在实际项目中，你可以将这些页面集成到导航系统中。
 *
 * 使用方法：
 * 1. 在导航中添加 Recharge 和 RechargeRecord 路由
 * 2. 在个人中心或其他入口添加充值按钮
 * 3. 处理充值支付逻辑
 * 4. 更新用户钻石余额
 */
@Composable
fun RechargeDemo() {
    val snackbarHostState = remember { SnackbarHostState() }
    val scope = rememberCoroutineScope()

    // 模拟用户钻石余额
    val diamondBalance =
        DiamondBalance(
            balance = 99999,
            totalRecharged = 199960,
            totalConsumed = 99961,
        )

    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) },
    ) { paddingValues ->
        Box(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
        ) {
            RechargeContent(
                diamondBalance = diamondBalance,
                rechargePackages = RechargePackages.defaultPackages,
                onRecordClick = {
                    scope.launch {
                        snackbarHostState.showSnackbar("跳转到充值记录页面")
                    }
                },
                onRechargeClick = { selectedPackage ->
                    scope.launch {
                        snackbarHostState.showSnackbar(
                            "选择充值套餐：${selectedPackage.formattedDiamonds}钻石 ${selectedPackage.formattedPrice}",
                        )
                    }
                },
            )
        }
    }
}

/**
 * 充值记录演示
 */
@Composable
fun RechargeRecordDemo() {
    val snackbarHostState = remember { SnackbarHostState() }
    val scope = rememberCoroutineScope()

    // 模拟充值记录数据
    val records =
        listOf(
            RechargeRecord(
                id = "1",
                description = "充值钻石",
                diamonds = 9980,
                timestamp = DateTimeUtils.currentTimeMillis(),
                type = RechargeRecordType.RECHARGE,
            ),
            RechargeRecord(
                id = "2",
                description = "发送礼物给小明",
                diamonds = -100,
                timestamp = DateTimeUtils.currentTimeMillis() - 3600000,
                type = RechargeRecordType.CONSUME,
            ),
            RechargeRecord(
                id = "3",
                description = "新用户注册奖励",
                diamonds = 50,
                timestamp = DateTimeUtils.currentTimeMillis() - 7200000,
                type = RechargeRecordType.GIFT,
            ),
            RechargeRecord(
                id = "4",
                description = "充值钻石",
                diamonds = 1980,
                timestamp = DateTimeUtils.currentTimeMillis() - 86400000,
                type = RechargeRecordType.RECHARGE,
            ),
            RechargeRecord(
                id = "5",
                description = "发送语音礼物",
                diamonds = -50,
                timestamp = DateTimeUtils.currentTimeMillis() - 172800000,
                type = RechargeRecordType.CONSUME,
            ),
        )

    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) },
    ) { paddingValues ->
        Box(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
        ) {

        }
    }
}

/**
 * 集成到项目的示例代码
 *
 * 在个人中心页面中添加充值入口：
 *
 * ```kotlin
 * // 在个人中心页面中
 * Row(
 *     modifier = Modifier
 *         .fillMaxWidth()
 *         .clickable { navController.navigate(Route.Recharge) }
 *         .padding(16.dp),
 *     verticalAlignment = Alignment.CenterVertically
 * ) {
 *     Image(
 *         painter = painterResource(id = R.drawable.ic_green_diamond),
 *         contentDescription = null,
 *         modifier = Modifier.size(24.dp)
 *     )
 *
 *     SizeWidth(12.dp)
 *
 *     Column(modifier = Modifier.weight(1f)) {
 *         Text(text = "钻石余额")
 *         Text(text = "${userDiamondBalance}", color = Color.Green)
 *     }
 *
 *     Text(text = "充值", color = Color.Blue)
 * }
 * ```
 *
 * 在主导航控制器中添加路由处理：
 *
 * ```kotlin
 * when (currentRoute) {
 *     is Route.Recharge -> {
 *         RechargeScreen(
 *             diamondBalance = userDiamondBalance,
 *             onBackClick = { navController.popBackStack() },
 *             onRecordClick = { navController.navigate(Route.RechargeRecord) },
 *             onRechargeClick = { package ->
 *                 // 处理充值逻辑
 *                 handleRecharge(package)
 *             }
 *         )
 *     }
 *     is Route.RechargeRecord -> {
 *         RechargeRecordScreen(
 *             records = userRechargeRecords,
 *             onBackClick = { navController.popBackStack() }
 *         )
 *     }
 *     // ... 其他路由
 * }
 * ```
 *
 * 充值支付处理示例：
 *
 * ```kotlin
 * fun handleRecharge(package: RechargePackage) {
 *     // 1. 创建充值订单
 *     val order = createRechargeOrder(package)
 *
 *     // 2. 调用支付SDK
 *     PaymentSDK.pay(
 *         amount = package.price,
 *         orderId = order.orderId,
 *         onSuccess = {
 *             // 支付成功，更新用户钻石余额
 *             updateUserDiamondBalance(package.diamonds)
 *             showSuccessMessage()
 *         },
 *         onFailure = { error ->
 *             // 支付失败处理
 *             showErrorMessage(error)
 *         }
 *     )
 * }
 * ```
 */

@Preview(showBackground = true)
@Composable
private fun RechargeDemoPreview() {
    WakooTheme {
        RechargeDemo()
    }
}

@Preview(showBackground = true)
@Composable
private fun RechargeRecordDemoPreview() {
    WakooTheme {
        RechargeRecordDemo()
    }
}
