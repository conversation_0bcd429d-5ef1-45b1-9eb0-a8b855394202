package com.buque.wakoo.ui.widget.coordinatorlayout

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.Button
import androidx.compose.material3.Checkbox
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ListItem
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.Weight

@OptIn(ExperimentalMaterial3Api::class)
@Composable
@Preview
private fun CustomCollapsibleHeaderSample() {
    // 1. 创建并记住我们自己的 state
    val headerState = rememberCustomCollapsibleHeaderState()

    // 3. (可选) 创建一个开关来控制吸附动画
    val (enableSnap, setEnableSnap) = remember { mutableStateOf(false) }

    // 2. 创建 scrollBehavior，并将其内部的 state 替换为我们自己的 state
    // 这是关键的协作步骤！
    val scrollBehavior =
        rememberCustomCollapsibleScrollBehavior(
            state = headerState.topAppBarState,
            enableSnap = enableSnap,
        )

    Scaffold(
        modifier = Modifier.nestedScroll(scrollBehavior.nestedScrollConnection),
        topBar = {
            Surface(color = MaterialTheme.colorScheme.surface, shadowElevation = 4.dp) {
                // 4. 将新的 state 和开关传入组件
                CustomCollapsibleHeader(
                    state = headerState,
                    scrollBehavior = scrollBehavior,
                    collapsedContent = {
                        // --- 完全自定义的收起状态内容 ---
                        Row(
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .height(64.dp) // 典型的小 TopAppBar 高度
                                    .padding(horizontal = 8.dp),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            IconButton(onClick = { /*TODO*/ }) {
                                Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                            }
                            SizeWidth(8.dp)
                            Text("Profile", fontSize = 20.sp, fontWeight = FontWeight.SemiBold)
                            Weight()
                            IconButton(onClick = { /*TODO*/ }) {
                                Icon(Icons.Default.MoreVert, contentDescription = "More")
                            }
                        }
                    },
                    expandedContent = {
                        // --- 完全自定义的展开状态内容 ---
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalAlignment = Alignment.CenterHorizontally,
                        ) {
                            // 顶部留出与收起状态一致的导航栏空间
                            Box(modifier = Modifier.height(64.dp))
                            Image(
                                painter = painterResource(id = R.drawable.ic_app_logo), // 替换为你的图片
                                contentDescription = "Profile Picture",
                                modifier =
                                    Modifier
                                        .size(120.dp)
                                        .background(Color.Gray, CircleShape),
                                contentScale = ContentScale.Crop,
                            )
                            SizeHeight(16.dp)
                            Text(
                                "Android Developer",
                                fontSize = 24.sp,
                                fontWeight = FontWeight.Bold,
                            )
                            Text(
                                "@androiddev",
                                fontSize = 16.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                            )
                            SizeHeight(24.dp)
                        }
                    },
                )
            }
        },
    ) { paddingValues ->
        Column(modifier = Modifier.padding(paddingValues)) {
            // 控制按钮
            Row(
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceEvenly,
            ) {
                Button(onClick = { headerState.expand() }) { Text("Expand") }
                Button(onClick = { headerState.collapse() }) { Text("Collapse") }
            }
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier =
                    Modifier
                        .clickable { setEnableSnap(!enableSnap) }
                        .padding(16.dp),
            ) {
                Checkbox(checked = enableSnap, onCheckedChange = { setEnableSnap(it) })
                Text("Enable Snap Animation")
            }
            Text(
                // 实时显示折叠进度
                text = "Collapsed Fraction: ${"%.2f".format(headerState.collapsedFraction)}",
                modifier = Modifier.padding(horizontal = 16.dp),
            )

            // 滚动内容
            LazyColumn {
                items(100) {
                    ListItem(
                        headlineContent = { Text("Item $it") },
                        supportingContent = { Text("This is some description for item $it") },
                    )
                }
            }
        }
    }
}
