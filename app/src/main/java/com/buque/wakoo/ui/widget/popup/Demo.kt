package com.buque.wakoo.ui.widget.popup

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreVert // 导入三个点图标
import androidx.compose.material3.DropdownMenu // 导入 DropdownMenu
import androidx.compose.material3.DropdownMenuItem // 导入 DropdownMenuItem
import androidx.compose.material3.Icon // 导入 Icon
import androidx.compose.material3.Text // 导入 Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.buque.wakoo.ui.widget.Weight

@Composable
fun MoreOptionsPopupExample() {
    // 1. 管理弹出菜单的显示/隐藏状态
    var showMenu by remember { mutableStateOf(false) }

    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .wrapContentSize(Alignment.TopEnd) // 让Box包裹内容并在顶部右侧对齐
                .padding(16.dp), // 给Box一些内边距，方便演示
    ) {
        // 2. 触发器：三个点图标
        Icon(
            imageVector = Icons.Default.MoreVert, // 使用内置的三个点图标
            contentDescription = "更多选项",
            modifier =
                Modifier
                    .size(24.dp) // 图标大小
                    .clickable { showMenu = true } // 点击图标时显示菜单
                    .padding(4.dp), // 给点击区域一些额外空间
        )

        // 3. DropdownMenu 实现弹窗效果
        DropdownMenu(
            expanded = showMenu, // 控制菜单是否展开
            onDismissRequest = { showMenu = false }, // 点击菜单外部或按返回键时关闭菜单
        ) {
            // DropdownMenuItem 用于菜单中的每个选项
            DropdownMenuItem(
                text = { Text("举报") }, // 菜单项文本
                onClick = {
                    // 处理“举报”点击事件
                    showMenu = false // 点击后关闭菜单
                    println("点击了举报")
                },
                leadingIcon = {
                    // 你可以放一个 Icon 在文本前面，例如你图片中的“灯泡”图标
                    // 这里只是一个占位符，因为没有你图片中具体的图标资源
                    Icon(
                        imageVector = Icons.Default.MoreVert, // 替换为实际的举报图标
                        contentDescription = "举报图标",
                    )
                },
            )
            DropdownMenuItem(
                text = { Text("不感兴趣") },
                onClick = {
                    // 处理“不感兴趣”点击事件
                    showMenu = false // 点击后关闭菜单
                    println("点击了不感兴趣")
                },
                leadingIcon = {
                    // 替换为实际的不感兴趣图标，例如一个带有斜杠的心形图标
                    Icon(
                        imageVector = Icons.Default.MoreVert, // 替换为实际的不感兴趣图标
                        contentDescription = "不感兴趣图标",
                    )
                },
            )
            // 你可以继续添加更多的 DropdownMenuItem
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewMoreOptionsPopupExample() {
    Column(modifier = Modifier.background(Color.LightGray)) {
        // 你的其他内容
        Text("这是下面的内容")
        Weight()
        MoreOptionsPopupExample()
    }
}
