package com.buque.wakoo.ui.screens.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.IconButton
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.buque.wakoo.navigation.RelationsKey
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooSecondaryUnSelected
import com.buque.wakoo.ui.widget.SizeSpacer
import com.buque.wakoo.ui.widget.StandardScreenScaffold
import com.buque.wakoo.ui.widget.WakooTitleBar
import kotlinx.coroutines.launch

private val TABS: List<RelationsKey.TabContent> =
    listOf(
        RelationsKey.Following, // 关注
        RelationsKey.Followers, // 粉丝
    )

@Composable
fun UserRelationsScreen(
    initIndex: Int,
    userId: String,
    modifier: Modifier = Modifier,
) {
    // Tab选项卡
    val scope = rememberCoroutineScope()
    val pagerState = rememberPagerState(initialPage = initIndex, pageCount = { TABS.size })
    val selectedTabIndex = pagerState.currentPage

    StandardScreenScaffold(
        topBar = {
            WakooTitleBar(
                title = {
                    TabRow(
                        selectedTabIndex = selectedTabIndex,
                        modifier =
                            Modifier.padding(
                                horizontal = 30.dp,
                            ),
                        divider = {},
                        containerColor = Color.Transparent,
                        indicator = { tabPositions ->
                            if (selectedTabIndex < tabPositions.size) {
                                Box(
                                    modifier =
                                        Modifier
                                            .tabIndicatorOffset(tabPositions[selectedTabIndex])
                                            .requiredWidth(12.dp)
                                            .height(3.dp)
                                            .background(
                                                WakooSecondarySelected,
                                                CircleShape,
                                            ),
                                )
                            }
                        },
                    ) {
                        TABS.forEachIndexed { index, tab ->
                            Tab(
                                selected = selectedTabIndex == index,
                                selectedContentColor = WakooSecondarySelected,
                                unselectedContentColor = WakooSecondaryUnSelected,
                                onClick = {
                                    scope.launch {
                                        pagerState.animateScrollToPage(index)
                                    }
                                },
                                content = {
                                    Box(modifier = Modifier.padding(bottom = 4.dp)) {
                                        tab.TabContent(selectedTabIndex == index)
                                    }
                                },
                            )
                        }
                    }
                },
                actions = {
                    IconButton(
                        onClick = {
                        },
                        enabled = false,
                    ) {
                        SizeSpacer(24.dp)
                    }
                },
            )
        },
        modifier = modifier,
    ) { padding ->
        HorizontalPager(
            state = pagerState,
            modifier =
                Modifier
                    .fillMaxSize(),
            beyondViewportPageCount = 1,
            contentPadding = padding,
        ) {
            // 内容区域
            RelationsListPage(userId, TABS[selectedTabIndex])
        }
    }
}
