package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.DeleteCircle: ImageVector
    get() {
        val current = _deleteCircle
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.DeleteCircle",
                defaultWidth = 20.0.dp,
                defaultHeight = 20.0.dp,
                viewportWidth = 20.0f,
                viewportHeight = 20.0f,
            ).apply {
                // M10 0 A10 10 0 1 0 10 20 10 10 0 1 0 10 0z
                path(
                    fill = SolidColor(Color(0xFF000000)),
                    fillAlpha = 0.5f,
                ) {
                    // M 10 0
                    moveTo(x = 10.0f, y = 0.0f)
                    // A 10 10 0 1 0 10 20
                    arcTo(
                        horizontalEllipseRadius = 10.0f,
                        verticalEllipseRadius = 10.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        x1 = 10.0f,
                        y1 = 20.0f,
                    )
                    // A 10 10 0 1 0 10 0z
                    arcTo(
                        horizontalEllipseRadius = 10.0f,
                        verticalEllipseRadius = 10.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        x1 = 10.0f,
                        y1 = 0.0f,
                    )
                    close()
                }
                // <rect width="2" height="12" rx="0.75" x="5.227" y="6.287" fill="#fff" />
                path(
                    fill = SolidColor(Color(0xFFFFFFFF)),
                ) {
                    // M 5.7573304 6.8173304
                    moveTo(x = 5.7573304f, y = 6.8173304f)
                    // a 0.74999994 0.74999994 0 0 1 0 -1.0606601
                    arcToRelative(
                        a = 0.74999994f,
                        b = 0.74999994f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = -1.0606601f,
                    )
                    // l 0.35355338 -0.35355338
                    lineToRelative(dx = 0.35355338f, dy = -0.35355338f)
                    // a 0.74999994 0.74999994 0 0 1 1.0606601 0
                    arcToRelative(
                        a = 0.74999994f,
                        b = 0.74999994f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.0606601f,
                        dy1 = 0.0f,
                    )
                    // l 7.424621 7.424621
                    lineToRelative(dx = 7.424621f, dy = 7.424621f)
                    // a 0.74999994 0.74999994 0 0 1 0 1.0606601
                    arcToRelative(
                        a = 0.74999994f,
                        b = 0.74999994f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = 1.0606601f,
                    )
                    // l -0.35355338 0.35355338
                    lineToRelative(dx = -0.35355338f, dy = 0.35355338f)
                    // a 0.74999994 0.74999994 0 0 1 -1.0606601 0z
                    arcToRelative(
                        a = 0.74999994f,
                        b = 0.74999994f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.0606601f,
                        dy1 = 0.0f,
                    )
                    close()
                }
                // <rect width="2" height="12" rx="0.75" x="13.712" y="5.227" fill="#fff" />
                path(
                    fill = SolidColor(Color(0xFFFFFFFF)),
                ) {
                    // M 13.181669 5.757331
                    moveTo(x = 13.181669f, y = 5.757331f)
                    // a 0.74999994 0.74999994 0 0 1 1.0606601 0
                    arcToRelative(
                        a = 0.74999994f,
                        b = 0.74999994f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.0606601f,
                        dy1 = 0.0f,
                    )
                    // l 0.35355338 0.35355338
                    lineToRelative(dx = 0.35355338f, dy = 0.35355338f)
                    // a 0.74999994 0.74999994 0 0 1 0 1.0606601
                    arcToRelative(
                        a = 0.74999994f,
                        b = 0.74999994f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = 1.0606601f,
                    )
                    // l -7.424621 7.424621
                    lineToRelative(dx = -7.424621f, dy = 7.424621f)
                    // a 0.74999994 0.74999994 0 0 1 -1.0606601 0
                    arcToRelative(
                        a = 0.74999994f,
                        b = 0.74999994f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.0606601f,
                        dy1 = 0.0f,
                    )
                    // l -0.35355338 -0.35355338
                    lineToRelative(dx = -0.35355338f, dy = -0.35355338f)
                    // a 0.74999994 0.74999994 0 0 1 0 -1.0606601z
                    arcToRelative(
                        a = 0.74999994f,
                        b = 0.74999994f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = -1.0606601f,
                    )
                    close()
                }
            }.build()
            .also { _deleteCircle = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.DeleteCircle,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((20.0).dp)
                        .height((20.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _deleteCircle: ImageVector? = null
