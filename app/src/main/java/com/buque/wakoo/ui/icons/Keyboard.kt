package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

public val WakooIcons.Keyboard: ImageVector
    get() {
        if (_keyboard != null) {
            return _keyboard!!
        }
        _keyboard = Builder(name = "Keyboard", defaultWidth = 28.0.dp, defaultHeight = 28.0.dp,
            viewportWidth = 28.0f, viewportHeight = 28.0f).apply {
            group {
                path(fill = SolidColor(Color(0xFF09244B)), stroke = null, strokeLineWidth = 0.0f,
                    strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                    pathFillType = NonZero) {
                    moveTo(23.3333f, 4.667f)
                    curveTo(23.922f, 4.6668f, 24.489f, 4.8891f, 24.9206f, 5.2894f)
                    curveTo(25.3523f, 5.6897f, 25.6167f, 6.2383f, 25.6608f, 6.8253f)
                    lineTo(25.6667f, 7.0003f)
                    verticalLineTo(21.0003f)
                    curveTo(25.6668f, 21.589f, 25.4445f, 22.156f, 25.0443f, 22.5876f)
                    curveTo(24.644f, 23.0193f, 24.0953f, 23.2837f, 23.5083f, 23.3278f)
                    lineTo(23.3333f, 23.3337f)
                    horizontalLineTo(4.6667f)
                    curveTo(4.078f, 23.3338f, 3.511f, 23.1115f, 3.0794f, 22.7112f)
                    curveTo(2.6477f, 22.311f, 2.3833f, 21.7623f, 2.3392f, 21.1753f)
                    lineTo(2.3333f, 21.0003f)
                    verticalLineTo(7.0003f)
                    curveTo(2.3331f, 6.4117f, 2.5555f, 5.8447f, 2.9557f, 5.413f)
                    curveTo(3.356f, 4.9814f, 3.9046f, 4.717f, 4.4917f, 4.6728f)
                    lineTo(4.6667f, 4.667f)
                    horizontalLineTo(23.3333f)
                    close()
                    moveTo(23.3333f, 7.0003f)
                    horizontalLineTo(4.6667f)
                    verticalLineTo(21.0003f)
                    horizontalLineTo(23.3333f)
                    verticalLineTo(7.0003f)
                    close()
                    moveTo(19.8333f, 16.3337f)
                    curveTo(20.1307f, 16.334f, 20.4167f, 16.4479f, 20.6329f, 16.652f)
                    curveTo(20.8492f, 16.8561f, 20.9793f, 17.1351f, 20.9967f, 17.432f)
                    curveTo(21.0141f, 17.7288f, 20.9175f, 18.0211f, 20.7267f, 18.2491f)
                    curveTo(20.5358f, 18.4772f, 20.2651f, 18.6237f, 19.9698f, 18.6588f)
                    lineTo(19.8333f, 18.667f)
                    horizontalLineTo(8.1667f)
                    curveTo(7.8693f, 18.6667f, 7.5833f, 18.5528f, 7.3671f, 18.3487f)
                    curveTo(7.1508f, 18.1445f, 7.0207f, 17.8655f, 7.0033f, 17.5687f)
                    curveTo(6.9859f, 17.2718f, 7.0824f, 16.9795f, 7.2733f, 16.7515f)
                    curveTo(7.4642f, 16.5235f, 7.7349f, 16.3769f, 8.0302f, 16.3418f)
                    lineTo(8.1667f, 16.3337f)
                    horizontalLineTo(19.8333f)
                    close()
                    moveTo(9.3333f, 12.8337f)
                    curveTo(9.6427f, 12.8337f, 9.9395f, 12.9566f, 10.1583f, 13.1754f)
                    curveTo(10.3771f, 13.3942f, 10.5f, 13.6909f, 10.5f, 14.0003f)
                    curveTo(10.5f, 14.3097f, 10.3771f, 14.6065f, 10.1583f, 14.8253f)
                    curveTo(9.9395f, 15.0441f, 9.6427f, 15.167f, 9.3333f, 15.167f)
                    horizontalLineTo(8.1667f)
                    curveTo(7.8572f, 15.167f, 7.5605f, 15.0441f, 7.3417f, 14.8253f)
                    curveTo(7.1229f, 14.6065f, 7.0f, 14.3097f, 7.0f, 14.0003f)
                    curveTo(7.0f, 13.6909f, 7.1229f, 13.3942f, 7.3417f, 13.1754f)
                    curveTo(7.5605f, 12.9566f, 7.8572f, 12.8337f, 8.1667f, 12.8337f)
                    horizontalLineTo(9.3333f)
                    close()
                    moveTo(14.5833f, 12.8337f)
                    curveTo(14.8807f, 12.834f, 15.1667f, 12.9479f, 15.3829f, 13.152f)
                    curveTo(15.5992f, 13.3561f, 15.7293f, 13.6351f, 15.7467f, 13.932f)
                    curveTo(15.7641f, 14.2288f, 15.6675f, 14.5211f, 15.4767f, 14.7491f)
                    curveTo(15.2858f, 14.9772f, 15.0151f, 15.1237f, 14.7198f, 15.1588f)
                    lineTo(14.5833f, 15.167f)
                    horizontalLineTo(13.4167f)
                    curveTo(13.1193f, 15.1667f, 12.8333f, 15.0528f, 12.6171f, 14.8487f)
                    curveTo(12.4008f, 14.6445f, 12.2707f, 14.3655f, 12.2533f, 14.0687f)
                    curveTo(12.2359f, 13.7718f, 12.3324f, 13.4795f, 12.5233f, 13.2515f)
                    curveTo(12.7142f, 13.0235f, 12.9849f, 12.8769f, 13.2802f, 12.8418f)
                    lineTo(13.4167f, 12.8337f)
                    horizontalLineTo(14.5833f)
                    close()
                    moveTo(19.8333f, 12.8337f)
                    curveTo(20.1427f, 12.8337f, 20.4395f, 12.9566f, 20.6583f, 13.1754f)
                    curveTo(20.8771f, 13.3942f, 21.0f, 13.6909f, 21.0f, 14.0003f)
                    curveTo(21.0f, 14.3097f, 20.8771f, 14.6065f, 20.6583f, 14.8253f)
                    curveTo(20.4395f, 15.0441f, 20.1427f, 15.167f, 19.8333f, 15.167f)
                    horizontalLineTo(18.6667f)
                    curveTo(18.3572f, 15.167f, 18.0605f, 15.0441f, 17.8417f, 14.8253f)
                    curveTo(17.6229f, 14.6065f, 17.5f, 14.3097f, 17.5f, 14.0003f)
                    curveTo(17.5f, 13.6909f, 17.6229f, 13.3942f, 17.8417f, 13.1754f)
                    curveTo(18.0605f, 12.9566f, 18.3572f, 12.8337f, 18.6667f, 12.8337f)
                    horizontalLineTo(19.8333f)
                    close()
                    moveTo(9.3333f, 9.3337f)
                    curveTo(9.6307f, 9.334f, 9.9167f, 9.4479f, 10.1329f, 9.652f)
                    curveTo(10.3492f, 9.8561f, 10.4793f, 10.1351f, 10.4967f, 10.432f)
                    curveTo(10.5141f, 10.7288f, 10.4175f, 11.0211f, 10.2267f, 11.2491f)
                    curveTo(10.0358f, 11.4772f, 9.7651f, 11.6237f, 9.4698f, 11.6588f)
                    lineTo(9.3333f, 11.667f)
                    horizontalLineTo(8.1667f)
                    curveTo(7.8693f, 11.6667f, 7.5833f, 11.5528f, 7.3671f, 11.3487f)
                    curveTo(7.1508f, 11.1445f, 7.0207f, 10.8655f, 7.0033f, 10.5687f)
                    curveTo(6.9859f, 10.2718f, 7.0824f, 9.9796f, 7.2733f, 9.7515f)
                    curveTo(7.4642f, 9.5235f, 7.7349f, 9.377f, 8.0302f, 9.3418f)
                    lineTo(8.1667f, 9.3337f)
                    horizontalLineTo(9.3333f)
                    close()
                    moveTo(14.5833f, 9.3337f)
                    curveTo(14.8927f, 9.3337f, 15.1895f, 9.4566f, 15.4083f, 9.6754f)
                    curveTo(15.6271f, 9.8942f, 15.75f, 10.1909f, 15.75f, 10.5003f)
                    curveTo(15.75f, 10.8097f, 15.6271f, 11.1065f, 15.4083f, 11.3253f)
                    curveTo(15.1895f, 11.5441f, 14.8927f, 11.667f, 14.5833f, 11.667f)
                    horizontalLineTo(13.4167f)
                    curveTo(13.1072f, 11.667f, 12.8105f, 11.5441f, 12.5917f, 11.3253f)
                    curveTo(12.3729f, 11.1065f, 12.25f, 10.8097f, 12.25f, 10.5003f)
                    curveTo(12.25f, 10.1909f, 12.3729f, 9.8942f, 12.5917f, 9.6754f)
                    curveTo(12.8105f, 9.4566f, 13.1072f, 9.3337f, 13.4167f, 9.3337f)
                    horizontalLineTo(14.5833f)
                    close()
                    moveTo(19.8333f, 9.3337f)
                    curveTo(20.1307f, 9.334f, 20.4167f, 9.4479f, 20.6329f, 9.652f)
                    curveTo(20.8492f, 9.8561f, 20.9793f, 10.1351f, 20.9967f, 10.432f)
                    curveTo(21.0141f, 10.7288f, 20.9175f, 11.0211f, 20.7267f, 11.2491f)
                    curveTo(20.5358f, 11.4772f, 20.2651f, 11.6237f, 19.9698f, 11.6588f)
                    lineTo(19.8333f, 11.667f)
                    horizontalLineTo(18.6667f)
                    curveTo(18.3693f, 11.6667f, 18.0833f, 11.5528f, 17.8671f, 11.3487f)
                    curveTo(17.6508f, 11.1445f, 17.5207f, 10.8655f, 17.5033f, 10.5687f)
                    curveTo(17.4859f, 10.2718f, 17.5824f, 9.9796f, 17.7733f, 9.7515f)
                    curveTo(17.9642f, 9.5235f, 18.2349f, 9.377f, 18.5302f, 9.3418f)
                    lineTo(18.6667f, 9.3337f)
                    horizontalLineTo(19.8333f)
                    close()
                }
            }
        }
            .build()
        return _keyboard!!
    }

private var _keyboard: ImageVector? = null
