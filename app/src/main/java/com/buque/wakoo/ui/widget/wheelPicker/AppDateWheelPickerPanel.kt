package com.buque.wakoo.ui.widget.wheelPicker

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import com.buque.wakoo.navigation.dialog.DialogScope
import kotlinx.datetime.LocalDate

@Composable
fun DialogScope.AppDateWheelPickerPanel(
    title: String,
    initSelectedDate: LocalDate,
    modifier: Modifier = Modifier,
    yearRange: IntRange = remember { (0..9999) },
    onConfirm: (Boolean, LocalDate) -> Unit,
) {
    var selectedDate by remember { mutableStateOf(initSelectedDate) }

    WheelPickerPanelScaffold(
        title = title,
        onDismissRequest = {
            dismiss()
        },
        onConfirm = {
            dismiss()
            onConfirm(initSelectedDate != selectedDate, selectedDate)
        },
        modifier = modifier,
    ) {
        DateWheelPicker(
            selectedDate = selectedDate,
            onDateChanged = { selectedDate = it },
            modifier = modifier,
            yearRange = yearRange,
            enabledDateRange =
                remember(yearRange) {
                    WheelPickerUtils.createDateRangeToToday(yearRange)
                },
            isMonthInfinite = true,
            isDayInfinite = true,
        )
    }
}
