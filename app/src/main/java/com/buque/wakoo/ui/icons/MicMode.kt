package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.MicMode: ImageVector
    get() {
        val current = _MicMode
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.MicMode",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // M12 3 a3 3 0 0 0 -3 3 v6 a3 3 0 1 0 6 0 V6 a3 3 0 0 0 -3 -3 m0 -2 a5 5 0 0 1 5 5 v6 a5 5 0 0 1 -10 0 V6 a5 5 0 0 1 5 -5 M2.2 13.96 l1.95 -.4 a8 8 0 0 0 15.7 0 l1.96 .4 a10 10 0 0 1 -19.62 0
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 12 3
                    moveTo(x = 12.0f, y = 3.0f)
                    // a 3 3 0 0 0 -3 3
                    arcToRelative(
                        a = 3.0f,
                        b = 3.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -3.0f,
                        dy1 = 3.0f,
                    )
                    // v 6
                    verticalLineToRelative(dy = 6.0f)
                    // a 3 3 0 1 0 6 0
                    arcToRelative(
                        a = 3.0f,
                        b = 3.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        dx1 = 6.0f,
                        dy1 = 0.0f,
                    )
                    // V 6
                    verticalLineTo(y = 6.0f)
                    // a 3 3 0 0 0 -3 -3
                    arcToRelative(
                        a = 3.0f,
                        b = 3.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -3.0f,
                        dy1 = -3.0f,
                    )
                    // m 0 -2
                    moveToRelative(dx = 0.0f, dy = -2.0f)
                    // a 5 5 0 0 1 5 5
                    arcToRelative(
                        a = 5.0f,
                        b = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 5.0f,
                        dy1 = 5.0f,
                    )
                    // v 6
                    verticalLineToRelative(dy = 6.0f)
                    // a 5 5 0 0 1 -10 0
                    arcToRelative(
                        a = 5.0f,
                        b = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -10.0f,
                        dy1 = 0.0f,
                    )
                    // V 6
                    verticalLineTo(y = 6.0f)
                    // a 5 5 0 0 1 5 -5
                    arcToRelative(
                        a = 5.0f,
                        b = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 5.0f,
                        dy1 = -5.0f,
                    )
                    // M 2.2 13.96
                    moveTo(x = 2.2f, y = 13.96f)
                    // l 1.95 -0.4
                    lineToRelative(dx = 1.95f, dy = -0.4f)
                    // a 8 8 0 0 0 15.7 0
                    arcToRelative(
                        a = 8.0f,
                        b = 8.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 15.7f,
                        dy1 = 0.0f,
                    )
                    // l 1.96 0.4
                    lineToRelative(dx = 1.96f, dy = 0.4f)
                    // a 10 10 0 0 1 -19.62 0
                    arcToRelative(
                        a = 10.0f,
                        b = 10.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -19.62f,
                        dy1 = 0.0f,
                    )
                }
            }.build()
            .also { _MicMode = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.MicMode,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _MicMode: ImageVector? = null
