package com.buque.wakoo.ui.screens.chatgroup.panel

import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.buque.wakoo.app.OnDataCallback
import com.buque.wakoo.navigation.dialog.DialogController
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.dialog.BottomPanelScaffold
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight

/**
 * 编辑群信息
 */
@Composable
fun DialogScope.EditGroupInfoPanel(
    title: String,
    content: String,
    placeholder: String,
    maxLength: Int,
    textFieldHeight: Dp,
    modifier: Modifier = Modifier,
    onSave: OnDataCallback<String>,
) {
    var inputContent by rememberSaveable { mutableStateOf(content) }

    BottomPanelScaffold(
        title = title,
        useClose = true,
        modifier =
            Modifier
                .windowInsetsPadding(WindowInsets.ime)
                .then(modifier),
        backgroundColor = WakooWhite,
    ) {
        AppTextField(
            value = inputContent,
            onValueChange = {
                inputContent = it
            },
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(textFieldHeight),
            placeholder = placeholder,
            maxLength = maxLength,
            showLengthTip = true,
            backgroundColor = Color(0xFFF8F8F8),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
            keyboardActions = KeyboardActions(onDone = { onSave(inputContent) }),
        )

        SizeHeight(30.dp)

        GradientButton(
            text = "保存",
            onClick = {
                onSave(inputContent)
            },
            modifier =
                Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth(),
        )
    }
}

@Preview
@Composable
private fun PreviewEditGroupInfoPanel1() {
    WakooTheme {
        DialogController.preview.apply {
            EditGroupInfoPanel(
                title = "修改群组名称",
                content = "",
                placeholder = "请输入家族昵称",
                maxLength = 20,
                textFieldHeight = 100.dp,
            ) {
            }
        }
    }
}

@Preview
@Composable
private fun PreviewEditGroupInfoPanel2() {
    WakooTheme {
        DialogController.preview.apply {
            EditGroupInfoPanel(
                title = "修改群组简介",
                content = "",
                placeholder = "请输入家族昵称",
                maxLength = 500,
                textFieldHeight = 168.dp,
            ) {
            }
        }
    }
}
