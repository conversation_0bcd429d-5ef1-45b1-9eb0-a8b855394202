package com.buque.wakoo.ui.widget.popup

import androidx.annotation.FloatRange
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp

/**
 * 定义箭头的位置
 */
enum class ArrowPosition {
    Top,
    Bottom,
}

/**
 * 带有可定位指示箭头的气泡形状
 * @param cornerRadius 气泡的圆角半径
 * @param arrowPosition 箭头的位置 (顶部或底部)
 * @param arrowWidth 箭头的宽度
 * @param arrowHeight 箭头的高度
 * @param arrowPositionBias 箭头在水平方向上的位置偏向。
 *                          范围从 0.0f (最左边) 到 1.0f (最右边)。
 *                          0.5f 表示箭头居中。
 */
class BubbleShape(
    private val cornerRadius: Dp = 8.dp,
    private val arrowPosition: ArrowPosition = ArrowPosition.Top,
    private val arrowWidth: Dp = 12.dp,
    private val arrowHeight: Dp = 6.5.dp,
    @FloatRange(from = 0.0, to = 1.0)
    private val arrowPositionBias: Float = 0.5f,
) : Shape {
    override fun createOutline(
        size: Size,
        layoutDirection: LayoutDirection,
        density: Density,
    ): Outline {
        val cornerRadiusPx = with(density) { cornerRadius.toPx() }
        val arrowWidthPx = with(density) { arrowWidth.toPx() }
        val arrowHeightPx = with(density) { arrowHeight.toPx() }

        // --- 主要改动在这里 ---
        // 1. 根据偏向值计算箭头中心的 X 坐标
        val arrowCenterXPx = size.width * arrowPositionBias
        // 2. 计算箭头的起始点（左侧点）
        val arrowStartPoint =
            (arrowCenterXPx - arrowWidthPx / 2)
                // 3. 保证箭头不会超出气泡的圆角边界
                .coerceIn(cornerRadiusPx, size.width - cornerRadiusPx - arrowWidthPx)
        // --- 改动结束 ---

        val path =
            Path().apply {
                // 根据箭头位置，调整矩形主体的绘制区域
                val rectYStart = if (arrowPosition == ArrowPosition.Top) arrowHeightPx else 0f
                val rectYEnd =
                    if (arrowPosition == ArrowPosition.Bottom) size.height - arrowHeightPx else size.height

                // 绘制气泡的矩形主体
                addRoundRect(
                    androidx.compose.ui.geometry.RoundRect(
                        rect = Rect(0f, rectYStart, size.width, rectYEnd),
                        radiusX = cornerRadiusPx,
                        radiusY = cornerRadiusPx,
                    ),
                )

                // 绘制箭头
                when (arrowPosition) {
                    ArrowPosition.Top -> {
                        moveTo(arrowStartPoint, arrowHeightPx)
                        lineTo(arrowStartPoint + arrowWidthPx / 2, 0f)
                        lineTo(arrowStartPoint + arrowWidthPx, arrowHeightPx)
                        close()
                    }

                    ArrowPosition.Bottom -> {
                        val bottomY = size.height - arrowHeightPx
                        moveTo(arrowStartPoint, bottomY)
                        lineTo(arrowStartPoint + arrowWidthPx / 2, size.height)
                        lineTo(arrowStartPoint + arrowWidthPx, bottomY)
                        close()
                    }
                }
            }
        return Outline.Generic(path)
    }
}
