package com.buque.wakoo.ui.widget.coordinatorlayout

import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.SpringSpec
import androidx.compose.animation.core.animate
import androidx.compose.material3.TopAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.listSaver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * 一个稳定的状态对象，用于控制和观察 [CustomCollapsibleHeader] 的状态。
 *
 * 它封装了所有与滚动、高度和动画相关的状态，并提供了编程式控制的 API。
 * 通过 `rememberCustomCollapsibleHeaderState()` 创建的实例可以在配置更改后被自动保存和恢复。
 *
 * @param coroutineScope 一个 CoroutineScope，用于启动动画协程。
 * @param initialCollapsedHeight 初始的收起高度。
 * @param initialExpandedHeight 初始的展开高度。
 * @param initialHeightOffset 初始的高度偏移量（来自保存的状态）。
 * @param initialContentOffset 初始的内容滚动偏移量（来自保存的状态）。
 */
@Stable
class CustomCollapsibleHeaderState(
    private val coroutineScope: CoroutineScope,
    initialCollapsedHeight: Dp,
    initialExpandedHeight: Dp,
    initialHeightOffset: Float,
    initialContentOffset: Float,
) {
    /**
     * 内部持有一个 Material3 的 TopAppBarState 来复用其滚动逻辑和状态存储。
     * 构造时将保存的状态传递给它。
     */
    internal val topAppBarState =
        TopAppBarState(
            initialHeightOffsetLimit = Float.NEGATIVE_INFINITY, // 限制值将在测量后动态设置
            initialHeightOffset = initialHeightOffset,
            initialContentOffset = initialContentOffset,
        )

    /**
     * 收起状态的高度。此值在首次测量后被设置。
     */
    var collapsedHeight: Dp by mutableStateOf(initialCollapsedHeight)
        internal set // 只能在模块内部被修改

    /**
     * 展开状态的高度。此值在首次测量后被设置。
     */
    var expandedHeight: Dp by mutableStateOf(initialExpandedHeight)
        internal set // 只能在模块内部被修改

    /**
     * 当前的折叠进度。
     * 0.0 表示完全展开，1.0 表示完全收起。
     * 此值为只读，由内部的滚动状态驱动，可用于外部联动动画。
     */
    val collapsedFraction: Float
        get() = topAppBarState.collapsedFraction

    /**
     * 以动画方式展开头部。
     * @param animationSpec 展开动画的规格。
     */
    fun expand(animationSpec: AnimationSpec<Float> = SpringSpec()) {
        coroutineScope.launch {
            topAppBarState.animateTo(
                0f,
                animationSpec,
            )
        }
    }

    /**
     * 以动画方式收起头部。
     * @param animationSpec 收起动画的规格。
     */
    fun collapse(animationSpec: AnimationSpec<Float> = SpringSpec()) {
        coroutineScope.launch {
            topAppBarState.animateTo(
                topAppBarState.heightOffsetLimit,
                animationSpec,
            )
        }
    }

    // 内部动画辅助函数，用于驱动 heightOffset 的变化
    private suspend fun TopAppBarState.animateTo(
        targetValue: Float,
        animationSpec: AnimationSpec<Float>,
    ) {
        animate(
            initialValue = heightOffset,
            targetValue = targetValue,
            animationSpec = animationSpec,
        ) { value, _ ->
            heightOffset = value
        }
    }
}

/**
 * 创建并记住一个可被保存和恢复的 [CustomCollapsibleHeaderState]。
 *
 * @return 一个新的或被恢复的 [CustomCollapsibleHeaderState] 实例。
 */
@Composable
fun rememberCustomCollapsibleHeaderState(): CustomCollapsibleHeaderState {
    val coroutineScope = rememberCoroutineScope()

    // 定义 Saver，它知道如何保存和恢复 State
    val saver =
        remember(coroutineScope) {
            listSaver<CustomCollapsibleHeaderState, Any>(
                save = { state ->
                    // 将所有需要持久化的状态打包成一个可序列化的列表
                    listOf(
                        state.collapsedHeight.value, // 0: 收起高度 (Float)
                        state.expandedHeight.value, // 1: 展开高度 (Float)
                        state.topAppBarState.heightOffset, // 2: 当前高度偏移 (Float)
                        state.topAppBarState.contentOffset, // 3: 内容滚动偏移 (Float)
                    )
                },
                restore = { list ->
                    // 从列表中恢复状态，并使用当前组合作用域的 coroutineScope 创建新实例
                    CustomCollapsibleHeaderState(
                        coroutineScope = coroutineScope, // **关键**: 使用当前作用域的 scope
                        initialCollapsedHeight = (list[0] as Float).dp,
                        initialExpandedHeight = (list[1] as Float).dp,
                        initialHeightOffset = list[2] as Float,
                        initialContentOffset = list[3] as Float,
                    )
                },
            )
        }

    // 使用 rememberSaveable 来创建或恢复 state
    return rememberSaveable(saver = saver) {
        // 这个 lambda 只在首次创建 state 时（或无法恢复时）被调用
        CustomCollapsibleHeaderState(
            coroutineScope = coroutineScope,
            initialCollapsedHeight = Dp.Unspecified,
            initialExpandedHeight = Dp.Unspecified,
            initialHeightOffset = 0f,
            initialContentOffset = 0f,
        )
    }
}
