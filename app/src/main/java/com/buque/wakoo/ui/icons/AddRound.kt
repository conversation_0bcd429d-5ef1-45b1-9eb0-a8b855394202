package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.AddRound: ImageVector
    get() {
        val current = _addRound
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.AddRound",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // M12 0 a1 1 0 0 1 1 1 v10 h10 a1 1 0 1 1 0 2 H13 v10 a1 1 0 1 1 -2 0 V13 H1 a1 1 0 1 1 0 -2 h10 V1 a1 1 0 0 1 1 -1
                path(
                    fill = SolidColor(Color(0xFF999999)),
                ) {
                    // M 12 0
                    moveTo(x = 12.0f, y = 0.0f)
                    // a 1 1 0 0 1 1 1
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.0f,
                        dy1 = 1.0f,
                    )
                    // v 10
                    verticalLineToRelative(dy = 10.0f)
                    // h 10
                    horizontalLineToRelative(dx = 10.0f)
                    // a 1 1 0 1 1 0 2
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = 2.0f,
                    )
                    // H 13
                    horizontalLineTo(x = 13.0f)
                    // v 10
                    verticalLineToRelative(dy = 10.0f)
                    // a 1 1 0 1 1 -2 0
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = true,
                        dx1 = -2.0f,
                        dy1 = 0.0f,
                    )
                    // V 13
                    verticalLineTo(y = 13.0f)
                    // H 1
                    horizontalLineTo(x = 1.0f)
                    // a 1 1 0 1 1 0 -2
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = -2.0f,
                    )
                    // h 10
                    horizontalLineToRelative(dx = 10.0f)
                    // V 1
                    verticalLineTo(y = 1.0f)
                    // a 1 1 0 0 1 1 -1
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.0f,
                        dy1 = -1.0f,
                    )
                }
            }.build()
            .also { _addRound = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.AddRound,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _addRound: ImageVector? = null
