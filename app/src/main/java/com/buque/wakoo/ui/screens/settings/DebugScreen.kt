package com.buque.wakoo.ui.screens.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.buque.wakoo.bean.BasicUser
import com.buque.wakoo.core.webview.AppLinkNavigator
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.navigation.GalleryScreenKey
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.RootNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.screens.japan.boost.AwardType
import com.buque.wakoo.ui.screens.japan.boost.BoostPage
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.media.previewer.MediaAlbum
import com.buque.wakoo.ui.widget.media.previewer.MediaItem

@Composable
fun DebugScreen(controller: RootNavController) {
    val ctrl = LocalAppNavController.current
    val dc = rememberDialogController()
    val lm = LocalLoadingManager.current
    val scope = rememberCoroutineScope()
    val u by AccountManager.userStateFlow.collectAsState()
    val scrollState = rememberScrollState()
    TitleScreenScaffold("Debug") {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .verticalScroll(scrollState)
                    .padding(it),
        ) {
            Text(u?.toString() ?: "", modifier = Modifier.padding(16.dp))
            FlowRow {
                Button(onClick = {
                    controller.push(Route.Web("${EnvironmentManager.current.apiUrl}h5/hello"))
                }) {
                    Text("H5 测试")
                }

                SolidButton("群组", onClick = {
                    controller.push(Route.ChatGroup("564"))
                })

                SolidButton("消息", onClick = {
                    controller.push(Route.InfoNotification)
                })

                var id by remember { mutableStateOf("") }
                Button(onClick = {
                    dc.easyPost {
                        Column(modifier = Modifier.width(270.dp)) {
                            AppTextField(id, onValueChange = { newUid ->
                                id = newUid
                            })
                            SolidButton("打开群组", onClick = {
                                dismiss()
                                controller.push(Route.ChatGroupDetail(id))
                            })
                            SolidButton("打开个人主页", onClick = {
                                dismiss()
                                controller.push(Route.UserProfile(BasicUser.sampleBoy.copy(id = id)))
                            })
                        }
                    }
                }) {
                    Text("打开用户主页")
                }
                Button(onClick = {
                    AppLinkNavigator.go("wakoo://page/settings", ctrl, dc)
                }) {
                    Text("打开设置")
                }
                Button(onClick = {
                    AppLinkNavigator.go("wakoo://page/report?type=1&target_id=123", ctrl, dc)
                }) {
                    Text("打开Report")
                }

                Button(onClick = {
                    controller.push(Route.BoostPage(AwardType.inc))
                }) {
                    Text("打开收益页")
                }
            }


            Button(onClick = {
                val album =
                    MediaAlbum(
                        items =
                            listOf(
                                // Photo by Mahyar Motebassem (https://unsplash.com/photos/f0d83M-PkNw).
                                MediaItem.Image(
                                    fullSizedUrl = "https://unsplash.com/photos/f0d83M-PkNw/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODMzODU2fA&force=true",
                                    placeholderImageUrl = "https://unsplash.com/photos/f0d83M-PkNw/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODMzODU2fA&force=true&w=300",
                                    aspectRatio = 300f / 375f,
                                ),
                                // Photo by Jack White (https://unsplash.com/photos/jDCIBr88RGU/).
                                MediaItem.Image(
                                    fullSizedUrl = "https://unsplash.com/photos/L_SjEwDtJEI/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODM0MjAxfA&force=true",
                                    placeholderImageUrl = "https://unsplash.com/photos/L_SjEwDtJEI/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODM0MjAxfA&force=true&w=300",
                                    aspectRatio = 300f / 450f,
                                ),
                                // Photo by Romain Guy (https://www.flickr.com/photos/romainguy/).
                                MediaItem.Image(
                                    fullSizedUrl = "https://live.staticflickr.com/4734/39442725251_be4b6395a2_o_d.jpg",
                                    placeholderImageUrl = "https://live.staticflickr.com/4734/39442725251_ed2353237e_c_d.jpg",
                                    aspectRatio = 533f / 800f,
                                ),
                                MediaItem.Image(
                                    fullSizedUrl = "https://live.staticflickr.com/4687/39511378181_e815b89822_o_d.jpg",
                                    placeholderImageUrl = "https://live.staticflickr.com/4687/39511378181_ab0c158858_c_d.jpg",
                                    aspectRatio = 449 / 800f,
                                ),
                                MediaItem.Image(
                                    fullSizedUrl = "https://unsplash.com/photos/f0d83M-PkNw/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODMzODU2fA&force=true",
                                    placeholderImageUrl = "https://unsplash.com/photos/f0d83M-PkNw/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODMzODU2fA&force=true&w=300",
                                    aspectRatio = 300f / 375f,
                                ),
                                // Photo by Jack White (https://unsplash.com/photos/jDCIBr88RGU/).
                                MediaItem.Image(
                                    fullSizedUrl = "https://unsplash.com/photos/L_SjEwDtJEI/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODM0MjAxfA&force=true",
                                    placeholderImageUrl = "https://unsplash.com/photos/L_SjEwDtJEI/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODM0MjAxfA&force=true&w=300",
                                    aspectRatio = 300f / 450f,
                                ),
                                // Photo by Romain Guy (https://www.flickr.com/photos/romainguy/).
                                MediaItem.Image(
                                    fullSizedUrl = "https://live.staticflickr.com/4734/39442725251_be4b6395a2_o_d.jpg",
                                    placeholderImageUrl = "https://live.staticflickr.com/4734/39442725251_ed2353237e_c_d.jpg",
                                    aspectRatio = 533f / 800f,
                                ),
                                MediaItem.Image(
                                    fullSizedUrl = "https://live.staticflickr.com/4687/39511378181_e815b89822_o_d.jpg",
                                    placeholderImageUrl = "https://live.staticflickr.com/4687/39511378181_ab0c158858_c_d.jpg",
                                    aspectRatio = 449 / 800f,
                                ),
                                MediaItem.Image(
                                    fullSizedUrl = "https://unsplash.com/photos/f0d83M-PkNw/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODMzODU2fA&force=true",
                                    placeholderImageUrl = "https://unsplash.com/photos/f0d83M-PkNw/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODMzODU2fA&force=true&w=300",
                                    aspectRatio = 300f / 375f,
                                ),
                                // Photo by Jack White (https://unsplash.com/photos/jDCIBr88RGU/).
                                MediaItem.Image(
                                    fullSizedUrl = "https://unsplash.com/photos/L_SjEwDtJEI/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODM0MjAxfA&force=true",
                                    placeholderImageUrl = "https://unsplash.com/photos/L_SjEwDtJEI/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQ3ODM0MjAxfA&force=true&w=300",
                                    aspectRatio = 300f / 450f,
                                ),
                                // Photo by Romain Guy (https://www.flickr.com/photos/romainguy/).
                                MediaItem.Image(
                                    fullSizedUrl = "https://live.staticflickr.com/4734/39442725251_be4b6395a2_o_d.jpg",
                                    placeholderImageUrl = "https://live.staticflickr.com/4734/39442725251_ed2353237e_c_d.jpg",
                                    aspectRatio = 533f / 800f,
                                ),
                                MediaItem.Image(
                                    fullSizedUrl = "https://live.staticflickr.com/4687/39511378181_e815b89822_o_d.jpg",
                                    placeholderImageUrl = "https://live.staticflickr.com/4687/39511378181_ab0c158858_c_d.jpg",
                                    aspectRatio = 449 / 800f,
                                ),
                            ),
                    )
                controller.push(GalleryScreenKey(album))
            }) {
                Text("打开图库")
            }
        }
    }
}
