package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Liked: ImageVector
    get() {
        val current = _liked
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.Liked",
                defaultWidth = 16.0.dp,
                defaultHeight = 16.0.dp,
                viewportWidth = 16.0f,
                viewportHeight = 16.0f,
            ).apply {
                // M8.5 3.58 a3.25 3.25 0 0 1 4.35 .01 l.11 .11 a3.25 3.25 0 0 1 .14 4.45 L8 13.26 2.9 8.15 a3.25 3.25 0 0 1 .02 -4.33 L3.04 3.7 A3.25 3.25 0 0 1 7.5 3.58 L8 4.03z
                path(
                    fill =
                        Brush.linearGradient(
                            0.0f to Color(0xFFA3FF2C),
                            1.0f to Color(0xFF31FFA1),
                            start = Offset(x = 1.333f, y = 8.162f),
                            end = Offset(x = 14.667f, y = 8.162f),
                        ),
                    stroke = SolidColor(Color(0xFF111111)),
                    strokeLineWidth = 1.5f,
                ) {
                    // M 8.5 3.58
                    moveTo(x = 8.5f, y = 3.58f)
                    // a 3.25 3.25 0 0 1 4.35 0.01
                    arcToRelative(
                        a = 3.25f,
                        b = 3.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 4.35f,
                        dy1 = 0.01f,
                    )
                    // l 0.11 0.11
                    lineToRelative(dx = 0.11f, dy = 0.11f)
                    // a 3.25 3.25 0 0 1 0.14 4.45
                    arcToRelative(
                        a = 3.25f,
                        b = 3.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.14f,
                        dy1 = 4.45f,
                    )
                    // L 8 13.26
                    lineTo(x = 8.0f, y = 13.26f)
                    // L 2.9 8.15
                    lineTo(x = 2.9f, y = 8.15f)
                    // a 3.25 3.25 0 0 1 0.02 -4.33
                    arcToRelative(
                        a = 3.25f,
                        b = 3.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.02f,
                        dy1 = -4.33f,
                    )
                    // L 3.04 3.7
                    lineTo(x = 3.04f, y = 3.7f)
                    // A 3.25 3.25 0 0 1 7.5 3.58
                    arcTo(
                        horizontalEllipseRadius = 3.25f,
                        verticalEllipseRadius = 3.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 7.5f,
                        y1 = 3.58f,
                    )
                    // L 8 4.03z
                    lineTo(x = 8.0f, y = 4.03f)
                    close()
                }
            }.build()
            .also { _liked = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Liked,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((16.0).dp)
                        .height((16.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _liked: ImageVector? = null
