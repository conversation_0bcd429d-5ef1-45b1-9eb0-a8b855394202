package com.buque.wakoo.ui.widget.richtext

import android.widget.Toast
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.LayoutScopeMarker
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withLink
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import com.buque.wakoo.ui.widget.richtext.RichTextPart.InlineContent
import kotlin.math.roundToInt

// 使用密封接口来清晰地表示三种不同的内容部分：文本、静态尺寸组件、动态尺寸组件。
private sealed interface RichTextPart {
    data class Text(
        val annotatedString: AnnotatedString,
    ) : RichTextPart

    sealed interface InlineContent : RichTextPart {
        val alternateText: String
    }

    data class SizedComposable(
        val width: Number,
        val height: Number,
        override val alternateText: String,
        val content: @Composable () -> Unit,
    ) : InlineContent

    data class DpSizedComposable(
        val width: Dp,
        val height: Dp,
        override val alternateText: String,
        val content: @Composable () -> Unit,
    ) : InlineContent

    data class DynamicComposable(
        override val alternateText: String,
        val content: @Composable () -> Unit,
    ) : InlineContent
}

/**
 * 为 RichText 组件提供一个专属的DSL（领域特定语言）作用域。
 * 用户只能在这个作用域内调用指定的方法来构建富文本。
 */
@LayoutScopeMarker
interface RichTextScope {
    /**
     * 进入一个 AnnotatedString 构建器作用域，用于创建复杂的样式文本。
     * 这样用户就可以使用所有官方的API，如 pushStyle, pop, withStyle 等。
     * @param block 一个以 AnnotatedString.Builder 为接收者的 lambda。
     */
    fun withBuilder(block: AnnotatedString.Builder.() -> Unit)

    /**
     * 为尺寸未知的组件（如网络图片）提供。它会触发 SubcomposeLayout 测量。
     */
    @Suppress("ktlint:standard:function-naming")
    fun InlineContent(
        alternateText: String = "[widget]",
        content: @Composable () -> Unit,
    )

    /**
     * 为尺寸已知的组件提供。它会跳过 SubcomposeLayout，直接使用传入的尺寸，性能更高。
     */
    @Suppress("ktlint:standard:function-naming")
    fun InlineSizedContent(
        width: Dp,
        height: Dp,
        alternateText: String = "[widget]",
        content: @Composable () -> Unit,
    )

    @Suppress("ktlint:standard:function-naming")
    fun InlineSizedContent(
        width: Number,
        height: Number,
        alternateText: String = "[widget]",
        content: @Composable () -> Unit,
    )

    /**
     * 追加一段无样式的纯文本的便捷方法。
     * 它是 `withBuilder { append(text) }` 的简化写法。
     * @param text 要追加的文本。
     */
    fun append(text: String) {
        withBuilder { append(text) }
    }
}

private class RichTextScopeImpl : RichTextScope {
    val parts = mutableListOf<RichTextPart>()

    val measuredSizes = mutableStateMapOf<Int, IntSize>()

    val dynamicComposableParts = mutableMapOf<Int, @Composable () -> Unit>()

    val allComposableParts = mutableMapOf<Int, @Composable () -> Unit>()

    override fun withBuilder(block: AnnotatedString.Builder.() -> Unit) {
        val builder = AnnotatedString.Builder().apply(block)
        if (builder.length > 0) {
            parts.add(RichTextPart.Text(builder.toAnnotatedString()))
        }
    }

    override fun InlineContent(
        alternateText: String,
        content: @Composable () -> Unit,
    ) {
        parts.add(RichTextPart.DynamicComposable(alternateText, content))
    }

    override fun InlineSizedContent(
        width: Number,
        height: Number,
        alternateText: String,
        content: @Composable (() -> Unit),
    ) {
        parts.add(RichTextPart.SizedComposable(width, height, alternateText, content))
    }

    override fun InlineSizedContent(
        width: Dp,
        height: Dp,
        alternateText: String,
        content: @Composable () -> Unit,
    ) {
        parts.add(RichTextPart.DpSizedComposable(width, height, alternateText, content))
    }

    fun build(density: Density) {
        parts.forEachIndexed { index, part ->
            when (part) {
                is RichTextPart.SizedComposable -> {
                    measuredSizes[index] =
                        with(density) {
                            IntSize(part.width.toInt(), part.height.toInt())
                        }
                    allComposableParts[index] = part.content
                }

                is RichTextPart.DpSizedComposable -> {
                    measuredSizes[index] =
                        with(density) {
                            IntSize(part.width.toPx().roundToInt(), part.height.toPx().roundToInt())
                        }
                    allComposableParts[index] = part.content
                }

                is RichTextPart.DynamicComposable -> {
                    dynamicComposableParts[index] = part.content
                    allComposableParts[index] = part.content
                }

                else -> Unit
            }
        }
    }
}

/**
 * 一个功能完备且经过性能优化的富文本组件。
 * 它能够自动测量尺寸未知的内联组件，并高效处理尺寸已知的组件。
 *
 * @param modifier 要应用于此组件的 Modifier。
 * @param style 应用于文本的默认 TextStyle。
 * @param content 一个在 `RichTextScope` 作用域下的 lambda，你可以在其中调用 `append` (用于简单文本)，
 * `withBuilder` (用于带样式的文本)，`InlineContent` (用于动态尺寸视图) 和 `InlineSizedContent` (用于固定尺寸视图)。
 */
@Composable
fun RichText(
    modifier: Modifier = Modifier,
    key: Any? = null,
    color: Color = Color.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    fontStyle: FontStyle? = null,
    fontWeight: FontWeight? = null,
    fontFamily: FontFamily? = null,
    letterSpacing: TextUnit = TextUnit.Unspecified,
    textDecoration: TextDecoration? = null,
    textAlign: TextAlign? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    onTextLayout: (TextLayoutResult) -> Unit = {},
    style: TextStyle = LocalTextStyle.current,
    content: RichTextScope.() -> Unit,
) {
    val density = LocalDensity.current
    val impl =
        remember(key, density, content) {
            RichTextScopeImpl().apply {
                content()
                build(density)
            }
        }
    val measuredSizes = impl.measuredSizes

    // 提取出所有需要“动态测量”的组件。SubcomposeLayout 的工作量将仅限于此列表。
    val dynamicComposableParts = impl.dynamicComposableParts
    // 提取所有内联组件（包括静态和动态的），用于最终的渲染。
    val allComposableParts = impl.allComposableParts

    Box(modifier = modifier) {
        Text(
            text =
                buildAnnotatedString {
                    impl.parts.forEachIndexed { index, part ->
                        when (part) {
                            is RichTextPart.Text -> append(part.annotatedString)
                            // 对于所有类型的组件，只要其尺寸已存入 measuredSizes，就为其插入占位符。
                            is InlineContent -> {
                                if (measuredSizes.containsKey(index)) {
                                    appendInlineContent(index.toString(), part.alternateText)
                                }
                            }
                        }
                    }
                },
            modifier = modifier,
            color = color,
            fontSize = fontSize,
            fontStyle = fontStyle,
            fontWeight = fontWeight,
            fontFamily = fontFamily,
            letterSpacing = letterSpacing,
            textDecoration = textDecoration,
            textAlign = textAlign,
            lineHeight = lineHeight,
            overflow = overflow,
            softWrap = softWrap,
            maxLines = maxLines,
            minLines = minLines,
            onTextLayout = onTextLayout,
            style = style,
            inlineContent =
                measuredSizes
                    .map { (index, size) ->
                        index.toString() to
                            InlineTextContent(
                                placeholder =
                                    with(density) {
                                        Placeholder(
                                            width = size.width.toSp(),
                                            height = size.height.toSp(),
                                            placeholderVerticalAlign = PlaceholderVerticalAlign.TextCenter,
                                        )
                                    },
                                children = { allComposableParts[index]?.invoke() },
                            )
                    }.toMap(),
        )

        // 这个测量层现在只处理动态尺寸的组件，大大减轻了负担。
        // 如果没有动态尺寸的组件，`subcompose` 的列表将为空，几乎没有性能开销。
        SubcomposeLayout { constraints ->
            subcompose(Unit) {
                dynamicComposableParts.forEach { (index, itemContent) ->
                    key(index) { itemContent() }
                }
            }.forEachIndexed { i, measurable ->
                val placeable = measurable.measure(constraints)
                val originalIndex = dynamicComposableParts.keys.toList()[i]
                if (measuredSizes[originalIndex] != IntSize(placeable.width, placeable.height)) {
                    measuredSizes[originalIndex] = IntSize(placeable.width, placeable.height)
                }
            }
            layout(0, 0) {}
        }
    }
}

@Preview
@Composable
private fun RichTextPreview() {
    val context = LocalContext.current
    Column(modifier = Modifier.padding(16.dp)) {
        RichText(style = LocalTextStyle.current.copy(fontSize = 16.sp)) {
            withBuilder {
                append("欢迎使用优化后的 RichText！")
                pushStyle(SpanStyle(fontWeight = FontWeight.Bold, color = Color.Blue))
                append("现在可以高效处理已知尺寸的组件。")
                pop()
            }

            val clickableAnnotation =
                LinkAnnotation.Clickable(
                    tag = "TOS_LINK", // 一个唯一的标识符，用于区分不同的链接
                    linkInteractionListener = {
                        // 这里是你的点击事件处理逻辑
                        // 例如：打开一个网页、跳转到新页面等
                        Toast.makeText(context, "疯狂", Toast.LENGTH_SHORT).show()
                    },
                )

            withBuilder {
                withLink(clickableAnnotation) {
                    // 3. 为了让它看起来像链接，再给它加上样式
                    withStyle(
                        style =
                            SpanStyle(
                                fontWeight = FontWeight.Bold,
                                textDecoration = TextDecoration.Underline, // 添加下划线
                            ),
                    ) {
                        append("《用户协议》")
                    }
                }
            }

            append("\n例如，这个图标尺寸是固定的: ")

            // 使用新的 InlineSizedContent，性能更高
            val iconSize = 50.dp
            InlineSizedContent(width = iconSize, height = iconSize) {
                Icon(
                    imageVector = Icons.Default.Info,
                    contentDescription = "Info",
                    modifier = Modifier.fillMaxSize(),
                    tint = MaterialTheme.colorScheme.secondary,
                )
            }

            append("\n而对于尺寸未知的网络图片: ")

            // 仍然使用 InlineContent，它会自动测量
            InlineContent {
                AsyncImage(
                    model = "https://picsum.photos/id/237/150/100",
                    contentDescription = "小狗",
                    modifier =
                        Modifier.clickable {
                            Toast.makeText(context, "大结局", Toast.LENGTH_SHORT).show()
                        },
                )
            }
            append("，它仍然能完美处理。")
        }
    }
}
