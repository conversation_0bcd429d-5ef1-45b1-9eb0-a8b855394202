package com.buque.wakoo.ui.screens.liveroom

import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.bean.RoomOnlineInfo
import com.buque.wakoo.bean.User
import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.repository.LiveRoomRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.serialization.Serializable

@Suppress("ktlint:standard:property-naming")
enum class RoomRole(
    val value: Int,
) {
    Owner(0),
    Admin(1),
    Member(2),
}

data class RoomMember(
    val user: User,
    @Volatile var inRoom: Boolean, // 是否在房间(房主始终认为在房间), 可以随时变更，不需要感知, 这个值不是那么重要，主要是缓存大小控制
    private val roomInfo: LiveRoomInfoState,
    @Volatile var referenceCounter: Int = 0, // 引用计数，可以随时变更，不需要感知，不再房间且计数为0会被移除
) {
    val id: String = user.id

    val isFollowing: Boolean
        get() = if (user is UserInfo) user.extra.isFollowed else false
}

sealed interface MicSeatsInfo {
    val index: Int

    val isEmpty: Boolean
        get() = this is Empty

    data class Empty(
        override val index: Int,
    ) : MicSeatsInfo

    data class User(
        override val index: Int,
        val user: UserInfo,
        val score: Int,
    ) : MicSeatsInfo
}

data class LiveRoomExtraInfo(
    val reqUpMicCount: Int = 0,
    val reqUpMicIng: Boolean = false,
)

/**
 * 房间基础信息，基本不变化或变化很小的
 */
data class LiveRoomInfoState(
    private val basicInfoState: State<BasicRoomInfo>,
    private val onlineInfo: RoomOnlineInfo,
    private val allUserMapState: Map<String, RoomMember>,
    private val micListState: List<MicSeatsInfo>,
    private val adminIdsState: Set<String>,
    private val blackIdsState: Set<String>,
    private val extraInfoState: State<LiveRoomExtraInfo>,
    private val loadingState: State<Boolean>,
    private val repository: LiveRoomRepository,
) {
    companion object {
        val preview get() = LiveRoomRepository(BasicRoomInfo.preview, { _, _ -> }).roomInfoState
    }

    val events: Flow<RoomEvent>
        get() = repository.events

    val id: String = basicInfo.id

    val basicInfo: BasicRoomInfo
        get() = basicInfoState.value

    val isInvalid: Boolean
        get() = basicInfo.ownerIsInvalid || basicInfo.roomMode.isUnKnown

    val onlineCount: Int
        get() = onlineInfo.totalCount.value

    val onlinePreviewList: List<User>
        get() = onlineInfo.previewList

    val extraInfo: LiveRoomExtraInfo
        get() = extraInfoState.value

    val allUserMap: Map<String, RoomMember>
        get() = allUserMapState

    val micList: List<MicSeatsInfo>
        get() = micListState

    val adminIds: Set<String>
        get() = adminIdsState

    val blackIds: Set<String>
        get() = blackIdsState

    val isLoading: Boolean
        get() = loadingState.value

    fun sendEvent(event: RoomEvent) {
        repository.sendEvent(event)
    }

    @Composable
    fun rememberInBlackListState(userId: String): State<Boolean> =
        remember(userId, blackIdsState) {
            derivedStateOf {
                isInBlackList(userId)
            }
        }

    @Composable
    fun rememberRoomRoleState(userId: String): State<RoomRole> =
        remember(userId, basicInfo.ownerId, adminIdsState) {
            derivedStateOf {
                getRoomRole(userId)
            }
        }

    @Composable
    fun rememberInMicState(userId: String): State<Boolean> =
        remember(userId, micListState) {
            derivedStateOf {
                isInMic(userId)
            }
        }

    @Composable
    fun rememberMuteState(userId: String) =
        remember(userId) {
            derivedStateOf {
                repository.rtcHelper.getMicStateById(userId).isMuted
            }
        }

    @Composable
    fun rememberVolumeState(userId: String) =
        remember(userId) {
            derivedStateOf {
                repository.rtcHelper.getMicStateById(userId).let {
                    if (it.isMuted) {
                        0f
                    } else {
                        it.volume / 90f
                    }
                }
            }
        }

    @Composable
    fun trackRoomMember(
        user: User,
        needFetchLatest: Boolean = true,
    ): RoomMember = repository.trackRoomMember(user, needFetchLatest)

    fun isInBlackList(userId: String): Boolean = blackIdsState.contains(userId)

    fun getRoomRole(userId: String): RoomRole =
        if (basicInfo.ownerId == userId) {
            RoomRole.Owner
        } else if (adminIdsState.contains(userId)) {
            RoomRole.Admin
        } else {
            RoomRole.Member
        }

    fun isInMic(userId: String): Boolean = micListState.any { it is MicSeatsInfo.User && it.user.id == userId }

    fun requireRoomUser(user: User): User = repository.requireRoomUser(user)
}

@Serializable
sealed interface LiveRoomMode {
    val value: Int

    companion object {
        fun valueOf(value: Int): LiveRoomMode =
            when (value) {
                Normal.value -> Normal
                Radio.value -> Radio
                else -> UnKnown(value)
            }
    }

    fun stringOf(): String

    val isUnKnown: Boolean
        get() = this is UnKnown

    @Serializable
    object Normal : LiveRoomMode {
        override val value: Int = 5

        override fun stringOf(): String = "闲聊模式"
    }

    @Serializable
    object Radio : LiveRoomMode {
        override val value: Int = 4

        override fun stringOf(): String = "电台模式"
    }

    @Serializable
    data class UnKnown(
        override val value: Int,
    ) : LiveRoomMode {
        override fun stringOf(): String = "未知模式"
    }
}

@Serializable
sealed interface LiveMicMode {
    val value: Int

    companion object {
        fun valueOf(value: Int): LiveMicMode =
            when (value) {
                Free.value -> Free
                Request.value -> Request
                else -> UnKnown(value)
            }
    }

    val isUnKnown: Boolean
        get() = this is UnKnown

    fun stringOf(): String

    @Serializable
    object Free : LiveMicMode {
        override val value: Int = 1

        override fun stringOf(): String = "自由上麦模式"
    }

    @Serializable
    object Request : LiveMicMode {
        override val value: Int = 2

        override fun stringOf(): String = "请求上麦模式"
    }

    @Serializable
    data class UnKnown(
        override val value: Int,
    ) : LiveMicMode {
        override fun stringOf(): String = "未知模式"
    }
}
