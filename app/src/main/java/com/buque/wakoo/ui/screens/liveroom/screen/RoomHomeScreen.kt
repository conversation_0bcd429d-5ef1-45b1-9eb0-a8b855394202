package com.buque.wakoo.ui.screens.liveroom.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im_business.message.ui.entry.GiftMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.LiveRoomSystemMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.LiveRoomUserSystemMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.RecallMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.RoomContent
import com.buque.wakoo.im_business.message.ui.entry.TextMsgEntry
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.rememberDialogControllerWithParamsScope
import com.buque.wakoo.ui.screens.liveroom.InputTextState
import com.buque.wakoo.ui.screens.liveroom.LiveMicLayout
import com.buque.wakoo.ui.screens.liveroom.LiveRoomBottomLayout
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInputLayout
import com.buque.wakoo.ui.screens.liveroom.LiveRoomTopLayout
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.overScrollVertical
import com.buque.wakoo.ui.widget.rememberOverscrollFlingBehavior
import com.buque.wakoo.viewmodel.liveroom.LiveRoomViewModel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

@Composable
fun LiveRoomScreen(viewModel: LiveRoomViewModel) {
    val roomInfoState = viewModel.roomInfoState
    Box(
        modifier = Modifier.fillMaxSize(),
    ) {
        val data =
            if (!roomInfoState.basicInfo.background.isNullOrBlank()) {
                roomInfoState.basicInfo.background
            } else {
                R.drawable.bg_live_room_radio
            }
        NetworkImage(
            data = data,
            modifier = Modifier.fillMaxSize(),
            placeholder = painterResource(R.drawable.bg_live_room_radio),
            error = painterResource(R.drawable.bg_live_room_radio),
        )

        if (roomInfoState.isInvalid) {
            return
        }

        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .systemBarsPadding(),
            verticalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            // 语音房顶部操作栏
            LiveRoomTopLayout(roomInfoState)

            Box(modifier = Modifier.fillMaxWidth()) {
                // 语音房麦位
                LiveMicLayout(roomInfoState)
            }

            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .weight(1f),
            ) {
                val isLoading by remember {
                    derivedStateOf {
                        viewModel.paginateState.nextLoadState.isLoading
                    }
                }

                val isEnd by remember {
                    derivedStateOf {
                        viewModel.paginateState.nextLoadState.isEnd
                    }
                }

                val listState = rememberLazyListState()

                viewModel.bindListState(listState)

                LazyColumn(
                    modifier =
                        Modifier
                            .fillMaxSize()
                            .overScrollVertical(),
                    state = listState,
                    contentPadding = PaddingValues(vertical = 16.dp, horizontal = 16.dp),
                    reverseLayout = true,
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                    flingBehavior = rememberOverscrollFlingBehavior { listState },
                ) {
                    items(viewModel.messageList, contentType = { item ->
                        item::class.simpleName
                    }) { item ->
                        val uiEntry = item.uiEntry
                        when (uiEntry) {
                            is RecallMsgEntry -> {
                                uiEntry.RoomContent(roomInfoState)
                            }

                            is TextMsgEntry -> {
                                uiEntry.RoomContent(roomInfoState)
                            }

                            is GiftMsgEntry -> {
                                uiEntry.RoomContent(roomInfoState)
                            }

                            is LiveRoomSystemMsgEntry -> {
                                uiEntry.RoomContent(roomInfoState)
                            }

                            is LiveRoomUserSystemMsgEntry -> {
                                uiEntry.RoomContent(roomInfoState)
                            }

                            else -> Spacer(Modifier)
                        }
                    }

                    if (isLoading) {
                        item(key = "isLoading", contentType = "isLoading") {
                            Box(
                                modifier =
                                    Modifier
                                        .padding(top = 10.dp)
                                        .fillMaxWidth(),
                                contentAlignment = Alignment.Center,
                            ) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(16.dp),
                                    color = WakooGreen,
                                    strokeWidth = 1.5.dp,
                                )
                            }
                        }
                    }

                    item(key = "room_system_announcement", contentType = "room_system_announcement") {
                        val density = LocalDensity.current
                        val fontSize =
                            with(density) {
                                10.dp.toPx().toSp()
                            }

                        Box(
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .background(Color(0x1AFFFFFF), RoundedCornerShape(8.dp))
                                    .padding(8.dp),
                        ) {
                            Text(
                                text = "官方严禁未成年人充值，严禁宣传政治、色情、暴力等内容，如有任何违法违规行为请及时向官方举报。",
                                fontSize = fontSize,
                                lineHeight = fontSize * 1.3f,
                                color = Color.White.copy(alpha = 0.5f),
                            )
                        }
                    }
                }
            }

            LiveRoomBottomLayout(roomInfoState) {
                viewModel.setInputTextState(InputTextState.Visible())
            }

            SizeHeight(10.dp)
        }

        LiveRoomInputLayout(
            inputState = viewModel.inputTextState.value,
            onInputStateChange = {
                viewModel.setInputTextState(it)
            },
            modifier =
                Modifier
                    .imePadding()
                    .fillMaxWidth()
                    .background(Color.White),
        ) {
            viewModel.sendMessage(MessageBundle.Text.create(it))
        }
    }

    val dialogController = rememberDialogControllerWithParamsScope(roomInfoState)

    LaunchedEffect(dialogController, roomInfoState) {
        roomInfoState.events
            .onEach { event ->
                if (event is RoomEvent.Dialog) {
                    dialogController.easyPost(
                        dialogProperties = event.dialogProperties,
                        content = {
                            event.content(this, it as LiveRoomInfoState)
                        },
                    )
                } else if (event is RoomEvent.RestorableDialog) {
                    dialogController.post(event.destination)
                } else if (event is RoomEvent.At) {
                    viewModel.setInputTextState(InputTextState.Visible("@${event.user.name} "))
                }
            }.launchIn(this)
    }
}

@Preview
@Composable
private fun LiveRoomScreenPreview() {
    WakooTheme {
        LiveRoomScreen(viewModel<LiveRoomViewModel>(factory = LiveRoomViewModel.Factory(BasicRoomInfo.preview)))
    }
}
