package com.buque.wakoo.ui.widget.pagination

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.listSaver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter

/**
 * 创建一个分页状态
 */
@Composable
fun <Key : Any> rememberPaginateState(
    initialNextKey: Key? = null,
    initialPrevKey: Key? = null,
    nextEnabled: Boolean = true,
    prevEnabled: Boolean = false,
    buffer: Int = 12,
): PaginateState<Key> =
    rememberSaveable(saver = PaginateState.createSaver()) {
        PaginateState(
            initialNextKey = initialNextKey,
            initialPrevKey = initialPrevKey,
            nextEnabled = nextEnabled,
            prevEnabled = prevEnabled,
            buffer = buffer,
        )
    }

/**
 * LazyColumn分页状态管理类，支持向前和向后分页加载
 */
@Stable
class PaginateState<Key : Any> internal constructor(
    initialNextKey: Key? = null,
    initialPrevKey: Key? = null,
    nextEnabled: Boolean = true,
    prevEnabled: Boolean = false,
    private val buffer: Int = 12,
) {
    private var _nextEnabled: Boolean by mutableStateOf(nextEnabled)
    val nextEnabled: Boolean get() = _nextEnabled

    private var _prevEnabled: Boolean by mutableStateOf(prevEnabled)
    val prevEnabled: Boolean get() = _prevEnabled

    var nextLoadState: LoadState by mutableStateOf(LoadState.Idle)
        private set

    var prevLoadState: LoadState by mutableStateOf(LoadState.Idle)
        private set

    private var nextKey: Key? = initialNextKey

    private var prevKey: Key? = initialPrevKey

    /**
     * 获取当前向后分页的键
     */
    fun getNextKey(): Key? = nextKey

    /**
     * 获取当前向前分页的键
     */
    fun getPrevKey(): Key? = prevKey

    /**
     * 启用或禁用向前分页
     */
    fun setPrevEnabled(enabled: Boolean) {
        _prevEnabled = enabled
        if (!enabled) {
            prevLoadState = LoadState.End
        } else if (prevLoadState == LoadState.End) {
            prevLoadState = LoadState.Idle
        }
    }

    /**
     * 启用或禁用向后分页
     */
    fun setNextEnabled(enabled: Boolean) {
        _nextEnabled = enabled
        if (!enabled) {
            nextLoadState = LoadState.End
        } else if (nextLoadState == LoadState.End) {
            nextLoadState = LoadState.Idle
        }
    }

    /**
     * 重置向前分页状态
     */
    fun resetPrev(key: Key? = null) {
        prevKey = key
        prevLoadState = LoadState.Idle
    }

    /**
     * 重置向后分页状态
     */
    fun resetNext(key: Key? = null) {
        nextKey = key
        nextLoadState = LoadState.Idle
    }

    /**
     * 完全重置分页状态
     */
    fun reset(
        prevKey: Key? = null,
        nextKey: Key? = null,
        prevEnabled: Boolean = this.prevEnabled,
        nextEnabled: Boolean = this.nextEnabled,
    ) {
        this.prevKey = prevKey
        this.nextKey = nextKey
        setPrevEnabled(prevEnabled)
        setNextEnabled(nextEnabled)
    }

    fun resetState(
        prevEnable: Boolean?,
        prevKey: Key?,
        nextEnable: Boolean?,
        nextKey: Key?,
    ) {
        if (prevEnable != null) {
            _prevEnabled = prevEnable
            if (prevEnable) {
                this.prevKey = prevKey
                this.prevLoadState = LoadState.Idle
            } else {
                this.prevLoadState = LoadState.End
            }
        }

        if (nextEnable != null) {
            _nextEnabled = nextEnable
            if (nextEnable) {
                this.nextKey = nextKey
                this.nextLoadState = LoadState.Idle
            } else {
                this.nextLoadState = LoadState.End
            }
        }
    }

    /**
     * 重试向后加载
     */
    fun retryNext(): Boolean {
        if (nextEnabled && nextLoadState.isFailure) {
            nextLoadState = LoadState.Idle
            return true
        }
        return false
    }

    /**
     * 重试向前加载
     */
    fun retryPrev(): Boolean {
        if (prevEnabled && prevLoadState.isFailure) {
            prevLoadState = LoadState.Idle
            return true
        }
        return false
    }

    /**
     * 重试所有失败的加载
     */
    fun retry(): Boolean = retryNext() || retryPrev()

    /**
     * 手动触发一次加载
     */
    suspend fun loadTriggerOnce(
        next: Boolean,
        key: Key? = getLoadKey(next),
        ignoreEnabled: Boolean = false,
        onLoad: suspend (PagingScope<Key>) -> LoadResult<Key>,
    ) {
        if (!ignoreEnabled && !getLoadEnabled(next)) {
            return
        }
        if (getLoadState(next).isLoading) {
            return
        }
        loadPageData(next, key, onLoad)
    }

    /**
     * 将分页状态绑定到LazyListState
     */
    @Composable
    fun ConnectToState(
        listState: LazyListState,
        onLoad: suspend (PagingScope<Key>) -> LoadResult<Key>,
    ) {
        if (nextEnabled) {
            LaunchedEffect(listState) {
                val triggerNext by derivedStateOf {
                    val layoutInfo = listState.layoutInfo
                    val totalItemsNumber = layoutInfo.totalItemsCount
                    val lastVisibleItemIndex =
                        (layoutInfo.visibleItemsInfo.lastOrNull()?.index ?: 0) + 1
                    lastVisibleItemIndex > (totalItemsNumber - buffer)
                }

                snapshotFlow {
                    if (nextLoadState.canLoad && triggerNext) {
                        PaginateAction.Load
                    } else if (nextLoadState.isLoading) {
                        PaginateAction.Nothing
                    } else {
                        PaginateAction.Cancel
                    }
                }.filter {
                    it != PaginateAction.Nothing
                }.collectLatest {
                    if (it is PaginateAction.Cancel) {
                        return@collectLatest
                    }

                    loadPageData(next = true, key = nextKey, onLoad = onLoad)
                }
            }
        }

        if (prevEnabled) {
            LaunchedEffect(listState) {
                val triggerPrev by derivedStateOf {
                    val layoutInfo = listState.layoutInfo
                    val firstVisibleItemIndex =
                        layoutInfo.visibleItemsInfo.firstOrNull()?.index ?: 0
                    firstVisibleItemIndex < buffer
                }

                snapshotFlow {
                    if (prevLoadState.canLoad && triggerPrev) {
                        PaginateAction.Load
                    } else if (prevLoadState.isLoading) {
                        PaginateAction.Nothing
                    } else {
                        PaginateAction.Cancel
                    }
                }.filter {
                    it != PaginateAction.Nothing
                }.collectLatest {
                    if (it is PaginateAction.Cancel) {
                        return@collectLatest
                    }

                    loadPageData(next = false, key = prevKey, onLoad = onLoad)
                }
            }
        }
    }


    @Composable
    fun ConnectToGridState(
        gridState: LazyGridState,
        onLoad: suspend (PagingScope<Key>) -> LoadResult<Key>,
    ) {
        if (nextEnabled) {
            LaunchedEffect(gridState) {
                val triggerNext by derivedStateOf {
                    val layoutInfo = gridState.layoutInfo
                    val totalItemsNumber = layoutInfo.totalItemsCount
                    val lastVisibleItemIndex =
                        (layoutInfo.visibleItemsInfo.lastOrNull()?.index ?: 0) + 1
                    lastVisibleItemIndex > (totalItemsNumber - buffer)
                }

                snapshotFlow {
                    if (nextLoadState.canLoad && triggerNext) {
                        PaginateAction.Load
                    } else if (nextLoadState.isLoading) {
                        PaginateAction.Nothing
                    } else {
                        PaginateAction.Cancel
                    }
                }.filter {
                    it != PaginateAction.Nothing
                }.collectLatest {
                    if (it is PaginateAction.Cancel) return@collectLatest
                    loadPageData(next = true, key = nextKey, onLoad = onLoad)
                }
            }
        }

        if (prevEnabled) {
            LaunchedEffect(gridState) {
                val triggerPrev by derivedStateOf {
                    val layoutInfo = gridState.layoutInfo
                    val firstVisibleItemIndex =
                        layoutInfo.visibleItemsInfo.firstOrNull()?.index ?: 0
                    firstVisibleItemIndex < buffer
                }

                snapshotFlow {
                    if (prevLoadState.canLoad && triggerPrev) {
                        PaginateAction.Load
                    } else if (prevLoadState.isLoading) {
                        PaginateAction.Nothing
                    } else {
                        PaginateAction.Cancel
                    }
                }.filter {
                    it != PaginateAction.Nothing
                }.collectLatest {
                    if (it is PaginateAction.Cancel) return@collectLatest
                    loadPageData(next = false, key = prevKey, onLoad = onLoad)
                }
            }
        }
    }

    /**
     * 刷新数据
     */
    suspend fun refresh(
        key: Key,
        next: Boolean,
        onLoad: suspend (PagingScope<Key>) -> LoadResult<Key>,
    ) {
        loadPageData(next, key, onLoad)
    }

    private suspend fun loadPageData(
        next: Boolean,
        key: Key?,
        onLoad: suspend (PagingScope<Key>) -> LoadResult<Key>,
    ) {
        setLoadState(next, LoadState.Loading(key))
        val scope = PagingScope(key, next)
        val result = onLoad(scope)

        PaginateUtils.processLoadResult(
            result = result,
            key = key,
            updateKey = { setLoadKey(next, it) },
            updateState = { setLoadState(next, it) },
        )
    }

    private fun getLoadEnabled(next: Boolean) =
        if (next) {
            nextEnabled
        } else {
            prevEnabled
        }

    private fun setLoadState(
        next: Boolean,
        state: LoadState,
    ) {
        if (next) {
            nextLoadState = state
        } else {
            prevLoadState = state
        }
    }

    private fun getLoadState(next: Boolean) =
        if (next) {
            nextLoadState
        } else {
            prevLoadState
        }

    private fun setLoadKey(
        next: Boolean,
        key: Key?,
    ) {
        if (next) {
            nextKey = key
        } else {
            prevKey = key
        }
    }

    private fun getLoadKey(next: Boolean) =
        if (next) {
            nextKey
        } else {
            prevKey
        }

    companion object {
        /**
         * 创建保存器，用于在配置更改时保存状态
         */
        fun <Key : Any> createSaver() =
            listSaver(
                save = {
                    listOf(
                        it.nextKey,
                        it.prevKey,
                        it._nextEnabled,
                        it._prevEnabled,
                        it.buffer,
                    )
                },
                restore = {
                    PaginateState(
                        initialNextKey = it[0] as? Key?,
                        initialPrevKey = it[1] as? Key?,
                        nextEnabled = it[2] as Boolean,
                        prevEnabled = it[3] as Boolean,
                        buffer = it[4] as Int,
                    )
                },
            )
    }
}
