package com.buque.wakoo.ui.widget.drag

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.offset
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.zIndex
import kotlin.math.roundToInt

@Composable
fun DraggableFloatingItem(
    state: DraggableFloatingState,
    onDragEnd: () -> Unit,
    modifier: Modifier = Modifier,
    // 提供备用动画参数，增加灵活性
    nonStickyEnter: EnterTransition = fadeIn(),
    nonStickyExit: ExitTransition = fadeOut(),
    content: @Composable () -> Unit,
) {
    // *** 核心修正：动态计算进出动画 ***
    val enterTransition =
        if (state.isStickyToEdge) {
            // 如果贴边，根据当前位置判断从哪边滑入
            slideInHorizontally(initialOffsetX = { fullWidth ->
                if (state.isStuckToLeft) -fullWidth else fullWidth
            })
        } else {
            // 如果不贴边，使用备用动画
            nonStickyEnter
        }

    val exitTransition =
        if (state.isStickyToEdge) {
            // 如果贴边，根据当前位置判断滑出到哪边
            slideOutHorizontally(targetOffsetX = { fullWidth ->
                if (state.isStuckToLeft) -fullWidth else fullWidth
            })
        } else {
            // 如果不贴边，使用备用动画
            nonStickyExit
        }

    // 一个不可见的 Box，在 isVisible 为 true 时出现，专门用于测量尺寸
    // 它的存在会触发 onSizeChanged，进而驱动 state.isInitialized 变为 true
    if (state.isVisible && !state.isInitialized) {
        Box(
            modifier =
                Modifier
                    .onSizeChanged { componentSize ->
                        state.setSizes(parent = state.parentSize, component = componentSize)
                    }.graphicsLayer { alpha = 0f },
        ) {
            content()
        }
    }

    // 第二个 Box，负责实际的显示和交互
    AnimatedVisibility(
        // 当 isInitialized 准备好后，它才变得可见，并开始动画
        visible = state.isVisible && state.isInitialized,
        enter = enterTransition,
        exit = exitTransition,
        modifier = Modifier.zIndex(if (state.isBeingDragged) 1f else 0f), // 核心：应用 zIndex
    ) {
        Box(
            modifier =
                modifier
                    .onSizeChanged { componentSize ->
                        state.setSizes(parent = state.parentSize, component = componentSize)
                    }.offset {
                        IntOffset(
                            state.offset.value.x
                                .roundToInt(),
                            state.offset.value.y
                                .roundToInt(),
                        )
                    }.pointerInput(state.isDraggable) {
                        if (state.isDraggable) {
                            detectDragGestures(
                                onDragStart = { state.dragStart() }, // 调用 dragStart
                                onDrag = { change, dragAmount ->
                                    change.consume()
                                    state.drag(dragAmount)
                                },
                                onDragEnd = {
                                    // dragEnd 已经被 manager 拦截了，所以我们需要在 manager 中调用
                                    state.dragEnd()
                                    onDragEnd()
                                },
                                onDragCancel = { state.dragEnd() },
                            )
                        }
                    },
        ) {
            content()
        }
    }
}
