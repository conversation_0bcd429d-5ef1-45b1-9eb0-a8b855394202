package com.buque.wakoo.ui.widget.media.previewer

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.surfaceColorAtElevation
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.buque.wakoo.ext.clipToScreenRect
import com.buque.wakoo.navigation.GalleryScreenKey
import com.buque.wakoo.ui.dialog.FullDialog
import me.saket.telephoto.zoomable.rememberZoomablePeekOverlayState
import me.saket.telephoto.zoomable.zoomablePeekOverlay

@Composable
@OptIn(ExperimentalMaterial3Api::class)
internal fun GalleryScreen(key: GalleryScreenKey) {
    val previewState = rememberPreviewState()

    var clipRect by remember {
        mutableStateOf<Rect?>(null)
    }

    val density = LocalDensity.current

    Scaffold(
        topBar = { TopAppBar(title = { Text("图库") }) },
    ) { contentPadding ->
        AlbumGrid(
            modifier =
                Modifier
                    .padding(contentPadding)
                    .fillMaxSize()
                    .onGloballyPositioned {
                        clipRect = it.boundsInRoot()
                    },
            album = key.album,
            state = previewState,
            onImageClick = { index, _ ->
                previewState.enterPreview(MediaViewerKey(key.album, index), with(density) { 10.dp.toPx() })
            },
        )
    }

    // 转场覆盖层，负责在转场期间显示动画元素和全屏查看器。
    TransitionOverlay(state = previewState, overlayClipRect = clipRect)
}

@Composable
private fun AlbumGrid(
    album: MediaAlbum,
    state: MediaPreviewState,
    modifier: Modifier = Modifier,
    onImageClick: (Int, MediaItem) -> Unit,
) {
    LazyVerticalStaggeredGrid(
        modifier = modifier,
        columns = StaggeredGridCells.Adaptive(minSize = 160.dp),
        contentPadding = PaddingValues(4.dp),
        verticalItemSpacing = 4.dp,
        horizontalArrangement = Arrangement.spacedBy(4.dp),
    ) {
        itemsIndexed(items = album.items) { index, item ->
            Box(
                modifier =
                    with(state) {
                        Modifier
                            .background(MaterialTheme.colorScheme.surfaceColorAtElevation(4.dp))
                            .fillMaxWidth()
                            .aspectRatio(item.aspectRatio)
                            .clickable { onImageClick(index, item) }
                            .registerGridItem(IndexedMediaItem(index, item))
                            .zoomablePeekOverlay(rememberZoomablePeekOverlayState())
                    },
            ) {
                AsyncImage(
                    modifier = Modifier.fillMaxSize(),
                    model =
                        ImageRequest
                            .Builder(LocalContext.current)
                            .data(item.placeholderImageUrl)
                            .memoryCacheKey(item.placeholderImageUrl)
                            .crossfade(300)
                            .build(),
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                )
            }
        }
    }
}

/**
 * 转场覆盖层，它是一个在屏幕最上层的容器，仅在转场进行时显示。
 * 它包含一个全屏对话框，作为承载动画和全屏查看器的“画布”。
 */
@Composable
fun TransitionOverlay(
    state: MediaPreviewState,
    overlayClipRect: Rect? = null,
) {
    BoxWithConstraints(modifier = Modifier.fillMaxSize()) {
        // 1. 让状态管理器获取容器的最新尺寸。
        state.UpdateContainerConstraints(constraints)

        if (state.transitionState is TransitionState.Idle) {
            return@BoxWithConstraints
        }

        // 2. 启动转场生命周期管理，处理状态机和返回事件。
        state.ManageTransitionLifecycle()

        FullDialog(
            onDismissRequest = { state.exitPreview() },
        ) {
            val currentState = state.transitionState

            Box(modifier = Modifier.fillMaxSize()) {
                // 全屏媒体查看器，它在进入动画快结束时才渐显出来。
                if (currentState is TransitionState.Transiting) {
                    Box(
                        modifier =
                            Modifier
                                .fillMaxSize(),
                    ) {
                        MediaPreviewViewer(
                            state = state,
                            overlayClipRect = overlayClipRect,
                            modifier = Modifier.fillMaxSize(),
                        )
                    }
                }

                // 正在移动的共享元素（图片），它在动画期间浮在最上层。
                if (currentState is TransitionState.Transiting && currentState.anim < AnimState.End) {
                    val currentBounds = state.animatableBounds.value

                    val density = LocalDensity.current

                    AsyncImage(
                        model = currentState.indexItem.item.placeholderImageUrl,
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier =
                            Modifier
                                .run {
                                    if (overlayClipRect != null) {
                                        clipToScreenRect(overlayClipRect)
                                    } else {
                                        this
                                    }
                                }.size(
                                    with(density) {
                                        DpSize(currentBounds.width.toDp(), currentBounds.height.toDp())
                                    },
                                ).graphicsLayer {
                                    translationX = currentBounds.left
                                    translationY = currentBounds.top

                                    clip = true
                                    shape =
                                        with(density) {
                                            RoundedCornerShape(state.animatableRadius.value.toDp())
                                        }
                                },
                    )
                }
            }
        }
    }
}
