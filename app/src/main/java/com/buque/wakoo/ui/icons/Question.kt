package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.QuestionLine: ImageVector
    get() {
        if (_QuestionLine != null) {
            return _QuestionLine!!
        }
        _QuestionLine = ImageVector.Builder(
            name = "QuestionLine",
            defaultWidth = 12.dp,
            defaultHeight = 12.dp,
            viewportWidth = 12f,
            viewportHeight = 12f
        ).apply {
            group(
                clipPathData = PathData {
                    moveTo(0f, 0f)
                    horizontalLineToRelative(12f)
                    verticalLineToRelative(12f)
                    horizontalLineToRelative(-12f)
                    close()
                }
            ) {
                path(fill = SolidColor(Color(0xFF86909C))) {
                    moveTo(6f, 1f)
                    curveTo(8.762f, 1f, 11f, 3.239f, 11f, 6f)
                    curveTo(11f, 8.762f, 8.762f, 11f, 6f, 11f)
                    curveTo(3.239f, 11f, 1f, 8.762f, 1f, 6f)
                    curveTo(1f, 3.239f, 3.239f, 1f, 6f, 1f)
                    close()
                    moveTo(6f, 2f)
                    curveTo(4.939f, 2f, 3.922f, 2.421f, 3.172f, 3.172f)
                    curveTo(2.421f, 3.922f, 2f, 4.939f, 2f, 6f)
                    curveTo(2f, 7.061f, 2.421f, 8.078f, 3.172f, 8.828f)
                    curveTo(3.922f, 9.579f, 4.939f, 10f, 6f, 10f)
                    curveTo(7.061f, 10f, 8.078f, 9.579f, 8.828f, 8.828f)
                    curveTo(9.579f, 8.078f, 10f, 7.061f, 10f, 6f)
                    curveTo(10f, 4.939f, 9.579f, 3.922f, 8.828f, 3.172f)
                    curveTo(8.078f, 2.421f, 7.061f, 2f, 6f, 2f)
                    close()
                    moveTo(6f, 8f)
                    curveTo(6.133f, 8f, 6.26f, 8.053f, 6.354f, 8.146f)
                    curveTo(6.447f, 8.24f, 6.5f, 8.367f, 6.5f, 8.5f)
                    curveTo(6.5f, 8.633f, 6.447f, 8.76f, 6.354f, 8.854f)
                    curveTo(6.26f, 8.947f, 6.133f, 9f, 6f, 9f)
                    curveTo(5.867f, 9f, 5.74f, 8.947f, 5.646f, 8.854f)
                    curveTo(5.553f, 8.76f, 5.5f, 8.633f, 5.5f, 8.5f)
                    curveTo(5.5f, 8.367f, 5.553f, 8.24f, 5.646f, 8.146f)
                    curveTo(5.74f, 8.053f, 5.867f, 8f, 6f, 8f)
                    close()
                    moveTo(6f, 3.25f)
                    curveTo(6.421f, 3.25f, 6.829f, 3.397f, 7.154f, 3.665f)
                    curveTo(7.479f, 3.933f, 7.7f, 4.306f, 7.78f, 4.719f)
                    curveTo(7.859f, 5.133f, 7.793f, 5.561f, 7.591f, 5.931f)
                    curveTo(7.389f, 6.301f, 7.065f, 6.588f, 6.674f, 6.745f)
                    curveTo(6.616f, 6.767f, 6.564f, 6.801f, 6.522f, 6.845f)
                    curveTo(6.499f, 6.871f, 6.496f, 6.903f, 6.497f, 6.936f)
                    lineTo(6.5f, 7f)
                    curveTo(6.5f, 7.127f, 6.451f, 7.25f, 6.364f, 7.343f)
                    curveTo(6.276f, 7.435f, 6.157f, 7.491f, 6.029f, 7.499f)
                    curveTo(5.902f, 7.506f, 5.777f, 7.465f, 5.679f, 7.383f)
                    curveTo(5.581f, 7.301f, 5.519f, 7.185f, 5.503f, 7.058f)
                    lineTo(5.5f, 7f)
                    verticalLineTo(6.875f)
                    curveTo(5.5f, 6.299f, 5.965f, 5.952f, 6.302f, 5.817f)
                    curveTo(6.439f, 5.762f, 6.559f, 5.671f, 6.648f, 5.553f)
                    curveTo(6.737f, 5.436f, 6.793f, 5.296f, 6.809f, 5.149f)
                    curveTo(6.825f, 5.002f, 6.8f, 4.854f, 6.738f, 4.72f)
                    curveTo(6.676f, 4.586f, 6.579f, 4.472f, 6.456f, 4.389f)
                    curveTo(6.334f, 4.306f, 6.191f, 4.258f, 6.044f, 4.25f)
                    curveTo(5.896f, 4.242f, 5.75f, 4.275f, 5.619f, 4.344f)
                    curveTo(5.489f, 4.413f, 5.38f, 4.517f, 5.304f, 4.643f)
                    curveTo(5.228f, 4.77f, 5.187f, 4.915f, 5.188f, 5.063f)
                    curveTo(5.188f, 5.195f, 5.135f, 5.322f, 5.041f, 5.416f)
                    curveTo(4.947f, 5.51f, 4.82f, 5.563f, 4.688f, 5.563f)
                    curveTo(4.555f, 5.563f, 4.428f, 5.51f, 4.334f, 5.416f)
                    curveTo(4.24f, 5.322f, 4.188f, 5.195f, 4.188f, 5.063f)
                    curveTo(4.188f, 4.582f, 4.378f, 4.121f, 4.718f, 3.781f)
                    curveTo(5.058f, 3.441f, 5.519f, 3.25f, 6f, 3.25f)
                    close()
                }
            }
        }.build()

        return _QuestionLine!!
    }

@Suppress("ObjectPropertyName")
private var _QuestionLine: ImageVector? = null
