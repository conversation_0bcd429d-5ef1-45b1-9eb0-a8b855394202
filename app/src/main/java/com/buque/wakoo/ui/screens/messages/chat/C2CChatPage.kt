package com.buque.wakoo.ui.screens.messages.chat

import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.User
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.bean.MsgSendCondition
import com.buque.wakoo.im.bean.SendParams
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.inter.MsgFilter
import com.buque.wakoo.im.utils.WatchMessageEventEffect
import com.buque.wakoo.im_business.interf.IC2CAction
import com.buque.wakoo.im_business.message.MessageC2CUserScaffold
import com.buque.wakoo.im_business.message.MessageThemeBubble
import com.buque.wakoo.im_business.message.UIMessageEntry
import com.buque.wakoo.im_business.message.ui.entry.C2CContent
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
import com.buque.wakoo.im_business.message.ui.entry.TimeMsgEntry
import com.buque.wakoo.im_business.panel.voice.AudioCenterStatusWidget
import com.buque.wakoo.im_business.viewmodel.C2CChatViewModel
import com.buque.wakoo.im_business.wigets.C2CBottomBar
import com.buque.wakoo.im_business.wigets.C2CBottomPanel
import com.buque.wakoo.im_business.wigets.ChatScaffold
import com.buque.wakoo.im_business.wigets.KeyboardPanelState
import com.buque.wakoo.im_business.wigets.rememberPanelState
import com.buque.wakoo.im_business.wigets.withAutoHidePanel
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.navigation.rememberLauncherForResult
import com.buque.wakoo.ui.dialog.AnyPopDialogProperties
import com.buque.wakoo.ui.icons.More
import com.buque.wakoo.ui.icons.Report
import com.buque.wakoo.ui.icons.UserForbidLine
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.widget.WakooTitleBar
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults
import com.buque.wakoo.ui.widget.gift.C2CBottomGiftPanel
import com.buque.wakoo.ui.widget.gift.GiftViewModel
import com.buque.wakoo.ui.widget.media.data.common.MediaItem
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.media.manager.pauseIf
import com.buque.wakoo.ui.widget.media.manager.releaseIf
import com.buque.wakoo.ui.widget.overScrollVertical
import com.buque.wakoo.ui.widget.pagination.PaginateState
import com.buque.wakoo.ui.widget.popup.BubbleShape
import com.buque.wakoo.ui.widget.rememberOverscrollFlingBehavior
import com.buque.wakoo.utils.topFadingEdge

@Composable
fun C2CChatScreen(
    user: User,
    viewModel: C2CChatViewModel,
    modifier: Modifier = Modifier,
) {
    val rootNavController = LocalAppNavController.root

    val listState = rememberLazyListState()

    val panelState = rememberPanelState(listState, viewModel.panels, 12)

    val targetUser by viewModel.targetUserFlow.collectAsStateWithLifecycle()

    val selfUser = SelfUser!!.basic

    val giftViewModel =
        viewModel<GiftViewModel>(initializer = {
            GiftViewModel(user.id, ConversationType.C2C)
        })

    WatchMessageEventEffect(
        object : IMCompatListener {
            override val filter: MsgFilter = MsgFilter(targetUser.id)

            override fun onMessageCheckFail(
                condition: MsgSendCondition?,
                throwable: Throwable?,
            ) {
                if (condition != null) {
                    showToast(condition.toastMsg.ifEmpty { condition.toastMsg2 })
                }
            }
        },
    )

    val launcher =
        rootNavController.rememberLauncherForResult<Route.MediaSelector, List<MediaItem>> { result ->
            if (result.isNotEmpty()) {
                IMCompatCore.sendMessages(
                    SendParams(user.id, ConversationType.C2C),
                    result.map {
                        MessageBundle.Image.create(it.uriString.toUri(), "", it.width, it.height)
                    },
                )
            }
        }

    val dialogController = rememberDialogController(key = "c2c-chat")

    val onAction =
        remember {
            object : IC2CAction {
                override fun onSendMessage(message: MessageBundle) {
                    IMCompatCore.sendC2CMessage(targetUser.id, message)
                }

                override fun onSendMultipleMessage(messages: List<MessageBundle>) {
                    if (messages.isNotEmpty()) {
                        IMCompatCore.sendMessages(SendParams(user.id, ConversationType.C2C), messages)
                    } else {
                        showToast("消息发送失败")
                    }
                }

                override fun onResendMessage(message: UCInstanceMessage) {
                    IMCompatCore.sendMessage(SendParams(user.id, ConversationType.C2C), message.base)
                }

                override fun onReportUser() {
                    rootNavController.push(Route.Report(1, targetUser.id))
                }

                override fun onForbidUser() {
                    viewModel.blackUser(targetUser.id)
                }

                override fun onGoMediaSelector() {
                    launcher.launch(Route.MediaSelector())
                }

                override fun onShowGiftPanel() {
                    dialogController.easyPost(
                        dialogProperties = AnyPopDialogProperties(useCustomAnimation = true),
                        content = {
                            val giftModel = giftViewModel.giftListModelState
                            C2CBottomGiftPanel(null, giftModel, {
                                showToast("充值金币弹窗")
                            }) { gift, params ->
                                giftViewModel.sendGiftC2C(gift, params)
                            }
                        },
                    )
                }
            }
        }

    val lifecycleOwner = LocalLifecycleOwner.current

    // 清理资源
    DisposableEffect(Unit) {
        val observer =
            LifecycleEventObserver { source, event ->
                if (event == Lifecycle.Event.ON_STOP) {
                    MediaPlayerManager.pauseIf {
                        it.tag.startsWith("voiceMsg")
                    }
                }
            }

        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            MediaPlayerManager.releaseIf {
                it.tag.startsWith("voiceMsg")
            }
        }
    }

    Box(
        modifier = Modifier.fillMaxSize(),
    ) {
        val paginateState = viewModel.bindListState(listState)
        C2CChatPage(
            targetUser = targetUser,
            selfUser = selfUser,
            messageList = viewModel.messageList,
            paginateState = paginateState,
            listState = listState,
            panelState = panelState,
            modifier = modifier,
            overlayContent = {
                AudioCenterStatusWidget()
                giftViewModel.GiftEffectView()
            },
            safeContent = {
            },
            onAction = onAction,
        )
    }
}

@Composable
private fun C2CChatPage(
    targetUser: User,
    selfUser: User,
    messageList: List<UIMessageEntry>,
    paginateState: PaginateState<*>,
    listState: LazyListState,
    panelState: KeyboardPanelState,
    modifier: Modifier = Modifier,
    hiddenModules: List<String>? = null,
    overlayContent: @Composable BoxScope.() -> Unit = {},
    safeContent: @Composable BoxScope.() -> Unit = {},
    onAction: IC2CAction,
) {
    val textFieldValue =
        rememberSaveable(stateSaver = TextFieldValue.Saver) {
            mutableStateOf(TextFieldValue())
        }

    var expanded by rememberSaveable { mutableStateOf(false) }

    ChatScaffold(
        panelState = panelState,
        modifier = modifier.background(Color.White),
        topBar = {
            WakooTitleBar(targetUser.name, actions = {
                Box {
                    WakooTitleBarDefaults.IconButtonAction(
                        imageVector = WakooIcons.More,
                        onClick = {
                            expanded = true
                        },
                    )

                    val bubbleShape =
                        remember {
                            BubbleShape(arrowPositionBias = 0.85f)
                        }

                    // 弹出菜单主体
                    DropdownMenu(
                        expanded = expanded, // 菜单的展开状态
                        onDismissRequest = { expanded = false }, // 点击菜单外部或按返回键时关闭菜单
                        shape = bubbleShape,
                        offset = DpOffset((-6).dp, (-12).dp),
                        tonalElevation = 0.dp,
                        shadowElevation = 1.dp,
                        containerColor = Color.White,
                    ) {
                        // 菜单项
                        Row(
                            modifier =
                                Modifier
                                    .clickable(onClick = {
                                        expanded = false
                                        onAction.onReportUser()
                                    })
                                    .padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                        ) {
                            Icon(
                                imageVector = WakooIcons.Report,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp),
                                tint = Color(0xFF111111),
                            )
                            Text(
                                text = "举报",
                                modifier = Modifier.weight(1f),
                                color = Color(0xFF666666),
                                fontSize = 14.sp,
                                textAlign = TextAlign.Center,
                            )
                        }

                        HorizontalDivider(
                            modifier = Modifier.padding(horizontal = 12.dp),
                            thickness = 0.5.dp,
                            color = Color(0xFFE5E5E5),
                        )

                        Row(
                            modifier =
                                Modifier
                                    .clickable(onClick = {
                                        expanded = false
                                        onAction.onForbidUser()
                                    })
                                    .padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                        ) {
                            Icon(
                                imageVector = WakooIcons.UserForbidLine,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp),
                                tint = Color(0xFF111111),
                            )
                            Text(
                                text = "拉黑",
                                modifier = Modifier.weight(1f),
                                color = Color(0xFF666666),
                                fontSize = 14.sp,
                                textAlign = TextAlign.Center,
                            )
                        }
                    }
                }
            })
        },
        bottomBar = {
            C2CBottomBar(panelState, textFieldValue, hiddenModules, onAction)
        },
        panelContent = {
            C2CBottomPanel(panelState, textFieldValue, hiddenModules, onAction)
        },
        overlayContent = overlayContent,
        safeContent = safeContent,
    ) {
        val isLoading by remember {
            derivedStateOf {
                paginateState.nextLoadState.isLoading
            }
        }

        val isEnd by remember {
            derivedStateOf {
                paginateState.nextLoadState.isEnd
            }
        }

        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .background(Color(0xFFF5F7F9)),
        ) {
            Column(
                modifier =
                    Modifier.topFadingEdge(
                        color = Color(0x15CCCCCC),
                        width = 20.dp,
                        spec = spring(),
                        isVisible = listState.canScrollBackward,
                    ),
            ) {
                LazyColumn(
                    modifier =
                        Modifier
                            .withAutoHidePanel(panelState)
                            .fillMaxSize()
                            .overScrollVertical(),
                    state = listState,
                    reverseLayout = true,
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                    flingBehavior = rememberOverscrollFlingBehavior { listState },
                ) {
                    item {
                        Spacer(Modifier.height(8.dp))
                    }

                    items(messageList, contentType = { item ->
                        item::class.simpleName
                    }) { item ->
                        val uiEntry = item.uiEntry
                        when (uiEntry) {
                            is MsgLayoutContent -> {
                                uiEntry.Render({
                                    MessageC2CUserScaffold(item, onAction) { it() }
                                }, {
                                    MessageThemeBubble(entry = item) { it() }
                                }, onAction)
                            }

                            is TimeMsgEntry -> {
                                uiEntry.C2CContent()
                            }

                            else -> Spacer(Modifier)
                        }
                    }

                    if (isLoading) {
                        item(key = "isLoading", contentType = "isLoading") {
                            Box(
                                modifier =
                                    Modifier
                                        .padding(top = 10.dp)
                                        .fillMaxWidth(),
                                contentAlignment = Alignment.Center,
                            ) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(16.dp),
                                    strokeWidth = 1.5.dp,
                                )
                            }
                        }
                    }

                    if (isEnd) {
                        item(key = "userCard", contentType = "userCard") {
//                            UserCardItem(
//                                user = targetUser,
//                                modifier = Modifier
//                                    .padding(horizontal = 16.dp)
//                                    .fillMaxWidth()
//                                    .clip(Shapes.corner12)
//                                    .background(Color.White),
//                                onCellClick = {
//                                    onAction.onNavigateTo(profileDestination(targetUser.id))
//                                }
//                            )
                        }
                    }
                }
            }
        }
    }
}

// @Composable
// private fun UserCardItem(
//    user: User,
//    modifier: Modifier = Modifier,
//    onCellClick: OnClick = {},
//    onButtonClick: OnClick = { onCellClick() },
// ) {
//    Row(
//        modifier = modifier
//            .clickable(onClick = onCellClick)
//            .padding(12.dp)
//    ) {
//        Box(modifier = Modifier.size(48.dp)) {
//            ComposeImage(
//                model = user.avatarUrl, modifier = Modifier
//                    .fillMaxSize()
//                    .clip(CircleShape)
//            )
//
//            if (user.onlineStatus == 0) {
//                Spacer(
//                    modifier = Modifier
//                        .size(10.dp)
//                        .clip(CircleShape)
//                        .background(Color(0xFF18E046))
//                        .border(1.dp, Color.White, CircleShape)
//                        .align(Alignment.BottomEnd)
//                )
//            }
//        }
//        Column(
//            modifier = Modifier
//                .animateContentSize()
//                .fillMaxWidth()
//                .padding(start = 8.dp)
//        ) {
//            Row(
//                modifier = Modifier.fillMaxWidth(),
//                verticalAlignment = Alignment.CenterVertically
//            ) {
//                Column(
//                    modifier = Modifier
//                        .animateContentSize()
//                        .weight(1f),
//                    verticalArrangement = Arrangement.spacedBy(2.dp),
//                ) {
//                    Row(verticalAlignment = Alignment.CenterVertically) {
//                        Text(
//                            modifier = Modifier.weight(1f, false),
//                            text = user.nickname,
//                            fontSize = 16.sp,
//                            fontWeight = FontWeight.Medium,
//                            maxLines = 1,
//                            overflow = TextOverflow.Ellipsis
//                        )
//                        Spacer(modifier = Modifier.width(2.dp))
//                        AgeGender(age = user.age, isBoy = user.isBoy)
//                    }
//
//                    val tags = remember(user.locationLabel, user.height, user.career) {
//                        listOf(
//                            user.locationLabel.ifEmpty {
//                                user.nativeProfile?.cityCode?.name
//                            },
//                            if (user.height > 0) "${user.height}cm" else "",
//                            user.career.ifEmpty {
//                                user.nativeProfile?.job?.name
//                            }
//                        ).filter { !it.isNullOrBlank() }
//                            .joinToString(separator = " | ")
//                    }
//                    if (tags.isNotEmpty()) {
//                        //标签
//                        Text(text = tags, fontSize = 12.sp, color = Color(0xFF4E5969))
//                    }
//                }
//
//                AppButton(
//                    text = stringResource(id = R.string.cpd主页),
//                    modifier = Modifier
//                        .padding(start = 4.dp, top = 4.dp)
//                        .widthIn(min = 56.dp)
//                        .height(24.dp),
//                    background = Color.Transparent,
//                    color = Color(0xFFFF5E8B),
//                    fontSize = 12.sp,
//                    border = BorderStroke(0.5.dp, Color(0xFFFF5E8B)),
//                    contentPadding = PaddingValues(horizontal = 10.dp),
//                    onClick = onButtonClick
//                )
//            }
//
//            Spacer(modifier = Modifier.height(4.dp))
//            //简介
//            Text(
//                text = user.shortIntro.ifEmpty { stringResource(id = user.introEmptyRes) },
//                fontSize = 12.sp,
//                color = Color(0xFF86909C),
//                maxLines = 2,
//                overflow = TextOverflow.Ellipsis
//            )
//
//            val images = remember {
//                user.albumList.asReversed().take(4)
//            }
//            if (images.isNotEmpty()) {
//                Spacer(modifier = Modifier.height(12.dp))
//                Row(
//                    modifier = Modifier.fillMaxWidth(),
//                    horizontalArrangement = Arrangement.spacedBy(8.dp)
//                ) {
//                    images.forEach { item ->
//                        ComposeImage(
//                            model = item.url, modifier = Modifier
//                                .size(60.dp)
//                                .clip(RoundedCornerShape(8.dp))
//                        )
//                    }
//                }
//            }
//        }
//    }
// }

// @Composable
// private fun RechargeCoinToChatPanel(
//    title: String,
//    hint: String,
//    leftAvatar: String = "",
//    rightAvatar: String = "",
//    onDismiss: OnClick = {}
// ) {
//    val vm = viewModel<WalletViewModel>()
//    LaunchedEffect(key1 = vm) {
//        vm.actionFlow.onEach {
//            when (it) {
//                WalletViewModel.ACTION_OPEN_AGENT_CHAT -> {
//                    onDismiss()
//                }
//
//                else -> {}
//            }
//        }.launchIn(this)
//    }
//    RechargePageScaffold(viewModel = vm) { purchaseHelper, flow, onConfirm ->
//        RechargeCoinToChatPanel(
//            purchaseHelper = purchaseHelper,
//            flow = flow,
//            title = title,
//            hint = hint,
//            leftAvatar = leftAvatar,
//            rightAvatar = rightAvatar,
//            onConfirm = onConfirm
//        )
//    }
// }
//
//
// @Composable
// private fun RechargeCoinToChatPanel(
//    purchaseHelper: AppPurchaseHelper,
//    flow: Flow<List<Purchase>>,
//    title: String = stringResource(id = R.string.充值金币继续和她聊天),
//    hint: String = stringResource(R.string.你的免费聊天次数已用完),
//    leftAvatar: String = "",
//    rightAvatar: String = "",
//    onConfirm: () -> Unit = {},
// ) {
//    val density = LocalDensity.current
//    Box(
//        modifier = Modifier
//            .fillMaxWidth()
//            .padding(top = 16.dp)
//            .background(Color.White, RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp))
//            .paint(
//                painterResource(id = R.drawable.bg_top_recharge_coin_to_chat),
//                contentScale = ContentScale.FillWidth,
//                alignment = Alignment.TopCenter
//            )
//            .padding(bottom = with(density) {
//                WindowInsets.navigationBars
//                    .getBottom(density)
//                    .toDp()
//            }.coerceAtLeast(15.dp))
//    ) {
//
//        Row(
//            modifier = Modifier
//                .align(Alignment.TopCenter)
//                .offset(y = (-16).dp),
//            horizontalArrangement = Arrangement.spacedBy((-16).dp)
//        ) {
//            CircleComposeImage(
//                model = leftAvatar, modifier = Modifier
//                    .size(80.dp)
//                    .border(0.75.dp, Color.White, CircleShape)
//            )
//            CircleComposeImage(
//                model = rightAvatar, modifier = Modifier
//                    .size(80.dp)
//                    .border(0.75.dp, Color.White, CircleShape)
//            )
//        }
//
//        Column(
//            modifier = Modifier
//                .fillMaxWidth()
//                .padding(top = 80.dp, start = 16.dp, end = 16.dp),
//            horizontalAlignment = Alignment.CenterHorizontally
//        ) {
//
//            Text(
//                text = title,
//                color = Color(0xFF1D2129),
//                fontSize = 14.sp,
//                fontWeight = FontWeight.Medium
//            )
//
//            Spacer(modifier = Modifier.height(10.dp))
//
//            Text(
//                text = hint,
//                color = Color(0xFF4E5969),
//                fontSize = 12.sp,
//                textAlign = TextAlign.Center
//            )
//
//            Spacer(modifier = Modifier.height(15.dp))
//
//            RechargePageContent(
//                purchaseHelper = purchaseHelper,
//                flow = flow,
//                exposureName = DataTrace.Exposure.Recharge.私聊消息拦截_充值页面,
//                clickName = DataTrace.Click.Recharge.私聊消息拦截_充值页面_充值按钮点击,
//                onConfirm = onConfirm
//            )
//        }
//    }
// }
