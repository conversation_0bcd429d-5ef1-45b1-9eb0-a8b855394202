package com.buque.wakoo.ui.screens.vip

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.buque.wakoo.ui.theme.WakooTheme

/**
 * VIP页面演示
 * 用于在Android Studio中预览完整的VIP页面
 */
@Preview(
    name = "VIP页面 - 浅色主题",
    showBackground = true,
    showSystemUi = true,
)
@Composable
private fun VipPageDemoPreview() {
    WakooTheme {
        Surface(
            modifier = Modifier.fillMaxSize(),
        ) {
            VipPage(
                userName = "幼儿园搬花",
                vipExpireDate = "2025.06.08",
                onBackClick = { /* 演示用，无操作 */ },
                onPurchaseClick = { plan ->
                    // 演示用，打印选择的套餐
                    println("选择了套餐: ${plan.displayName} - ${plan.price}")
                },
                onRestorePurchaseClick = {
                    // 演示用，打印恢复购买
                    println("点击了恢复购买")
                },
            )
        }
    }
}

/**
 * VIP页面演示 - 不同用户名
 */
@Preview(
    name = "VIP页面 - 不同用户",
    showBackground = true,
    showSystemUi = true,
)
@Composable
private fun VipPageDifferentUserPreview() {
    WakooTheme {
        Surface(
            modifier = Modifier.fillMaxSize(),
        ) {
            VipPage(
                userName = "音乐达人小王",
                vipExpireDate = "2025.12.31",
                onBackClick = { },
                onPurchaseClick = { },
                onRestorePurchaseClick = { },
            )
        }
    }
}

/**
 * 单独的用户VIP卡片预览
 */
@Preview(
    name = "用户VIP卡片",
    showBackground = true,
)
@Composable
private fun UserVipCardPreview() {
    WakooTheme {
        UserVipCard(
            userName = "幼儿园搬花",
            expireDate = "2025.06.08",
        )
    }
}

/**
 * 单独的VIP权益卡片预览
 */
@Preview(
    name = "VIP权益卡片",
    showBackground = true,
)
@Composable
private fun VipBenefitsCardPreview() {
    WakooTheme {
        VipBenefitsCard()
    }
}

/**
 * 单独的价格选择区域预览
 */
@Preview(
    name = "价格选择区域",
    showBackground = true,
)
@Composable
private fun PricingSectionPreview() {
    WakooTheme {
        PricingSection(
            selectedPlan = VipPlanType.MONTHLY,
            onPlanSelected = { },
        )
    }
}
