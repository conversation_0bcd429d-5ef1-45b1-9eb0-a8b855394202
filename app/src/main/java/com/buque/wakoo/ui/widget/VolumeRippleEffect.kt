package com.buque.wakoo.ui.widget

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Slider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.ui.icons.ImagePlaceholder
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.widget.image.rememberConstrainedCenteredVectorPainter
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.util.UUID
import kotlin.math.min

/**
 * 定义一个波纹的数据结构。
 * 动画的核心是控制百分比（percent）的变化。
 *
 * @property key 一个唯一的标识符。
 * @property percent 半径的【百分比】动画控制器。
 * @property alpha 透明度的动画控制器。
 */
private data class Ripple(
    val key: UUID,
    val percent: Animatable<Float, *>,
    val alpha: Animatable<Float, *>,
)

/**
 * 一个根据音量大小产生自适应波面扩散动画的Composable。
 * 此版本将动画逻辑完全建立在百分比之上，实现了最佳的性能和代码解耦。
 *
 * @param volume 当前音量大小，浮点数，建议范围 [0.0f, 1.0f]。
 * @param modifier Modifier，用于定义组件的布局、尺寸等。
 * @param color 波纹的颜色。
 * @param minRadiusPercent 波纹的初始半径百分比。
 * @param maxRadiusPercent 波纹的最大半径百分比。
 * @param initialAlpha 波纹出现时的初始透明度。
 * @param animationDurationMillis 单个波纹的动画时长。
 * @param triggerThreshold 产生新波纹的音量阈值。
 * @param content 中心区域的内容。
 */
@Composable
fun VolumeRippleEffect(
    volume: Float,
    modifier: Modifier = Modifier,
    color: Color = Color.White,
    minRadiusPercent: Float = 0.4f,
    maxRadiusPercent: Float = 1.0f,
    initialAlpha: Float = 0.5f,
    animationDurationMillis: Int = 2000,
    triggerThreshold: Float = 0.1f,
    content: @Composable () -> Unit,
) {
    val ripples = remember { mutableStateListOf<Ripple>() }
    val currentVolume by rememberUpdatedState(volume)
    val coroutineScope = rememberCoroutineScope()

    val minDelayMillis = 400L // 音量最大时的最小延迟
    val maxDelayMillis = 1200L // 音量最弱时的最大延迟
    val delayRange = maxDelayMillis - minDelayMillis

    // LaunchedEffect 只在组件首次进入组合时启动一次，之后不再重启。
    // 这是最高效的方式。
    if (currentVolume >= triggerThreshold) {
        LaunchedEffect(Unit) {
            while (isActive) {
                // 创建一个新的波纹实例。
                val ripple =
                    Ripple(
                        key = UUID.randomUUID(),
                        // 百分比动画从 minRadiusPercent 开始。
                        percent = Animatable(minRadiusPercent),
                        alpha = Animatable(initialAlpha),
                    )
                ripples.add(ripple)

                // 为新波纹启动独立的动画协程。
                coroutineScope
                    .launch {
                        // 并发启动百分比和透明度的动画。
                        launch {
                            // 动画的目标值是 maxRadiusPercent。
                            ripple.percent.animateTo(
                                targetValue = maxRadiusPercent,
                                animationSpec = tween(durationMillis = animationDurationMillis, easing = LinearEasing),
                            )
                        }
                        launch {
                            ripple.alpha.animateTo(
                                targetValue = 0f,
                                animationSpec = tween(durationMillis = animationDurationMillis, easing = LinearEasing),
                            )
                        }
                    }.invokeOnCompletion {
                        // 动画结束后，从列表中移除。
                        ripples.remove(ripple)
                    }

                // 1. 计算出有效的音量调节范围，即从阈值到最大值。
                //    加上一个极小值避免分母为零。
                val effectiveVolumeRange = (1.0f - triggerThreshold).coerceAtLeast(0.001f)

                // 2. 将当前音量在有效范围内的位置，计算成一个 [0, 1] 的因子。
                //    音量越大，因子越接近 1.0。
                val volumeFactor = ((currentVolume - triggerThreshold) / effectiveVolumeRange).coerceIn(0f, 1f)

                delay(maxDelayMillis - (delayRange * volumeFactor).toLong())
            }
        }
    }

    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        // Canvas 在绘制每一帧时，独立计算像素值。
        Canvas(modifier = Modifier.fillMaxSize()) {
            // 在 DrawScope 内实时获取画布的基准半径。
            val baseRadius = min(size.width, size.height) / 2f

            // 遍历所有波纹并进行绘制。
            ripples.forEach { ripple ->
                // 核心：用基准半径乘以当前动画的百分比值，得到最终要绘制的像素半径。
                val radiusInPx = baseRadius * ripple.percent.value

                drawCircle(
                    color = color,
                    radius = radiusInPx,
                    alpha = ripple.alpha.value,
                )
            }
        }
        content()
    }
}

// --- 使用示例 (与之前相同) ---
@Preview(showBackground = true, widthDp = 360, heightDp = 640)
@Composable
private fun AdaptiveRippleDemoScreen() {
    var volume by remember { mutableStateOf(0f) }

    Column(
        modifier =
            Modifier
                .fillMaxSize()
                .background(Color(0xFF0D47A1)),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        VolumeRippleEffect(
            volume = volume,
            modifier = Modifier.size(300.dp),
            minRadiusPercent = 0.28f,
            maxRadiusPercent = 1.0f,
            initialAlpha = 0.6f,
            animationDurationMillis = 2500,
            triggerThreshold = 0.1f,
        ) {
            Image(
                painter =
                    rememberConstrainedCenteredVectorPainter(
                        imageVector = WakooIcons.ImagePlaceholder,
                        backgroundColor = Color(0xFFE9EAEF),
                    ),
                // 请确保有此资源
                contentDescription = "用户头像",
                modifier =
                    Modifier
                        .fillMaxSize(0.3f)
                        .aspectRatio(1f)
                        .clip(CircleShape),
                contentScale = ContentScale.Crop,
            )
        }
        Spacer(modifier = Modifier.height(80.dp))
        Text(
            text = "模拟音量: ${(volume * 100).toInt()}%",
            color = Color.White,
            fontSize = 18.sp,
        )
        Slider(
            value = volume,
            onValueChange = { volume = it },
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 32.dp),
        )
    }
}
