package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.At: ImageVector
    get() {
        val current = _at
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.At",
                defaultWidth = 16.0.dp,
                defaultHeight = 16.0.dp,
                viewportWidth = 16.0f,
                viewportHeight = 16.0f,
            ).apply {
                // M13.33 8 a5.33 5.33 0 1 0 -2.37 4.44 l.74 1.1 A6.67 6.67 0 1 1 14.67 8 v1.01 a2.33 2.33 0 0 1 -4.27 1.31 3.32 3.32 0 0 1 -5.73 -2.3 A3.33 3.33 0 0 1 10 5.32 h1.33 V9 a1 1 0 1 0 2 0z M8 6 a2 2 0 1 0 0 4 2 2 0 0 0 0 -4
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 13.33 8
                    moveTo(x = 13.33f, y = 8.0f)
                    // a 5.33 5.33 0 1 0 -2.37 4.44
                    arcToRelative(
                        a = 5.33f,
                        b = 5.33f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        dx1 = -2.37f,
                        dy1 = 4.44f,
                    )
                    // l 0.74 1.1
                    lineToRelative(dx = 0.74f, dy = 1.1f)
                    // A 6.67 6.67 0 1 1 14.67 8
                    arcTo(
                        horizontalEllipseRadius = 6.67f,
                        verticalEllipseRadius = 6.67f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = true,
                        x1 = 14.67f,
                        y1 = 8.0f,
                    )
                    // v 1.01
                    verticalLineToRelative(dy = 1.01f)
                    // a 2.33 2.33 0 0 1 -4.27 1.31
                    arcToRelative(
                        a = 2.33f,
                        b = 2.33f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -4.27f,
                        dy1 = 1.31f,
                    )
                    // a 3.32 3.32 0 0 1 -5.73 -2.3
                    arcToRelative(
                        a = 3.32f,
                        b = 3.32f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -5.73f,
                        dy1 = -2.3f,
                    )
                    // A 3.33 3.33 0 0 1 10 5.32
                    arcTo(
                        horizontalEllipseRadius = 3.33f,
                        verticalEllipseRadius = 3.33f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 10.0f,
                        y1 = 5.32f,
                    )
                    // h 1.33
                    horizontalLineToRelative(dx = 1.33f)
                    // V 9
                    verticalLineTo(y = 9.0f)
                    // a 1 1 0 1 0 2 0z
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        dx1 = 2.0f,
                        dy1 = 0.0f,
                    )
                    close()
                    // M 8 6
                    moveTo(x = 8.0f, y = 6.0f)
                    // a 2 2 0 1 0 0 4
                    arcToRelative(
                        a = 2.0f,
                        b = 2.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        dx1 = 0.0f,
                        dy1 = 4.0f,
                    )
                    // a 2 2 0 0 0 0 -4
                    arcToRelative(
                        a = 2.0f,
                        b = 2.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 0.0f,
                        dy1 = -4.0f,
                    )
                }
            }.build()
            .also { _at = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.At,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((16.0).dp)
                        .height((16.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _at: ImageVector? = null
