package com.buque.wakoo.ui.widget.coordinatorlayout

import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.DecayAnimationSpec
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.rememberSplineBasedDecay
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TopAppBarScrollBehavior
import androidx.compose.material3.TopAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.remember
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.unit.Velocity

/**
 * 创建并记住一个可配置的滚动行为，专门用于 [CustomCollapsibleHeader]。
 * 这个行为类似于 Material3 的 `exitUntilCollapsed`，但允许通过 `enableSnap` 参数来禁用吸附动画。
 *
 * @param state 要与此行为关联的 TopAppBarState。通常是 `headerState.topAppBarState`。
 * @param enableSnap 当用户通过快速滑动列表或拖拽头部使其停在中间位置时，是否自动吸附到展开或收起状态。
 * @param snapAnimationSpec 吸附动画的规格。
 * @param flingAnimationSpec 惯性滑动动画的规格。
 * @return 一个实现了 [TopAppBarScrollBehavior] 的对象。
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun rememberCustomCollapsibleScrollBehavior(
    state: TopAppBarState,
    enableSnap: Boolean = true,
    snapAnimationSpec: AnimationSpec<Float>? = spring(stiffness = Spring.StiffnessMediumLow),
    flingAnimationSpec: DecayAnimationSpec<Float>? = rememberSplineBasedDecay(),
): TopAppBarScrollBehavior {
    // 决策点：根据 `enableSnap` 开关，决定最终传递给 Behavior 的 `snapAnimationSpec` 是真实值还是 null。
    val finalSnapAnimationSpec = if (enableSnap) snapAnimationSpec else null

    // 使用 remember 来确保 ScrollBehavior 实例在重组间保持一致，
    // 并且只在关键参数（如 state 或动画规格）变化时才重新创建。
    return remember(
        state,
        finalSnapAnimationSpec,
        flingAnimationSpec,
    ) {
        CustomExitUntilCollapsedScrollBehavior(
            state = state,
            snapAnimationSpec = finalSnapAnimationSpec, // 直接传入最终的、可能为 null 的 spec
            flingAnimationSpec = flingAnimationSpec,
        )
    }
}

/**
 * 自定义的 ExitUntilCollapsedScrollBehavior 实现。
 * 从官方的私有类复制而来，主要目的是将其 `onPostFling` 的吸附逻辑暴露出来，使其可配置。
 */
@OptIn(ExperimentalMaterial3Api::class)
@Stable
private class CustomExitUntilCollapsedScrollBehavior(
    override val state: TopAppBarState,
    override val snapAnimationSpec: AnimationSpec<Float>?, // 直接持有 nullable spec
    override val flingAnimationSpec: DecayAnimationSpec<Float>?,
) : TopAppBarScrollBehavior {
    override val isPinned: Boolean = false
    override val nestedScrollConnection =
        object : NestedScrollConnection {
            override fun onPreScroll(
                available: Offset,
                source: NestedScrollSource,
            ): Offset {
                // 如果是向下滑动，不拦截，让列表先滚动
                if (available.y > 0f) return Offset.Zero

                val prevHeightOffset = state.heightOffset
                state.heightOffset += available.y
                // 如果高度发生了变化，说明头部正在收缩，我们“消费”掉这个滚动，不让列表滚动
                return if (prevHeightOffset != state.heightOffset) {
                    available.copy(x = 0f)
                } else {
                    Offset.Zero
                }
            }

            override fun onPostScroll(
                consumed: Offset,
                available: Offset,
                source: NestedScrollSource,
            ): Offset {
                state.contentOffset += consumed.y
                // 向上滚动时，根据列表消耗的滚动量来更新头部高度
                if (available.y < 0f || consumed.y < 0f) {
                    val oldHeightOffset = state.heightOffset
                    state.heightOffset += consumed.y
                    return Offset(
                        0f,
                        state.heightOffset - oldHeightOffset,
                    )
                }
                // 向下滚动到底时，重置内容偏移量，消除浮点数精度误差
                if (consumed.y == 0f && available.y > 0) {
                    state.contentOffset = 0f
                }
                // 当列表滚动到顶部后，如果还有向下的滚动量，则用于展开头部
                if (available.y > 0f) {
                    val oldHeightOffset = state.heightOffset
                    state.heightOffset += available.y
                    return Offset(
                        0f,
                        state.heightOffset - oldHeightOffset,
                    )
                }
                return Offset.Zero
            }

            override suspend fun onPostFling(
                consumed: Velocity,
                available: Velocity,
            ): Velocity {
                // 关键：调用我们自己的、可配置的 settleAppBar 函数
                return settleAppBar(
                    state = state,
                    velocity = available.y,
                    flingAnimationSpec = flingAnimationSpec,
                    snapAnimationSpec = snapAnimationSpec, // 直接使用成员变量，它可能为 null
                )
            }
        }
}
