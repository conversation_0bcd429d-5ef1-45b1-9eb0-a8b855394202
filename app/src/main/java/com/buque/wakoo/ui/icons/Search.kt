package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.Search: ImageVector
    get() {
        if (_Search != null) {
            return _Search!!
        }
        _Search = ImageVector.Builder(
            name = "Search",
            defaultWidth = 20.dp,
            defaultHeight = 20.dp,
            viewportWidth = 20f,
            viewportHeight = 20f
        ).apply {
            group(
                clipPathData = PathData {
                    moveTo(0f, 0f)
                    horizontalLineToRelative(20f)
                    verticalLineToRelative(20f)
                    horizontalLineToRelative(-20f)
                    close()
                }
            ) {
                path(fill = SolidColor(Color(0xFF111111))) {
                    moveTo(9.167f, 1.668f)
                    curveTo(13.307f, 1.668f, 16.667f, 5.028f, 16.667f, 9.168f)
                    curveTo(16.667f, 13.308f, 13.307f, 16.668f, 9.167f, 16.668f)
                    curveTo(5.027f, 16.668f, 1.667f, 13.308f, 1.667f, 9.168f)
                    curveTo(1.667f, 5.028f, 5.027f, 1.668f, 9.167f, 1.668f)
                    close()
                    moveTo(9.167f, 15.001f)
                    curveTo(12.389f, 15.001f, 15f, 12.391f, 15f, 9.168f)
                    curveTo(15f, 5.945f, 12.389f, 3.335f, 9.167f, 3.335f)
                    curveTo(5.944f, 3.335f, 3.334f, 5.945f, 3.334f, 9.168f)
                    curveTo(3.334f, 12.391f, 5.944f, 15.001f, 9.167f, 15.001f)
                    close()
                    moveTo(16.238f, 15.061f)
                    lineTo(18.595f, 17.417f)
                    lineTo(17.416f, 18.596f)
                    lineTo(15.059f, 16.239f)
                    lineTo(16.238f, 15.061f)
                    verticalLineTo(15.061f)
                    close()
                }
            }
        }.build()

        return _Search!!
    }

@Suppress("ObjectPropertyName")
private var _Search: ImageVector? = null
