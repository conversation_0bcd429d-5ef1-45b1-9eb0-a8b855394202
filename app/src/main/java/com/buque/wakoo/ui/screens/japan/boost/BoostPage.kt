package com.buque.wakoo.ui.screens.japan.boost

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.bean.boost.MissionInfo
import com.buque.wakoo.core.webview.AppLinkNavigator
import com.buque.wakoo.ext.parseValue
import com.buque.wakoo.ext.toastWhenError
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.SingleActionDialog
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.viewmodel.BoostState
import com.buque.wakoo.viewmodel.BoostViewModel
import kotlinx.coroutines.launch


object AwardType {
    //女收益
    const val inc = 7

    //男钻石
    const val dia = 5
}

@Composable
fun BoostPage(
    awardType: Int, viewModel: BoostViewModel = viewModel<BoostViewModel>(key = "boost_$awardType", initializer = {
        BoostViewModel(awardType)
    })
) {
    val nav = LocalAppNavController.current
    val dc = rememberDialogController()
    val lm = LocalLoadingManager.current
    val scope = rememberCoroutineScope()
    LaunchedEffect(viewModel) {
        viewModel.refreshState()
    }
    val state by viewModel.state
    val userInfo by AccountManager.userStateFlow.collectAsState()
    CStateLayout(state) {
        val list = rememberBoostItems(awardType, it, userInfo)
        LazyColumn(
            modifier = Modifier
                .background(Color(0xFFF5F7F9))
                .padding(16.dp)
                .systemBarsPadding(), verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            items(list) { item ->
                when (item) {
                    is CWidgetData -> CWidget(item.title, item.cCount, item.buttonText, onClick = {

                    })

                    is DWidgetData -> {
                        DWidget(item.ddCount,"说明：钻石可用于App内购买卡片背景，装扮，礼物等道具", onCharge = {

                        }, onClickRecord = {

                        })
                    }
                    is Distribution -> InviteWidget(item.title, item.content, item.button) {
                        AppLinkNavigator.go(item.link, nav, dc)
                    }

                    is InputInviteCodeBanner -> {
                        NetworkImage(
                            item.banner, modifier = Modifier
                                .fillMaxWidth()
                                .aspectRatio(344 / 134f)
                                .clickable(onClick = {
                                    dc.easyPost {
                                        InputInviteCodeContent(item.text) { code ->
                                            lm.show(null) {
                                                viewModel.fillInviteCode(code)
                                                    .onSuccess {

                                                        dismiss()
                                                    }
                                            }
                                        }
                                    }
                                })
                        )
                    }

                    is MissionWidgetData -> {
                        BoostMissionItem(item.missionInfo, onClick = {}, onAlert = { msg ->
                            dc.easyPost {
                                SingleActionDialog(content = msg, buttonConfig = DialogButtonStyles.Primary.copy("我知道了")) {
                                    dismiss()
                                }
                            }
                        })
                    }

                    is PWidgetData -> {
                        PWidget(
                            item.pCount,
                            "说明：积分通过做任务获得，可用于兑换App内的道具",
                            item.excTxt,
                            qaVisible = item.showQA,
                            recordVisible = item.showRecord,
                            onClickQA = {
                                dc.easyPost {
                                    SingleActionDialog(content = item.qaContent, buttonConfig = DialogButtonStyles.Primary.copy("我知道了")) {
                                        dismiss()
                                    }
                                }
                            },
                            onGetTool = {},
                            onClickExca = {},
                            onClickRecord = {

                            }
                        )
                    }

                    is SignInWidgetData -> {
                        BoostSignWidget(item.missionSeries) {
                            scope.launch {
                                lm.show(null) {
                                    viewModel.makeSign(
                                        item.missionSeries.seriesId.toString()
                                    ).onSuccess { obj ->
                                        val title = obj.parseValue<String>("award_info").orEmpty()
                                        val message = obj.parseValue<String>("message").orEmpty()
                                        val awardType = obj.parseValue<Int>("award_type") ?: awardType
                                        dc.easyPost {
                                            MissionCompleteContent(title, message, awardType) {
                                                dismiss()
                                            }
                                        }
                                    }
                                        .toastWhenError()

                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun rememberBoostItems(type: Int, state: BoostState, userInfo: UserInfo?): List<BoostPageWidgetData> {
    return remember(type, state, userInfo?.extra?.pt, userInfo?.extra?.cash, userInfo?.extra?.balance) {
        val config = state.config
        buildList {
            if (config.isShowInviteCodeBanner && config.inviteCodeAwardText.isNotEmpty()) {
                add(InputInviteCodeBanner(config.inviteCodeBannerUrl, config.inviteCodeAwardText))
            }
            if (type == AwardType.dia) {
                add(DWidgetData(userInfo?.extra?.balance?.toString() ?: "0"))
            }
            if (type == AwardType.inc) {
                add(
                    PWidgetData(
                        userInfo?.extra?.pt ?: "0",
                        config.isShowGainPointRuleEntry,
                        true,
                        config.exchangeJpyBtn,
                        config.gainPointRuleContent
                    )
                )
                if (config.isShowExtract) {
                    add(CWidgetData(userInfo?.extra?.cash ?: "0", config.myExtract, config.extractBtn, config.extractHistoryUrl))
                }
                if (config.isShowDistribution) {
                    add(
                        Distribution(
                            link = config.distributionUrl,
                            title = config.distributionTitle,
                            content = config.distributionContent,
                            button = config.distributionBtn
                        )
                    )
                }
            }
            val s = state.signInfo
            if (s != null) {
                add(SignInWidgetData(s))
            }
            val m = state.missions
            if (m != null) {
                addAll(
                    m.missionSeriesList
                        .filter { it.seriesType != MissionInfo.MissionSeries.SERIES_TYPE_SIGN }
                        .map { MissionWidgetData(it) }
                )
            }
        }
    }
}