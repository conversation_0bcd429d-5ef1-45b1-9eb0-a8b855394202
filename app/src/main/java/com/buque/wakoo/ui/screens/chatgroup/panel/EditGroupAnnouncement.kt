package com.buque.wakoo.ui.screens.chatgroup.panel

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.app.OnDataCallback
import com.buque.wakoo.navigation.dialog.DialogController
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SolidButton

/**
 * 编辑群公告
 */
@Composable
fun DialogScope.GroupAnnouncement(
    content: String,
    modifier: Modifier = Modifier,
    onSave: OnDataCallback<String> = {},
) {
    Column(
        modifier =
            modifier
                .background(
                    color = Color.White,
                    shape = RoundedCornerShape(8.dp),
                ).padding(horizontal = 12.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        var edit by rememberSaveable(content) {
            mutableStateOf(content)
        }
        SizeHeight(20.dp)

        Text(
            "群组公告",
            fontSize = 17.sp,
            color = Color(0xFF1D2129),
            fontWeight = FontWeight.Medium,
        )

        SizeHeight(12.dp)

        AppTextField(
            value = edit,
            onValueChange = {
                edit = it
            },
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(200.dp),
            placeholder = "请填写你的群组公告",
            maxLength = 500,
            backgroundColor = Color(0xFFF8F8F8),
            supportingStyle =
                MaterialTheme.typography.bodyMedium.copy(
                    color = Color(0xFFB6B6B6),
                ),
        )

        SizeHeight(20.dp)

        Row {
            SolidButton(
                text = "取消",
                onClick = {
                    dismiss()
                },
                modifier = Modifier.weight(1f),
                height = 36.dp,
                backgroundColor = Color(0xFFE9EAEF),
                textColor = WakooGrayText,
            )

            SizeWidth(12.dp)

            SolidButton(
                text = "保存",
                onClick = {
                    onSave(edit)
                    dismiss()
                },
                modifier = Modifier.weight(1f),
                height = 36.dp,
            )
        }

        SizeHeight(20.dp)
    }
}

@Preview
@Composable
private fun PreviewGroupAnnouncement() {
    DialogController.preview.apply {
        GroupAnnouncement("哈哈哈", modifier = Modifier.width(270.dp))
    }
}
