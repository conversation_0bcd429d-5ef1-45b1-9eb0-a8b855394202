package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Pause: ImageVector
    get() {
        val current = _pause
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.Pause",
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // <rect width="8" height="24" rx="1.0" fill="#111" />
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 0 1
                    moveTo(x = 0.0f, y = 1.0f)
                    // a 1 1 0 0 1 1 -1
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.0f,
                        dy1 = -1.0f,
                    )
                    // h 6
                    horizontalLineToRelative(dx = 6.0f)
                    // a 1 1 0 0 1 1 1
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.0f,
                        dy1 = 1.0f,
                    )
                    // v 22
                    verticalLineToRelative(dy = 22.0f)
                    // a 1 1 0 0 1 -1 1
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.0f,
                        dy1 = 1.0f,
                    )
                    // h -6
                    horizontalLineToRelative(dx = -6.0f)
                    // a 1 1 0 0 1 -1 -1z
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.0f,
                        dy1 = -1.0f,
                    )
                    close()
                }
                // <rect width="8" height="24" rx="1.0" x="16.0" fill="#111" />
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 16 1
                    moveTo(x = 16.0f, y = 1.0f)
                    // a 1 1 0 0 1 1 -1
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.0f,
                        dy1 = -1.0f,
                    )
                    // h 6
                    horizontalLineToRelative(dx = 6.0f)
                    // a 1 1 0 0 1 1 1
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.0f,
                        dy1 = 1.0f,
                    )
                    // v 22
                    verticalLineToRelative(dy = 22.0f)
                    // a 1 1 0 0 1 -1 1
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.0f,
                        dy1 = 1.0f,
                    )
                    // h -6
                    horizontalLineToRelative(dx = -6.0f)
                    // a 1 1 0 0 1 -1 -1z
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.0f,
                        dy1 = -1.0f,
                    )
                    close()
                }
            }.build()
            .also { _pause = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Pause,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _pause: ImageVector? = null
