package com.buque.wakoo.ui.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.bean.User
import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.network.api.bean.VoiceTag
import com.buque.wakoo.ui.icons.UserFemale
import com.buque.wakoo.ui.icons.UserMale
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage

/**
 * 性别和年龄标签组件
 *
 * @param gender 性别，使用 Gender 枚举
 * @param age 年龄
 * @param modifier Modifier 用于自定义样式
 */
@Composable
fun GenderAgeTag(
    user: User,
    modifier: Modifier = Modifier,
) {
    if (!user.ageIsSet || !user.genderIsSet) {
        return
    }
    // 根据性别选择图标和颜色
    // 使用 Row 实现水平布局
    Row(
        modifier =
            modifier
                .clip(CircleShape) // 使用 CircleShape 可以完美实现药丸形状
                .height(18.dp)
                .background(Color(0xFF111111))
                .padding(horizontal = 6.5.dp),
        // 设置内边距
        verticalAlignment = Alignment.CenterVertically, // 垂直居中对齐内部元素
    ) {
        // 性别图标
        Image(
            imageVector = if (user.isGirl) WakooIcons.UserFemale else WakooIcons.UserMale,
            contentDescription = "Gender Icon", // 为无障碍功能提供描述
            modifier = Modifier.size(14.dp), // 给图标一个固定大小
        )

        // 图标和文字之间的间距
        SizeWidth(2.dp)

        // 年龄文本
        Text(
            text = user.age.toString(),
            color = Color.White,
            fontSize = 13.sp,
            fontWeight = FontWeight.Medium, // 字体稍微加粗，更接近原图
        )
    }
}

// 预览函数，方便在 Android Studio 中查看效果
@Preview(
    showBackground = true,
    backgroundColor = 0xFFE0F2F1,
) // 使用一个类似的浅绿色背景
@Composable
private fun GenderAgeTagPreview() {
    GenderAgeTag(user = UserInfo.previewBoy)
}

@Preview(
    showBackground = true,
    backgroundColor = 0xFFE0F2F1,
)
@Composable
private fun GenderAgeTagFemalePreview() {
    GenderAgeTag(user = UserInfo.previewGirl)
}

/**
 * 一个简单的文本标签组件，用于显示像 "#唱歌" 这样的内容。
 *
 * @param text 要显示的文本内容。
 * @param modifier Modifier 用于自定义样式。
 */
@Composable
fun TextTagChip(
    text: String,
    modifier: Modifier = Modifier,
    contentColor: Color = Color(0xFF222222),
    containerColor: Color = Color.White,
) {
    // 使用 Box 作为容器
    Box(
        modifier =
            modifier
                .height(20.dp)
                .clip(RoundedCornerShape(4.dp))
                .background(containerColor)
                // 3. 为内部的文本添加内边距，使其与边框有距离
                .padding(horizontal = 6.dp),
        // 确保文本在 Box 中居中（虽然只有一个元素，但这是个好习惯）
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = text,
            color = contentColor, // 使用深灰色，比纯黑更柔和
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Medium,
            lineHeight = 14.sp,
        )
    }
}

@Composable
fun VoiceTagChip(
    selected: Boolean,
    tag: VoiceTag,
    modifier: Modifier = Modifier,
    selectedColor: Color = Color(0xFF66FE6B),
    onClick: () -> Unit = {},
) {
    Box(
        modifier =
            modifier
                .height(20.dp)
                .clip(RoundedCornerShape(4.dp))
                .border(
                    width = 0.5.dp,
                    color = Color(0xFFE5E5E5),
                    shape = RoundedCornerShape(4.dp),
                )
                .background(color = if (selected) selectedColor else Color.Transparent)
                .clickable(onClick = onClick)
                .padding(horizontal = 8.dp),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text =
                buildAnnotatedString {
                    withStyle(style = SpanStyle(fontFamily = FontFamily.MI_SANS)) {
                        append("#")
                    }
                    withStyle(style = SpanStyle(fontSize = 5.sp)) {
                        append(" ")
                    }
                    append(tag.name)
                },
            style = MaterialTheme.typography.labelLarge,
            color = Color(0xFF111111),
        )
    }
}

// 预览函数，方便在 Android Studio 中查看效果
@Preview(
    showBackground = true,
    backgroundColor = 0xFFE0F2F1,
) // 使用一个类似的浅绿色背景
@Composable
private fun TextTagPreview() {
    TextTagChip(text = "#唱歌")
}

@Preview
@Composable
private fun VoiceTagChipPreview() {
    WakooTheme {
        VoiceTagChip(false, tag = VoiceTag(name = "树洞"))
    }
}

/**
 * VIP标签带皇冠
 */
@Composable
fun VipCrownTag(modifier: Modifier = Modifier) {
    Image(
        painter = painterResource(R.drawable.ic_vip_with_crown),
        contentDescription = "vip",
        modifier =
            modifier.size(
                48.dp,
                18.dp,
            ),
    )
}

/**
 * VIP标签带皇冠
 */
@Composable
fun VipTag(modifier: Modifier = Modifier) {
    Image(
        painter = painterResource(R.drawable.ic_vip_without_crown),
        contentDescription = "vip",
        modifier =
            modifier.size(
                44.dp,
                20.dp,
            ),
    )
}

// 预览函数，方便在 Android Studio 中查看效果
@Preview(
    showBackground = true,
    backgroundColor = 0xFFE0F2F1,
) // 使用一个类似的浅绿色背景
@Composable
private fun VipCrownTagPreview() {
    VipCrownTag()
}

@Preview(
    showBackground = true,
    backgroundColor = 0xFFE0F2F1,
)
@Composable
private fun VipTagPreview() {
    VipTag()
}

@Composable
fun UserListItem(
    user: User,
    modifier: Modifier = Modifier,
    startContent: @Composable RowScope.(User) -> Unit = {
        AvatarNetworkImage(
            user = user,
            size = 48.dp,
        )
    },
    centerContent: @Composable BoxScope.(User) -> Unit = {
        Column {
            Text(
                text = user.name,
                style = MaterialTheme.typography.bodyLarge,
                color = Color(0xFF111111),
                fontWeight = FontWeight.Medium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
            SizeHeight(8.dp)
            GenderAgeTag(user = user)
        }
    },
    endContent: @Composable RowScope.(User) -> Unit = {},
) {
    SpaceListItemScaffold(
        startContent = { startContent(user) },
        centerContent = { centerContent(user) },
        endContent = { endContent(user) },
        modifier = modifier,
        space = 8.dp,
    )
}
