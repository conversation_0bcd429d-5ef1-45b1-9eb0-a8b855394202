package com.buque.wakoo.ui.icons // 请确保这是你正确的包名

// 必要的 Imports
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path // <--- 这是最关键的 import
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

// 为了方便管理，推荐使用 object 来组织图标
val WakooIcons.UpMic: ImageVector
    get() {
        if (_upMic != null) {
            return _upMic!!
        }
        _upMic =
            ImageVector
                .Builder(
                    name = "UpMic",
                    defaultWidth = 24.0.dp,
                    defaultHeight = 24.0.dp,
                    viewportWidth = 24.0f,
                    viewportHeight = 24.0f,
                ).apply {
                    // 现在 cubicTo, moveTo 等函数都可以被正确解析了
                    path(
                        fill = SolidColor(Color(0xFFFFFFFF)),
                        stroke = null,
                        strokeLineWidth = 0.0f,
                        strokeLineCap = Butt,
                        strokeLineJoin = Miter,
                        strokeLineMiter = 4.0f,
                        pathFillType = NonZero,
                    ) {
                        moveTo(8.0f, 3.0f)
                        curveTo(5.7909f, 3.0f, 4.0f, 4.7909f, 4.0f, 7.0f)
                        verticalLineTo(9.126f)
                        curveTo(2.2748f, 9.5701f, 1.0f, 11.1362f, 1.0f, 13.0f)
                        curveTo(1.0f, 14.4817f, 1.8052f, 15.7734f, 3.0f, 16.4646f)
                        verticalLineTo(19.0f)
                        verticalLineTo(21.0f)
                        horizontalLineTo(5.0f)
                        verticalLineTo(20.0f)
                        horizontalLineTo(19.0f)
                        verticalLineTo(21.0f)
                        horizontalLineTo(21.0f)
                        verticalLineTo(19.0f)
                        verticalLineTo(16.4646f)
                        curveTo(22.1948f, 15.7734f, 23.0f, 14.4817f, 23.0f, 13.0f)
                        curveTo(23.0f, 11.1362f, 21.7252f, 9.5701f, 20.0f, 9.126f)
                        verticalLineTo(7.0f)
                        curveTo(20.0f, 4.7909f, 18.2091f, 3.0f, 16.0f, 3.0f)
                        horizontalLineTo(8.0f)
                        close()
                        moveTo(18.0f, 9.126f)
                        curveTo(16.2748f, 9.5701f, 15.0f, 11.1362f, 15.0f, 13.0f)
                        horizontalLineTo(9.0f)
                        curveTo(9.0f, 11.1362f, 7.7252f, 9.5701f, 6.0f, 9.126f)
                        verticalLineTo(7.0f)
                        curveTo(6.0f, 5.8954f, 6.8954f, 5.0f, 8.0f, 5.0f)
                        horizontalLineTo(16.0f)
                        curveTo(17.1046f, 5.0f, 18.0f, 5.8954f, 18.0f, 7.0f)
                        verticalLineTo(9.126f)
                        close()
                        moveTo(9.0f, 15.0f)
                        horizontalLineTo(15.0f)
                        verticalLineTo(16.0f)
                        horizontalLineTo(17.0f)
                        verticalLineTo(13.0f)
                        curveTo(17.0f, 11.8954f, 17.8954f, 11.0f, 19.0f, 11.0f)
                        curveTo(20.1046f, 11.0f, 21.0f, 11.8954f, 21.0f, 13.0f)
                        curveTo(21.0f, 13.8693f, 20.4449f, 14.6114f, 19.6668f, 14.8865f)
                        curveTo(19.2672f, 15.0277f, 19.0f, 15.4055f, 19.0f, 15.8293f)
                        verticalLineTo(18.0f)
                        horizontalLineTo(5.0f)
                        verticalLineTo(15.8293f)
                        curveTo(5.0f, 15.4055f, 4.7328f, 15.0277f, 4.3332f, 14.8865f)
                        curveTo(3.5551f, 14.6114f, 3.0f, 13.8693f, 3.0f, 13.0f)
                        curveTo(3.0f, 11.8954f, 3.8954f, 11.0f, 5.0f, 11.0f)
                        curveTo(6.1046f, 11.0f, 7.0f, 11.8954f, 7.0f, 13.0f)
                        verticalLineTo(16.0f)
                        horizontalLineTo(9.0f)
                        verticalLineTo(15.0f)
                        close()
                    }
                }.build()
        return _upMic!!
    }

@Suppress("ObjectPropertyName")
private var _upMic: ImageVector? = null

@Preview(showBackground = true, backgroundColor = 0xFF000000)
@Composable
private fun IconPreview() {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Image(
            imageVector = WakooIcons.UpMic,
            contentDescription = null,
            modifier =
                Modifier
                    .width(100.dp)
                    .height(100.dp),
        )
    }
}
