package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Google: ImageVector
    get() {
        val current = _google
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.Google",
                defaultWidth = 20.0.dp,
                defaultHeight = 20.0.dp,
                viewportWidth = 20.0f,
                viewportHeight = 20.0f,
            ).apply {
                // M5.35 10 a5 5 0 0 1 .24 -1.49 L2.84 6.46 a7.9 7.9 0 0 0 0 7.08 l2.75 -2.06 A5 5 0 0 1 5.35 10
                path(
                    fill = SolidColor(Color(0xFFFBBC05)),
                ) {
                    // M 5.35 10
                    moveTo(x = 5.35f, y = 10.0f)
                    // a 5 5 0 0 1 0.24 -1.49
                    arcToRelative(
                        a = 5.0f,
                        b = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.24f,
                        dy1 = -1.49f,
                    )
                    // L 2.84 6.46
                    lineTo(x = 2.84f, y = 6.46f)
                    // a 7.9 7.9 0 0 0 0 7.08
                    arcToRelative(
                        a = 7.9f,
                        b = 7.9f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 0.0f,
                        dy1 = 7.08f,
                    )
                    // l 2.75 -2.06
                    lineToRelative(dx = 2.75f, dy = -2.06f)
                    // A 5 5 0 0 1 5.35 10
                    arcTo(
                        horizontalEllipseRadius = 5.0f,
                        verticalEllipseRadius = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 5.35f,
                        y1 = 10.0f,
                    )
                }
                // M10.19 5.27 a4.8 4.8 0 0 1 3.01 1.06 L15.58 4 c-1.5 -1.3 -3.41 -2 -5.4 -2 a8.2 8.2 0 0 0 -7.34 4.46 L5.6 8.5 a4.8 4.8 0 0 1 4.59 -3.24
                path(
                    fill = SolidColor(Color(0xFFEA4335)),
                ) {
                    // M 10.19 5.27
                    moveTo(x = 10.19f, y = 5.27f)
                    // a 4.8 4.8 0 0 1 3.01 1.06
                    arcToRelative(
                        a = 4.8f,
                        b = 4.8f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 3.01f,
                        dy1 = 1.06f,
                    )
                    // L 15.58 4
                    lineTo(x = 15.58f, y = 4.0f)
                    // c -1.5 -1.3 -3.41 -2 -5.4 -2
                    curveToRelative(
                        dx1 = -1.5f,
                        dy1 = -1.3f,
                        dx2 = -3.41f,
                        dy2 = -2.0f,
                        dx3 = -5.4f,
                        dy3 = -2.0f,
                    )
                    // a 8.2 8.2 0 0 0 -7.34 4.46
                    arcToRelative(
                        a = 8.2f,
                        b = 8.2f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -7.34f,
                        dy1 = 4.46f,
                    )
                    // L 5.6 8.5
                    lineTo(x = 5.6f, y = 8.5f)
                    // a 4.8 4.8 0 0 1 4.59 -3.24
                    arcToRelative(
                        a = 4.8f,
                        b = 4.8f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 4.59f,
                        dy1 = -3.24f,
                    )
                }
                // M10.19 14.73 a4.8 4.8 0 0 1 -4.6 -3.25 l-2.75 2.06 A8.2 8.2 0 0 0 10.19 18 c2 0 3.9 -.7 5.33 -2 l-2.62 -1.97 a5 5 0 0 1 -2.72 .7
                path(
                    fill = SolidColor(Color(0xFF34A853)),
                ) {
                    // M 10.19 14.73
                    moveTo(x = 10.19f, y = 14.73f)
                    // a 4.8 4.8 0 0 1 -4.6 -3.25
                    arcToRelative(
                        a = 4.8f,
                        b = 4.8f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -4.6f,
                        dy1 = -3.25f,
                    )
                    // l -2.75 2.06
                    lineToRelative(dx = -2.75f, dy = 2.06f)
                    // A 8.2 8.2 0 0 0 10.19 18
                    arcTo(
                        horizontalEllipseRadius = 8.2f,
                        verticalEllipseRadius = 8.2f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        x1 = 10.19f,
                        y1 = 18.0f,
                    )
                    // c 2 0 3.9 -0.7 5.33 -2
                    curveToRelative(
                        dx1 = 2.0f,
                        dy1 = 0.0f,
                        dx2 = 3.9f,
                        dy2 = -0.7f,
                        dx3 = 5.33f,
                        dy3 = -2.0f,
                    )
                    // l -2.62 -1.97
                    lineToRelative(dx = -2.62f, dy = -1.97f)
                    // a 5 5 0 0 1 -2.72 0.7
                    arcToRelative(
                        a = 5.0f,
                        b = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -2.72f,
                        dy1 = 0.7f,
                    )
                }
                // M18 10 a7 7 0 0 0 -.19 -1.45 H10.2 v3.09 h4.39 a3.6 3.6 0 0 1 -1.67 2.39 L15.52 16 A7.9 7.9 0 0 0 18 10
                path(
                    fill = SolidColor(Color(0xFF4285F4)),
                ) {
                    // M 18 10
                    moveTo(x = 18.0f, y = 10.0f)
                    // a 7 7 0 0 0 -0.19 -1.45
                    arcToRelative(
                        a = 7.0f,
                        b = 7.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -0.19f,
                        dy1 = -1.45f,
                    )
                    // H 10.2
                    horizontalLineTo(x = 10.2f)
                    // v 3.09
                    verticalLineToRelative(dy = 3.09f)
                    // h 4.39
                    horizontalLineToRelative(dx = 4.39f)
                    // a 3.6 3.6 0 0 1 -1.67 2.39
                    arcToRelative(
                        a = 3.6f,
                        b = 3.6f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.67f,
                        dy1 = 2.39f,
                    )
                    // L 15.52 16
                    lineTo(x = 15.52f, y = 16.0f)
                    // A 7.9 7.9 0 0 0 18 10
                    arcTo(
                        horizontalEllipseRadius = 7.9f,
                        verticalEllipseRadius = 7.9f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        x1 = 18.0f,
                        y1 = 10.0f,
                    )
                }
            }.build()
            .also { _google = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Google,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((20.0).dp)
                        .height((20.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _google: ImageVector? = null
