package com.buque.wakoo.ui.widget

import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.pulltorefresh.PullToRefreshDefaults
import androidx.compose.material3.pulltorefresh.PullToRefreshState
import androidx.compose.material3.pulltorefresh.pullToRefresh
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.unit.Dp

@Composable
@ExperimentalMaterial3Api
fun AppPullToRefreshBox(
    isRefreshing: Boolean,
    onRefresh: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    threshold: Dp = PullToRefreshDefaults.PositionalThreshold,
    state: PullToRefreshState = rememberPullToRefreshState(),
    contentAlignment: Alignment = Alignment.TopStart,
    indicator: @Composable BoxScope.() -> Unit = {
        AppPullToRefreshDefaults.Indicator(
            state = state,
            isRefreshing = isRefreshing,
            modifier = Modifier.align(Alignment.TopCenter),
            threshold= threshold
        )
    },
    content: @Composable BoxScope.() -> Unit,
) {
    Box(
        modifier =
            modifier.pullToRefresh(
                state = state,
                isRefreshing = isRefreshing,
                enabled = enabled,
                threshold = threshold,
                onRefresh = onRefresh,
            ),
        contentAlignment = contentAlignment,
    ) {
        content()
        indicator()
    }
}

 object AppPullToRefreshDefaults {

     @Composable
     fun Indicator(
         state: PullToRefreshState,
         isRefreshing: Boolean,
         modifier: Modifier = Modifier,
         containerColor: Color = Color(0xFFEAEBF0),
         color: Color = Color(0xFF66FE6B),
         threshold: Dp = PullToRefreshDefaults.PositionalThreshold,
     ){
         Box(
             modifier
                 .graphicsLayer {
                     val scaleFraction =
                         if (isRefreshing) {
                             1f
                         } else {
                             LinearOutSlowInEasing.transform(state.distanceFraction).coerceIn(0f, 1f)
                         }
                     scaleX = scaleFraction
                     scaleY = scaleFraction
                 },
         ) {
             PullToRefreshDefaults.Indicator(
                 state = state,
                 isRefreshing = isRefreshing,
                 containerColor =containerColor,
                 color = color,
                 threshold = threshold,
             )
         }
     }

 }

