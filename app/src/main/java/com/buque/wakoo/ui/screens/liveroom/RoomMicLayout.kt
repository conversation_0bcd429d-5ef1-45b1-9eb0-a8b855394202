package com.buque.wakoo.ui.screens.liveroom

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.buque.wakoo.R
import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ui.icons.MicSeat
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.liveroom.panel.LiveRoomUserInfoPanel
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.VolumeRippleEffect
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage

@Composable
fun LiveMicLayout(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    val onClick = { item: MicSeatsInfo ->
        if (item is MicSeatsInfo.User) {
            roomInfoState.sendEvent(
                RoomEvent.PanelDialog {
                    LiveRoomUserInfoPanel(item.user, roomInfoState)
                },
            )
        } else {
            roomInfoState.sendEvent(RoomEvent.UpMic(item.index))
        }
    }

    when (roomInfoState.basicInfo.roomMode) {
        LiveRoomMode.Normal -> {
            NormalModeMicLayout(roomInfoState, modifier, onClick)
        }

        LiveRoomMode.Radio -> {
            RadioModeMicLayout(roomInfoState, modifier, onClick)
        }

        else -> Unit
    }
}

@Composable
private fun NormalModeMicLayout(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
    onClick: (MicSeatsInfo) -> Unit,
) {
    // 使用 LazyVerticalGrid
    LazyVerticalGrid(
        columns = GridCells.Fixed(4),
        modifier = modifier.fillMaxWidth(),
        contentPadding = PaddingValues(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(6.dp),
    ) {
        val micList = roomInfoState.micList

        itemsIndexed(micList) { index, item ->
            MicSeatItem(
                roomInfoState = roomInfoState,
                item = item,
                modifier = Modifier.requiredWidthIn(max = 80.dp),
                onClick = onClick,
            )
        }
    }
}

@Composable
private fun RadioModeMicLayout(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
    onClick: (MicSeatsInfo) -> Unit,
) {
    // 使用 LazyVerticalGrid
    LazyVerticalGrid(
        columns = GridCells.Fixed(4),
        modifier = modifier.animateContentSize().fillMaxWidth(),
        contentPadding = PaddingValues(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(6.dp),
    ) {
        val micList = roomInfoState.micList

        itemsIndexed(micList, span = { index, item ->
            if (index == 0) {
                GridItemSpan(4)
            } else {
                GridItemSpan(1)
            }
        }) { index, item ->
            MicSeatItem(
                roomInfoState = roomInfoState,
                item = item,
                modifier = Modifier.requiredWidthIn(max = if (index == 0) 92.dp else 80.dp),
                onClick = onClick,
            )
        }
    }
}

@Composable
private fun MicSeatItem(
    roomInfoState: LiveRoomInfoState,
    item: MicSeatsInfo,
    modifier: Modifier = Modifier,
    emptySeatText: () -> String = {
        "点击加入"
    },
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    horizontalAlignment: Alignment.Horizontal = Alignment.CenterHorizontally,
    onClick: (MicSeatsInfo) -> Unit,
) {
    val context = LocalContext.current
    Column(
        verticalArrangement = verticalArrangement,
        horizontalAlignment = horizontalAlignment,
        modifier = modifier,
    ) {
        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .aspectRatio(1f)
                    .noEffectClick {
                        onClick(item)
                    },
            contentAlignment = Alignment.Center,
        ) {
            when (item) {
                is MicSeatsInfo.Empty -> {
                    Box(
                        modifier =
                            Modifier
                                .fillMaxSize(0.7f)
                                .background(Color(0x26FFFFFF), CircleShape),
                        contentAlignment = Alignment.Center,
                    ) {
                        Image(
                            imageVector = WakooIcons.MicSeat,
                            contentDescription = null,
                            modifier = Modifier.size(24.dp),
                        )
                    }
                }

                is MicSeatsInfo.User -> {
                    val volume by roomInfoState.rememberVolumeState(item.user.id)
                    val user by remember(item.user.id) {
                        derivedStateOf {
                            roomInfoState.requireRoomUser(item.user)
                        }
                    }
                    VolumeRippleEffect(
                        volume = volume,
                        color =
                            when {
                                !user.genderIsSet -> Color(0xFFFFFFFF)
                                user.isBoy -> Color(0xFF5AA1EA)
                                else -> Color(0xFFFF5E8B)
                            },
                        modifier = Modifier.fillMaxSize(),
                        minRadiusPercent = 0.65f,
                        maxRadiusPercent = 0.95f,
                        initialAlpha = 0.6f,
                        triggerThreshold = 0.05f,
                        animationDurationMillis = 3000,
                    ) {
                        if (user is UserInfo) {
                            val avatarFrame = (user as UserInfo).extra.avatarFrame
                            if (!avatarFrame.isNullOrBlank()) {
                                AsyncImage(
                                    model =
                                        ImageRequest
                                            .Builder(context)
                                            .data(avatarFrame)
                                            .crossfade(false)
                                            .build(),
                                    contentDescription = null,
                                    modifier = Modifier.fillMaxSize(),
                                    placeholder = null,
                                    error = null,
                                )
                            }
                        }

                        AvatarNetworkImage(
                            user = user,
                            modifier = Modifier.fillMaxSize(0.7f),
                            onClick = {
                                roomInfoState.sendEvent(
                                    RoomEvent.PanelDialog {
                                        LiveRoomUserInfoPanel(user, roomInfoState)
                                    },
                                )
                            },
                            size = Dp.Unspecified,
                        )
                    }
                }
            }
        }

        when (item) {
            is MicSeatsInfo.Empty -> {
                Text(
                    text = emptySeatText(),
                    color = Color.White.copy(alpha = 0.6f),
                    fontSize = 12.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
            }

            is MicSeatsInfo.User -> {
                val user by remember(item.user.id) {
                    derivedStateOf {
                        roomInfoState.requireRoomUser(item.user)
                    }
                }
                Text(
                    text = user.name,
                    color = Color.White,
                    fontSize = 12.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
                SizeHeight(4.dp)
                Row(
                    modifier =
                        Modifier
                            .height(12.dp)
                            .background(Color(0x26FFFFFF), CircleShape)
                            .padding(horizontal = 6.dp),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_mic_score),
                        contentDescription = null,
                        modifier = Modifier.height(8.dp),
                    )
                    SizeWidth(1.dp)
                    Text(
                        text = item.score.toString(),
                        color = WakooWhite,
                        fontSize = 9.sp,
                        lineHeight = 9.sp,
                    )
                }
            }
        }
    }
}
