package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Settings: ImageVector
    get() {
        val current = _settings
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.Settings", // Changed name for clarity
                defaultWidth = 24.0.dp,
                defaultHeight = 24.0.dp,
                viewportWidth = 24.0f,
                viewportHeight = 24.0f,
            ).apply {
                // The original SVG uses the default "nonzero" fill rule,
                // which results in filled shapes.
                // If the inner hexagon was meant to be transparent,
                // the fill rule would need to be `PathFillType.EvenOdd`.
                // But based on the provided image, the inner hexagon is NOT present,
                // and the two circles (hole) are present.
                // The SVG provided draws all three elements as filled shapes.
                // If the inner hexagon is part of the "gear teeth" it should be "cut out".
                // Let's assume the SVG means to draw a solid gear with a hole.
                // In that case, the inner hexagon path is likely part of the "teeth".

                // Let's try translating the SVG path exactly, keeping the fill rule as NonZero
                path(
                    fill = SolidColor(Color(0xFF000000)), // Fill with black
                    // Original SVG implies NonZero fill rule, which is Compose's default
                    // If you want the inner hexagon to be a "hole" or "cut-out",
                    // you might need PathFillType.EvenOdd for the entire path,
                    // or separate paths for the inner/outer shapes.
                    // Given the image, the inner hexagon (teeth) is *missing*.
                    // The provided SVG path `d="..."` means *all* subpaths are filled black.
                    // If the inner hexagon in your image is indeed missing,
                    // it implies a problem with how it's drawn, not its fill color.
                    // Let's follow the SVG's path precisely.
                    pathFillType = PathFillType.NonZero, // Default, but explicit for clarity
                ) {
                    // Outer Hexagon
                    moveTo(x = 12.0f, y = 1.0f)
                    lineTo(x = 21.5f, y = 6.5f)
                    lineTo(x = 21.5f, y = 17.5f) // V17.5
                    lineTo(x = 12.0f, y = 23.0f)
                    lineTo(x = 2.5f, y = 17.5f)
                    lineTo(x = 2.5f, y = 6.5f) // V6.5
                    close()

                    // Inner Hexagon (The one you didn't see in the image!)
                    // This is the problematic part in your old code due to relative vs absolute commands.
                    // Original SVG: M12 3.311L4.5 7.65311V16.3469L12 20.689L19.5 16.3469V7.65311L12 3.311ZM
                    moveTo(x = 12.0f, y = 3.311f) // ABSOLUTE moveTo
                    lineTo(x = 4.5f, y = 7.65311f)
                    lineTo(x = 4.5f, y = 16.3469f) // V16.3469
                    lineTo(x = 12.0f, y = 20.689f)
                    lineTo(x = 19.5f, y = 16.3469f)
                    lineTo(x = 19.5f, y = 7.65311f) // V7.65311
                    close() // Close the inner hexagon path

                    // Outer Circle (using two arcs, as in your original code, which is correct for circles)
                    // Original SVG uses Cubic Bezier (C) commands for circles.
                    // Your arc conversion is a common and acceptable way to represent circles from SVG.
                    moveTo(x = 12.0f, y = 16.0f) // Start for outer circle arc
                    arcToRelative(
                        a = 4.0f, // rx
                        b = 4.0f, // ry
                        theta = 0.0f, // xAxisRotation
                        isMoreThanHalf = true, // largeArcFlag
                        isPositiveArc = true, // sweepFlag
                        dx1 = 0.0f,
                        dy1 = -8.0f, // endpoint relative to current (12,16) -> (12, 8)
                    )
                    arcToRelative(
                        a = 4.0f,
                        b = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false, // largeArcFlag
                        isPositiveArc = true, // sweepFlag
                        dx1 = 0.0f,
                        dy1 = 8.0f, // endpoint relative to current (12,8) -> (12, 16)
                    )

                    // Inner Circle (using two arcs)
                    moveTo(x = 12.0f, y = 14.0f) // Start for inner circle arc (12,16) - (0,2) = (12,14)
                    arcToRelative(
                        a = 2.0f,
                        b = 2.0f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false, // sweepFlag changed to 0 for the first half
                        dx1 = 0.0f,
                        dy1 = -4.0f, // endpoint relative to current (12,14) -> (12, 10)
                    )
                    arcToRelative(
                        a = 2.0f,
                        b = 2.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false, // largeArcFlag
                        isPositiveArc = false, // sweepFlag changed to 0 for the second half
                        dx1 = 0.0f,
                        dy1 = 4.0f, // endpoint relative to current (12,10) -> (12, 14)
                    )
                }
            }.build()
            .also { _settings = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Settings,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _settings: ImageVector? = null
