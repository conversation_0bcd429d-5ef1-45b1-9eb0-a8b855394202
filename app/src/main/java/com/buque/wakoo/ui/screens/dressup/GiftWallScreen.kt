package com.buque.wakoo.ui.screens.dressup

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.pulltorefresh.PullToRefreshState
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.buque.wakoo.ui.widget.AppPullToRefreshBox
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.gift.GiftWallPager
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun GiftWallScreen(
    userId: Int,
    modifier: Modifier = Modifier,
) {
    SegColorTitleScreenScaffold("礼物墙") { pv ->
        val refreshState: PullToRefreshState = rememberPullToRefreshState()
        val scope = rememberCoroutineScope()
        var isRefreshing by remember {
            mutableStateOf(false)
        }
        var refreshFlag by remember {
            mutableStateOf(0)
        }
        AppPullToRefreshBox(
            isRefreshing = isRefreshing,
            onRefresh = {
                refreshFlag = (refreshFlag + 1) % 10
                isRefreshing = true
                scope.launch {
                    delay(2000)
                    isRefreshing = false
                }
            },
            modifier =
                Modifier
                    .padding(pv)
                    .background(color = Color.White),
            state = refreshState,
        ) {
            GiftWallPager(
                userId,
                3,
                refreshFlag = refreshFlag,
            )
        }
    }
}
