package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Play: ImageVector
    get() {
        val current = _play
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.Play",
                defaultWidth = 40.0.dp,
                defaultHeight = 40.0.dp,
                viewportWidth = 40.0f,
                viewportHeight = 40.0f,
            ).apply {
                // M10 33.66 V6.34 c0 -1.3 1.44 -2.1 2.55 -1.41 l21.86 13.66 a1.67 1.67 0 0 1 0 2.82 L12.55 35.07 A1.67 1.67 0 0 1 10 33.66
                path(
                    fill = SolidColor(Color(0xFF000000)),
                ) {
                    // M 10 33.66
                    moveTo(x = 10.0f, y = 33.66f)
                    // V 6.34
                    verticalLineTo(y = 6.34f)
                    // c 0 -1.3 1.44 -2.1 2.55 -1.41
                    curveToRelative(
                        dx1 = 0.0f,
                        dy1 = -1.3f,
                        dx2 = 1.44f,
                        dy2 = -2.1f,
                        dx3 = 2.55f,
                        dy3 = -1.41f,
                    )
                    // l 21.86 13.66
                    lineToRelative(dx = 21.86f, dy = 13.66f)
                    // a 1.67 1.67 0 0 1 0 2.82
                    arcToRelative(
                        a = 1.67f,
                        b = 1.67f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = 2.82f,
                    )
                    // L 12.55 35.07
                    lineTo(x = 12.55f, y = 35.07f)
                    // A 1.67 1.67 0 0 1 10 33.66
                    arcTo(
                        horizontalEllipseRadius = 1.67f,
                        verticalEllipseRadius = 1.67f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 10.0f,
                        y1 = 33.66f,
                    )
                }
            }.build()
            .also { _play = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Play,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((40.0).dp)
                        .height((40.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _play: ImageVector? = null
