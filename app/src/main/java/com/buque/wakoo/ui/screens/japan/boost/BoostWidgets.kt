package com.buque.wakoo.ui.screens.japan.boost

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.OnDataCallback
import com.buque.wakoo.bean.boost.MissionInfo
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ui.icons.ArrowRight
import com.buque.wakoo.ui.icons.GreenChecked
import com.buque.wakoo.ui.icons.QuestionLine
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooSecondaryText
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.image.NetworkImage

@Composable
internal fun BoostMissionItem(
    mission: MissionInfo.MissionSeries,
    onClick: OnDataCallback<MissionInfo.MissionSeries.Task> = {},
    onAlert: OnDataCallback<String> = {}
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White, RoundedCornerShape(12.dp))
            .padding(horizontal = 16.dp)
    ) {
        Text(mission.seriesTitle, modifier = Modifier.padding(top = 16.dp), fontSize = 16.sp, color = WakooText, fontWeight = FontWeight.Bold)
        Spacer(modifier = Modifier.height(8.dp))
        if (mission.treasureTotips.isNotEmpty()) {
            Text(
                text = mission.treasureTotips,
                style = MaterialTheme.typography.titleSmall.copy(color = WakooSecondaryText),
                fontSize = 14.sp
            )

            Spacer(modifier = Modifier.height(6.dp))
        }
        mission.tasks.forEachIndexed { index, item ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(62.dp)
                    .padding(vertical = 10.dp)
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Row {
                        Text(item.title)
                        if (item.extra.hint.isNotEmpty()) {
                            Icon(
                                WakooIcons.QuestionLine, contentDescription = "qa", modifier = Modifier
                                    .size(16.dp)
                                    .noEffectClick(onClick = {
                                        onAlert(item.extra.hint)
                                    })
                            )
                        }
                    }
                    SizeHeight(10.dp)
                    if (item.progress.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(text = item.progress, fontSize = 12.sp, color = WakooSecondaryText)
                    }
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        NetworkImage(item.extra.prizeIcon, modifier = Modifier.size(18.dp))
                        Spacer(modifier = Modifier.width(6.dp))
                        Text(text = item.prize, style = MaterialTheme.typography.labelMedium.copy(color = Color(0xFFFFB71A)))
                    }
                }
                GradientButton(
                    text = if (item.finished) "已完成" else "去完成",
                    fontSize = 12.sp,
                    onClick = {
                        onClick(item)
                    },
                    modifier = Modifier
                        .widthIn(72.dp)
                        .height(28.dp),
                    enabled = !item.finished,
                )
            }
            if (index < mission.tasks.lastIndex) {
                HorizontalDivider(thickness = 0.5.dp, color = Color(0x14000000))
            }
        }
    }

}

@Preview
@Composable
private fun MissionPreview() {
    val json =
        "[{\"series_id\":33,\"series_title\":\"ログインしてポイントを受け取る\",\"series_slug\":\"wkjp_female_check_in_task\",\"series_type\":3,\"prize_type\":7,\"tasks\":[{\"id\":132,\"title\":\"1日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"+60\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":133,\"title\":\"2日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"+150\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":134,\"title\":\"3日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"最大+1500\",\"prize_type\":8,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point2.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":135,\"title\":\"4日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"+300\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":136,\"title\":\"5日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"+450\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":137,\"title\":\"6日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"+600\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":138,\"title\":\"7日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"最大+6000\",\"prize_type\":8,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point3.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1}],\"today_finished\":false,\"treasure_totips\":\"0 日連続サインイン \",\"finished_totips\":\"3日目と7日目に登録してメガポイントをゲットしよう\",\"all_finished\":false,\"multiplier_help\":\"\",\"light_prize_infos\":[],\"current_light_count\":0,\"light_prize_rule_title\":\"\",\"light_prize_rule_content\":\"\"},{\"series_id\":34,\"series_title\":\"初心者向けタスク\",\"series_slug\":\"wkjp_female_newbie_tasks\",\"series_type\":2,\"prize_type\":7,\"tasks\":[{\"id\":139,\"title\":\"サウンド作品のように\",\"desc\":\"サウンド作品のように\",\"prize\":\"+100\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"audit_only\":false,\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":true,\"progress\":\"\",\"condition_type\":51,\"condition_times\":1},{\"id\":140,\"title\":\"サウンドクリエイターをフォローする\",\"desc\":\"サウンドクリエイターをフォローする\",\"prize\":\"+100\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"audit_only\":false,\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":true,\"progress\":\"\",\"condition_type\":52,\"condition_times\":1},{\"id\":141,\"title\":\"カスタムアバターをアップロードする\",\"desc\":\"カスタムアバターをアップロードする\",\"prize\":\"+100\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"audit_only\":false,\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":true,\"progress\":\"\",\"condition_type\":14,\"condition_times\":1},{\"id\":142,\"title\":\"個人情報の完全な紹介\",\"desc\":\"個人情報の完全な紹介\",\"prize\":\"+200\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"audit_only\":false,\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":true,\"progress\":\"\",\"condition_type\":16,\"condition_times\":1}],\"today_finished\":true,\"treasure_totips\":\"以下は初心者タスクで、完成するとデイリータスクが解放されます。\",\"finished_totips\":\"\",\"all_finished\":true,\"multiplier_help\":\"\",\"light_prize_infos\":[],\"current_light_count\":0,\"light_prize_rule_title\":\"\",\"light_prize_rule_content\":\"\"},{\"series_id\":35,\"series_title\":\"タスクを完了してポイントを獲得\",\"series_slug\":\"wkjp_female_daily_tasks\",\"series_type\":4,\"prize_type\":7,\"tasks\":[{\"id\":144,\"title\":\"1000個のダイヤモンドギフトを送受信\",\"desc\":\"1000個のダイヤモンドギフトを送受信\",\"prize\":\"+250\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"audit_only\":false,\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"今日が既に0コインのギフトを贈りました/受け取りました、あと1000コインのギフトが必要です\",\"condition_type\":29,\"condition_times\":1000},{\"id\":145,\"title\":\"5000個のダイヤモンドギフトを送受信\",\"desc\":\"5000個のダイヤモンドギフトを送受信\",\"prize\":\"+1250\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"audit_only\":false,\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"今日が既に0コインのギフトを贈りました/受け取りました、あと5000コインのギフトが必要です\",\"condition_type\":29,\"condition_times\":5000},{\"id\":146,\"title\":\"音声室での対話時間は30分を超える\",\"desc\":\"音声室での対話時間は30分を超える\",\"prize\":\"+200\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"audit_only\":false,\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":54,\"condition_times\":1800}],\"today_finished\":false,\"treasure_totips\":\"日々のタスクは0:00に更新\",\"finished_totips\":\"\",\"all_finished\":false,\"multiplier_help\":\"\",\"light_prize_infos\":[],\"current_light_count\":0,\"light_prize_rule_title\":\"\",\"light_prize_rule_content\":\"\"}]"
    val list = AppJson.decodeFromString<List<MissionInfo.MissionSeries>>(json)
    val scrollState = rememberScrollState()
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .verticalScroll(scrollState)
            .background(Color(0xFFF5F7F9))
            .padding(horizontal = 16.dp)
    ) {
        list.forEach {
            BoostMissionItem(it)
            Spacer(Modifier.height(12.dp))
        }
    }
}

@Composable
fun BoostSignWidget(task: MissionInfo.MissionSeries, onSign: OnAction = {}) {
    val moneyTypeColor = Color(0xFFFFB71A)
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = Color.White, shape = RoundedCornerShape(12.dp)
            )
            .padding(16.dp)
    ) {
        Text(
            task.seriesTitle, style = MaterialTheme.typography.titleMedium.copy(
                color = WakooText,
            )
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            task.treasureTotips, style = MaterialTheme.typography.titleSmall.copy(
                color = WakooSecondaryText,
            )
        )
        Spacer(modifier = Modifier.height(16.dp))
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            task.tasks.forEachIndexed { index, reward ->
                Column(
                    modifier = Modifier
                        .size(36.dp, 56.dp)
                        .background(
                            if (reward.finished) moneyTypeColor.copy(alpha = 0.33f)
                            else Color(0xFFF1F2F3),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .border(1.dp, if (reward.finished) moneyTypeColor else Color.Transparent, shape = RoundedCornerShape(8.dp))
                        .padding(vertical = 3.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.SpaceEvenly
                ) {
                    NetworkImage(
                        reward.extra.prizeIcon,
                        modifier = Modifier.height(16.dp),
                        contentScale = ContentScale.Inside
                    )

                    if (reward.finished) {
                        Icon(WakooIcons.GreenChecked, modifier = Modifier.size(16.dp), contentDescription = "check")
                    } else {
                        Text(
                            reward.title, style = MaterialTheme.typography.labelSmall.copy(
                                color = Color(0xFF86909C),
                            )
                        )
                    }
                }
            }
        }
        Spacer(modifier = Modifier.height(16.dp))

        SolidButton(
            "今日已签到".takeIf { task.todayFinished } ?: "签到",
            onClick = onSign,
            modifier = Modifier
                .fillMaxWidth()
                .height(36.dp),
            enabled = !task.todayFinished,
        )
    }

}

@Preview
@Composable
private fun SignPreview() {
    val json =
        "{\"series_id\":33,\"series_title\":\"ログインしてポイントを受け取る\",\"series_slug\":\"wkjp_female_check_in_task\",\"series_type\":3,\"prize_type\":7,\"tasks\":[{\"id\":132,\"title\":\"1日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"+60\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":133,\"title\":\"2日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"+150\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":134,\"title\":\"3日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"最大+1500\",\"prize_type\":8,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point2.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":135,\"title\":\"4日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"+300\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":136,\"title\":\"5日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"+450\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":137,\"title\":\"6日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"+600\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":138,\"title\":\"7日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"最大+6000\",\"prize_type\":8,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point3.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1}],\"today_finished\":false,\"treasure_totips\":\"0 日連続サインイン \",\"finished_totips\":\"3日目と7日目に登録してメガポイントをゲットしよう\",\"all_finished\":false,\"multiplier_help\":\"\",\"light_prize_infos\":[],\"current_light_count\":0,\"light_prize_rule_title\":\"\",\"light_prize_rule_content\":\"\"}"
    val signInfo = AppJson.decodeFromString<MissionInfo.MissionSeries>(
        json
    )
    BoostSignWidget(signInfo)
}

@Composable
fun DWidget(
    bigNum: String,
    desc: String,
    onCharge: OnAction = {},
    onClickRecord: OnAction = {}
) {
    val gray = Color(0xFF86909C)
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White, RoundedCornerShape(12.dp))
            .padding(16.dp, 12.dp)
    ) {
        Row(modifier = Modifier.fillMaxWidth()) {
            Box(
                modifier = Modifier
                    .size(58.dp)
                    // 设置内边距
                    .background(Color(0xFFE3FFEA), RoundedCornerShape(12.dp))
                    .padding(6.dp)
            ) {
                Image(painterResource(R.drawable.ic_green_diamond_straight), contentDescription = "coin", modifier = Modifier.fillMaxSize())
            }
            SizeWidth(8.dp)
            // 显示积分余额
            Column(modifier = Modifier.weight(1f)) {
                Text("钻石余额", fontSize = 16.sp, color = WakooText)
                SizeHeight(4.dp)
                Text(bigNum, fontSize = 32.sp, color = WakooText, fontWeight = FontWeight.Bold)
            }
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                GradientButton("充值", fontSize = 14.sp, onClick = onCharge, modifier = Modifier.heightIn(max = 28.dp))

                SizeHeight(6.dp)
                Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.clickable(onClick = onClickRecord)) {
                    Text("明细记录", fontSize = 12.sp, color = gray)
                    Icon(WakooIcons.ArrowRight, contentDescription = "arr", tint = gray)
                }
            }
        }
        // 显示描述文本
        SizeHeight(10.dp)
        Text(desc, fontSize = 12.sp, color = gray)
    }
}

@Composable
fun PWidget(
    bigNum: String,
    desc: String,
    excaTex: String,
    recordVisible: Boolean = false,
    qaVisible: Boolean = false,
    onClickQA: OnAction = {},
    onClickRecord: OnAction = {},
    onClickExca: OnAction = {},
    onGetTool: OnAction = {}
) {
    val gray = Color(0xFF86909C)
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White, RoundedCornerShape(12.dp))
            .padding(16.dp, 12.dp)
    ) {
        Row(modifier = Modifier.fillMaxWidth()) {
            Box(
                modifier = Modifier
                    .size(58.dp)
                    // 设置内边距
                    .background(Color(0xFFE3FFEA), RoundedCornerShape(12.dp))
                    .padding(6.dp)
            ) {
                Image(painterResource(R.drawable.ic_golden_coin), contentDescription = "coin", modifier = Modifier.fillMaxSize())
            }
            SizeWidth(8.dp)
            // 显示积分余额
            Column(modifier = Modifier.weight(1f)) {
                Text("积分余额", fontSize = 16.sp, color = WakooText)
                SizeHeight(4.dp)
                Text(bigNum, fontSize = 32.sp, color = WakooText, fontWeight = FontWeight.Bold)
                if (qaVisible) {
                    SizeHeight(4.dp)
                    Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.clickable(onClick = onClickQA)) {
                        Text("查看如何获取积分", fontSize = 12.sp, color = gray)
                        // 显示问号图标
                        SizeWidth(2.dp)
                        Icon(WakooIcons.QuestionLine, contentDescription = "qa", tint = gray)
                    }
                }
            }
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                GradientButton("兑换道具", fontSize = 14.sp, onClick = onGetTool, modifier = Modifier.heightIn(max = 28.dp))
                if (excaTex.isNotEmpty()) {
                    SizeHeight(6.dp)
                    OutlinedButton("兑换现金", fontSize = 14.sp, onClick = onClickExca, modifier = Modifier.heightIn(max = 28.dp))
                }
                SizeHeight(6.dp)
                // 显示明细记录的文本
                if (recordVisible) {
                    Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.clickable(onClick = onClickRecord)) {
                        Text("明细记录", fontSize = 12.sp, color = gray)
                        Icon(WakooIcons.ArrowRight, contentDescription = "arr", tint = gray)
                    }
                }
            }
        }
        // 显示描述文本
        SizeHeight(10.dp)
        Text(desc, fontSize = 12.sp, color = gray)
    }
}

@Composable
// 定义一个可组合函数CWidget，用于显示一个带有标题、大数字、按钮和点击事件的行
fun CWidget(title: String, bigNum: String, buttonText: String, onClick: OnAction = {}) {

    // 创建一个行布局，填充整个宽度，背景色为白色，圆角为12dp，内边距为16dp和12dp，垂直居中对齐
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White, RoundedCornerShape(12.dp))
            .padding(16.dp, 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 在行布局中添加一个图标，大小为48dp

        Image(painterResource(R.drawable.ic_currency), contentDescription = "icn", modifier = Modifier.size(48.dp))
        // 在行布局中添加一个列布局，权重为1f
        SizeWidth(12.dp)
        // 在列布局中添加一个文本，显示标题，字体大小为16sp，颜色为WakooText
        Column(modifier = Modifier.weight(1f)) {
            // 在列布局中添加一个高度为6dp的空白
            Text(title, fontSize = 16.sp, color = WakooText)
            // 在列布局中添加一个文本，显示大数字
            SizeHeight(6.dp)
            Row(verticalAlignment = Alignment.Bottom) {
                Text(bigNum, fontSize = 32.sp, color = WakooText, fontWeight = FontWeight.Bold, lineHeight = 32.sp)
                Text("JPY", fontSize = 12.sp, color = Color(0xFF86909C), modifier = Modifier.padding(bottom = 4.dp))
            }
        }
        // 在行布局中添加一个渐变按钮，显示按钮文本，点击事件为onClick，高度最大为28dp

        GradientButton(
            buttonText, onClick, modifier = Modifier
                .widthIn(min = 72.dp)
                .heightIn(max = 28.dp)
        )
    }
}

@Preview
@Composable
private fun PreviewPC() {
    Column(
        modifier = Modifier
            .background(Color(0xFFF5F7F9))
            .padding(horizontal = 16.dp)
    ) {
        PWidget("379191", "说明：钻石可用于App内购买卡片背景，装扮，礼物等道具", "兑换现金", recordVisible = true, qaVisible = true)
        SizeHeight(10.dp)
        CWidget("我的现金", "23929", "出金")
        SizeHeight(10.dp)
        InviteWidget(
            "友達を招待して登録し、 ダイヤをゲット",
            "友達を1人招待するごとに、5000ダイヤが報酬としてもらえます。 友達がUCOOでコインをチャージしたり、ダイヤをゲットしたりすると、あなたはダイヤがもらえます。",
            "今すぐ招待"
        )
    }
}

@Composable
fun InviteWidget(title: String, content: String, buttonText: String, onAction: OnAction = {}) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(343 / 174f)
            .paint(painterResource(R.drawable.card_invite), contentScale = ContentScale.FillBounds)
            .padding(horizontal = 16.dp)
            .padding(top = 2.dp, bottom = 12.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(title, modifier = Modifier.widthIn(max = 180.dp), color = Color(0xFFC98600), fontSize = 12.sp, textAlign = TextAlign.Center)
        Text(
            content, modifier = Modifier
                .fillMaxWidth()
                .padding(top = 6.dp)
                .weight(1f), textAlign = TextAlign.Center,
            color = WakooSecondaryText,
            fontSize = 12.sp
        )
        GradientButton(
            buttonText, onClick = onAction, modifier = Modifier
                .fillMaxWidth()
                .heightIn(max = 35.dp)
        )
    }
}

@Composable
fun MissionCompleteContent(title: String, content: String, awardType: Int = AwardType.inc, onClick: OnAction) {
    Column(
        modifier = Modifier
            .width(270.dp)
            .background(Color.White, RoundedCornerShape(12.dp))
            .padding(16.dp, 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        Image(
            painterResource(if (awardType == AwardType.inc) R.drawable.icon_reward_pt else R.drawable.icon_reward_dia),
            contentDescription = "icon",
            modifier = Modifier.size(96.dp),
            contentScale = ContentScale.FillBounds
        )

        SizeHeight(6.dp)
        Text(title, color = Color(0xFF118C6E), fontSize = 18.sp, fontWeight = FontWeight.Bold)

        SizeHeight(10.dp)
        Text(content, fontSize = 12.sp, color = WakooGrayText, textAlign = TextAlign.Center)
        SizeHeight(10.dp)

        GradientButton(
            "确认", onClick = onClick, modifier = Modifier
                .widthIn(min = 200.dp)
                .heightIn(max = 36.dp)
        )
    }
}

@Preview
@Composable
private fun MissionCompletePreview() {
    MissionCompleteContent("50积分", "完成“填写所有个人基础信息”任务，获得积分奖励") { }
    MissionCompleteContent("50钻石", "完成“填写所有个人基础信息”任务，获得积分奖励", awardType = AwardType.dia) { }
}

@Composable
fun InputInviteCodeContent(
    message: String, onCodeCallBack: OnDataCallback<String>
) {
    var code by remember { mutableStateOf("") }
    Column(
        modifier = Modifier
            .width(270.dp)
            .background(Color.White, RoundedCornerShape(12.dp))
            .padding(16.dp, 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        val bgColor = Color(0xFFF8F8F8)
        Text(message, fontSize = 12.sp, lineHeight = 22.sp)
        SizeHeight(10.dp)
        AppTextField(
            code, onValueChange = { code = it },
            maxLength = 8,
            showLengthTip = false,
            boxModifier = Modifier
                .fillMaxWidth()
                .height(54.dp),
            contentPadding = PaddingValues(12.dp),
            backgroundColor = bgColor,
            placeholder = "请填写邀请码"
        )
        SizeHeight(20.dp)
        GradientButton(
            "确认", onClick = {
                onCodeCallBack(code)
            }, modifier = Modifier
                .widthIn(min = 200.dp)
                .heightIn(max = 36.dp),
            enabled = code.isNotEmpty()
        )
    }
}

@Preview
@Composable
private fun InputCodePreview() {
    InputInviteCodeContent("如果你是通过好友推荐下载的本APP，你可以填写好友的邀请码，可立即领取xxx钻石奖励") { }
}