package com.buque.wakoo.ui.widget.gift

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.MutableTransitionState
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.produceState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.bean.GiftBean
import com.buque.wakoo.bean.GiftTab
import com.buque.wakoo.bean.GiftWrapper
import com.buque.wakoo.bean.User
import com.buque.wakoo.ext.AwaitContinuation
import com.buque.wakoo.ext.awaitContinuation
import com.buque.wakoo.ext.getIntOrNull
import com.buque.wakoo.ext.getStringOrNull
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.inter.MsgFilter
import com.buque.wakoo.im.utils.takeIsNotEmpty
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.message.types.UCGiftMessage
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.network.api.service.GiftApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.widget.EffectVideoAnimationView
import com.buque.wakoo.ui.widget.getEffectFile
import com.buque.wakoo.ui.widget.image.NetworkImage
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.selects.select
import kotlinx.serialization.json.JsonObject
import java.io.File
import kotlin.time.Duration.Companion.seconds

/**
 * 礼物场景
 */
enum class GiftScene {
    Group, // 部落、家族
    ROOM, // 语音房
    CP, // 语音房
}

@Stable
data class GiftListModel(
    val preview: Boolean = true,
    val balance: Int = 0,
    val list: List<GiftTab> = emptyList(),
) {
    fun getGift(giftID: Int): Pair<GiftTab, GiftBean>? {
        if (giftID == -1) {
            return null
        }
        list.forEach {
            val gift = it.gifts.find { it.id == giftID }
            if (gift != null) {
                return Pair(it, gift)
            }
        }
        return null
    }
}

/**
 *
 * @param [bid] 业务id,单聊为userid,家族为家族id
 * @param [rcId] 融云会话id
 *
 */
class GiftViewModel constructor(
    private val bid: String,
    val type: ConversationType,
    val rcId: String = bid,
    val autoFetchGiftData: Boolean = true,
) : ViewModel() {
    val giftListModelState = mutableStateOf(GiftListModel())

    //region 礼物 动效 处理

    //region 特效逻辑

    @Stable
    private data class GiftEffect(
        val queue1: List<File?>? = null,
        val queue2: List<File?>? = null,
        val msg: UCGiftMessage? = null,
        val extra: User? = null,
        private val taskAwait: AwaitContinuation = awaitContinuation(),
    ) {
        // 普通礼物
        constructor(
            file: File,
            boxFile: File? = null,
            msg: UCGiftMessage? = null,
            extra: User? = null,
            taskAwait: AwaitContinuation = awaitContinuation(),
        ) : this(
            queue1 = listOf(boxFile, file),
            msg = msg,
            taskAwait = taskAwait,
            extra = extra,
        )

        var isPlayed = false

        suspend fun await() {
            taskAwait.suspendUntilWithTimeout(15_000) // 默认最长动画15秒
        }

        fun resume() {
            taskAwait.resume(Unit)
        }
    }

    @Stable
    private data class GiftBanner(
        val giftModel: GiftWrapper,
        private val taskAwait: AwaitContinuation,
    ) {
        suspend fun await() {
            taskAwait.suspendUntilWithTimeout(5_000) // 默认最长动画5秒
        }

        fun resume() {
            taskAwait.resume(Unit)
        }
    }

    // 1. 礼物特效通道
    private val giftEffectChannel: Channel<GiftEffect> = Channel()

    // 2. 礼物横幅特效
    private val giftBannerChannel: Channel<GiftBanner> = Channel()

    // 3. 进场特效
    private val enterEffectChannel: Channel<GiftEffect> = Channel()

    private val normalEffectFlow = MutableSharedFlow<GiftEffect>()

    // 礼物横幅不配合yyeva组件
    private val giftBannerFlow = giftBannerChannel.receiveAsFlow()

    //region 礼物组件

    /**
     * 2.41.0 新增更新
     * 为了同时运行普通动效和进场横幅动效, 将GiftEffect对象中的动效文件分为两个queue
     * 保留原来的单个file参数
     *
     * todo 如果进场特效和礼物特效在同一个队列中, 那还需要保留[enterEffectChannel]属性吗?
     */
    @Composable
    fun GiftEffectView() {
        // 普通礼物特效
        GiftEffectForFlow(flow = normalEffectFlow)
        // 礼物计数横幅
        GiftBannerView()
    }

    /**
     * 礼物横幅组件
     */
    @Composable
    private fun GiftBannerView() {
        val banner =
            giftBannerFlow.run {
                produceState<GiftBanner?>(null, this) {
                    collect {
                        <EMAIL> = it
                        it.await()
                    }
                }
            }

        val scope = rememberCoroutineScope()

        banner.value?.apply {
            val state =
                remember(this) {
                    MutableTransitionState(false).apply {
                        // Start the animation immediately.
                        targetState = true
                    }
                }

            AnimatedVisibility(
                visibleState = state,
                modifier = Modifier.padding(top = 180.dp),
                enter = slideInHorizontally(initialOffsetX = { -it }),
                exit = fadeOut() + slideOutHorizontally(targetOffsetX = { -it }),
            ) {
                Row(
                    modifier =
                        Modifier
                            .padding(start = 10.dp)
                            .size(256.dp, 48.dp)
                            .clip(RoundedCornerShape(topStart = 24.dp, bottomStart = 24.dp))
                            .background(
                                brush =
                                    Brush.horizontalGradient(
                                        listOf(
                                            Color(0xFFFF9EC7),
                                            Color(0xFFFF96E1),
                                            Color(0x00FF8EFA),
                                        ),
                                    ),
                            ),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Spacer(modifier = Modifier.width(6.dp))

                    NetworkImage(
                        giftModel.sender.avatar,
                        modifier =
                            Modifier
                                .size(36.dp)
                                .clip(CircleShape),
                    )

                    Spacer(modifier = Modifier.width(4.dp))

                    Column(
                        modifier = Modifier.weight(1f),
                    ) {
                        Text(
                            text = giftModel.sender.name,
                            modifier = Modifier.basicMarquee(),
                            color = Color.White,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            maxLines = 1,
                        )
                        val receiverTarget = if (giftModel.receivers.isEmpty()) "所有人" else giftModel.receiverName
                        Text(
                            text = "送给${receiverTarget}${giftModel.gift.name}",
                            modifier = Modifier.basicMarquee(),
                            color = Color.White,
                            fontSize = 12.sp,
                            maxLines = 1,
                        )
                    }

                    Spacer(modifier = Modifier.width(4.dp))

                    NetworkImage(giftModel.gift.icon, modifier = Modifier.size(36.dp))

                    Spacer(modifier = Modifier.width(4.dp))

                    Text(
                        text = "x${giftModel.count}",
                        color = Color(0xFFFF5E8B),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                    )

                    Spacer(modifier = Modifier.width(8.dp))
                }
            }

            LaunchedEffect(this) {
                delay(3500)
                state.targetState = false

                snapshotFlow { state.isIdle && !state.currentState }
                    .filter {
                        it
                    }.onEach {
                        resume()
                    }.launchIn(scope)
            }
        }
    }

    @Composable
    private fun GiftEffectForFlow(flow: Flow<GiftEffect>) {
        val effect =
            flow.run {
                produceState<GiftEffect?>(null, this) {
                    collect {
                        <EMAIL> = it
                    }
                }
            }

        effect.value?.apply {
            // 总进度
            val totalCount by remember {
                derivedStateOf {
                    var cnt = 0
                    if (queue1.isNullOrEmpty()) {
                        cnt++
                    }
                    if (queue2.isNullOrEmpty()) {
                        cnt++
                    }
                    cnt
                }
            }
            // 已完成进度
            var doneCount by remember(msg) {
                mutableIntStateOf(0)
            }

            val ctx = LocalContext.current

            val evaResourceFetcher by remember(msg) {
                mutableStateOf(YYEVAResourceFetcher(mapOf("user" to this.extra as? User), ctx))
            }

            queue1?.let { effects ->
                EffectVideoAnimationView(
                    msg,
                    *effects.toTypedArray(),
                    iEvaFetchResource = evaResourceFetcher,
                    onStart = { hasPlayed ->
                        isPlayed = true
                        if (hasPlayed && msg != null && !msg.isPlayed) {
                            msg.isPlayed = true
                        }
                    },
                ) {
                    doneCount++
                }
            }

            queue2?.let { effects ->
                EffectVideoAnimationView(
                    msg,
                    *effects.toTypedArray(),
                    iEvaFetchResource = evaResourceFetcher,
                    onStart = { hasPlayed ->
                        isPlayed = true
                        if (hasPlayed && msg != null && !msg.isPlayed) {
                            msg.isPlayed = true
                        }
                    },
                ) {
                    doneCount++
                }
            }

            LaunchedEffect(key1 = doneCount) {
                if (doneCount >= totalCount) {
                    resume()
                }
            }
        }
    }

    //endregion

    //endregion

    private val msgListener =
        object : IMCompatListener {
            override val filter: MsgFilter = MsgFilter(rcId)

            override fun onRecvNewMessage(
                message: UCInstanceMessage,
                offline: Boolean,
            ) {
                handleRecvNewMessage(message)
            }

            override fun onPlayHistoryGiftEffect(messages: List<UCGiftMessage>) {
                messages.forEach {
                    handleRecvNewMessage(it)
                }
            }
        }

    private fun handleRecvNewMessage(msg: UCInstanceMessage) {
        if (msg is UCGiftMessage) {
            handleGiftMsg(msg)
        } else if (msg is UCCustomMessage) {
            when (msg.cmd) {
                IMEvent.USER_ENTRANCE -> {
                    handleEnteranceMsg(msg)
                }
            }
        }
    }

    private fun handleGiftMsg(msg: UCGiftMessage) {
        val giftModel = msg.gift
        viewModelScope.launch {
            val url = giftModel.gift.effectFile
            if (url.isNotEmpty()) {
                launch {
                    if (giftModel.isBlinxBox && giftModel.blindboxEffectFile.isNotEmpty()) {
                        val list =
                            coroutineScope {
                                listOf(
                                    async {
                                        getEffectFile(url, 15.seconds)
                                    },
                                    async {
                                        getEffectFile(giftModel.blindboxEffectFile, 15.seconds)
                                    },
                                )
                            }.awaitAll().filterNotNull()

                        if (list.size == 2) {
                            giftEffectChannel.send(GiftEffect(list[0], list[1], msg))
                        }
                    } else {
                        getEffectFile(giftModel.gift.effectFile, 15.seconds)?.also {
                            giftEffectChannel.send(GiftEffect(it, null, msg))
                        }
                    }
                }
            }

            launch {
                giftBannerChannel.send(GiftBanner(giftModel, awaitContinuation()))
            }
        }
    }

    private fun handleEnteranceMsg(msg: UCCustomMessage) {
        val sendUser = msg.getJsonValue<UserResponse>("user")?.toBasic()
//        val effectUrl = sendUser?.entryEffect?.takeIsNotEmpty()
//        val entranceBanner = sendUser?.entranceBanner?.takeIsNotEmpty()
        val effectUrl = ""
        val entranceBanner = ""

        viewModelScope.launch {
            val list =
                coroutineScope {
                    listOf(
                        async {
                            getEffectFile(effectUrl, 15.seconds)
                        },
                        async {
                            getEffectFile(entranceBanner, 15.seconds)
                        },
                    )
                }.awaitAll().filterNotNull()

            list.let {
                if (list.isEmpty()) {
                    return@let
                }
                if (list.size == 2) {
                    enterEffectChannel.send(
                        GiftEffect(
                            queue1 = listOf(list[0]),
                            queue2 = listOf(list[1]),
                            extra = sendUser,
                        ),
                    )
                } else {
                    enterEffectChannel.send(GiftEffect(list[0], extra = sendUser))
                }
            }
        }
    }

    //endregion

    init {

        if (autoFetchGiftData) {
            fetchGiftData()
        }

        viewModelScope.launch {
            AccountManager.userStateFlow
                .distinctUntilChangedBy {
                    it?.extra?.balance
                }.collectLatest {
                    it?.extra?.balance?.let {
                        giftListModelState.value = giftListModelState.value.copy(balance = it)
                    }
                }
        }

        viewModelScope.launch {
            while (isActive) {
                select {
                    enterEffectChannel.onReceive {
                        // 优先级更高
                        normalEffectFlow.emit(it)
                        it.await()
                    }
                    giftEffectChannel.onReceive {
                        normalEffectFlow.emit(it)
                        it.await()
                    }
                }
            }
        }

        IMCompatCore.addIMListener(msgListener)
    }

    override fun onCleared() {
        IMCompatCore.removeIMListener(msgListener)
    }

    //region 发送礼物 / 获取礼物面板

    fun fetchGiftData() {
        viewModelScope.launch {
//            val method = if (AppUserPartition.isCupid) {
//                val senceType = when (type) {
//                    ConversationType.GROUP -> 1
//                    ConversationType.CHATROOM -> 2
//                    else -> null
//                }
//                val senceID = if (senceType != null) bid else null
//                giftRepository.getGiftList(senceType, senceID)
//            } else {
//                giftRepository.getGiftList()
//            }

            // done 2.41.0 OK这个版本全部加上sence相关

            val senceType =
                when (type) {
                    ConversationType.GROUP -> 1
                    ConversationType.CHATROOM -> 2
                    ConversationType.C2C -> 4
                    else -> null
                }
            val senceID = if (senceType != 0) bid else null

            executeApiCallExpectingData {
                GiftApiService.instance.getGiftShelvesList(
                    scene_type = senceType,
                    scene_id = senceID,
                )
            }.onSuccess {
                giftListModelState.value =
                    giftListModelState.value.copy(
                        preview = false,
                        balance = it.balance,
                        list = it.tabs,
                    )
            }

//            giftRepository.getGiftList(senceType, senceID).onSuccess {
//                giftListModelState.value = giftListModelState.value.copy(
//                    preview = false,
//                    balance = it.balance,
//                    list = it.tabs
//                )
//            }
        }
    }

    fun sendGiftC2C(
        gift: GiftBean,
        params: GiftSendParams,
    ) {
        viewModelScope.launch {
//            giftRepository.sendGiftOnSingle(
//                bid,
//                gift.id,
//                count,
//                extra.fromPacket,
//                extra.greetings,
//                extra.isIntimate
//            )
//                .onSuccess { json ->
//                    handleSendGiftResult(json, gift, count, extra)
//                }
//                .toastError()
            executeApiCallExpectingData {
                GiftApiService.instance.giveC2CGift(
                    mapOf(
                        "target_user_id" to bid,
                        "gift_id" to gift.id,
                        "count" to params.number,
                        "from_packet" to params.fromPacket.toString(),
                        "greetings" to params.greetings,
                        "is_intimate_gift" to params.isIntimate.toString(),
                    ),
                )
            }.onSuccess {
                handleSendGiftResult(it, gift, params)
            }
        }
    }

    fun sendGiftToGroup(
        targets: List<String>,
        gift: GiftBean,
        params: GiftSendParams,
    ) {
        sendGiftAt(
            giftScene = GiftScene.Group,
            targets = targets,
            gift = gift,
            params = params,
        )
    }

    fun sendGiftAt(
        giftScene: GiftScene,
        targets: List<String>,
        gift: GiftBean,
        params: GiftSendParams,
    ) {
        viewModelScope.launch {
            val id = bid.toIntOrNull() ?: return@launch
            val receiverIds =
                if (targets.isEmpty()) {
                    ""
                } else {
                    targets.joinToString(",")
                }
            when (giftScene) {
                GiftScene.Group -> {
                    executeApiCallExpectingData {
                        GiftApiService.instance.giveGroupGift(
                            mapOf(
                                "group_id" to id,
                                "receiver_ids" to receiverIds,
                                "gift_id" to gift.id,
                                "count" to params.number,
                                "from_packet" to params.fromPacket.toString(),
                                "greetings" to params.greetings,
                            ),
                        )
                    }
//                    giftRepository.sendGiftOnTribe(
//                        id,
//                        targets.toSet(),
//                        gift.id,
//                        count,
//                        extra.fromPacket,
//                        extra.greetings,
//                        extra.comboId
//                    )
//                        .onSuccess { json ->
//                            handleSendGiftResult(json, gift, count, extra)
//                        }
//                        .toastError()
                }

                GiftScene.ROOM -> {
                    executeApiCallExpectingData {
                        GiftApiService.instance.giveChatRoomGift(
                            mapOf(
                                "chatgroup_id" to id,
                                "receiver_ids" to receiverIds,
                                "gift_id" to gift.id,
                                "count" to params.number,
                                "from_packet" to params.fromPacket.toString(),
                                "greetings" to params.greetings,
                            ),
                        )
                    }
//                    giftRepository.sendGiftOnRoom(
//                        id,
//                        targets.toSet(),
//                        gift.id,
//                        count,
//                        extra.fromPacket,
//                        extra.greetings,
//                        extra.comboId
//                    )
//                        .onSuccess { json ->
//                            handleSendGiftResult(json, gift, count, extra)
//                        }
//                        .toastError()
                }

                GiftScene.CP -> {
                    executeApiCallExpectingData {
                        GiftApiService.instance.giveC2CGift(
                            mapOf(
                                "room_id" to bid,
                                "gift_id" to gift.id,
                                "count" to params.number,
                                "from_packet" to params.fromPacket.toString(),
                                "greetings" to params.greetings.toString(),
                            ),
                        )
                    }
//                    giftRepository.sendGiftOnCouple(
//                        id,
//                        targets.toSet(),
//                        gift.id,
//                        count,
//                        extra.fromPacket,
//                        extra.greetings,
//                        extra.comboId
//                    )
//                        .onSuccess { json ->
//                            handleSendGiftResult(json, gift, count, extra)
//                        }
//                        .toastError()
                }
            }.onSuccess {
                handleSendGiftResult(it, gift, params)
            }
        }
    }

    //endregion

    private fun handleSendGiftResult(
        json: JsonObject,
        gift: GiftBean,
        params: GiftSendParams,
    ) {
        json.getIntOrNull("balance")?.also { balance ->
            giftListModelState.value.also {
                if (params.fromPacket) {
                    giftListModelState.value =
                        it.copy(
                            balance = balance,
                            list =
                                it.list.map { tab ->
                                    if (tab.tabId == -1) {
                                        tab.copy(
                                            gifts =
                                                tab.gifts
                                                    .mapNotNull { item ->
                                                        if (gift.id == item.id) {
                                                            if (item.giftCount > params.number) {
                                                                item.copy(giftCount = item.giftCount - params.number)
                                                            } else {
                                                                null
                                                            }
                                                        } else {
                                                            item
                                                        }
                                                    }.toMutableList(),
                                        )
                                    } else {
                                        tab
                                    }
                                },
                        )
                } else {
                    giftListModelState.value = it.copy(balance = balance)
                }
            }
        }
        json.getStringOrNull("toast_msg").takeIsNotEmpty()?.also {
            showToast(it)
        }
    }
}
