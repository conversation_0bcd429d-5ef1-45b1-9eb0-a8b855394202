package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.ImagePlaceholder: ImageVector
    get() {
        val current = _imagePlaceholder
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.ImagePlaceholder",
                defaultWidth = 147.0.dp,
                defaultHeight = 108.0.dp,
                viewportWidth = 147.0f,
                viewportHeight = 108.0f,
            ).apply {
                // M128.87 28.3 a12.1 12.1 0 0 1 13.1 10.98 l2.94 33.59 c.47 5.42 -3.43 9.78 -7.52 12.26 l-36.52 21.78 a3.76 3.76 0 0 1 -5.52 -4.29 l4.03 -13.64 -75.61 6.62 a12.1 12.1 0 0 1 -13.1 -11 l-.32 -3.7 -.21 -.5 -7.25 -20.68 a3.8 3.8 0 0 1 2.97 -4.99 l2.17 -.35 -.29 -3.36 a12.1 12.1 0 0 1 10.99 -13.09z M5.56 57.6 l.1 -.1z m9.27 -42.2 a2.44 2.44 0 0 1 3.72 -.53 l8.35 7.64 a2.45 2.45 0 0 1 -2.89 3.92 l-9.58 -5.64 a2.47 2.47 0 0 1 -.83 -3.4z M27.84 .84 a2.14 2.14 0 0 1 3.1 1.1 l5.82 16 a2.13 2.13 0 0 1 -1.24 2.72 l-1.78 .67 a2.1 2.1 0 0 1 -2.56 -.86 L22.49 6.57 a2.13 2.13 0 0 1 .72 -2.96z
                path(
                    fill = SolidColor(Color(0xFFA8ABB6)),
                ) {
                    // M 128.87 28.3
                    moveTo(x = 128.87f, y = 28.3f)
                    // a 12.1 12.1 0 0 1 13.1 10.98
                    arcToRelative(
                        a = 12.1f,
                        b = 12.1f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 13.1f,
                        dy1 = 10.98f,
                    )
                    // l 2.94 33.59
                    lineToRelative(dx = 2.94f, dy = 33.59f)
                    // c 0.47 5.42 -3.43 9.78 -7.52 12.26
                    curveToRelative(
                        dx1 = 0.47f,
                        dy1 = 5.42f,
                        dx2 = -3.43f,
                        dy2 = 9.78f,
                        dx3 = -7.52f,
                        dy3 = 12.26f,
                    )
                    // l -36.52 21.78
                    lineToRelative(dx = -36.52f, dy = 21.78f)
                    // a 3.76 3.76 0 0 1 -5.52 -4.29
                    arcToRelative(
                        a = 3.76f,
                        b = 3.76f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -5.52f,
                        dy1 = -4.29f,
                    )
                    // l 4.03 -13.64
                    lineToRelative(dx = 4.03f, dy = -13.64f)
                    // l -75.61 6.62
                    lineToRelative(dx = -75.61f, dy = 6.62f)
                    // a 12.1 12.1 0 0 1 -13.1 -11
                    arcToRelative(
                        a = 12.1f,
                        b = 12.1f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -13.1f,
                        dy1 = -11.0f,
                    )
                    // l -0.32 -3.7
                    lineToRelative(dx = -0.32f, dy = -3.7f)
                    // l -0.21 -0.5
                    lineToRelative(dx = -0.21f, dy = -0.5f)
                    // l -7.25 -20.68
                    lineToRelative(dx = -7.25f, dy = -20.68f)
                    // a 3.8 3.8 0 0 1 2.97 -4.99
                    arcToRelative(
                        a = 3.8f,
                        b = 3.8f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 2.97f,
                        dy1 = -4.99f,
                    )
                    // l 2.17 -0.35
                    lineToRelative(dx = 2.17f, dy = -0.35f)
                    // l -0.29 -3.36
                    lineToRelative(dx = -0.29f, dy = -3.36f)
                    // a 12.1 12.1 0 0 1 10.99 -13.09z
                    arcToRelative(
                        a = 12.1f,
                        b = 12.1f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 10.99f,
                        dy1 = -13.09f,
                    )
                    close()
                    // M 5.56 57.6
                    moveTo(x = 5.56f, y = 57.6f)
                    // l 0.1 -0.1z
                    lineToRelative(dx = 0.1f, dy = -0.1f)
                    close()
                    // m 9.27 -42.2
                    moveToRelative(dx = 9.27f, dy = -42.2f)
                    // a 2.44 2.44 0 0 1 3.72 -0.53
                    arcToRelative(
                        a = 2.44f,
                        b = 2.44f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 3.72f,
                        dy1 = -0.53f,
                    )
                    // l 8.35 7.64
                    lineToRelative(dx = 8.35f, dy = 7.64f)
                    // a 2.45 2.45 0 0 1 -2.89 3.92
                    arcToRelative(
                        a = 2.45f,
                        b = 2.45f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -2.89f,
                        dy1 = 3.92f,
                    )
                    // l -9.58 -5.64
                    lineToRelative(dx = -9.58f, dy = -5.64f)
                    // a 2.47 2.47 0 0 1 -0.83 -3.4z
                    arcToRelative(
                        a = 2.47f,
                        b = 2.47f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -0.83f,
                        dy1 = -3.4f,
                    )
                    close()
                    // M 27.84 0.84
                    moveTo(x = 27.84f, y = 0.84f)
                    // a 2.14 2.14 0 0 1 3.1 1.1
                    arcToRelative(
                        a = 2.14f,
                        b = 2.14f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 3.1f,
                        dy1 = 1.1f,
                    )
                    // l 5.82 16
                    lineToRelative(dx = 5.82f, dy = 16.0f)
                    // a 2.13 2.13 0 0 1 -1.24 2.72
                    arcToRelative(
                        a = 2.13f,
                        b = 2.13f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.24f,
                        dy1 = 2.72f,
                    )
                    // l -1.78 0.67
                    lineToRelative(dx = -1.78f, dy = 0.67f)
                    // a 2.1 2.1 0 0 1 -2.56 -0.86
                    arcToRelative(
                        a = 2.1f,
                        b = 2.1f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -2.56f,
                        dy1 = -0.86f,
                    )
                    // L 22.49 6.57
                    lineTo(x = 22.49f, y = 6.57f)
                    // a 2.13 2.13 0 0 1 0.72 -2.96z
                    arcToRelative(
                        a = 2.13f,
                        b = 2.13f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.72f,
                        dy1 = -2.96f,
                    )
                    close()
                }
                // M36.53 54.76 c.79 0 1.38 .71 1.23 1.49 L33.93 77.3 a1.25 1.25 0 0 1 -1.05 1.02 L26.7 79.3 l-.2 .02 a1.3 1.3 0 0 1 -1.22 -.92 l-2.8 -9.82 H22.4 l-1.3 9.71 a1.3 1.3 0 0 1 -1.04 1.08 l-6.13 1.01 -.2 .02 a1.25 1.25 0 0 1 -1.18 -.84 L5.29 58.87 a1.26 1.26 0 0 1 1 -1.66 l6.26 -1.02 .2 -.02 a1.3 1.3 0 0 1 1.21 .9 l2.93 9.87 h.07 l1.3 -9.55 a1.3 1.3 0 0 1 1.06 -1.07 l4.18 -.67 .2 -.02 a1.3 1.3 0 0 1 1.2 .9 l2.9 9.65 .09 -.01 1.3 -10.33 a1.24 1.24 0 0 1 1.24 -1.09z m13.98 -1.87 q1.28 0 2.54 .2 a9 9 0 0 1 3.4 1.23 7.5 7.5 0 0 1 2.46 2.49 8.5 8.5 0 0 1 1.12 3.94 l.71 13.93 a1.3 1.3 0 0 1 -1.05 1.31 l-5.47 .91 -.2 .02 c-.65 0 -1.21 -.5 -1.26 -1.16 l-.11 -1.33 h-.08 a5.6 5.6 0 0 1 -2.55 2.33 10 10 0 0 1 -4.2 .9 8 8 0 0 1 -5.01 -1.8 8 8 0 0 1 -1.92 -2.45 9 9 0 0 1 -.9 -3.33 7 7 0 0 1 .39 -3.05 8 8 0 0 1 1.54 -2.52 8 8 0 0 1 2.46 -1.8 9 9 0 0 1 4.08 -.88 7 7 0 0 1 2.23 .33 9 9 0 0 1 2.22 1.04 L50.8 62 q-.15 -1.82 -1.38 -2.32 a6 6 0 0 0 -2.3 -.38 q-.41 0 -.85 .03 a14 14 0 0 0 -3.23 .62 l-1.01 .33 a1.27 1.27 0 0 1 -1.64 -1.4 l.57 -3.55 a1.3 1.3 0 0 1 .82 -1 A23 23 0 0 1 45 53.5 a40 40 0 0 1 4.2 -.55z m19.63 -9.57 a1.27 1.27 0 0 1 1.26 1.22 l.51 15.4 H72 l2.82 -7.55 a1.3 1.3 0 0 1 1.18 -.83 l6.07 .01 c.9 0 1.51 .92 1.16 1.75 L79.6 62 l5.84 7.58 a1.26 1.26 0 0 1 -.36 1.85 l-5.65 3.39 A1.3 1.3 0 0 1 78.77 75 a1.3 1.3 0 0 1 -1.07 -.6 l-5.3 -8.5 H72.3 l.93 8.05 a1.26 1.26 0 0 1 -1.04 1.38 l-5.68 .96 -.21 .02 a1.26 1.26 0 0 1 -1.26 -1.15 L62.5 45.72 a1.26 1.26 0 0 1 1.06 -1.35 l6.38 -1.03z m53.63 3.84 q.9 0 1.78 .12 a11.18 11.18 0 0 1 7.92 4.68 13 13 0 0 1 2.06 4.7 12 12 0 0 1 .16 5.13 11.5 11.5 0 0 1 -5.15 7.62 14 14 0 0 1 -4.7 1.92 16 16 0 0 1 -5.1 .25 12 12 0 0 1 -4.48 -1.5 11.66 11.66 0 0 1 -5.13 -6.53 q0 .12 -.03 .23 a11.5 11.5 0 0 1 -5.14 7.62 14 14 0 0 1 -4.7 1.91 16 16 0 0 1 -5.1 .26 12 12 0 0 1 -4.48 -1.5 11 11 0 0 1 -3.46 -3.17 12 12 0 0 1 -2.02 -4.7 12 12 0 0 1 -.15 -5.12 12 12 0 0 1 1.76 -4.35 q1.3 -1.95 3.38 -3.3 a14 14 0 0 1 4.7 -1.91 15 15 0 0 1 5.08 -.26 11.18 11.18 0 0 1 7.92 4.68 q1.08 1.5 1.7 3.42 l.05 -.26 a12 12 0 0 1 1.76 -4.36 q1.3 -1.95 3.38 -3.3 a15.4 15.4 0 0 1 8 -2.29 M48.4 67.29 a6 6 0 0 0 -1.4 .2 4.4 4.4 0 0 0 -2.14 1.1 q-.82 .82 -.63 1.71 .24 .9 1.25 1.28 a3 3 0 0 0 1.17 .2 6 6 0 0 0 1.37 -.18 4 4 0 0 0 2.22 -1.18 q.7 -.82 .47 -1.67 -.19 -.75 -1.12 -1.21 a3 3 0 0 0 -1.19 -.25 m50.73 -9.89 a5 5 0 0 0 -2.39 .68 5 5 0 0 0 -2.39 2.38 3.3 3.3 0 0 0 .18 2.9 3.5 3.5 0 0 0 3.16 1.97 5.3 5.3 0 0 0 2.46 -.68 4.8 4.8 0 0 0 2.35 -2.38 3.4 3.4 0 0 0 -.18 -2.92 3.6 3.6 0 0 0 -3.19 -1.95 m24.58 -1.98 a5 5 0 0 0 -2.39 .68 4.8 4.8 0 0 0 -2.39 2.39 3.3 3.3 0 0 0 .18 2.9 3.5 3.5 0 0 0 3.16 1.96 q1.16 0 2.46 -.68 a4.8 4.8 0 0 0 2.35 -2.37 3.4 3.4 0 0 0 -.18 -2.93 3.6 3.6 0 0 0 -3.19 -1.95
                path(
                    fill = SolidColor(Color(0xFFE9EAEF)),
                ) {
                    // M 36.53 54.76
                    moveTo(x = 36.53f, y = 54.76f)
                    // c 0.79 0 1.38 0.71 1.23 1.49
                    curveToRelative(
                        dx1 = 0.79f,
                        dy1 = 0.0f,
                        dx2 = 1.38f,
                        dy2 = 0.71f,
                        dx3 = 1.23f,
                        dy3 = 1.49f,
                    )
                    // L 33.93 77.3
                    lineTo(x = 33.93f, y = 77.3f)
                    // a 1.25 1.25 0 0 1 -1.05 1.02
                    arcToRelative(
                        a = 1.25f,
                        b = 1.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.05f,
                        dy1 = 1.02f,
                    )
                    // L 26.7 79.3
                    lineTo(x = 26.7f, y = 79.3f)
                    // l -0.2 0.02
                    lineToRelative(dx = -0.2f, dy = 0.02f)
                    // a 1.3 1.3 0 0 1 -1.22 -0.92
                    arcToRelative(
                        a = 1.3f,
                        b = 1.3f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.22f,
                        dy1 = -0.92f,
                    )
                    // l -2.8 -9.82
                    lineToRelative(dx = -2.8f, dy = -9.82f)
                    // H 22.4
                    horizontalLineTo(x = 22.4f)
                    // l -1.3 9.71
                    lineToRelative(dx = -1.3f, dy = 9.71f)
                    // a 1.3 1.3 0 0 1 -1.04 1.08
                    arcToRelative(
                        a = 1.3f,
                        b = 1.3f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.04f,
                        dy1 = 1.08f,
                    )
                    // l -6.13 1.01
                    lineToRelative(dx = -6.13f, dy = 1.01f)
                    // l -0.2 0.02
                    lineToRelative(dx = -0.2f, dy = 0.02f)
                    // a 1.25 1.25 0 0 1 -1.18 -0.84
                    arcToRelative(
                        a = 1.25f,
                        b = 1.25f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.18f,
                        dy1 = -0.84f,
                    )
                    // L 5.29 58.87
                    lineTo(x = 5.29f, y = 58.87f)
                    // a 1.26 1.26 0 0 1 1 -1.66
                    arcToRelative(
                        a = 1.26f,
                        b = 1.26f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.0f,
                        dy1 = -1.66f,
                    )
                    // l 6.26 -1.02
                    lineToRelative(dx = 6.26f, dy = -1.02f)
                    // l 0.2 -0.02
                    lineToRelative(dx = 0.2f, dy = -0.02f)
                    // a 1.3 1.3 0 0 1 1.21 0.9
                    arcToRelative(
                        a = 1.3f,
                        b = 1.3f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.21f,
                        dy1 = 0.9f,
                    )
                    // l 2.93 9.87
                    lineToRelative(dx = 2.93f, dy = 9.87f)
                    // h 0.07
                    horizontalLineToRelative(dx = 0.07f)
                    // l 1.3 -9.55
                    lineToRelative(dx = 1.3f, dy = -9.55f)
                    // a 1.3 1.3 0 0 1 1.06 -1.07
                    arcToRelative(
                        a = 1.3f,
                        b = 1.3f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.06f,
                        dy1 = -1.07f,
                    )
                    // l 4.18 -0.67
                    lineToRelative(dx = 4.18f, dy = -0.67f)
                    // l 0.2 -0.02
                    lineToRelative(dx = 0.2f, dy = -0.02f)
                    // a 1.3 1.3 0 0 1 1.2 0.9
                    arcToRelative(
                        a = 1.3f,
                        b = 1.3f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.2f,
                        dy1 = 0.9f,
                    )
                    // l 2.9 9.65
                    lineToRelative(dx = 2.9f, dy = 9.65f)
                    // l 0.09 -0.01
                    lineToRelative(dx = 0.09f, dy = -0.01f)
                    // l 1.3 -10.33
                    lineToRelative(dx = 1.3f, dy = -10.33f)
                    // a 1.24 1.24 0 0 1 1.24 -1.09z
                    arcToRelative(
                        a = 1.24f,
                        b = 1.24f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.24f,
                        dy1 = -1.09f,
                    )
                    close()
                    // m 13.98 -1.87
                    moveToRelative(dx = 13.98f, dy = -1.87f)
                    // q 1.28 0 2.54 0.2
                    quadToRelative(
                        dx1 = 1.28f,
                        dy1 = 0.0f,
                        dx2 = 2.54f,
                        dy2 = 0.2f,
                    )
                    // a 9 9 0 0 1 3.4 1.23
                    arcToRelative(
                        a = 9.0f,
                        b = 9.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 3.4f,
                        dy1 = 1.23f,
                    )
                    // a 7.5 7.5 0 0 1 2.46 2.49
                    arcToRelative(
                        a = 7.5f,
                        b = 7.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 2.46f,
                        dy1 = 2.49f,
                    )
                    // a 8.5 8.5 0 0 1 1.12 3.94
                    arcToRelative(
                        a = 8.5f,
                        b = 8.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.12f,
                        dy1 = 3.94f,
                    )
                    // l 0.71 13.93
                    lineToRelative(dx = 0.71f, dy = 13.93f)
                    // a 1.3 1.3 0 0 1 -1.05 1.31
                    arcToRelative(
                        a = 1.3f,
                        b = 1.3f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.05f,
                        dy1 = 1.31f,
                    )
                    // l -5.47 0.91
                    lineToRelative(dx = -5.47f, dy = 0.91f)
                    // l -0.2 0.02
                    lineToRelative(dx = -0.2f, dy = 0.02f)
                    // c -0.65 0 -1.21 -0.5 -1.26 -1.16
                    curveToRelative(
                        dx1 = -0.65f,
                        dy1 = 0.0f,
                        dx2 = -1.21f,
                        dy2 = -0.5f,
                        dx3 = -1.26f,
                        dy3 = -1.16f,
                    )
                    // l -0.11 -1.33
                    lineToRelative(dx = -0.11f, dy = -1.33f)
                    // h -0.08
                    horizontalLineToRelative(dx = -0.08f)
                    // a 5.6 5.6 0 0 1 -2.55 2.33
                    arcToRelative(
                        a = 5.6f,
                        b = 5.6f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -2.55f,
                        dy1 = 2.33f,
                    )
                    // a 10 10 0 0 1 -4.2 0.9
                    arcToRelative(
                        a = 10.0f,
                        b = 10.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -4.2f,
                        dy1 = 0.9f,
                    )
                    // a 8 8 0 0 1 -5.01 -1.8
                    arcToRelative(
                        a = 8.0f,
                        b = 8.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -5.01f,
                        dy1 = -1.8f,
                    )
                    // a 8 8 0 0 1 -1.92 -2.45
                    arcToRelative(
                        a = 8.0f,
                        b = 8.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.92f,
                        dy1 = -2.45f,
                    )
                    // a 9 9 0 0 1 -0.9 -3.33
                    arcToRelative(
                        a = 9.0f,
                        b = 9.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -0.9f,
                        dy1 = -3.33f,
                    )
                    // a 7 7 0 0 1 0.39 -3.05
                    arcToRelative(
                        a = 7.0f,
                        b = 7.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.39f,
                        dy1 = -3.05f,
                    )
                    // a 8 8 0 0 1 1.54 -2.52
                    arcToRelative(
                        a = 8.0f,
                        b = 8.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.54f,
                        dy1 = -2.52f,
                    )
                    // a 8 8 0 0 1 2.46 -1.8
                    arcToRelative(
                        a = 8.0f,
                        b = 8.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 2.46f,
                        dy1 = -1.8f,
                    )
                    // a 9 9 0 0 1 4.08 -0.88
                    arcToRelative(
                        a = 9.0f,
                        b = 9.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 4.08f,
                        dy1 = -0.88f,
                    )
                    // a 7 7 0 0 1 2.23 0.33
                    arcToRelative(
                        a = 7.0f,
                        b = 7.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 2.23f,
                        dy1 = 0.33f,
                    )
                    // a 9 9 0 0 1 2.22 1.04
                    arcToRelative(
                        a = 9.0f,
                        b = 9.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 2.22f,
                        dy1 = 1.04f,
                    )
                    // L 50.8 62
                    lineTo(x = 50.8f, y = 62.0f)
                    // q -0.15 -1.82 -1.38 -2.32
                    quadToRelative(
                        dx1 = -0.15f,
                        dy1 = -1.82f,
                        dx2 = -1.38f,
                        dy2 = -2.32f,
                    )
                    // a 6 6 0 0 0 -2.3 -0.38
                    arcToRelative(
                        a = 6.0f,
                        b = 6.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -2.3f,
                        dy1 = -0.38f,
                    )
                    // q -0.41 0 -0.85 0.03
                    quadToRelative(
                        dx1 = -0.41f,
                        dy1 = 0.0f,
                        dx2 = -0.85f,
                        dy2 = 0.03f,
                    )
                    // a 14 14 0 0 0 -3.23 0.62
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -3.23f,
                        dy1 = 0.62f,
                    )
                    // l -1.01 0.33
                    lineToRelative(dx = -1.01f, dy = 0.33f)
                    // a 1.27 1.27 0 0 1 -1.64 -1.4
                    arcToRelative(
                        a = 1.27f,
                        b = 1.27f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.64f,
                        dy1 = -1.4f,
                    )
                    // l 0.57 -3.55
                    lineToRelative(dx = 0.57f, dy = -3.55f)
                    // a 1.3 1.3 0 0 1 0.82 -1
                    arcToRelative(
                        a = 1.3f,
                        b = 1.3f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.82f,
                        dy1 = -1.0f,
                    )
                    // A 23 23 0 0 1 45 53.5
                    arcTo(
                        horizontalEllipseRadius = 23.0f,
                        verticalEllipseRadius = 23.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 45.0f,
                        y1 = 53.5f,
                    )
                    // a 40 40 0 0 1 4.2 -0.55z
                    arcToRelative(
                        a = 40.0f,
                        b = 40.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 4.2f,
                        dy1 = -0.55f,
                    )
                    close()
                    // m 19.63 -9.57
                    moveToRelative(dx = 19.63f, dy = -9.57f)
                    // a 1.27 1.27 0 0 1 1.26 1.22
                    arcToRelative(
                        a = 1.27f,
                        b = 1.27f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.26f,
                        dy1 = 1.22f,
                    )
                    // l 0.51 15.4
                    lineToRelative(dx = 0.51f, dy = 15.4f)
                    // H 72
                    horizontalLineTo(x = 72.0f)
                    // l 2.82 -7.55
                    lineToRelative(dx = 2.82f, dy = -7.55f)
                    // a 1.3 1.3 0 0 1 1.18 -0.83
                    arcToRelative(
                        a = 1.3f,
                        b = 1.3f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.18f,
                        dy1 = -0.83f,
                    )
                    // l 6.07 0.01
                    lineToRelative(dx = 6.07f, dy = 0.01f)
                    // c 0.9 0 1.51 0.92 1.16 1.75
                    curveToRelative(
                        dx1 = 0.9f,
                        dy1 = 0.0f,
                        dx2 = 1.51f,
                        dy2 = 0.92f,
                        dx3 = 1.16f,
                        dy3 = 1.75f,
                    )
                    // L 79.6 62
                    lineTo(x = 79.6f, y = 62.0f)
                    // l 5.84 7.58
                    lineToRelative(dx = 5.84f, dy = 7.58f)
                    // a 1.26 1.26 0 0 1 -0.36 1.85
                    arcToRelative(
                        a = 1.26f,
                        b = 1.26f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -0.36f,
                        dy1 = 1.85f,
                    )
                    // l -5.65 3.39
                    lineToRelative(dx = -5.65f, dy = 3.39f)
                    // A 1.3 1.3 0 0 1 78.77 75
                    arcTo(
                        horizontalEllipseRadius = 1.3f,
                        verticalEllipseRadius = 1.3f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 78.77f,
                        y1 = 75.0f,
                    )
                    // a 1.3 1.3 0 0 1 -1.07 -0.6
                    arcToRelative(
                        a = 1.3f,
                        b = 1.3f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.07f,
                        dy1 = -0.6f,
                    )
                    // l -5.3 -8.5
                    lineToRelative(dx = -5.3f, dy = -8.5f)
                    // H 72.3
                    horizontalLineTo(x = 72.3f)
                    // l 0.93 8.05
                    lineToRelative(dx = 0.93f, dy = 8.05f)
                    // a 1.26 1.26 0 0 1 -1.04 1.38
                    arcToRelative(
                        a = 1.26f,
                        b = 1.26f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.04f,
                        dy1 = 1.38f,
                    )
                    // l -5.68 0.96
                    lineToRelative(dx = -5.68f, dy = 0.96f)
                    // l -0.21 0.02
                    lineToRelative(dx = -0.21f, dy = 0.02f)
                    // a 1.26 1.26 0 0 1 -1.26 -1.15
                    arcToRelative(
                        a = 1.26f,
                        b = 1.26f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.26f,
                        dy1 = -1.15f,
                    )
                    // L 62.5 45.72
                    lineTo(x = 62.5f, y = 45.72f)
                    // a 1.26 1.26 0 0 1 1.06 -1.35
                    arcToRelative(
                        a = 1.26f,
                        b = 1.26f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.06f,
                        dy1 = -1.35f,
                    )
                    // l 6.38 -1.03z
                    lineToRelative(dx = 6.38f, dy = -1.03f)
                    close()
                    // m 53.63 3.84
                    moveToRelative(dx = 53.63f, dy = 3.84f)
                    // q 0.9 0 1.78 0.12
                    quadToRelative(
                        dx1 = 0.9f,
                        dy1 = 0.0f,
                        dx2 = 1.78f,
                        dy2 = 0.12f,
                    )
                    // a 11.18 11.18 0 0 1 7.92 4.68
                    arcToRelative(
                        a = 11.18f,
                        b = 11.18f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 7.92f,
                        dy1 = 4.68f,
                    )
                    // a 13 13 0 0 1 2.06 4.7
                    arcToRelative(
                        a = 13.0f,
                        b = 13.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 2.06f,
                        dy1 = 4.7f,
                    )
                    // a 12 12 0 0 1 0.16 5.13
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.16f,
                        dy1 = 5.13f,
                    )
                    // a 11.5 11.5 0 0 1 -5.15 7.62
                    arcToRelative(
                        a = 11.5f,
                        b = 11.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -5.15f,
                        dy1 = 7.62f,
                    )
                    // a 14 14 0 0 1 -4.7 1.92
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -4.7f,
                        dy1 = 1.92f,
                    )
                    // a 16 16 0 0 1 -5.1 0.25
                    arcToRelative(
                        a = 16.0f,
                        b = 16.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -5.1f,
                        dy1 = 0.25f,
                    )
                    // a 12 12 0 0 1 -4.48 -1.5
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -4.48f,
                        dy1 = -1.5f,
                    )
                    // a 11.66 11.66 0 0 1 -5.13 -6.53
                    arcToRelative(
                        a = 11.66f,
                        b = 11.66f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -5.13f,
                        dy1 = -6.53f,
                    )
                    // q 0 0.12 -0.03 0.23
                    quadToRelative(
                        dx1 = 0.0f,
                        dy1 = 0.12f,
                        dx2 = -0.03f,
                        dy2 = 0.23f,
                    )
                    // a 11.5 11.5 0 0 1 -5.14 7.62
                    arcToRelative(
                        a = 11.5f,
                        b = 11.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -5.14f,
                        dy1 = 7.62f,
                    )
                    // a 14 14 0 0 1 -4.7 1.91
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -4.7f,
                        dy1 = 1.91f,
                    )
                    // a 16 16 0 0 1 -5.1 0.26
                    arcToRelative(
                        a = 16.0f,
                        b = 16.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -5.1f,
                        dy1 = 0.26f,
                    )
                    // a 12 12 0 0 1 -4.48 -1.5
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -4.48f,
                        dy1 = -1.5f,
                    )
                    // a 11 11 0 0 1 -3.46 -3.17
                    arcToRelative(
                        a = 11.0f,
                        b = 11.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -3.46f,
                        dy1 = -3.17f,
                    )
                    // a 12 12 0 0 1 -2.02 -4.7
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -2.02f,
                        dy1 = -4.7f,
                    )
                    // a 12 12 0 0 1 -0.15 -5.12
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -0.15f,
                        dy1 = -5.12f,
                    )
                    // a 12 12 0 0 1 1.76 -4.35
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.76f,
                        dy1 = -4.35f,
                    )
                    // q 1.3 -1.95 3.38 -3.3
                    quadToRelative(
                        dx1 = 1.3f,
                        dy1 = -1.95f,
                        dx2 = 3.38f,
                        dy2 = -3.3f,
                    )
                    // a 14 14 0 0 1 4.7 -1.91
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 4.7f,
                        dy1 = -1.91f,
                    )
                    // a 15 15 0 0 1 5.08 -0.26
                    arcToRelative(
                        a = 15.0f,
                        b = 15.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 5.08f,
                        dy1 = -0.26f,
                    )
                    // a 11.18 11.18 0 0 1 7.92 4.68
                    arcToRelative(
                        a = 11.18f,
                        b = 11.18f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 7.92f,
                        dy1 = 4.68f,
                    )
                    // q 1.08 1.5 1.7 3.42
                    quadToRelative(
                        dx1 = 1.08f,
                        dy1 = 1.5f,
                        dx2 = 1.7f,
                        dy2 = 3.42f,
                    )
                    // l 0.05 -0.26
                    lineToRelative(dx = 0.05f, dy = -0.26f)
                    // a 12 12 0 0 1 1.76 -4.36
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.76f,
                        dy1 = -4.36f,
                    )
                    // q 1.3 -1.95 3.38 -3.3
                    quadToRelative(
                        dx1 = 1.3f,
                        dy1 = -1.95f,
                        dx2 = 3.38f,
                        dy2 = -3.3f,
                    )
                    // a 15.4 15.4 0 0 1 8 -2.29
                    arcToRelative(
                        a = 15.4f,
                        b = 15.4f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 8.0f,
                        dy1 = -2.29f,
                    )
                    // M 48.4 67.29
                    moveTo(x = 48.4f, y = 67.29f)
                    // a 6 6 0 0 0 -1.4 0.2
                    arcToRelative(
                        a = 6.0f,
                        b = 6.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -1.4f,
                        dy1 = 0.2f,
                    )
                    // a 4.4 4.4 0 0 0 -2.14 1.1
                    arcToRelative(
                        a = 4.4f,
                        b = 4.4f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -2.14f,
                        dy1 = 1.1f,
                    )
                    // q -0.82 0.82 -0.63 1.71
                    quadToRelative(
                        dx1 = -0.82f,
                        dy1 = 0.82f,
                        dx2 = -0.63f,
                        dy2 = 1.71f,
                    )
                    // q 0.24 0.9 1.25 1.28
                    quadToRelative(
                        dx1 = 0.24f,
                        dy1 = 0.9f,
                        dx2 = 1.25f,
                        dy2 = 1.28f,
                    )
                    // a 3 3 0 0 0 1.17 0.2
                    arcToRelative(
                        a = 3.0f,
                        b = 3.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 1.17f,
                        dy1 = 0.2f,
                    )
                    // a 6 6 0 0 0 1.37 -0.18
                    arcToRelative(
                        a = 6.0f,
                        b = 6.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 1.37f,
                        dy1 = -0.18f,
                    )
                    // a 4 4 0 0 0 2.22 -1.18
                    arcToRelative(
                        a = 4.0f,
                        b = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 2.22f,
                        dy1 = -1.18f,
                    )
                    // q 0.7 -0.82 0.47 -1.67
                    quadToRelative(
                        dx1 = 0.7f,
                        dy1 = -0.82f,
                        dx2 = 0.47f,
                        dy2 = -1.67f,
                    )
                    // q -0.19 -0.75 -1.12 -1.21
                    quadToRelative(
                        dx1 = -0.19f,
                        dy1 = -0.75f,
                        dx2 = -1.12f,
                        dy2 = -1.21f,
                    )
                    // a 3 3 0 0 0 -1.19 -0.25
                    arcToRelative(
                        a = 3.0f,
                        b = 3.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -1.19f,
                        dy1 = -0.25f,
                    )
                    // m 50.73 -9.89
                    moveToRelative(dx = 50.73f, dy = -9.89f)
                    // a 5 5 0 0 0 -2.39 0.68
                    arcToRelative(
                        a = 5.0f,
                        b = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -2.39f,
                        dy1 = 0.68f,
                    )
                    // a 5 5 0 0 0 -2.39 2.38
                    arcToRelative(
                        a = 5.0f,
                        b = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -2.39f,
                        dy1 = 2.38f,
                    )
                    // a 3.3 3.3 0 0 0 0.18 2.9
                    arcToRelative(
                        a = 3.3f,
                        b = 3.3f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 0.18f,
                        dy1 = 2.9f,
                    )
                    // a 3.5 3.5 0 0 0 3.16 1.97
                    arcToRelative(
                        a = 3.5f,
                        b = 3.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 3.16f,
                        dy1 = 1.97f,
                    )
                    // a 5.3 5.3 0 0 0 2.46 -0.68
                    arcToRelative(
                        a = 5.3f,
                        b = 5.3f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 2.46f,
                        dy1 = -0.68f,
                    )
                    // a 4.8 4.8 0 0 0 2.35 -2.38
                    arcToRelative(
                        a = 4.8f,
                        b = 4.8f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 2.35f,
                        dy1 = -2.38f,
                    )
                    // a 3.4 3.4 0 0 0 -0.18 -2.92
                    arcToRelative(
                        a = 3.4f,
                        b = 3.4f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -0.18f,
                        dy1 = -2.92f,
                    )
                    // a 3.6 3.6 0 0 0 -3.19 -1.95
                    arcToRelative(
                        a = 3.6f,
                        b = 3.6f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -3.19f,
                        dy1 = -1.95f,
                    )
                    // m 24.58 -1.98
                    moveToRelative(dx = 24.58f, dy = -1.98f)
                    // a 5 5 0 0 0 -2.39 0.68
                    arcToRelative(
                        a = 5.0f,
                        b = 5.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -2.39f,
                        dy1 = 0.68f,
                    )
                    // a 4.8 4.8 0 0 0 -2.39 2.39
                    arcToRelative(
                        a = 4.8f,
                        b = 4.8f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -2.39f,
                        dy1 = 2.39f,
                    )
                    // a 3.3 3.3 0 0 0 0.18 2.9
                    arcToRelative(
                        a = 3.3f,
                        b = 3.3f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 0.18f,
                        dy1 = 2.9f,
                    )
                    // a 3.5 3.5 0 0 0 3.16 1.96
                    arcToRelative(
                        a = 3.5f,
                        b = 3.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 3.16f,
                        dy1 = 1.96f,
                    )
                    // q 1.16 0 2.46 -0.68
                    quadToRelative(
                        dx1 = 1.16f,
                        dy1 = 0.0f,
                        dx2 = 2.46f,
                        dy2 = -0.68f,
                    )
                    // a 4.8 4.8 0 0 0 2.35 -2.37
                    arcToRelative(
                        a = 4.8f,
                        b = 4.8f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 2.35f,
                        dy1 = -2.37f,
                    )
                    // a 3.4 3.4 0 0 0 -0.18 -2.93
                    arcToRelative(
                        a = 3.4f,
                        b = 3.4f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -0.18f,
                        dy1 = -2.93f,
                    )
                    // a 3.6 3.6 0 0 0 -3.19 -1.95
                    arcToRelative(
                        a = 3.6f,
                        b = 3.6f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -3.19f,
                        dy1 = -1.95f,
                    )
                }
            }.build()
            .also { _imagePlaceholder = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.ImagePlaceholder,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((147.0).dp)
                        .height((108.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _imagePlaceholder: ImageVector? = null
