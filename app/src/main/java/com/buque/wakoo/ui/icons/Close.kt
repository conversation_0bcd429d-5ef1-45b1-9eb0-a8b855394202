package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Close: ImageVector
    get() {
        val current = _close
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.Close",
                defaultWidth = 16.0.dp,
                defaultHeight = 16.0.dp,
                viewportWidth = 16.0f,
                viewportHeight = 16.0f,
            ).apply {
                // m7.06 8 -5.2 -5.2 .94 -.94 L8 7.06 l5.2 -5.2 .94 .94 L8.94 8 l5.2 5.2 -.94 .94 L8 8.94 l-5.2 5.2 -.94 -.95z
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 7.06 8
                    moveTo(x = 7.06f, y = 8.0f)
                    // l -5.2 -5.2
                    lineToRelative(dx = -5.2f, dy = -5.2f)
                    // l 0.94 -0.94
                    lineToRelative(dx = 0.94f, dy = -0.94f)
                    // L 8 7.06
                    lineTo(x = 8.0f, y = 7.06f)
                    // l 5.2 -5.2
                    lineToRelative(dx = 5.2f, dy = -5.2f)
                    // l 0.94 0.94
                    lineToRelative(dx = 0.94f, dy = 0.94f)
                    // L 8.94 8
                    lineTo(x = 8.94f, y = 8.0f)
                    // l 5.2 5.2
                    lineToRelative(dx = 5.2f, dy = 5.2f)
                    // l -0.94 0.94
                    lineToRelative(dx = -0.94f, dy = 0.94f)
                    // L 8 8.94
                    lineTo(x = 8.0f, y = 8.94f)
                    // l -5.2 5.2
                    lineToRelative(dx = -5.2f, dy = 5.2f)
                    // l -0.94 -0.95z
                    lineToRelative(dx = -0.94f, dy = -0.95f)
                    close()
                }
            }.build()
            .also { _close = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Close,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((16.0).dp)
                        .height((16.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _close: ImageVector? = null
