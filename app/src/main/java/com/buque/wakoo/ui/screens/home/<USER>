package com.buque.wakoo.ui.screens.home

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.IconButton
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.lerp
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.max
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.lerp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.zIndex
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LifecycleEventEffect
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.buque.wakoo.R
import com.buque.wakoo.app.Const
import com.buque.wakoo.app.DevicesKV
import com.buque.wakoo.bean.LocalSelfUserProvider
import com.buque.wakoo.bean.MyLiveRoomInfo
import com.buque.wakoo.ext.getIntOrNull
import com.buque.wakoo.navigation.VoiceListTab
import com.buque.wakoo.network.api.service.NotificationApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.icons.NotificationFill
import com.buque.wakoo.ui.icons.TabIndicator
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.voice.VoiceVerticalListPager
import com.buque.wakoo.ui.theme.WakooSelected
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooUnSelected
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.NoIndicationInteractionSource
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.media.manager.pauseIf
import com.buque.wakoo.ui.widget.media.manager.releaseIf
import com.buque.wakoo.ui.widget.rememberSelectionFractions
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch

private val TABS: List<VoiceListTab.ISquare> =
    listOf(
        VoiceListTab.Recommend,
        VoiceListTab.Follow,
    )

private const val PAGE_TAG_PREFIX = "square-tab-"

@Composable
fun SquareTabPage(
    onCreateLiveRoom: () -> Unit = {},
    onJoinMyLiveRoom: (MyLiveRoomInfo) -> Unit = {},
    toNotification: () -> Unit = {},
) {
    val scope = rememberCoroutineScope()
    val pagerState = rememberPagerState(pageCount = { TABS.size })
    val selectedTabIndex = pagerState.currentPage

    val selectionFractions by rememberSelectionFractions(pagerState)

    val tabWidths = remember { mutableStateMapOf<Int, Float>() }

    var notificationCount by rememberSaveable {
        mutableIntStateOf(0)
    }

    val coroutineScope = rememberCoroutineScope()
    LifecycleEventEffect(Lifecycle.Event.ON_START) {
        coroutineScope.launch {
            executeApiCallExpectingData {
                NotificationApiService.instance.getUnreadBadge()
            }.getOrNull()?.getIntOrNull("notification")?.also {
                notificationCount = it
            }
        }
    }

    val selfUser = LocalSelfUserProvider.current

    if (!LocalInspectionMode.current) {
        var firstEnter by remember {
            mutableStateOf(DevicesKV.getBoolean(Const.KVKey.FIRST_ENTER_VOICE_FEED, true))
        }

        if (firstEnter) {
            LaunchedEffect(Unit) {
                DevicesKV.putBoolean(Const.KVKey.FIRST_ENTER_VOICE_FEED, false)
                delay(5000)
                firstEnter = false
            }

            Dialog(onDismissRequest = {
                firstEnter = false
            }) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Image(
                        painter = painterResource(R.drawable.bg_voice_feed_guide),
                        contentDescription = null,
                        modifier = Modifier.height(223.dp),
                        contentScale = ContentScale.FillHeight,
                    )
                    SizeHeight(12.dp)
                    Text(text = "上下滑动可以切换声音", fontSize = 16.sp, color = WakooWhite)
                }
            }
        }
    }

    Column(
        modifier =
            Modifier
                .fillMaxSize()
                .statusBarsPadding(),
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            ScrollableTabRow(
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier.weight(1f),
                divider = {},
                edgePadding = 24.dp,
                containerColor = Color.Transparent,
                indicator = { tabPositions ->
                    if (selectedTabIndex < tabPositions.size) {
                        Box(
                            modifier =
                                Modifier
                                    .tabIndicatorOffset(tabPositions[selectedTabIndex])
                                    .fillMaxWidth(),
                        ) {
                            Image(
                                modifier =
                                    Modifier
                                        .size(
                                            width = 48.dp,
                                            height = 10.6.dp,
                                        )
                                        .offset(
                                            x = (-3).dp,
                                            y = 0.5.dp,
                                        ),
                                contentDescription = null,
                                imageVector = WakooIcons.TabIndicator,
                            )
                        }
                    }
                },
            ) {
                var extraPadding by remember {
                    mutableStateOf(0.dp)
                }

                TABS.forEachIndexed { index, tab ->
                    val fraction by remember {
                        derivedStateOf {
                            selectionFractions.getOrElse(index) { 1f }
                        }
                    }
                    var textWidth by remember {
                        mutableFloatStateOf(0f)
                    }
                    Tab(
                        selected = selectedTabIndex == index,
                        modifier =
                            Modifier
                                .zIndex(1f)
                                .graphicsLayer {
                                    if (index > 0) {
                                        val totalTranslation =
                                            run {
                                                var temp = 0f
                                                TABS.forEachIndexed { i, _ ->
                                                    if (i < index) {
                                                        val scale =
                                                            selectionFractions.getOrElse(i) { 1f } * 0.2f
                                                        temp += (tabWidths[i] ?: 0f) * scale
                                                    } else {
                                                        return@forEachIndexed
                                                    }
                                                }
                                                temp
                                            }
                                        translationX = totalTranslation
                                    }
                                },
                        selectedContentColor = WakooSelected,
                        unselectedContentColor = WakooUnSelected,
                        interactionSource = remember { NoIndicationInteractionSource() },
                        onClick = {
                            scope.launch {
                                pagerState.animateScrollToPage(index)
                            }
                        },
                        content = {
                            val animatedColor =
                                lerp(
                                    start = WakooUnSelected,
                                    stop = WakooSelected,
                                    fraction = fraction,
                                )
                            Box(
                                modifier =
                                    Modifier.padding(
                                        top = 5.dp,
                                        end = 20.dp,
                                    ),
                            ) {
                                tab.TabContent(
                                    selected = selectedTabIndex == index,
                                    modifier =
                                        Modifier
                                            .graphicsLayer {
                                                textWidth = size.width
                                                tabWidths[index] = textWidth
                                                extraPadding =
                                                    max(
                                                        extraPadding,
                                                        textWidth.toDp(),
                                                    )
                                                val scale =
                                                    lerp(
                                                        1f,
                                                        1.2f,
                                                        fraction,
                                                    )
                                                scaleX = scale
                                                scaleY = scale
                                                transformOrigin =
                                                    TransformOrigin(
                                                        0f,
                                                        1f,
                                                    )
                                            },
                                    color = animatedColor,
                                )
                            }
                        },
                    )
                }
                if (extraPadding.value > 0) {
                    SizeWidth(extraPadding)
                }
            }

            val myRoom = selfUser.roomExtra?.myLiveRoom
            OutlinedButton(
                text = if (myRoom == null) "创建电台" else "我的房间",
                onClick = {
                    if (myRoom == null) {
                        onCreateLiveRoom()
                    } else {
                        onJoinMyLiveRoom(myRoom)
                    }
                },
                height = 28.dp,
                minWidth = 64.dp,
                paddingValues = PaddingValues(horizontal = 8.dp),
                fontSize = 12.sp,
            )

            IconButton(onClick = toNotification) {
                BadgedBox(badge = {
                    if (notificationCount > 0) {
                        Badge {
                            Text(
                                text = notificationCount.toString(),
                                modifier =
                                    Modifier.semantics {
                                        contentDescription = "$notificationCount new notifications"
                                    },
                                fontSize = 10.sp,
                            )
                        }
                    }
                }) {
                    Image(
                        imageVector = WakooIcons.NotificationFill,
                        contentDescription = null,
                        modifier = Modifier.size(24.dp),
                    )
                }
            }

            SizeWidth(10.dp)
        }

        val lifecycleOwner = LocalLifecycleOwner.current

        DisposableEffect(Unit) {
            val observer =
                LifecycleEventObserver { source, event ->
                    if (event == Lifecycle.Event.ON_STOP) {
                        MediaPlayerManager.pauseIf {
                            it.tag.startsWith(PAGE_TAG_PREFIX)
                        }
                    }
                }

            lifecycleOwner.lifecycle.addObserver(observer)

            onDispose {
                // Composable 离开组合时执行清理
                MediaPlayerManager.releaseIf {
                    it.tag.startsWith(PAGE_TAG_PREFIX)
                }
                lifecycleOwner.lifecycle.removeObserver(observer)
            }
        }

        HorizontalPager(
            state = pagerState,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .weight(1f),
            beyondViewportPageCount = 1,
        ) { page ->
            val tab = TABS[page]
            LaunchedEffect(Unit) {
                snapshotFlow {
                    pagerState.currentPage != page
                }.filter { it }.collectLatest {
                    MediaPlayerManager.releaseIf {
                        it.tag.startsWith("$PAGE_TAG_PREFIX$tab")
                    }
                }
            }

            VoiceVerticalListPager(
                tagPrefix = PAGE_TAG_PREFIX,
                tab = tab,
                indexInParent = page,
                parentPagerState = pagerState,
            )
        }
    }
}

data class Post(
    val id: Int,
    val userName: String,
    val userAvatar: Int,
    val audioTitle: String,
    val audioDuration: String,
    val likes: Int,
    val stars: Int,
    val time: String,
)

@Preview(showBackground = true)
@Composable
private fun SquareTabPagePreview() {
    WakooTheme {
        SquareTabPage()
    }
}
