package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.Report: ImageVector
    get() {
        val current = _Report
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.Report",
                defaultWidth = 16.0.dp,
                defaultHeight = 16.0.dp,
                viewportWidth = 16.0f,
                viewportHeight = 16.0f,
            ).apply {
                path(
                    fill = SolidColor(Color(0xFF999999)),
                ) {
                    // Main body of the icon (outer shape)
                    moveTo(x = 2.66675f, y = 13.334f)
                    verticalLineTo(y = 9.33398f)
                    // This is an absolute A (Arc) command in SVG
                    arcTo(
                        horizontalEllipseRadius = 5.33333f, // 10.9456 - 8.00008 = 2.94552, then x 2, check. The SVG path has C and then an A, this is tricky.
                        verticalEllipseRadius = 5.33333f,
                        theta = 0f, // x-axis-rotation
                        isMoreThanHalf = false, // large-arc-flag (0 for < 180 deg)
                        isPositiveArc = true, // sweep-flag (1 for clockwise)
                        x1 = 13.3334f, // end X of the arc
                        y1 = 9.33398f, // end Y of the arc
                    )
                    verticalLineTo(y = 13.334f)
                    horizontalLineTo(x = 14.0001f)
                    verticalLineTo(y = 14.6673f)
                    horizontalLineTo(x = 2.00009f)
                    verticalLineTo(y = 13.334f)
                    horizontalLineTo(x = 2.66675f)
                    close()

                    // Inner rectangle of the main body
                    moveTo(x = 4.00009f, y = 13.334f)
                    horizontalLineTo(x = 12.0001f)
                    verticalLineTo(y = 9.33398f)
                    // This is another absolute A (Arc) command in SVG
                    arcTo(
                        horizontalEllipseRadius = 4.0f,
                        verticalEllipseRadius = 4.0f,
                        theta = 0f, // x-axis-rotation
                        isMoreThanHalf = false, // large-arc-flag (0 for < 180 deg)
                        isPositiveArc = false, // sweep-flag (0 for counter-clockwise)
                        x1 = 4.00009f, // end X of the arc
                        y1 = 9.33398f, // end Y of the arc
                    )
                    verticalLineTo(y = 13.334f)
                    close()

                    // Top vertical line
                    moveTo(x = 7.33341f, y = 1.33398f)
                    horizontalLineTo(x = 8.66675f)
                    verticalLineTo(y = 3.33398f)
                    horizontalLineTo(x = 7.33341f)
                    verticalLineTo(y = 1.33398f)
                    close()

                    // Top right diagonal line
                    moveTo(x = 13.1855f, y = 3.20572f)
                    lineTo(x = 14.1283f, y = 4.14854f)
                    lineTo(x = 12.7141f, y = 5.56275f)
                    lineTo(x = 11.7713f, y = 4.61994f)
                    lineTo(x = 13.1855f, y = 3.20572f)
                    close()

                    // Top left diagonal line
                    moveTo(x = 1.87183f, y = 4.14854f)
                    lineTo(x = 2.81463f, y = 3.20572f)
                    lineTo(x = 4.22885f, y = 4.61994f)
                    lineTo(x = 3.28604f, y = 5.56275f)
                    lineTo(x = 1.87183f, y = 4.14854f)
                    close()

                    // The problematic internal arc (left half) - this is actually two Cubic Beziers!
                    moveTo(x = 4.66675f, y = 9.33398f)
                    // C 4.66675 7.49305 6.15913 6.00065 8.00008 6.00065
                    curveTo(
                        x1 = 4.66675f,
                        y1 = 7.49305f,
                        x2 = 6.15913f,
                        y2 = 6.00065f,
                        x3 = 8.00008f,
                        y3 = 6.00065f,
                    )
                    verticalLineTo(y = 7.33398f)
                    // C 6.89555 7.33398 6.00009 8.22938 6.00009 9.33398
                    curveTo(
                        x1 = 6.89555f,
                        y1 = 7.33398f,
                        x2 = 6.00009f,
                        y2 = 8.22938f,
                        x3 = 6.00009f,
                        y3 = 9.33398f,
                    )
                    horizontalLineTo(x = 4.66675f)
                    close()
                }
            }.build()
            .also { _Report = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.Report,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((16.0).dp)
                        .height((16.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _Report: ImageVector? = null
