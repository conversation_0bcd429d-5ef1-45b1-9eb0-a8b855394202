package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.FollowBg: ImageVector
    get() {
        val current = _followBg
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.FollowBg",
                defaultWidth = 97.0.dp,
                defaultHeight = 62.0.dp,
                viewportWidth = 97.0f,
                viewportHeight = 62.0f,
            ).apply {
                // <rect width="90" height="56" rx="14.0" x="6.5" y="6.0" fill="#222" />
                path(
                    fill = SolidColor(Color(0xFF222222)),
                ) {
                    // M 6.5 20
                    moveTo(x = 6.5f, y = 20.0f)
                    // a 14 14 0 0 1 14 -14
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 14.0f,
                        dy1 = -14.0f,
                    )
                    // h 62
                    horizontalLineToRelative(dx = 62.0f)
                    // a 14 14 0 0 1 14 14
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 14.0f,
                        dy1 = 14.0f,
                    )
                    // v 28
                    verticalLineToRelative(dy = 28.0f)
                    // a 14 14 0 0 1 -14 14
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -14.0f,
                        dy1 = 14.0f,
                    )
                    // h -62
                    horizontalLineToRelative(dx = -62.0f)
                    // a 14 14 0 0 1 -14 -14z
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -14.0f,
                        dy1 = -14.0f,
                    )
                    close()
                }
                // M20.72 3 c-2.34 0 -4.34 2.06 -5.44 5.2 -1.1 -3.14 -3.1 -5.2 -5.44 -5.2 C6.3 3 3.5 7.78 3.5 13.88 s2.79 10.87 6.34 10.87 c2.34 0 4.34 -2.06 5.44 -5.2 1.1 3.14 3.1 5.2 5.44 5.2 3.56 0 6.34 -4.78 6.34 -10.87 C27.06 7.78 24.28 3 20.72 3 m-7.83 17.49 c-.85 1.55 -1.96 2.45 -3.05 2.45 s-2.2 -.9 -3.05 -2.45 a12 12 0 0 1 -1.16 -3.31 3.63 3.63 0 1 0 0 -6.6 A12 12 0 0 1 6.8 7.25 c.85 -1.55 1.96 -2.45 3.05 -2.45 1.1 0 2.2 .9 3.05 2.45 a14 14 0 0 1 1.48 6.62 14 14 0 0 1 -1.48 6.6 m10.88 0 c-.85 1.55 -1.96 2.45 -3.05 2.45 -1.1 0 -2.2 -.9 -3.05 -2.45 a12 12 0 0 1 -1.16 -3.31 3.63 3.63 0 1 0 0 -6.6 12 12 0 0 1 1.16 -3.32 c.85 -1.55 1.96 -2.45 3.05 -2.45 s2.2 .9 3.05 2.45 a14 14 0 0 1 1.48 6.62 14 14 0 0 1 -1.48 6.6
                path(
                    fill =
                        Brush.linearGradient(
                            0.0f to Color(0xFF90FF3F),
                            1.0f to Color(0xFF3BFF96),
                            start = Offset(x = 29.281f, y = 5.875f),
                            end = Offset(x = 1.281f, y = 24.375f),
                        ),
                ) {
                    // M 20.72 3
                    moveTo(x = 20.72f, y = 3.0f)
                    // c -2.34 0 -4.34 2.06 -5.44 5.2
                    curveToRelative(
                        dx1 = -2.34f,
                        dy1 = 0.0f,
                        dx2 = -4.34f,
                        dy2 = 2.06f,
                        dx3 = -5.44f,
                        dy3 = 5.2f,
                    )
                    // c -1.1 -3.14 -3.1 -5.2 -5.44 -5.2
                    curveToRelative(
                        dx1 = -1.1f,
                        dy1 = -3.14f,
                        dx2 = -3.1f,
                        dy2 = -5.2f,
                        dx3 = -5.44f,
                        dy3 = -5.2f,
                    )
                    // C 6.3 3 3.5 7.78 3.5 13.88
                    curveTo(
                        x1 = 6.3f,
                        y1 = 3.0f,
                        x2 = 3.5f,
                        y2 = 7.78f,
                        x3 = 3.5f,
                        y3 = 13.88f,
                    )
                    // s 2.79 10.87 6.34 10.87
                    reflectiveCurveToRelative(
                        dx1 = 2.79f,
                        dy1 = 10.87f,
                        dx2 = 6.34f,
                        dy2 = 10.87f,
                    )
                    // c 2.34 0 4.34 -2.06 5.44 -5.2
                    curveToRelative(
                        dx1 = 2.34f,
                        dy1 = 0.0f,
                        dx2 = 4.34f,
                        dy2 = -2.06f,
                        dx3 = 5.44f,
                        dy3 = -5.2f,
                    )
                    // c 1.1 3.14 3.1 5.2 5.44 5.2
                    curveToRelative(
                        dx1 = 1.1f,
                        dy1 = 3.14f,
                        dx2 = 3.1f,
                        dy2 = 5.2f,
                        dx3 = 5.44f,
                        dy3 = 5.2f,
                    )
                    // c 3.56 0 6.34 -4.78 6.34 -10.87
                    curveToRelative(
                        dx1 = 3.56f,
                        dy1 = 0.0f,
                        dx2 = 6.34f,
                        dy2 = -4.78f,
                        dx3 = 6.34f,
                        dy3 = -10.87f,
                    )
                    // C 27.06 7.78 24.28 3 20.72 3
                    curveTo(
                        x1 = 27.06f,
                        y1 = 7.78f,
                        x2 = 24.28f,
                        y2 = 3.0f,
                        x3 = 20.72f,
                        y3 = 3.0f,
                    )
                    // m -7.83 17.49
                    moveToRelative(dx = -7.83f, dy = 17.49f)
                    // c -0.85 1.55 -1.96 2.45 -3.05 2.45
                    curveToRelative(
                        dx1 = -0.85f,
                        dy1 = 1.55f,
                        dx2 = -1.96f,
                        dy2 = 2.45f,
                        dx3 = -3.05f,
                        dy3 = 2.45f,
                    )
                    // s -2.2 -0.9 -3.05 -2.45
                    reflectiveCurveToRelative(
                        dx1 = -2.2f,
                        dy1 = -0.9f,
                        dx2 = -3.05f,
                        dy2 = -2.45f,
                    )
                    // a 12 12 0 0 1 -1.16 -3.31
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.16f,
                        dy1 = -3.31f,
                    )
                    // a 3.63 3.63 0 1 0 0 -6.6
                    arcToRelative(
                        a = 3.63f,
                        b = 3.63f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        dx1 = 0.0f,
                        dy1 = -6.6f,
                    )
                    // A 12 12 0 0 1 6.8 7.25
                    arcTo(
                        horizontalEllipseRadius = 12.0f,
                        verticalEllipseRadius = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 6.8f,
                        y1 = 7.25f,
                    )
                    // c 0.85 -1.55 1.96 -2.45 3.05 -2.45
                    curveToRelative(
                        dx1 = 0.85f,
                        dy1 = -1.55f,
                        dx2 = 1.96f,
                        dy2 = -2.45f,
                        dx3 = 3.05f,
                        dy3 = -2.45f,
                    )
                    // c 1.1 0 2.2 0.9 3.05 2.45
                    curveToRelative(
                        dx1 = 1.1f,
                        dy1 = 0.0f,
                        dx2 = 2.2f,
                        dy2 = 0.9f,
                        dx3 = 3.05f,
                        dy3 = 2.45f,
                    )
                    // a 14 14 0 0 1 1.48 6.62
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.48f,
                        dy1 = 6.62f,
                    )
                    // a 14 14 0 0 1 -1.48 6.6
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.48f,
                        dy1 = 6.6f,
                    )
                    // m 10.88 0
                    moveToRelative(dx = 10.88f, dy = 0.0f)
                    // c -0.85 1.55 -1.96 2.45 -3.05 2.45
                    curveToRelative(
                        dx1 = -0.85f,
                        dy1 = 1.55f,
                        dx2 = -1.96f,
                        dy2 = 2.45f,
                        dx3 = -3.05f,
                        dy3 = 2.45f,
                    )
                    // c -1.1 0 -2.2 -0.9 -3.05 -2.45
                    curveToRelative(
                        dx1 = -1.1f,
                        dy1 = 0.0f,
                        dx2 = -2.2f,
                        dy2 = -0.9f,
                        dx3 = -3.05f,
                        dy3 = -2.45f,
                    )
                    // a 12 12 0 0 1 -1.16 -3.31
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.16f,
                        dy1 = -3.31f,
                    )
                    // a 3.63 3.63 0 1 0 0 -6.6
                    arcToRelative(
                        a = 3.63f,
                        b = 3.63f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        dx1 = 0.0f,
                        dy1 = -6.6f,
                    )
                    // a 12 12 0 0 1 1.16 -3.32
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.16f,
                        dy1 = -3.32f,
                    )
                    // c 0.85 -1.55 1.96 -2.45 3.05 -2.45
                    curveToRelative(
                        dx1 = 0.85f,
                        dy1 = -1.55f,
                        dx2 = 1.96f,
                        dy2 = -2.45f,
                        dx3 = 3.05f,
                        dy3 = -2.45f,
                    )
                    // s 2.2 0.9 3.05 2.45
                    reflectiveCurveToRelative(
                        dx1 = 2.2f,
                        dy1 = 0.9f,
                        dx2 = 3.05f,
                        dy2 = 2.45f,
                    )
                    // a 14 14 0 0 1 1.48 6.62
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.48f,
                        dy1 = 6.62f,
                    )
                    // a 14 14 0 0 1 -1.48 6.6
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.48f,
                        dy1 = 6.6f,
                    )
                }
                // M27.06 13.88 C27.06 7.78 24.28 3 20.72 3 c-2.34 0 -4.34 2.06 -5.44 5.2 -1.1 -3.14 -3.1 -5.2 -5.44 -5.2 C6.3 3 3.5 7.78 3.5 13.88 s2.79 10.87 6.34 10.87 c2.34 0 4.34 -2.06 5.44 -5.2 1.1 3.14 3.1 5.2 5.44 5.2 3.56 0 6.34 -4.78 6.34 -10.87 M9.84 4.8 c1.1 0 2.2 .9 3.05 2.45 a14 14 0 0 1 1.48 6.62 14 14 0 0 1 -1.48 6.6 c-.84 1.56 -1.96 2.46 -3.05 2.46 s-2.2 -.9 -3.05 -2.45 a12 12 0 0 1 -1.16 -3.31 3.6 3.6 0 0 0 3.27 -.14 l.2 -.12 a4 4 0 0 0 1.1 -1.12 l.11 -.2 a4 4 0 0 0 .44 -1.5 v-.22 a4 4 0 0 0 -.34 -1.54 l-.1 -.2 a4 4 0 0 0 -1.03 -1.18 l-.19 -.13 a3.6 3.6 0 0 0 -3.46 -.26 12 12 0 0 1 1.16 -3.3 C7.64 5.7 8.75 4.8 9.84 4.8 m11.79 9.07 a4 4 0 0 0 -.34 -1.54 l-.1 -.2 a4 4 0 0 0 -1.04 -1.18 l-.18 -.13 a3.6 3.6 0 0 0 -3.46 -.26 12 12 0 0 1 1.16 -3.3 c.85 -1.56 1.96 -2.46 3.05 -2.46 s2.2 .9 3.05 2.45 a14 14 0 0 1 1.48 6.62 14 14 0 0 1 -1.48 6.6 c-.85 1.56 -1.96 2.46 -3.05 2.46 -1.1 0 -2.2 -.9 -3.05 -2.45 a12 12 0 0 1 -1.16 -3.31 3.6 3.6 0 0 0 3.26 -.14 l.2 -.12 a4 4 0 0 0 1.1 -1.12 l.12 -.2 a4 4 0 0 0 .43 -1.5z m8.43 0 c0 3.46 -.78 6.73 -2.24 9.23 -1.44 2.46 -3.84 4.64 -7.1 4.64 -2.23 0 -4.04 -1 -5.44 -2.44 a7.4 7.4 0 0 1 -5.44 2.44 c-3.26 0 -5.66 -2.18 -7.1 -4.64 a18.6 18.6 0 0 1 -2.24 -9.23 c0 -3.47 .79 -6.74 2.25 -9.24 C4.18 2.18 6.58 0 9.85 0 c2.22 0 4.03 1 5.43 2.44 A7.4 7.4 0 0 1 20.72 0 c3.26 0 5.66 2.18 7.1 4.64 a18.6 18.6 0 0 1 2.24 9.24
                path(
                    fill = SolidColor(Color(0xFF222222)),
                ) {
                    // M 27.06 13.88
                    moveTo(x = 27.06f, y = 13.88f)
                    // C 27.06 7.78 24.28 3 20.72 3
                    curveTo(
                        x1 = 27.06f,
                        y1 = 7.78f,
                        x2 = 24.28f,
                        y2 = 3.0f,
                        x3 = 20.72f,
                        y3 = 3.0f,
                    )
                    // c -2.34 0 -4.34 2.06 -5.44 5.2
                    curveToRelative(
                        dx1 = -2.34f,
                        dy1 = 0.0f,
                        dx2 = -4.34f,
                        dy2 = 2.06f,
                        dx3 = -5.44f,
                        dy3 = 5.2f,
                    )
                    // c -1.1 -3.14 -3.1 -5.2 -5.44 -5.2
                    curveToRelative(
                        dx1 = -1.1f,
                        dy1 = -3.14f,
                        dx2 = -3.1f,
                        dy2 = -5.2f,
                        dx3 = -5.44f,
                        dy3 = -5.2f,
                    )
                    // C 6.3 3 3.5 7.78 3.5 13.88
                    curveTo(
                        x1 = 6.3f,
                        y1 = 3.0f,
                        x2 = 3.5f,
                        y2 = 7.78f,
                        x3 = 3.5f,
                        y3 = 13.88f,
                    )
                    // s 2.79 10.87 6.34 10.87
                    reflectiveCurveToRelative(
                        dx1 = 2.79f,
                        dy1 = 10.87f,
                        dx2 = 6.34f,
                        dy2 = 10.87f,
                    )
                    // c 2.34 0 4.34 -2.06 5.44 -5.2
                    curveToRelative(
                        dx1 = 2.34f,
                        dy1 = 0.0f,
                        dx2 = 4.34f,
                        dy2 = -2.06f,
                        dx3 = 5.44f,
                        dy3 = -5.2f,
                    )
                    // c 1.1 3.14 3.1 5.2 5.44 5.2
                    curveToRelative(
                        dx1 = 1.1f,
                        dy1 = 3.14f,
                        dx2 = 3.1f,
                        dy2 = 5.2f,
                        dx3 = 5.44f,
                        dy3 = 5.2f,
                    )
                    // c 3.56 0 6.34 -4.78 6.34 -10.87
                    curveToRelative(
                        dx1 = 3.56f,
                        dy1 = 0.0f,
                        dx2 = 6.34f,
                        dy2 = -4.78f,
                        dx3 = 6.34f,
                        dy3 = -10.87f,
                    )
                    // M 9.84 4.8
                    moveTo(x = 9.84f, y = 4.8f)
                    // c 1.1 0 2.2 0.9 3.05 2.45
                    curveToRelative(
                        dx1 = 1.1f,
                        dy1 = 0.0f,
                        dx2 = 2.2f,
                        dy2 = 0.9f,
                        dx3 = 3.05f,
                        dy3 = 2.45f,
                    )
                    // a 14 14 0 0 1 1.48 6.62
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.48f,
                        dy1 = 6.62f,
                    )
                    // a 14 14 0 0 1 -1.48 6.6
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.48f,
                        dy1 = 6.6f,
                    )
                    // c -0.84 1.56 -1.96 2.46 -3.05 2.46
                    curveToRelative(
                        dx1 = -0.84f,
                        dy1 = 1.56f,
                        dx2 = -1.96f,
                        dy2 = 2.46f,
                        dx3 = -3.05f,
                        dy3 = 2.46f,
                    )
                    // s -2.2 -0.9 -3.05 -2.45
                    reflectiveCurveToRelative(
                        dx1 = -2.2f,
                        dy1 = -0.9f,
                        dx2 = -3.05f,
                        dy2 = -2.45f,
                    )
                    // a 12 12 0 0 1 -1.16 -3.31
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.16f,
                        dy1 = -3.31f,
                    )
                    // a 3.6 3.6 0 0 0 3.27 -0.14
                    arcToRelative(
                        a = 3.6f,
                        b = 3.6f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 3.27f,
                        dy1 = -0.14f,
                    )
                    // l 0.2 -0.12
                    lineToRelative(dx = 0.2f, dy = -0.12f)
                    // a 4 4 0 0 0 1.1 -1.12
                    arcToRelative(
                        a = 4.0f,
                        b = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 1.1f,
                        dy1 = -1.12f,
                    )
                    // l 0.11 -0.2
                    lineToRelative(dx = 0.11f, dy = -0.2f)
                    // a 4 4 0 0 0 0.44 -1.5
                    arcToRelative(
                        a = 4.0f,
                        b = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 0.44f,
                        dy1 = -1.5f,
                    )
                    // v -0.22
                    verticalLineToRelative(dy = -0.22f)
                    // a 4 4 0 0 0 -0.34 -1.54
                    arcToRelative(
                        a = 4.0f,
                        b = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -0.34f,
                        dy1 = -1.54f,
                    )
                    // l -0.1 -0.2
                    lineToRelative(dx = -0.1f, dy = -0.2f)
                    // a 4 4 0 0 0 -1.03 -1.18
                    arcToRelative(
                        a = 4.0f,
                        b = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -1.03f,
                        dy1 = -1.18f,
                    )
                    // l -0.19 -0.13
                    lineToRelative(dx = -0.19f, dy = -0.13f)
                    // a 3.6 3.6 0 0 0 -3.46 -0.26
                    arcToRelative(
                        a = 3.6f,
                        b = 3.6f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -3.46f,
                        dy1 = -0.26f,
                    )
                    // a 12 12 0 0 1 1.16 -3.3
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.16f,
                        dy1 = -3.3f,
                    )
                    // C 7.64 5.7 8.75 4.8 9.84 4.8
                    curveTo(
                        x1 = 7.64f,
                        y1 = 5.7f,
                        x2 = 8.75f,
                        y2 = 4.8f,
                        x3 = 9.84f,
                        y3 = 4.8f,
                    )
                    // m 11.79 9.07
                    moveToRelative(dx = 11.79f, dy = 9.07f)
                    // a 4 4 0 0 0 -0.34 -1.54
                    arcToRelative(
                        a = 4.0f,
                        b = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -0.34f,
                        dy1 = -1.54f,
                    )
                    // l -0.1 -0.2
                    lineToRelative(dx = -0.1f, dy = -0.2f)
                    // a 4 4 0 0 0 -1.04 -1.18
                    arcToRelative(
                        a = 4.0f,
                        b = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -1.04f,
                        dy1 = -1.18f,
                    )
                    // l -0.18 -0.13
                    lineToRelative(dx = -0.18f, dy = -0.13f)
                    // a 3.6 3.6 0 0 0 -3.46 -0.26
                    arcToRelative(
                        a = 3.6f,
                        b = 3.6f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = -3.46f,
                        dy1 = -0.26f,
                    )
                    // a 12 12 0 0 1 1.16 -3.3
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.16f,
                        dy1 = -3.3f,
                    )
                    // c 0.85 -1.56 1.96 -2.46 3.05 -2.46
                    curveToRelative(
                        dx1 = 0.85f,
                        dy1 = -1.56f,
                        dx2 = 1.96f,
                        dy2 = -2.46f,
                        dx3 = 3.05f,
                        dy3 = -2.46f,
                    )
                    // s 2.2 0.9 3.05 2.45
                    reflectiveCurveToRelative(
                        dx1 = 2.2f,
                        dy1 = 0.9f,
                        dx2 = 3.05f,
                        dy2 = 2.45f,
                    )
                    // a 14 14 0 0 1 1.48 6.62
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.48f,
                        dy1 = 6.62f,
                    )
                    // a 14 14 0 0 1 -1.48 6.6
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.48f,
                        dy1 = 6.6f,
                    )
                    // c -0.85 1.56 -1.96 2.46 -3.05 2.46
                    curveToRelative(
                        dx1 = -0.85f,
                        dy1 = 1.56f,
                        dx2 = -1.96f,
                        dy2 = 2.46f,
                        dx3 = -3.05f,
                        dy3 = 2.46f,
                    )
                    // c -1.1 0 -2.2 -0.9 -3.05 -2.45
                    curveToRelative(
                        dx1 = -1.1f,
                        dy1 = 0.0f,
                        dx2 = -2.2f,
                        dy2 = -0.9f,
                        dx3 = -3.05f,
                        dy3 = -2.45f,
                    )
                    // a 12 12 0 0 1 -1.16 -3.31
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.16f,
                        dy1 = -3.31f,
                    )
                    // a 3.6 3.6 0 0 0 3.26 -0.14
                    arcToRelative(
                        a = 3.6f,
                        b = 3.6f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 3.26f,
                        dy1 = -0.14f,
                    )
                    // l 0.2 -0.12
                    lineToRelative(dx = 0.2f, dy = -0.12f)
                    // a 4 4 0 0 0 1.1 -1.12
                    arcToRelative(
                        a = 4.0f,
                        b = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 1.1f,
                        dy1 = -1.12f,
                    )
                    // l 0.12 -0.2
                    lineToRelative(dx = 0.12f, dy = -0.2f)
                    // a 4 4 0 0 0 0.43 -1.5z
                    arcToRelative(
                        a = 4.0f,
                        b = 4.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = false,
                        dx1 = 0.43f,
                        dy1 = -1.5f,
                    )
                    close()
                    // m 8.43 0
                    moveToRelative(dx = 8.43f, dy = 0.0f)
                    // c 0 3.46 -0.78 6.73 -2.24 9.23
                    curveToRelative(
                        dx1 = 0.0f,
                        dy1 = 3.46f,
                        dx2 = -0.78f,
                        dy2 = 6.73f,
                        dx3 = -2.24f,
                        dy3 = 9.23f,
                    )
                    // c -1.44 2.46 -3.84 4.64 -7.1 4.64
                    curveToRelative(
                        dx1 = -1.44f,
                        dy1 = 2.46f,
                        dx2 = -3.84f,
                        dy2 = 4.64f,
                        dx3 = -7.1f,
                        dy3 = 4.64f,
                    )
                    // c -2.23 0 -4.04 -1 -5.44 -2.44
                    curveToRelative(
                        dx1 = -2.23f,
                        dy1 = 0.0f,
                        dx2 = -4.04f,
                        dy2 = -1.0f,
                        dx3 = -5.44f,
                        dy3 = -2.44f,
                    )
                    // a 7.4 7.4 0 0 1 -5.44 2.44
                    arcToRelative(
                        a = 7.4f,
                        b = 7.4f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -5.44f,
                        dy1 = 2.44f,
                    )
                    // c -3.26 0 -5.66 -2.18 -7.1 -4.64
                    curveToRelative(
                        dx1 = -3.26f,
                        dy1 = 0.0f,
                        dx2 = -5.66f,
                        dy2 = -2.18f,
                        dx3 = -7.1f,
                        dy3 = -4.64f,
                    )
                    // a 18.6 18.6 0 0 1 -2.24 -9.23
                    arcToRelative(
                        a = 18.6f,
                        b = 18.6f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -2.24f,
                        dy1 = -9.23f,
                    )
                    // c 0 -3.47 0.79 -6.74 2.25 -9.24
                    curveToRelative(
                        dx1 = 0.0f,
                        dy1 = -3.47f,
                        dx2 = 0.79f,
                        dy2 = -6.74f,
                        dx3 = 2.25f,
                        dy3 = -9.24f,
                    )
                    // C 4.18 2.18 6.58 0 9.85 0
                    curveTo(
                        x1 = 4.18f,
                        y1 = 2.18f,
                        x2 = 6.58f,
                        y2 = 0.0f,
                        x3 = 9.85f,
                        y3 = 0.0f,
                    )
                    // c 2.22 0 4.03 1 5.43 2.44
                    curveToRelative(
                        dx1 = 2.22f,
                        dy1 = 0.0f,
                        dx2 = 4.03f,
                        dy2 = 1.0f,
                        dx3 = 5.43f,
                        dy3 = 2.44f,
                    )
                    // A 7.4 7.4 0 0 1 20.72 0
                    arcTo(
                        horizontalEllipseRadius = 7.4f,
                        verticalEllipseRadius = 7.4f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 20.72f,
                        y1 = 0.0f,
                    )
                    // c 3.26 0 5.66 2.18 7.1 4.64
                    curveToRelative(
                        dx1 = 3.26f,
                        dy1 = 0.0f,
                        dx2 = 5.66f,
                        dy2 = 2.18f,
                        dx3 = 7.1f,
                        dy3 = 4.64f,
                    )
                    // a 18.6 18.6 0 0 1 2.24 9.24
                    arcToRelative(
                        a = 18.6f,
                        b = 18.6f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 2.24f,
                        dy1 = 9.24f,
                    )
                }
            }.build()
            .also { _followBg = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.FollowBg,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((97.0).dp)
                        .height((62.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _followBg: ImageVector? = null
