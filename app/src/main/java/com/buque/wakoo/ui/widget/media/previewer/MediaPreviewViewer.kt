package com.buque.wakoo.ui.widget.media.previewer

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.SnapSpec
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.surfaceColorAtElevation
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableFloatState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.buque.wakoo.ext.animatableClipToScreenRect
import com.buque.wakoo.ui.widget.media.previewer.flick.FlickToDismiss
import com.buque.wakoo.ui.widget.media.previewer.flick.FlickToDismissState
import com.buque.wakoo.ui.widget.media.previewer.flick.rememberFlickToDismissState
import me.saket.telephoto.ExperimentalTelephotoApi
import me.saket.telephoto.zoomable.coil3.ZoomableAsyncImage
import me.saket.telephoto.zoomable.rememberZoomableImageState
import me.saket.telephoto.zoomable.rememberZoomableState
import kotlin.math.abs

@Composable
@OptIn(ExperimentalMaterial3Api::class)
internal fun MediaPreviewViewer(
    state: MediaPreviewState,
    overlayClipRect: Rect?,
    modifier: Modifier = Modifier,
) {
    val currentState = state.transitionState as TransitionState.Transiting
    val key = currentState.key
    Box(
        modifier = modifier.fillMaxSize(),
    ) {
        val pagerState =
            rememberPagerState(
                initialPage = key.initialIndex,
                pageCount = { key.album.items.size },
            )

        state.currentPageIndex = { pagerState.currentPage }

        val titleAlphaState =
            remember {
                mutableFloatStateOf(1f)
            }

        val clipped by remember {
            derivedStateOf {
                val currentState = state.transitionState
                currentState is TransitionState.Enter && currentState.anim < AnimState.Finished
            }
        }

        val backgroundAlphaState =
            remember {
                mutableFloatStateOf(0f)
            }

        val animatedAlpha by animateFloatAsState(
            targetValue = backgroundAlphaState.floatValue,
            animationSpec =
                if (currentState is TransitionState.Enter && currentState.anim == AnimState.End) {
                    spring()
                } else {
                    tween(250)
                },
            label = "Background alpha",
        )

        HorizontalPager(
            modifier =
                Modifier
                    .fillMaxSize()
                    .background(
                        MaterialTheme.colorScheme
                            .surfaceColorAtElevation(2.dp)
                            .copy(alpha = animatedAlpha),
                    ).run {
                        if (overlayClipRect != null) {
                            animatableClipToScreenRect(clipped, overlayClipRect)
                        } else {
                            this
                        }
                    },
            state = pagerState,
            beyondViewportPageCount = 1,
            userScrollEnabled = currentState is TransitionState.Enter && currentState.anim == AnimState.End,
        ) { pageNum ->
            MediaPage(
                state = state,
                titleAlphaState = titleAlphaState,
                backgroundAlphaState = backgroundAlphaState,
                modifier = Modifier.fillMaxSize(),
                model = key.album.items[pageNum],
                isActivePage = pagerState.settledPage == pageNum,
            )
        }

        var visibleState by remember {
            mutableStateOf(false)
        }

        LaunchedEffect(Unit) {
            snapshotFlow {
                val currentState = state.transitionState
                currentState is TransitionState.Enter && currentState.anim >= AnimState.Running
            }.collect {
                visibleState = it
            }
        }

        AnimatedVisibility(
            visible = visibleState,
            enter = fadeIn() + slideInVertically(),
            exit = fadeOut() + slideOutVertically(),
        ) {
            CenterAlignedTopAppBar(
                modifier = Modifier.alpha(titleAlphaState.floatValue),
                title = {
                    Box(
                        modifier =
                            Modifier
                                .background(MaterialTheme.colorScheme.background.copy(alpha = 0.4f), CircleShape)
                                .padding(horizontal = 10.dp, vertical = 6.dp),
                    ) {
                        Text(
                            text = "${pagerState.currentPage + 1}/${key.album.items.size}",
                            style = MaterialTheme.typography.titleMedium,
                        )
                    }
                },
                navigationIcon = { CloseNavIconButton() },
                colors = TopAppBarDefaults.topAppBarColors(containerColor = Color.Transparent),
            )
        }
    }
}

@Composable
private fun CloseNavIconButton() {
    val backDispatcher = LocalOnBackPressedDispatcherOwner.current!!.onBackPressedDispatcher
    IconButton(
        onClick = { backDispatcher.onBackPressed() },
        colors = titleBarIconButtonColors(),
    ) {
        Icon(Icons.Rounded.Close, contentDescription = "Go back")
    }
}

@Composable
private fun titleBarIconButtonColors() =
    IconButtonDefaults.iconButtonColors(
        containerColor = MaterialTheme.colorScheme.background.copy(alpha = 0.4f),
    )

@Composable
@OptIn(ExperimentalTelephotoApi::class)
private fun MediaPage(
    state: MediaPreviewState,
    titleAlphaState: MutableFloatState,
    backgroundAlphaState: MutableFloatState,
    model: MediaItem,
    isActivePage: Boolean,
    modifier: Modifier = Modifier,
) {
    val zoomableState = rememberZoomableState()

    val flickState = rememberFlickToDismissState(rotateOnDrag = false)

    if (isActivePage) {
        state.currentPageOffset = { flickState.offset }
    }

    CloseScreenOnFlickDismissEffect(flickState)

    LaunchedEffect(flickState) {
        snapshotFlow {
            flickState.gestureState
        }.collect {
            titleAlphaState.floatValue = if (it is FlickToDismissState.GestureState.Dragging) 0f else 1f
        }
    }

    LaunchedEffect(flickState) {
        snapshotFlow {
            val currentState = state.transitionState
            if (currentState is TransitionState.Enter) {
                if (currentState.anim == AnimState.End) {
                    val flickGestureState = flickState.gestureState
                    when (flickGestureState) {
                        is FlickToDismissState.GestureState.Dismissed,
                        is FlickToDismissState.GestureState.Dismissing,
                        -> 0f

                        is FlickToDismissState.GestureState.Dragging ->
                            if (flickGestureState.willDismissOnRelease) {
                                0f
                            } else {
                                1f - (abs(flickGestureState.offsetFraction) / 0.2f)
                            }

                        is FlickToDismissState.GestureState.Idle,
                        is FlickToDismissState.GestureState.Resetting,
                        -> 1f
                    }
                } else if (currentState.anim == AnimState.Ready) {
                    0f
                } else {
                    1f
                }
            } else {
                0f
            }
        }.collect {
            backgroundAlphaState.floatValue = it
        }
    }

    val currentState = state.transitionState as TransitionState.Transiting

    FlickToDismiss(
        state = flickState,
        modifier = modifier,
    ) {
        Box(
            modifier =
                Modifier.alpha(
                    if ((currentState is TransitionState.Enter && currentState.anim >= AnimState.Finished) ||
                        (currentState is TransitionState.Exit && currentState.anim == AnimState.Ready)
                    ) {
                        1f
                    } else {
                        0f
                    },
                ),
        ) {
            when (model) {
                is MediaItem.Image -> {
                    val imageState = rememberZoomableImageState(zoomableState)
                    ZoomableAsyncImage(
                        modifier = Modifier.fillMaxSize(),
                        state = imageState,
                        model =
                            ImageRequest
                                .Builder(LocalContext.current)
                                .data(model.fullSizedUrl)
                                .placeholderMemoryCacheKey(model.placeholderImageUrl)
                                .crossfade(300)
                                .build(),
                        contentDescription = null,
                    )

                    AnimatedVisibility(
                        modifier = Modifier.align(Alignment.Center),
                        visible = !imageState.isImageDisplayed,
                    ) {
                        CircularProgressIndicator(color = Color.White)
                    }
                }
            }
        }
    }

    if (flickState.gestureState is FlickToDismissState.GestureState.Dragging) {
        LaunchedEffect(Unit) {
            zoomableState.resetZoom()
        }
    }
    if (!isActivePage) {
        LaunchedEffect(Unit) {
            zoomableState.resetZoom(animationSpec = SnapSpec())
        }
    }
}

@Composable
private fun CloseScreenOnFlickDismissEffect(flickState: FlickToDismissState) {
    val gestureState = flickState.gestureState

    if (gestureState is FlickToDismissState.GestureState.Dismissing) {
        val backDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher
        LaunchedEffect(Unit) {
            backDispatcher?.onBackPressed()
        }
    }
}
