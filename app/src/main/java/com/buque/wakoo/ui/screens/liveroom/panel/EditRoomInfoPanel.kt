package com.buque.wakoo.ui.screens.liveroom.panel

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.mutableStateSetOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.bean.LiveRoomEditInfo
import com.buque.wakoo.bean.LocalSelfUserProvider
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.dialog.BottomPanelScaffold
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.ImeButtonScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.VoiceTagChip
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.utils.preload.LiveRoomPreload

@Composable
fun DialogScope.EditRoomInfoPanel(
    title: String,
    buttonText: String,
    modifier: Modifier = Modifier,
    info: LiveRoomEditInfo? = null,
    onConfirm: (LiveRoomEditInfo) -> Unit,
) {
    var inputTitle by rememberSaveable { mutableStateOf(info?.title.orEmpty()) }

    var inputNotice by rememberSaveable { mutableStateOf(info?.desc.orEmpty()) }

    val selectedTagIds =
        rememberSaveable {
            mutableStateSetOf<Int>().also {
                if (info != null) {
                    it.addAll(info.tagIds)
                }
            }
        }

    if (info == null) {
        if (LocalSelfUserProvider.current.roomExtra?.myLiveRoom != null) {
            LaunchedEffect(Unit) {
                dismiss()
                showToast("您已创建直播间")
            }
        }
    }

    BottomPanelScaffold(
        title = title,
        useClose = true,
        modifier =
            modifier
                .windowInsetsPadding(WindowInsets.ime)
                .fillMaxWidth()
                .fillMaxHeight(0.64f),
        backgroundColor = WakooWhite,
    ) {
        val tags by LiveRoomPreload.getTagsFlow(true).collectAsStateWithLifecycle()
        ImeButtonScaffold(
            modifier = Modifier.fillMaxSize(),
            buttonModifier =
                Modifier
                    .fillMaxWidth()
                    .background(WakooWhite)
                    .padding(
                        top = 20.dp,
                        bottom = 24.dp,
                    ),
            buttonContent = {
                val nextEnable by remember {
                    derivedStateOf {
                        inputTitle.isNotBlank() && inputNotice.isNotBlank() && selectedTagIds.isNotEmpty()
                    }
                }

                GradientButton(
                    text = buttonText,
                    onClick = {
                        onConfirm(
                            LiveRoomEditInfo(inputTitle, inputNotice, selectedTagIds.toList()),
                        )
                    },
                    modifier =
                        Modifier
                            .padding(horizontal = 12.dp)
                            .fillMaxWidth(),
                    enabled = nextEnable,
                )
            },
        ) {
            CStateLayout(tags, useScrollableLayout = false, onRetry = {
                LiveRoomPreload.refreshTags(false)
            }) { data ->
                Column(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    Text(
                        text = "直播间标题:",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color(0xFF111111),
                    )

                    SizeHeight(8.dp)

                    val focusManager = LocalFocusManager.current

                    AppTextField(
                        value = inputTitle,
                        onValueChange = {
                            inputTitle = it
                        },
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(40.dp),
                        placeholder = "请输入直播标题",
                        maxLength = 20,
                        singleLine = true,
                        backgroundColor = Color(0xFFE9EAEF),
                        supportingStyle =
                            MaterialTheme.typography.bodyMedium.copy(
                                color = Color(0xFFB6B6B6),
                            ),
                        contentPadding = PaddingValues(start = 12.dp, top = 10.dp, end = 60.dp, bottom = 10.dp),
                        keyboardOptions =
                            KeyboardOptions(
                                imeAction = ImeAction.Next,
                            ),
                        keyboardActions =
                            KeyboardActions(
                                onNext = {
                                    focusManager.moveFocus(FocusDirection.Down)
                                },
                            ),
                    )

                    SizeHeight(16.dp)

                    Text(
                        text = "直播间介绍:",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color(0xFF111111),
                    )

                    SizeHeight(8.dp)

                    AppTextField(
                        value = inputNotice,
                        onValueChange = {
                            inputNotice = it
                        },
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(112.dp),
                        placeholder = "请输入直播间介绍",
                        maxLength = 80,
                        backgroundColor = Color(0xFFE9EAEF),
                        supportingStyle =
                            MaterialTheme.typography.bodyMedium.copy(
                                color = Color(0xFFB6B6B6),
                            ),
                    )

                    SizeHeight(16.dp)

                    Text(
                        text = "直播间标签:",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color(0xFF111111),
                    )

                    SizeHeight(8.dp)

                    FlowRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp),
                    ) {
                        data.forEach {
                            VoiceTagChip(selectedTagIds.contains(it.id), it) {
                                if (selectedTagIds.contains(it.id)) {
                                    selectedTagIds.remove(it.id)
                                } else {
                                    selectedTagIds.add(it.id)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
