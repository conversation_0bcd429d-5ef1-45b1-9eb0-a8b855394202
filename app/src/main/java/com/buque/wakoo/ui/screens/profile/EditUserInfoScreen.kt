package com.buque.wakoo.ui.screens.profile

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.LocalSelfUserProvider
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.easyPostBottomPanel
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.navigation.rememberLauncherForResult
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.screens.login.Gender
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SingleTextSettingsMenuItem
import com.buque.wakoo.ui.widget.TowTextsSettingsMenuItem
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.media.data.common.MediaItem
import com.buque.wakoo.ui.widget.wheelPicker.AppDateWheelPickerPanel
import com.buque.wakoo.ui.widget.wheelPicker.WheelPicker
import com.buque.wakoo.ui.widget.wheelPicker.WheelPickerPanelScaffold
import com.buque.wakoo.ui.widget.wheelPicker.rememberWheelPickerState
import com.buque.wakoo.viewmodel.EditUserInfoViewModel
import kotlinx.datetime.Clock
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.minus
import kotlinx.datetime.todayIn

@Composable
fun EditUserInfoScreen(onNicknameClick: () -> Unit = {}) {
    val user = LocalSelfUserProvider.current

    val viewModel = viewModel<EditUserInfoViewModel>()

    val scope = rememberCoroutineScope()

    val loading = LocalLoadingManager.current

    val rootNavController = LocalAppNavController.root

    val dialogController = rememberDialogController(render = false)

    val launcher =
        rootNavController.rememberLauncherForResult<Route.MediaSelector, List<MediaItem>> { result ->
            if (result.isNotEmpty()) {
                loading.show(scope) {
                    viewModel.updateAvatar(result.single().uriString.toUri())
                }
            }
        }

    SegColorTitleScreenScaffold(
        title = "编辑资料",
    ) { paddingValues ->
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
        ) {
            SingleTextSettingsMenuItem(
                titleText = "头像",
                previewContent = {
                    AvatarNetworkImage(
                        user = user,
                        size = 48.dp,
                        enabled = false,
                    )
                },
                onClick = {
                    launcher.launch(Route.MediaSelector())
                },
            )

            HorizontalDivider(
                color = Color(0xFFE5E5E5),
                thickness = 0.5.dp,
                modifier = Modifier.padding(horizontal = 16.dp),
            )

            TowTextsSettingsMenuItem(
                titleText = "昵称",
                previewText = user.name,
                onClick = onNicknameClick,
            )

            HorizontalDivider(
                color = Color(0xFFE5E5E5),
                thickness = 0.5.dp,
                modifier = Modifier.padding(horizontal = 16.dp),
            )

            TowTextsSettingsMenuItem(
                titleText = "生日",
                previewText = user.birthday.ifEmpty { "请选择" },
                onClick = {
                    dialogController.easyPostBottomPanel(
                        useSystemDialog = false,
                    ) {
                        val initSelectedDate =
                            user.birthday.takeIf { it.isNotEmpty() }?.let {
                                LocalDate.parse(it)
                            } ?: Clock.System
                                .todayIn(TimeZone.currentSystemDefault())
                                .minus(18, DateTimeUnit.YEAR)
                        AppDateWheelPickerPanel(
                            title = "选择生日",
                            initSelectedDate = initSelectedDate,
                            onConfirm = { changed, date ->
                                if (changed) {
                                    viewModel.updateBirthday(date.toString())
                                }
                            },
                        )
                    }
                },
            )

            HorizontalDivider(
                color = Color(0xFFE5E5E5),
                thickness = 0.5.dp,
                modifier = Modifier.padding(horizontal = 16.dp),
            )

            AnimatedVisibility(!user.genderIsSet) {
                Column {
                    TowTextsSettingsMenuItem(
                        titleText = "性别",
                        previewText = user.displayGender,
                        onClick = {
                            dialogController.easyPostBottomPanel(
                                useSystemDialog = false,
                            ) {
                                val state =
                                    rememberWheelPickerState(
                                        initialIndex = 0,
                                        itemCount = Gender.entries.size,
                                    )

                                WheelPickerPanelScaffold(
                                    title = "选择性别",
                                    onDismissRequest = {
                                        dismiss()
                                    },
                                    onConfirm = {
                                        Gender.entries
                                            .getOrNull(state.snappedIndex)
                                            ?.toIntVale()
                                            ?.takeIf {
                                                it > 0
                                            }?.also {
                                                viewModel.updateGender(it)
                                            }
                                        dismiss()
                                    },
                                ) {
                                    WheelPicker(
                                        items = Gender.entries,
                                        state = state,
                                        visibleItemsCount = Gender.entries.size.coerceAtMost(5),
                                    )
                                }
                            }
                        },
                    )

                    HorizontalDivider(
                        color = Color(0xFFE5E5E5),
                        thickness = 0.5.dp,
                        modifier = Modifier.padding(horizontal = 16.dp),
                    )
                }
            }
        }
    }

    dialogController.RenderDialogs(Unit)
}

@Preview(showBackground = true)
@Composable
private fun EditProfileScreenPreview() {
    WakooTheme {
        EditUserInfoScreen()
    }
}
