package com.buque.wakoo.ui.widget.drag

import androidx.compose.animation.core.spring
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.toSize
import com.buque.wakoo.ui.widget.drag.DraggableFloatingState.Companion.DraggableFloatingStateSaver
import com.buque.wakoo.ui.widget.drag.FloatingLayoutManagerState.Companion.FloatingLayoutManagerStateSaver
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Stable
class FloatingLayoutManagerState(
    var scope: CoroutineScope,
    var collisionSpacing: Float,
) {
    companion object {
        val FloatingLayoutManagerStateSaver =
            Saver<FloatingLayoutManagerState, Any>(
                save = { managerState ->
                    // 我们只保存每个子 state 的 saved list，不保存 manager 自己的状态
                    managerState.states.map {
                        with(DraggableFloatingStateSaver) {
                            save(it)
                        }
                    }
                },
                restore = { savedList ->
                    val restoredStates =
                        (savedList as List<List<Any?>>).mapNotNull {
                            DraggableFloatingStateSaver.restore(it)
                        }
                    // 创建一个新的 manager state，并填入恢复的子 states
                    FloatingLayoutManagerState(
                        scope = CoroutineScope(Dispatchers.Main.immediate), // 临时 scope
                        collisionSpacing = 0f, // 这个值会在 remember 中被重设
                    ).apply {
                        states.addAll(restoredStates)
                    }
                },
            )
    }

    internal val states = mutableStateListOf<DraggableFloatingState>()
    var parentSize by mutableStateOf(IntSize.Zero)

    fun onDragEnd(draggedState: DraggableFloatingState) {
        resolveCollisions(draggedState)
    }

    fun onVisibilityChanged(state: DraggableFloatingState) {
        if (state.isVisible) {
            resolveCollisions(state)
        }
    }

    private fun resolveCollisions(activeState: DraggableFloatingState) {
        scope.launch {
            var targetOffset = activeState.getFinalTargetOffset()
            val otherVisibleStates = states.filter { it != activeState && it.isVisible }
            val otherTargetRects =
                otherVisibleStates.map {
                    Rect(it.getFinalTargetOffset(), it.componentSize.toSize())
                }

            val isOverlapping =
                otherTargetRects.any { otherRect ->
                    val activeRect = Rect(targetOffset, activeState.componentSize.toSize())
                    activeRect.inflate(collisionSpacing).overlaps(otherRect)
                }

            if (isOverlapping) {
                // *** 核心修正：调用新的、带约束的 findAvailablePosition ***
                val newPosition =
                    findAvailablePosition(
                        activeState = activeState,
                        initialTarget = targetOffset,
                        otherTargetRects = otherTargetRects,
                    )

                if (newPosition != null) {
                    targetOffset = newPosition
                }
            }

            val finalCoercedOffset = activeState.getCoercedOffset(targetOffset)
            activeState.offset.animateTo(finalCoercedOffset, spring())
        }
    }

    /**
     * 寻找可用位置的全新实现，能够感知贴边状态。
     */
    private fun findAvailablePosition(
        activeState: DraggableFloatingState,
        initialTarget: Offset,
        otherTargetRects: List<Rect>,
    ): Offset? {
        // --- 情况一：如果组件需要贴边 ---
        if (activeState.isStickyToEdge) {
            // 我们只在垂直方向上搜索，水平方向（X坐标）是固定的
            val targetX = initialTarget.x // 保持贴边的X坐标不变

            // 搜索策略：从当前Y坐标开始，交替向上和向下搜索
            val searchStep = 10f // 每次移动10个像素
            for (i in 0..100) { // 限制搜索次数，防止无限循环
                val dy = i * searchStep

                // 1. 尝试向下移动
                val downwardPos = activeState.getCoercedOffset(Offset(targetX, initialTarget.y + dy))
                if (isPositionAvailable(downwardPos, activeState.componentSize.toSize(), otherTargetRects)) {
                    return downwardPos
                }

                // 2. 尝试向上移动
                val upwardPos = activeState.getCoercedOffset(Offset(targetX, initialTarget.y - dy))
                if (isPositionAvailable(upwardPos, activeState.componentSize.toSize(), otherTargetRects)) {
                    return upwardPos
                }
            }
        } else {
            // 使用我们之前的“螺旋搜索”策略，因为它适用于自由浮动的组件
            val componentSize = activeState.componentSize.toSize()
            val step = (collisionSpacing + 20).toInt()
            val maxRadius = (parentSize.width + parentSize.height) / 2

            if (isPositionAvailable(initialTarget, componentSize, otherTargetRects)) {
                return initialTarget
            }

            for (radius in step..maxRadius step step) {
                for (angle in 0 until 360 step 15) {
                    val rad = Math.toRadians(angle.toDouble())
                    val testX = initialTarget.x + (radius * kotlin.math.cos(rad)).toFloat()
                    val testY = initialTarget.y + (radius * kotlin.math.sin(rad)).toFloat()
                    val testPosition = activeState.getCoercedOffset(Offset(testX, testY))

                    if (isPositionAvailable(testPosition, componentSize, otherTargetRects)) {
                        return testPosition
                    }
                }
            }
        }

        // 如果所有策略都失败，返回 null
        return null
    }

    private fun isPositionAvailable(
        position: Offset,
        size: Size,
        otherTargetRects: List<Rect>,
    ): Boolean {
        val testRect = Rect(position, size)
        if (testRect.left < 0f || testRect.top < 0f || testRect.right > parentSize.width || testRect.bottom > parentSize.height) {
            return false
        }
        return !otherTargetRects.any { otherRect ->
            testRect.inflate(collisionSpacing).overlaps(otherRect)
        }
    }
}

@Composable
fun rememberFloatingLayoutManagerState(collisionSpacing: Dp = 10.dp): FloatingLayoutManagerState {
    val scope = rememberCoroutineScope()
    val collisionSpacingPx = with(LocalDensity.current) { collisionSpacing.toPx() }

    val state =
        rememberSaveable(saver = FloatingLayoutManagerStateSaver) {
            FloatingLayoutManagerState(scope, collisionSpacingPx)
        }

    // 恢复后，注入最新的 scope 和参数
    LaunchedEffect(scope, collisionSpacingPx, state) {
        state.scope = scope
        state.collisionSpacing = collisionSpacingPx
    }

    return state
}

interface FloatingLayoutScope {
    @Composable
    fun DraggableItem(
        state: DraggableFloatingState,
        modifier: Modifier = Modifier,
        content: @Composable () -> Unit,
    )
}

private class FloatingLayoutScopeImpl(
    private val managerState: FloatingLayoutManagerState,
) : FloatingLayoutScope {
    @Composable
    override fun DraggableItem(
        state: DraggableFloatingState,
        modifier: Modifier,
        content: @Composable () -> Unit,
    ) {
        DisposableEffect(state) {
            managerState.states.add(state)
            onDispose { managerState.states.remove(state) }
        }

        LaunchedEffect(state.isVisible) {
            managerState.onVisibilityChanged(state)
        }

        // *** 这是最核心的修复：将 parentSize 从管理器同步到子 State ***
        LaunchedEffect(managerState.parentSize) {
            state.setSizes(parent = managerState.parentSize, component = state.componentSize)
        }

        DraggableFloatingItem(
            state = state,
            onDragEnd = { managerState.onDragEnd(state) },
            modifier = modifier,
        ) {
            content()
        }
    }
}

@Composable
fun FloatingLayoutManager(
    modifier: Modifier = Modifier,
    state: FloatingLayoutManagerState,
    content: @Composable FloatingLayoutScope.() -> Unit,
) {
    BoxWithConstraints(modifier = modifier.fillMaxSize()) {
        val parentSize = IntSize(constraints.maxWidth, constraints.maxHeight)
        state.parentSize = parentSize

        val scope = FloatingLayoutScopeImpl(state)
        scope.content()
    }
}
