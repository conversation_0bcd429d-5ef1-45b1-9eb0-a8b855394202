package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.MicSeat: ImageVector
    get() {
        val current = _MicSeat
        if (current != null) return current

        return ImageVector.Builder(
            name = "com.buque.wakoo.ui.theme.WakooTheme.MicSeat",
            defaultWidth = 24.0.dp,
            defaultHeight = 24.0.dp,
            viewportWidth = 24.0f,
            viewportHeight = 24.0f,
        ).apply {
            // M16 3 H8 a4 4 0 0 0 -4 4 v1 a5 5 0 0 1 5 5 h6 a5 5 0 0 1 5 -5 V7 a4 4 0 0 0 -4 -4 m4 7 a3 3 0 0 0 -3 3 v3 h-2 v-1 H9 v1 H7 v-3 a3 3 0 1 0 -4 2.83 V21 h2 v-1 h14 v1 h2 v-5.17 A3 3 0 0 0 20 10
            path(
                fill = SolidColor(Color(0xFFFFFFFF)),
            ) {
                // M 16 3
                moveTo(x = 16.0f, y = 3.0f)
                // H 8
                horizontalLineTo(x = 8.0f)
                // a 4 4 0 0 0 -4 4
                arcToRelative(
                    a = 4.0f,
                    b = 4.0f,
                    theta = 0.0f,
                    isMoreThanHalf = false,
                    isPositiveArc = false,
                    dx1 = -4.0f,
                    dy1 = 4.0f,
                )
                // v 1
                verticalLineToRelative(dy = 1.0f)
                // a 5 5 0 0 1 5 5
                arcToRelative(
                    a = 5.0f,
                    b = 5.0f,
                    theta = 0.0f,
                    isMoreThanHalf = false,
                    isPositiveArc = true,
                    dx1 = 5.0f,
                    dy1 = 5.0f,
                )
                // h 6
                horizontalLineToRelative(dx = 6.0f)
                // a 5 5 0 0 1 5 -5
                arcToRelative(
                    a = 5.0f,
                    b = 5.0f,
                    theta = 0.0f,
                    isMoreThanHalf = false,
                    isPositiveArc = true,
                    dx1 = 5.0f,
                    dy1 = -5.0f,
                )
                // V 7
                verticalLineTo(y = 7.0f)
                // a 4 4 0 0 0 -4 -4
                arcToRelative(
                    a = 4.0f,
                    b = 4.0f,
                    theta = 0.0f,
                    isMoreThanHalf = false,
                    isPositiveArc = false,
                    dx1 = -4.0f,
                    dy1 = -4.0f,
                )
                // m 4 7
                moveToRelative(dx = 4.0f, dy = 7.0f)
                // a 3 3 0 0 0 -3 3
                arcToRelative(
                    a = 3.0f,
                    b = 3.0f,
                    theta = 0.0f,
                    isMoreThanHalf = false,
                    isPositiveArc = false,
                    dx1 = -3.0f,
                    dy1 = 3.0f,
                )
                // v 3
                verticalLineToRelative(dy = 3.0f)
                // h -2
                horizontalLineToRelative(dx = -2.0f)
                // v -1
                verticalLineToRelative(dy = -1.0f)
                // H 9
                horizontalLineTo(x = 9.0f)
                // v 1
                verticalLineToRelative(dy = 1.0f)
                // H 7
                horizontalLineTo(x = 7.0f)
                // v -3
                verticalLineToRelative(dy = -3.0f)
                // a 3 3 0 1 0 -4 2.83
                arcToRelative(
                    a = 3.0f,
                    b = 3.0f,
                    theta = 0.0f,
                    isMoreThanHalf = true,
                    isPositiveArc = false,
                    dx1 = -4.0f,
                    dy1 = 2.83f,
                )
                // V 21
                verticalLineTo(y = 21.0f)
                // h 2
                horizontalLineToRelative(dx = 2.0f)
                // v -1
                verticalLineToRelative(dy = -1.0f)
                // h 14
                horizontalLineToRelative(dx = 14.0f)
                // v 1
                verticalLineToRelative(dy = 1.0f)
                // h 2
                horizontalLineToRelative(dx = 2.0f)
                // v -5.17
                verticalLineToRelative(dy = -5.17f)
                // A 3 3 0 0 0 20 10
                arcTo(
                    horizontalEllipseRadius = 3.0f,
                    verticalEllipseRadius = 3.0f,
                    theta = 0.0f,
                    isMoreThanHalf = false,
                    isPositiveArc = false,
                    x1 = 20.0f,
                    y1 = 10.0f,
                )
            }
        }.build().also { _MicSeat = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.MicSeat,
                contentDescription = null,
                modifier = Modifier
                    .width((24.0).dp)
                    .height((24.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _MicSeat: ImageVector? = null
