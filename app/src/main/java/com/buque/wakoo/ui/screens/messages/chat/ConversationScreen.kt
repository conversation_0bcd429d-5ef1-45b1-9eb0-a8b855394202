package com.buque.wakoo.ui.screens.messages.chat

import android.os.SystemClock
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Badge
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.R
import com.buque.wakoo.ext.click
import com.buque.wakoo.im.inter.IUCConversation
import com.buque.wakoo.im_business.UIMessageUtils
import com.buque.wakoo.im_business.conversation.AppConversationManger
import com.buque.wakoo.im_business.conversation.BecomeToMemberConversation
import com.buque.wakoo.im_business.conversation.C2CConversation
import com.buque.wakoo.im_business.conversation.ConvLabel
import com.buque.wakoo.im_business.conversation.TribeConversation
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.onEach

@Composable
fun ConversationScreen(onAction: (IUCConversation) -> Unit) {
    val conversationList by AppConversationManger.conversationsFlow.collectAsStateWithLifecycle()
    val listState = rememberLazyListState()

    LaunchedEffect(key1 = listState) {
        snapshotFlow {
            if (conversationList.isNotEmpty() && !listState.isScrollInProgress) {
                val firstVisibleItemIndex =
                    listState.layoutInfo.visibleItemsInfo
                        .firstOrNull()
                        ?.index ?: 0
                val lastVisibleItemIndex =
                    (
                        listState.layoutInfo.visibleItemsInfo
                            .lastOrNull()
                            ?.index ?: 0
                    ).plus(1)
                Triple(
                    first = true,
                    second = firstVisibleItemIndex.minus(2).coerceAtLeast(0),
                    third = lastVisibleItemIndex.plus(2).coerceIn(0, conversationList.lastIndex),
                )
            } else {
                Triple(false, 0, 0)
            }
        }.filter {
            it.first && it.second < it.third
        }.onEach {
            val currentTime = SystemClock.elapsedRealtime()
            val checkStatusList =
                conversationList
                    .subList(it.second, (it.third + 1).coerceAtMost(conversationList.size))
                    .filter { item ->
                        item is C2CConversation &&
                            currentTime.minus(
                                AppConversationManger.getLastUpdateInfoTimestamp(item.id),
                            ) > 120_000
                    }.joinToString { item ->
                        AppConversationManger.setLastUpdateInfoTimestamp((item as C2CConversation).id, currentTime)
                        item.id
                    }
            AppConversationManger.updateUsersOnlineStatus(checkStatusList)
        }.collect()
    }

    LazyColumn(
        modifier =
            Modifier
                .fillMaxSize()
                .background(Color.White),
    ) {
        items(conversationList) {
            ConversationItem(it, onAction)
        }
    }
}

@Composable
private fun ConversationItem(
    item: IUCConversation,
    onClick: (IUCConversation) -> Unit,
) {
    when (item) {
        is C2CConversation -> {
            C2CConversationWidget(item) {
                onClick(item)
            }
        }

        is TribeConversation -> {
            GroupConversation(item) {
                onClick(item)
            }
        }

        BecomeToMemberConversation -> {
        }
    }
}

/**
 * 所有的数据都不需要在这里进行处理
 * 全部都由[AppConversationManger]组装完毕
 *
 * 要添加标签什么的, 要到[AppConversationManger.getLabels]去添加
 */
@Composable
private fun C2CConversationWidget(
    item: C2CConversation,
    onClick: () -> Unit,
) {
    val ctx = LocalContext.current
    Row(
        Modifier
            .fillMaxWidth()
            .padding(16.dp)
            .click(noEffect = true, onClick = onClick),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // 头像
        AvatarNetworkImage(item.user)
        Spacer(Modifier.width(6.dp))
        // 内容
        Column(modifier = Modifier.weight(1f)) {
            // 标题/标签/时间
            Row(modifier = Modifier.fillMaxWidth()) {
                Row(modifier = Modifier.weight(1f)) {
                    Text(
                        buildString {
                            append(item.getDisplayName(ctx))
                        },
                        style = MaterialTheme.typography.bodyLarge,
                        color = Color(0xFF111111),
                    )
                    item.labels?.forEach { label ->
                        if (label is ConvLabel.ComposeUILabel) {
                            label.LabelContent()
                        }
                    }
                }
                Text(
                    UIMessageUtils.getMessageTimeFormatText(ctx, item.timestamp),
                    color = Color(0xFF999999),
                    style = MaterialTheme.typography.labelLarge,
                )
            }
            Spacer(Modifier.height(6.dp))
            // 内容/未读数
            Row {
                Text(
                    buildString {
                        append(item.getDisplaySummary(ctx))
                    },
                    modifier = Modifier.weight(1f),
                    style = MaterialTheme.typography.labelLarge,
                    color = Color(0xFF999999),
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1,
                )

                if (item.unreadCount > 0) {
                    Badge(containerColor = Color(0xffF53F3F)) {
                        Text(
                            buildString {
                                append(if (item.unreadCount > 99) "99+" else item.unreadCount)
                            },
                            style =
                                TextStyle(
                                    fontSize = 11.sp,
                                    lineHeight = 16.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = Color.White,
                                ),
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun GroupConversation(
    conv: TribeConversation,
    onClick: () -> Unit,
) {
    val ctx = LocalContext.current
    if (conv is TribeConversation.Instance) {
        Row(
            Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .click(noEffect = true, onClick = onClick),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 头像
            NetworkImage(
                conv.group.avatarUrl,
                modifier =
                    Modifier
                        .size(48.dp)
                        .clip(CircleShape),
            )
            Spacer(Modifier.width(6.dp))
            // 内容
            Column(modifier = Modifier.weight(1f)) {
                // 标题/标签/时间
                Row(modifier = Modifier.fillMaxWidth()) {
                    Row(modifier = Modifier.weight(1f)) {
                        Text(
                            buildString {
                                append(conv.getDisplayName(ctx))
                            },
                            style = MaterialTheme.typography.bodyLarge,
                            color = Color(0xFF111111),
                        )
                    }
                    Text(
                        UIMessageUtils.getMessageTimeFormatText(ctx, conv.imConversation.timestamp),
                        color = Color(0xFF999999),
                        style = MaterialTheme.typography.labelLarge,
                    )
                }
                Spacer(Modifier.height(6.dp))
                // 内容/未读数
                Row {
                    Text(
                        buildString {
                            append(conv.getDisplaySummary(ctx))
                        },
                        modifier = Modifier.weight(1f),
                        style = MaterialTheme.typography.labelLarge,
                        color = Color(0xFF999999),
                        overflow = TextOverflow.Ellipsis,
                        maxLines = 1,
                    )

                    if (conv.imConversation.unreadCount > 0) {
                        Badge(containerColor = Color(0xffF53F3F)) {
                            Text(
                                buildString {
                                    append(if (conv.imConversation.unreadCount > 99) "99+" else conv.imConversation.unreadCount)
                                },
                                style =
                                    TextStyle(
                                        fontSize = 11.sp,
                                        lineHeight = 16.sp,
                                        fontWeight = FontWeight.Medium,
                                        color = Color.White,
                                    ),
                            )
                        }
                    }
                }
            }
        }
    } else if (conv is TribeConversation.Empty) {
        Row(
            Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 头像
            Image(
                painter = painterResource(R.drawable.ic_group_avatar_empty),
                modifier =
                    Modifier
                        .size(48.dp)
                        .clip(CircleShape),
                contentDescription = "",
            )
            Spacer(Modifier.width(6.dp))
            // 内容
            Column(modifier = Modifier.weight(1f)) {
                // 标题/标签/时间
                Row(modifier = Modifier.fillMaxWidth()) {
                    Row(modifier = Modifier.weight(1f)) {
                        Text(
                            buildString {
                                append(conv.getDisplayName(ctx))
                            },
                            style = MaterialTheme.typography.bodyLarge,
                            color = Color(0xFF111111),
                        )
                    }
                }
                Spacer(Modifier.height(6.dp))
                // 内容/未读数
                Row {
                    Text(
                        buildString {
                            append(conv.getDisplaySummary(ctx))
                        },
                        modifier = Modifier.weight(1f),
                        style = MaterialTheme.typography.labelLarge,
                        color = Color(0xFF999999),
                        overflow = TextOverflow.Ellipsis,
                        maxLines = 1,
                    )
                }
            }

            GradientButton(
                "加入群组",
                onClick = onClick,
                height = 32.dp,
                fontSize = 12.sp,
                paddingValues = PaddingValues(horizontal = 12.dp),
            )
        }
    } else {
    }
}
