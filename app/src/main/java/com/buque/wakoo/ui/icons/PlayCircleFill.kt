package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.PlayCircleFill: ImageVector
    get() {
        val current = _playCircleFill
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.PlayCircleFill",
                defaultWidth = 32.0.dp,
                defaultHeight = 32.0.dp,
                viewportWidth = 32.0f,
                viewportHeight = 32.0f,
            ).apply {
                // M16 29.33 a13.33 13.33 0 1 1 0 -26.66 13.33 13.33 0 0 1 0 26.66
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 16 29.33
                    moveTo(x = 16.0f, y = 29.33f)
                    // a 13.33 13.33 0 1 1 0 -26.66
                    arcToRelative(
                        a = 13.33f,
                        b = 13.33f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = -26.66f,
                    )
                    // a 13.33 13.33 0 0 1 0 26.66
                    arcToRelative(
                        a = 13.33f,
                        b = 13.33f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = 26.66f,
                    )
                }
                // M21.36 15.13 a1 1 0 0 1 0 1.74 l-7.29 4.2 a1 1 0 0 1 -1.5 -.86 v-8.42 a1 1 0 0 1 1.5 -.86z
                path(
                    fill = SolidColor(Color(0xFF66FE6B)),
                ) {
                    // M 21.36 15.13
                    moveTo(x = 21.36f, y = 15.13f)
                    // a 1 1 0 0 1 0 1.74
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = 1.74f,
                    )
                    // l -7.29 4.2
                    lineToRelative(dx = -7.29f, dy = 4.2f)
                    // a 1 1 0 0 1 -1.5 -0.86
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.5f,
                        dy1 = -0.86f,
                    )
                    // v -8.42
                    verticalLineToRelative(dy = -8.42f)
                    // a 1 1 0 0 1 1.5 -0.86z
                    arcToRelative(
                        a = 1.0f,
                        b = 1.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.5f,
                        dy1 = -0.86f,
                    )
                    close()
                }
            }.build()
            .also { _playCircleFill = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.PlayCircleFill,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((32.0).dp)
                        .height((32.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _playCircleFill: ImageVector? = null
