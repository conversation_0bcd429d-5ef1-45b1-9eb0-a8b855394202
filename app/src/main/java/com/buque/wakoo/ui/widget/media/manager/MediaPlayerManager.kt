package com.buque.wakoo.ui.widget.media.manager

import android.annotation.SuppressLint
import android.content.Context
import androidx.annotation.OptIn
import androidx.compose.runtime.Stable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.media3.common.C
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.database.StandaloneDatabaseProvider
import androidx.media3.datasource.DataSource
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.exoplayer.source.preload.DefaultPreloadManager
import androidx.media3.exoplayer.source.preload.TargetPreloadStatusControl
import androidx.media3.exoplayer.util.EventLogger
import com.buque.wakoo.WakooApplication
import java.io.File
import kotlin.math.abs

interface PlayerManager {
    val currentPlay: PlayMediaItem?

    val playMediaItemList: Set<PlayMediaItem>

    val playMediaStateList: Collection<PlayMediaState>

    fun enqueue(playItem: PlayMediaItem)

    fun play(playItem: PlayMediaItem)

    fun pause(playItem: PlayMediaItem)

    fun release(playItem: PlayMediaItem)

    fun stateFor(playItem: PlayMediaItem): PlayMediaState

    fun getPlayerPosition(playItem: PlayMediaItem): Long

    fun getPlayerDuration(playItem: PlayMediaItem): Long
}

@Stable
sealed class PlayerStatus {
    sealed class Idle : PlayerStatus() {
        data object Initial : Idle()

        data object Evicted : Idle()
    }

    sealed class Play : PlayerStatus() {
        data object Requested : Play()

        data object Confirmed : Play()
    }

    sealed class Pause : PlayerStatus() {
        data object Requested : Pause()

        data object Confirmed : Pause()
    }
}

@OptIn(UnstableApi::class)
@SuppressLint("StaticFieldLeak")
@Stable
object MediaPlayerManager : PlayerManager {
    private const val LOAD_CONTROL_MIN_BUFFER_MS = 5_000
    private const val LOAD_CONTROL_MAX_BUFFER_MS = 20_000
    private const val LOAD_CONTROL_BUFFER_FOR_PLAYBACK_MS = 500

    private val context: Context = WakooApplication.instance

    private val preloadManager: DefaultPreloadManager

    private val preloadControl: DefaultPreloadControl

    private val singletonPlayer: ExoPlayer

    override var currentPlay: PlayMediaItem? by mutableStateOf(null)

    override val playMediaItemList: Set<PlayMediaItem>
        get() = urlToStates.keys

    override val playMediaStateList: Collection<PlayMediaState>
        get() = urlToStates.values

    val playbackRequestedTag =
        derivedStateOf {
            currentPlay?.let {
                if (urlToStates[it]?.isPlaybackRequested == true) {
                    it.tag
                } else {
                    null
                }
            }
        }

    val currentPlayingTag =
        derivedStateOf {
            currentPlay?.let {
                if (urlToStates[it]?.isConfirmedPlaying == true) {
                    it.tag
                } else {
                    null
                }
            }
        }

    val actuallyPlayingTag =
        derivedStateOf {
            currentPlay?.let {
                if (urlToStates[it]?.isActuallyPlaying == true) {
                    it.tag
                } else {
                    null
                }
            }
        }

    private val urlToStates = mutableStateMapOf<PlayMediaItem, PlayMediaState>()

    init {

        val cacheDir = File(context.cacheDir, "media_cache")
        val databaseProvider = StandaloneDatabaseProvider(context)

        // 创建带缓存的数据源工厂
        val cacheDataSourceFactory =
            CacheDataSource
                .Factory()
                .setCache(
                    SimpleCache(
                        cacheDir,
                        LeastRecentlyUsedCacheEvictor(1000 * 1024 * 1024),
                        databaseProvider,
                    ),
                ).setUpstreamDataSourceFactory(DefaultHttpDataSource.Factory())
                .setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)

        val loadControl =
            DefaultLoadControl
                .Builder()
                .setBufferDurationsMs(
                    // minBufferMs =
                    LOAD_CONTROL_MIN_BUFFER_MS,
                    // maxBufferMs =
                    LOAD_CONTROL_MAX_BUFFER_MS,
                    // bufferForPlaybackMs =
                    LOAD_CONTROL_BUFFER_FOR_PLAYBACK_MS,
                    // bufferForPlaybackAfterRebufferMs =
                    DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS,
                ).setPrioritizeTimeOverSizeThresholds(true)
                .build()

        preloadControl = DefaultPreloadControl()

        val preloadManagerBuilder =
            DefaultPreloadManager
                .Builder(context, preloadControl)
                .setMediaSourceFactory(DefaultMediaSourceFactory(cacheDataSourceFactory as DataSource.Factory))
                .setLoadControl(loadControl)

        preloadManager = preloadManagerBuilder.build()

        singletonPlayer =
            preloadManagerBuilder
                .buildExoPlayer(
                    ExoPlayer
                        .Builder(context)
                        .setVideoScalingMode(C.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING),
                ).apply {
                    addAnalyticsListener(EventLogger())
                    repeatMode = Player.REPEAT_MODE_ONE
                    playWhenReady = false
                }
    }

    override fun enqueue(playItem: PlayMediaItem) {
        if (!urlToStates.contains(playItem)) {
            urlToStates[playItem] = PlayMediaState(playItem)
            singletonPlayer.addMediaItem(playItem.toExoPlayerMediaItem())
        }
    }

    override fun play(playItem: PlayMediaItem) {
        val previousItem = currentPlay

        if (previousItem != playItem) {
            currentPlay = playItem

            if (previousItem != null) {
                urlToStates[previousItem]?.apply {
                    status = PlayerStatus.Pause.Requested
                    player?.apply {
                        setVideoSurface(null)
                        pause()
                        playerPosition = currentPosition
                    }
                    player = null
                }
            }

            enqueue(playItem)
        } else {
            if (singletonPlayer.isPlaying && singletonPlayer.currentMediaItem?.mediaId == playItem.tag) {
                return
            }
        }

        urlToStates.getValue(playItem).apply state@{
            if (previousItem != playItem) {
                renderedFirstFrame = false
            }

            status =
                when (val currentStatus = status) {
                    PlayerStatus.Idle.Initial, is PlayerStatus.Pause -> PlayerStatus.Play.Requested

                    PlayerStatus.Idle.Evicted, is PlayerStatus.Play -> currentStatus
                }

            player =
                singletonPlayer.apply {
                    if (previousItem != playItem) {
                        urlToStates[previousItem]?.playerListener?.let(::removeListener)
                        addListener(<EMAIL>)
                    }

                    if (currentMediaItem?.mediaId == playItem.tag) {
                        seekTo(
                            if (playItem.mediaType == MediaType.AUDIO) {
                                0
                            } else {
                                playerPosition
                            },
                        )
                    } else {
                        seekTo(
                            // mediaItemIndex =
                            (0..<mediaItemCount)
                                .indexOfFirst { getMediaItemAt(it).mediaId == playItem.tag },
                            // positionMs =
                            if (playItem.mediaType == MediaType.AUDIO) {
                                0
                            } else {
                                playerPosition
                            },
                        )
                    }

                    prepare()
                }
        }
    }

    override fun pause(playItem: PlayMediaItem) {
        if (currentPlay != playItem) {
            return
        }
        currentPlay?.let(urlToStates::get)?.apply {
            status = PlayerStatus.Pause.Requested
        }
        singletonPlayer.pause()
    }

    override fun release(playItem: PlayMediaItem) {
        currentPlay
            ?.takeIf {
                it == playItem
            }?.also {
                urlToStates.remove(it)?.apply {
                    status = PlayerStatus.Idle.Evicted
                    player = null
                    singletonPlayer.stop()
                    singletonPlayer.setVideoSurface(null)
                    singletonPlayer.removeListener(playerListener)
                    singletonPlayer.removeMediaItem(singletonPlayer.currentMediaItemIndex)
                }
                currentPlay = null
            } ?: run {
            urlToStates.remove(playItem)?.apply {
                status = PlayerStatus.Idle.Evicted
                player = null
                val index =
                    (0..<singletonPlayer.mediaItemCount)
                        .indexOfFirst { singletonPlayer.getMediaItemAt(it).mediaId == tag }
                if (index > -1) {
                    singletonPlayer.removeMediaItem(index)
                }
            }
        }
    }

    override fun stateFor(playItem: PlayMediaItem): PlayMediaState {
        enqueue(playItem)
        return urlToStates.getValue(playItem)
    }

    override fun getPlayerPosition(playItem: PlayMediaItem): Long =
        if (currentPlay == playItem) {
            singletonPlayer
                .takeIf {
                    it.currentMediaItem?.mediaId == playItem.tag
                }?.currentPosition ?: urlToStates[playItem]?.playerPosition ?: 0
        } else {
            urlToStates[playItem]?.playerPosition ?: 0
        }

    override fun getPlayerDuration(playItem: PlayMediaItem): Long =
        if (currentPlay == playItem) {
            singletonPlayer
                .takeIf {
                    it.currentMediaItem?.mediaId == playItem.tag
                }?.duration ?: urlToStates[playItem]?.duration ?: -1
        } else {
            urlToStates[playItem]?.duration ?: -1
        }

    @UnstableApi
    class DefaultPreloadControl(
        var currentPlayingIndex: Int = C.INDEX_UNSET,
    ) : TargetPreloadStatusControl<Int> {
        @OptIn(UnstableApi::class)
        override fun getTargetPreloadStatus(rankingData: Int): DefaultPreloadManager.Status? {
            if (abs(rankingData - currentPlayingIndex) == 2) {
                return DefaultPreloadManager.Status(DefaultPreloadManager.Status.STAGE_LOADED_FOR_DURATION_MS, 500L)
            } else if (abs(rankingData - currentPlayingIndex) == 1) {
                return DefaultPreloadManager.Status(DefaultPreloadManager.Status.STAGE_LOADED_FOR_DURATION_MS, 1000L)
            }
            return null
        }
    }
}

fun PlayerManager.pauseIf(predicate: (PlayMediaItem) -> Boolean) {
    currentPlay?.takeIf(predicate)?.also { pause(it) }
}

fun PlayerManager.releaseIf(predicate: (PlayMediaItem) -> Boolean) {
    playMediaItemList.filter(predicate).forEach { release(it) }
}

fun PlayerManager.pause(tag: String) {
    if (currentPlay?.tag != tag) {
        return
    }
    currentPlay
        ?.takeIf {
            it.tag == tag
        }?.also {
            pause(it)
        }
}

fun PlayerManager.release(tag: String) {
    val playItem =
        currentPlay?.takeIf {
            it.tag == tag
        } ?: playMediaItemList.firstOrNull {
            it.tag == tag
        }
    if (playItem != null) {
        release(playItem)
    }
}

fun PlayerManager.getPlayerPosition(tag: String): Long {
    val playItem =
        currentPlay?.takeIf {
            it.tag == tag
        } ?: playMediaItemList.firstOrNull {
            it.tag == tag
        }
    return if (playItem != null) {
        getPlayerPosition(playItem)
    } else {
        0
    }
}

fun PlayerManager.getPlayerDuration(tag: String): Long {
    val playItem =
        currentPlay?.takeIf {
            it.tag == tag
        } ?: playMediaItemList.firstOrNull {
            it.tag == tag
        }
    return if (playItem != null) {
        getPlayerDuration(playItem)
    } else {
        -1
    }
}
