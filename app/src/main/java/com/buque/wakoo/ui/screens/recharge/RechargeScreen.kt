package com.buque.wakoo.ui.screens.recharge

import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.bean.DiamondBalance
import com.buque.wakoo.bean.RechargePackage
import com.buque.wakoo.bean.RechargePackages
import com.buque.wakoo.consts.Contracts
import com.buque.wakoo.consts.Pay
import com.buque.wakoo.core.pay.AppPayCoreKit
import com.buque.wakoo.core.pay.PayRequest
import com.buque.wakoo.network.api.bean.ChargeDataEntity
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.WakooTitleBar
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.ui.widget.state.isSuccess
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.toCState
import com.buque.wakoo.viewmodel.WalletEvent
import com.buque.wakoo.viewmodel.WalletViewModel
import kotlinx.coroutines.launch


@Composable
fun RechargeScreen(onRecordClick: () -> Unit = {}, onOpenWeb: (String) -> Unit = {}) {
    val viewModel = viewModel<WalletViewModel>()
    LaunchedEffect(viewModel) {
        viewModel.sendEvent(WalletEvent.Fetch)
    }
    val rechargeGoods by viewModel.rechargeGoodsState.collectAsState()
    val uiState = remember(rechargeGoods) {
        rechargeGoods.toCState()
    }

    LogUtils.i(uiState.toString())

    var selectedPackage by remember { mutableStateOf<RechargePackage?>(null) }
    val activity = LocalActivity.current
    val scope = rememberCoroutineScope()
    val loading by AppPayCoreKit.loading
    val loadingMgr = LocalLoadingManager.current
    if (!loading) {
        loadingMgr.dismiss()
    }
    Box(
        modifier =
            Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F7F9)),
    ) {
        // 背景渐变
        BackgroundGradient()
        Column(
            modifier = Modifier.fillMaxSize(),
        ) {
            // 状态栏占位
            // 顶部导航栏
            WakooTitleBar(title = stringResource(R.string.charge_diamond), actions = {
                Text(
                    text = stringResource(R.string.record),
                    fontSize = 12.sp,
                    color = Color(0xFF111111),
                    modifier = Modifier
                        .clickable(onClick = onRecordClick)
                        .padding(16.dp, 4.dp)
                )
            })

            CStateLayout(uiState) { data: ChargeDataEntity ->

                val diamondBalance = remember(data.balance) {
                    DiamondBalance(data.balance)
                }

                val rechargePackages: List<RechargePackage> = remember(data, selectedPackage) {
                    data.chargeItems.map { item ->
                        RechargePackage(
                            id = item.productId,
                            diamonds = item.ucoin,
                            price = item.currencyNumber,
                            isSelected = item.productId == selectedPackage?.id,
                            isPopular = false,
                            unit = item.currencyMark
                        )
                    }
                }
                LaunchedEffect(rechargePackages) {
                    if (selectedPackage == null) {
                        selectedPackage = rechargePackages.firstOrNull()
                    }
                }

                // 主要内容
                Column(
                    modifier =
                        Modifier
                            .weight(1f)
                            .verticalScroll(rememberScrollState()),
                ) {
                    SizeHeight(16.dp)

                    // 钻石余额卡片
                    DiamondBalanceCard(
                        balance = diamondBalance,
                        modifier = Modifier.padding(horizontal = 16.dp),
                    )

                    SizeHeight(32.dp)

                    // 充值套餐选择
                    RechargePackageSection(
                        packages = rechargePackages,
                        selectedPackage = selectedPackage,
                        onPackageSelected = { selectedPackage = it },
                        modifier = Modifier.padding(horizontal = 16.dp),
                    )
                    SizeHeight(100.dp)
                }
            }
        }

        if (uiState.isSuccess) {
            // 底部充值按钮
            BottomRechargeButton(
                onCheckChargeAgreement = {
                    onOpenWeb(Contracts.boost)
                },
                selectedPackage = selectedPackage,
                onRechargeClick = {
                    val act = activity
                    val pkg = selectedPackage
                    if (act != null && pkg != null) {
                        scope.launch {
                            loadingMgr.show(this) {
                                AppPayCoreKit.buy(act, PayRequest(Pay.CALL_TYPE_GOOGLE_SDK, pkg.id))
                            }
                        }
                    }
                },
                modifier = Modifier.align(Alignment.BottomCenter),
            )
        }
    }
}

/**
 * 充值钻石页面
 */
@Composable
fun RechargeContent(
    onRecordClick: () -> Unit = {},
    onRechargeClick: (RechargePackage) -> Unit = {},
    diamondBalance: DiamondBalance = DiamondBalance(balance = 99999),
    rechargePackages: List<RechargePackage> = RechargePackages.defaultPackages,
) {
    var selectedPackage by remember { mutableStateOf<RechargePackage?>(null) }

    Box(
        modifier =
            Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F7F9)),
    ) {
        // 背景渐变
        BackgroundGradient()
        Column(
            modifier = Modifier.fillMaxSize(),
        ) {
            // 状态栏占位
            // 顶部导航栏
            WakooTitleBar(title = stringResource(R.string.charge_diamond), actions = {
                Text(
                    text = stringResource(R.string.record),
                    fontSize = 12.sp,
                    color = Color(0xFF111111),
                    modifier = Modifier
                        .clickable(onClick = onRecordClick)
                        .padding(16.dp, 4.dp)
                )
            })

            // 主要内容
            Column(
                modifier =
                    Modifier
                        .weight(1f)
                        .verticalScroll(rememberScrollState()),
            ) {
                SizeHeight(16.dp)

                // 钻石余额卡片
                DiamondBalanceCard(
                    balance = diamondBalance,
                    modifier = Modifier.padding(horizontal = 16.dp),
                )

                SizeHeight(32.dp)

                // 充值套餐选择
                RechargePackageSection(
                    packages = rechargePackages,
                    selectedPackage = selectedPackage,
                    onPackageSelected = { selectedPackage = it },
                    modifier = Modifier.padding(horizontal = 16.dp),
                )

                SizeHeight(100.dp)
            }
        }

        // 底部充值按钮
        BottomRechargeButton(
            selectedPackage = selectedPackage,
            onRechargeClick = { selectedPackage?.let { onRechargeClick(it) } },
            modifier = Modifier.align(Alignment.BottomCenter),
        )
    }
}

/**
 * 背景渐变
 */
@Composable
private fun BackgroundGradient() {
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .height(200.dp)
                .background(
                    Brush.verticalGradient(
                        colors =
                            listOf(
                                Color.White,
                                Color.White.copy(alpha = 0f),
                            ),
                    ),
                ),
    )

    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .height(200.dp)
                .background(
                    Brush.verticalGradient(
                        colors =
                            listOf(
                                Color(0xFF92FCC1),
                                Color(0xFF92FCC1).copy(alpha = 0f),
                            ),
                    ),
                ),
    )
}

/**
 * 钻石余额卡片
 */
@Composable
private fun DiamondBalanceCard(
    balance: DiamondBalance,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier =
            modifier
                .fillMaxWidth()
                .height(120.dp)
                .clip(RoundedCornerShape(24.dp))
                .background(
                    Brush.verticalGradient(
                        colors =
                            listOf(
                                Color(0xFFF3FFD3),
                                Color(0xFFADFFB1),
                            ),
                    ),
                ),
    ) {
        // 大钻石图标
        Image(
            painter = painterResource(id = R.drawable.ic_green_diamond_straight),
            contentDescription = null,
            modifier =
                Modifier
                    .size(72.dp)
                    .align(Alignment.CenterEnd)
                    .padding(end = 24.dp)
                    .rotate(15f),
        )

        // 余额信息
        Column(
            modifier =
                Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = 24.dp),
        ) {
            Text(
                text = stringResource(R.string.diamond_balance),
                style =
                    MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp,
                    ),
                color = Color(0xFF0B570E),
            )

            SizeHeight(12.dp)

            Text(
                text = balance.formattedBalance,
                style =
                    MaterialTheme.typography.titleLarge.copy(
                        fontWeight = FontWeight.Black,
                        fontSize = 20.sp,
                    ),
                color = Color(0xFF0B570E),
            )
        }
    }
}

/**
 * 充值套餐选择区域
 */
@Composable
private fun RechargePackageSection(
    packages: List<RechargePackage>,
    selectedPackage: RechargePackage?,
    onPackageSelected: (RechargePackage) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        Text(
            text = stringResource(R.string.select_charge_option),
            style =
                MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium,
                    fontSize = 14.sp,
                ),
            color = WakooGrayText,
        )

        SizeHeight(16.dp)

        LazyVerticalGrid(
            columns = GridCells.Fixed(3),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier.height(156.dp), // 固定高度以适应2行
        ) {
            items(packages) { packages ->
                RechargePackageCard(
                    packages = packages,
                    isSelected = selectedPackage?.id == packages.id,
                    onClick = { onPackageSelected(packages) },
                )
            }
        }
    }
}

/**
 * 充值套餐卡片
 */
@Composable
private fun RechargePackageCard(
    packages: RechargePackage,
    isSelected: Boolean,
    onClick: () -> Unit,
) {
    Box(
        modifier =
            Modifier
                .size(width = 109.dp, height = 72.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(
                    if (isSelected) Color(0xFFE8FFE9) else Color.White,
                )
                .border(
                    width = if (isSelected) 2.dp else 0.dp,
                    color = if (isSelected) Color(0xFF66FE6B) else Color.Transparent,
                    shape = RoundedCornerShape(12.dp),
                )
                .clickable { onClick() },
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.fillMaxSize(),
        ) {
            // 钻石图标和数量
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp),
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_green_diamond_straight),
                    contentDescription = null,
                    modifier = Modifier.size(16.dp),
                )

                Text(
                    text = packages.formattedDiamonds,
                    style =
                        MaterialTheme.typography.titleSmall.copy(
                            fontWeight = FontWeight.Black,
                            fontSize = 16.sp,
                        ),
                    color = Color(0xFF0B570E),
                )
            }

            SizeHeight(12.dp)

            // 价格
            Text(
                text = packages.formattedPrice,
                style =
                    MaterialTheme.typography.labelLarge.copy(
                        fontWeight = FontWeight.Medium,
                        fontSize = 12.sp,
                    ),
                color = Color(0xFF0B570E),
            )
        }
    }
}

/**
 * 底部充值按钮
 */
@Composable
private fun BottomRechargeButton(
    selectedPackage: RechargePackage?,
    onRechargeClick: () -> Unit,
    modifier: Modifier = Modifier,
    onCheckChargeAgreement: OnAction = {}
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = Color.Transparent,
    ) {
        // 白色渐变蒙层
        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(122.dp)
                    .background(
                        Brush.verticalGradient(
                            colors =
                                listOf(
                                    Color.White.copy(alpha = 0f),
                                    Color.White.copy(alpha = 0.6f),
                                    Color.White,
                                    Color.White,
                                ),
                        ),
                    ),
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 28.dp)
                        .align(Alignment.BottomCenter),
            ) {
                // 充值按钮
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(44.dp)
                            .clip(RoundedCornerShape(28.dp))
                            .background(
                                if (selectedPackage != null) {
                                    Brush.linearGradient(
                                        colors =
                                            listOf(
                                                Color(0xFFA3FF2C),
                                                Color(0xFF31FFA1),
                                            ),
                                    )
                                } else {
                                    Brush.linearGradient(
                                        colors =
                                            listOf(
                                                Color(0xFFE5E5E5),
                                                Color(0xFFE5E5E5),
                                            ),
                                    )
                                },
                            )
                            .clickable(enabled = selectedPackage != null) { onRechargeClick() },
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = stringResource(R.string.charge_now),
                        style =
                            MaterialTheme.typography.titleSmall.copy(
                                fontWeight = FontWeight.Medium,
                                fontSize = 16.sp,
                            ),
                        color = if (selectedPackage != null) Color.Black else WakooGrayText,
                    )
                }

                SizeHeight(16.dp)

                // 协议文字
                Text(
                    text = stringResource(R.string.desc_charge_bt),
                    style =
                        MaterialTheme.typography.labelLarge.copy(
                            fontSize = 12.sp,
                        ),
                    color = Color(0xFFB6B6B6),
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .clickable(onClick = onCheckChargeAgreement),
                )

                SizeHeight(34.dp) // HomeIndicator 空间
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun RechargeScreenPreview() {
    WakooTheme {
        RechargeContent()
    }
}

@Preview(showBackground = true)
@Composable
private fun DiamondBalanceCardPreview() {
    WakooTheme {
        DiamondBalanceCard(
            balance = DiamondBalance(balance = 99999),
            modifier = Modifier.padding(16.dp),
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun RechargePackageCardPreview() {
    WakooTheme {
        Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
            RechargePackageCard(
                packages =
                    RechargePackage(
                        id = "test1",
                        diamonds = 300,
                        price = 30.0.toString(),
                    ),
                isSelected = false,
                onClick = {},
            )
            RechargePackageCard(
                packages =
                    RechargePackage(
                        id = "test2",
                        diamonds = 9980,
                        price = 998.0.toString(),
                    ),
                isSelected = true,
                onClick = {},
            )
        }
    }
}
