package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.PauseCircleFill: ImageVector
    get() {
        val current = _pauseCircleFill
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.PauseCircleFill",
                defaultWidth = 32.0.dp,
                defaultHeight = 32.0.dp,
                viewportWidth = 32.0f,
                viewportHeight = 32.0f,
            ).apply {
                // M16 29.33 a13.33 13.33 0 1 1 0 -26.66 13.33 13.33 0 0 1 0 26.66
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 16 29.33
                    moveTo(x = 16.0f, y = 29.33f)
                    // a 13.33 13.33 0 1 1 0 -26.66
                    arcToRelative(
                        a = 13.33f,
                        b = 13.33f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = -26.66f,
                    )
                    // a 13.33 13.33 0 0 1 0 26.66
                    arcToRelative(
                        a = 13.33f,
                        b = 13.33f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.0f,
                        dy1 = 26.66f,
                    )
                }
                // <rect width="3" height="8" rx="0.5" x="12.005" y="12.0" fill="#66FE6B" />
                path(
                    fill = SolidColor(Color(0xFF66FE6B)),
                ) {
                    // M 12.005 12.5
                    moveTo(x = 12.005f, y = 12.5f)
                    // a 0.5 0.5 0 0 1 0.5 -0.5
                    arcToRelative(
                        a = 0.5f,
                        b = 0.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.5f,
                        dy1 = -0.5f,
                    )
                    // h 2
                    horizontalLineToRelative(dx = 2.0f)
                    // a 0.5 0.5 0 0 1 0.5 0.5
                    arcToRelative(
                        a = 0.5f,
                        b = 0.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.5f,
                        dy1 = 0.5f,
                    )
                    // v 7
                    verticalLineToRelative(dy = 7.0f)
                    // a 0.5 0.5 0 0 1 -0.5 0.5
                    arcToRelative(
                        a = 0.5f,
                        b = 0.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -0.5f,
                        dy1 = 0.5f,
                    )
                    // h -2
                    horizontalLineToRelative(dx = -2.0f)
                    // a 0.5 0.5 0 0 1 -0.5 -0.5z
                    arcToRelative(
                        a = 0.5f,
                        b = 0.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -0.5f,
                        dy1 = -0.5f,
                    )
                    close()
                }
                // <rect width="3" height="8" rx="0.5" x="17.357" y="12.0" fill="#66FE6B" />
                path(
                    fill = SolidColor(Color(0xFF66FE6B)),
                ) {
                    // M 17.357 12.5
                    moveTo(x = 17.357f, y = 12.5f)
                    // a 0.5 0.5 0 0 1 0.5 -0.5
                    arcToRelative(
                        a = 0.5f,
                        b = 0.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.5f,
                        dy1 = -0.5f,
                    )
                    // h 2
                    horizontalLineToRelative(dx = 2.0f)
                    // a 0.5 0.5 0 0 1 0.5 0.5
                    arcToRelative(
                        a = 0.5f,
                        b = 0.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.5f,
                        dy1 = 0.5f,
                    )
                    // v 7
                    verticalLineToRelative(dy = 7.0f)
                    // a 0.5 0.5 0 0 1 -0.5 0.5
                    arcToRelative(
                        a = 0.5f,
                        b = 0.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -0.5f,
                        dy1 = 0.5f,
                    )
                    // h -2
                    horizontalLineToRelative(dx = -2.0f)
                    // a 0.5 0.5 0 0 1 -0.5 -0.5z
                    arcToRelative(
                        a = 0.5f,
                        b = 0.5f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -0.5f,
                        dy1 = -0.5f,
                    )
                    close()
                }
            }.build()
            .also { _pauseCircleFill = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.PauseCircleFill,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((32.0).dp)
                        .height((32.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _pauseCircleFill: ImageVector? = null
