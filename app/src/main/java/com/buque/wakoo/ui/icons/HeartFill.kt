package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.HeartFill: ImageVector
    get() {
        val current = _heartFill
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.HeartFill",
                defaultWidth = 29.0.dp,
                defaultHeight = 28.0.dp,
                viewportWidth = 29.0f,
                viewportHeight = 28.0f,
            ).apply {
                // M24.12 5.55 a7 7 0 0 1 .27 9.6 l-9.89 9.92 -9.9 -9.91 A7 7 0 0 1 12.48 4 L7.9 8.58 l1.65 1.65 L14.5 5.3 l-.01 -.02 .01 .01 a7 7 0 0 1 9.62 .27
                path(
                    fill =
                        Brush.linearGradient(
                            0.0f to Color(0xFFA3FF2C),
                            1.0f to Color(0xFF31FFA1),
                            start = Offset(x = 2.833f, y = 14.283f),
                            end = Offset(x = 26.167f, y = 14.283f),
                        ),
                ) {
                    // M 24.12 5.55
                    moveTo(x = 24.12f, y = 5.55f)
                    // a 7 7 0 0 1 0.27 9.6
                    arcToRelative(
                        a = 7.0f,
                        b = 7.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 0.27f,
                        dy1 = 9.6f,
                    )
                    // l -9.89 9.92
                    lineToRelative(dx = -9.89f, dy = 9.92f)
                    // l -9.9 -9.91
                    lineToRelative(dx = -9.9f, dy = -9.91f)
                    // A 7 7 0 0 1 12.48 4
                    arcTo(
                        horizontalEllipseRadius = 7.0f,
                        verticalEllipseRadius = 7.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 12.48f,
                        y1 = 4.0f,
                    )
                    // L 7.9 8.58
                    lineTo(x = 7.9f, y = 8.58f)
                    // l 1.65 1.65
                    lineToRelative(dx = 1.65f, dy = 1.65f)
                    // L 14.5 5.3
                    lineTo(x = 14.5f, y = 5.3f)
                    // l -0.01 -0.02
                    lineToRelative(dx = -0.01f, dy = -0.02f)
                    // l 0.01 0.01
                    lineToRelative(dx = 0.01f, dy = 0.01f)
                    // a 7 7 0 0 1 9.62 0.27
                    arcToRelative(
                        a = 7.0f,
                        b = 7.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 9.62f,
                        dy1 = 0.27f,
                    )
                }
            }.build()
            .also { _heartFill = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.HeartFill,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((29.0).dp)
                        .height((28.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _heartFill: ImageVector? = null
