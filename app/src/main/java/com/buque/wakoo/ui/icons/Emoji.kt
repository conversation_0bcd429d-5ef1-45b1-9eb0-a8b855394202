package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

public val WakooIcons.Emoji: ImageVector
    get() {
        if (_emoji != null) {
            return _emoji!!
        }
        _emoji = Builder(name = "Emoji", defaultWidth = 28.0.dp, defaultHeight = 28.0.dp,
            viewportWidth = 28.0f, viewportHeight = 28.0f).apply {
            group {
                path(fill = SolidColor(Color(0xFF1D2129)), stroke = null, strokeLineWidth = 0.0f,
                    strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                    pathFillType = NonZero) {
                    moveTo(14.0f, 2.3335f)
                    curveTo(20.4435f, 2.3335f, 25.6667f, 7.5567f, 25.6667f, 14.0002f)
                    curveTo(25.6667f, 20.4437f, 20.4435f, 25.6668f, 14.0f, 25.6668f)
                    curveTo(7.5565f, 25.6668f, 2.3333f, 20.4437f, 2.3333f, 14.0002f)
                    curveTo(2.3333f, 7.5567f, 7.5565f, 2.3335f, 14.0f, 2.3335f)
                    close()
                    moveTo(14.0f, 4.6668f)
                    curveTo(11.5246f, 4.6668f, 9.1507f, 5.6502f, 7.4003f, 7.4005f)
                    curveTo(5.65f, 9.1508f, 4.6667f, 11.5248f, 4.6667f, 14.0002f)
                    curveTo(4.6667f, 16.4755f, 5.65f, 18.8495f, 7.4003f, 20.5998f)
                    curveTo(9.1507f, 22.3502f, 11.5246f, 23.3335f, 14.0f, 23.3335f)
                    curveTo(16.4753f, 23.3335f, 18.8493f, 22.3502f, 20.5997f, 20.5998f)
                    curveTo(22.35f, 18.8495f, 23.3333f, 16.4755f, 23.3333f, 14.0002f)
                    curveTo(23.3333f, 11.5248f, 22.35f, 9.1508f, 20.5997f, 7.4005f)
                    curveTo(18.8493f, 5.6502f, 16.4753f, 4.6668f, 14.0f, 4.6668f)
                    close()
                    moveTo(17.2667f, 16.1667f)
                    curveTo(17.3755f, 16.0569f, 17.5051f, 15.9698f, 17.6479f, 15.9105f)
                    curveTo(17.7907f, 15.8512f, 17.9438f, 15.8209f, 18.0984f, 15.8213f)
                    curveTo(18.2531f, 15.8218f, 18.406f, 15.8529f, 18.5485f, 15.913f)
                    curveTo(18.691f, 15.9731f, 18.8201f, 16.0608f, 18.9283f, 16.1712f)
                    curveTo(19.0366f, 16.2817f, 19.1218f, 16.4125f, 19.179f, 16.5561f)
                    curveTo(19.2362f, 16.6997f, 19.2644f, 16.8533f, 19.2617f, 17.0079f)
                    curveTo(19.2591f, 17.1625f, 19.2258f, 17.315f, 19.1637f, 17.4566f)
                    curveTo(19.1016f, 17.5982f, 19.0119f, 17.726f, 18.9f, 17.8327f)
                    curveTo(17.5926f, 19.1169f, 15.8326f, 19.8355f, 14.0f, 19.8335f)
                    curveTo(12.1674f, 19.8355f, 10.4074f, 19.1169f, 9.1f, 17.8327f)
                    curveTo(8.8842f, 17.615f, 8.7627f, 17.3211f, 8.7619f, 17.0145f)
                    curveTo(8.7611f, 16.708f, 8.8809f, 16.4135f, 9.0955f, 16.1946f)
                    curveTo(9.3101f, 15.9757f, 9.6022f, 15.8501f, 9.9087f, 15.8448f)
                    curveTo(10.2152f, 15.8396f, 10.5114f, 15.9552f, 10.7333f, 16.1667f)
                    curveTo(11.6046f, 17.0232f, 12.7782f, 17.5023f, 14.0f, 17.5002f)
                    curveTo(15.2717f, 17.5002f, 16.4232f, 16.9927f, 17.2667f, 16.1667f)
                    close()
                    moveTo(9.9167f, 9.3335f)
                    curveTo(10.3808f, 9.3335f, 10.8259f, 9.5179f, 11.1541f, 9.8461f)
                    curveTo(11.4823f, 10.1742f, 11.6667f, 10.6194f, 11.6667f, 11.0835f)
                    curveTo(11.6667f, 11.5476f, 11.4823f, 11.9927f, 11.1541f, 12.3209f)
                    curveTo(10.8259f, 12.6491f, 10.3808f, 12.8335f, 9.9167f, 12.8335f)
                    curveTo(9.4525f, 12.8335f, 9.0074f, 12.6491f, 8.6792f, 12.3209f)
                    curveTo(8.351f, 11.9927f, 8.1667f, 11.5476f, 8.1667f, 11.0835f)
                    curveTo(8.1667f, 10.6194f, 8.351f, 10.1742f, 8.6792f, 9.8461f)
                    curveTo(9.0074f, 9.5179f, 9.4525f, 9.3335f, 9.9167f, 9.3335f)
                    close()
                    moveTo(18.0833f, 9.3335f)
                    curveTo(18.5475f, 9.3335f, 18.9926f, 9.5179f, 19.3208f, 9.8461f)
                    curveTo(19.649f, 10.1742f, 19.8333f, 10.6194f, 19.8333f, 11.0835f)
                    curveTo(19.8333f, 11.5476f, 19.649f, 11.9927f, 19.3208f, 12.3209f)
                    curveTo(18.9926f, 12.6491f, 18.5475f, 12.8335f, 18.0833f, 12.8335f)
                    curveTo(17.6192f, 12.8335f, 17.1741f, 12.6491f, 16.8459f, 12.3209f)
                    curveTo(16.5177f, 11.9927f, 16.3333f, 11.5476f, 16.3333f, 11.0835f)
                    curveTo(16.3333f, 10.6194f, 16.5177f, 10.1742f, 16.8459f, 9.8461f)
                    curveTo(17.1741f, 9.5179f, 17.6192f, 9.3335f, 18.0833f, 9.3335f)
                    close()
                }
            }
        }
            .build()
        return _emoji!!
    }

private var _emoji: ImageVector? = null
