package com.buque.wakoo.ui.theme;

import androidx.compose.runtime.ProvidedValue;
import androidx.navigation3.ui.LocalNavigationEventDispatcherOwner;
import androidx.navigationevent.NavigationEventDispatcherOwner;

public class FixPreview {

    public static ProvidedValue<NavigationEventDispatcherOwner> generateProvidedValue(NavigationEventDispatcherOwner owner) {
        return LocalNavigationEventDispatcherOwner.INSTANCE.provides$navigation3_ui(owner);
    }
}
