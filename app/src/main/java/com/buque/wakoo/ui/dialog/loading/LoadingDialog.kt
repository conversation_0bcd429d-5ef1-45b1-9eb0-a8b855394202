package com.buque.wakoo.ui.dialog.loading

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.DialogProperties
import com.buque.wakoo.ui.dialog.FullDialog

/**
 * 在你的 App 的根 Composable 节点使用此函数。
 * 它负责提供 LoadingManager 实例，并根据其内部状态来渲染全局的 Loading 浮层。
 */
@Composable
fun ProvideLoadingManager(
    loadingManager: LoadingManager = rememberLoadingManager(),
    content: @Composable () -> Unit,
) {
    CompositionLocalProvider(LocalLoadingManager provides loadingManager) {
        // 渲染应用的主内容
        content()

        // 观察 LoadingManager 中的状态
        val currentState = loadingManager.state.value
        if (currentState != null) {
            // 如果存在 Loading 状态，则渲染 LoadingPopup
            LoadingPopup(
                state = currentState,
                onDismissRequest = {
                    loadingManager.dismiss()
                },
            )
        }
    }
}

/**
 * 默认的 Loading 布局样式。
 *
 * @param text 在指示器下方显示的文本。
 * @param progress 进度值 (0.0 到 1.0)。如果为 null，则显示不确定的无限旋转动画。
 */
@Composable
fun DefaultLoadingLayout(
    text: String? = null,
    progress: Float?,
) {
    Column(
        modifier =
            Modifier
                .size(92.dp)
                .background(Color.Black.copy(alpha = 0.8f), RoundedCornerShape(16.dp)),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        if (progress != null) {
            // 显示带进度的圆形指示器
            CircularProgressIndicator(
                progress = { progress },
                modifier = Modifier.size(24.dp),
                color = Color(0xFFFAFBFC),
                trackColor = Color(0xFF666666),
                strokeWidth = 4.dp,
                gapSize = 4.5.dp,
            )
        } else {
            // 显示无限旋转的圆形指示器
            CircularProgressIndicator(
                color = Color(0xFFFAFBFC),
                modifier = Modifier.size(24.dp),
                strokeWidth = 4.5.dp,
            )
        }
        if (!text.isNullOrEmpty()) {
            // SizeHeight(8.dp) // 自定义间距组件
            Text(
                text = text,
                color = Color(0xFFFAFBFC),
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
            )
        }
    }
}

/**
 * 核心的浮层组件，负责处理遮罩、点击事件和无障碍语义。
 *
 * @param state 当前的 Loading 状态，包含UI配置和内容。
 * @param onDismissRequest 当用户请求关闭时（例如点击返回键）触发的回调。
 */
@Composable
private fun LoadingPopup(
    state: LoadingState,
    onDismissRequest: () -> Unit,
) {
    val properties = state.properties

    FullDialog(
        onDismissRequest = onDismissRequest,
        properties =
            DialogProperties(
                dismissOnBackPress = properties.dismissOnBackPress,
                dismissOnClickOutside = properties.dismissOnClickOutside,
            ),
    ) {
        Box(
            modifier =
                Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center,
        ) {
            // 如果不允许点击穿透，则绘制一个遮罩层
            if (!properties.isClickPenetrate) {
                Box(
                    modifier =
                        Modifier
                            .fillMaxSize()
                            .background(properties.scrimColor)
                            // 添加点击事件处理器，用于处理点击外部关闭的逻辑
                            .clickable(
                                interactionSource = remember { MutableInteractionSource() },
                                indication = null,
                                onClick = { if (properties.dismissOnClickOutside) onDismissRequest() },
                            ),
                )
            }
            // 在中心位置渲染真正的 Loading UI 内容
            state.content(state.progress.value)
        }
    }
}
