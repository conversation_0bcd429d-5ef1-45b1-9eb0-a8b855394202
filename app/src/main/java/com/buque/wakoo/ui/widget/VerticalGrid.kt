package com.buque.wakoo.ui.widget

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Composable
fun VerticalGrid(
    modifier: Modifier = Modifier,
    columns: Int = 2,
    horizontalSpace: Dp = 0.dp,
    verticalSpace: Dp = 0.dp,
    wrapRowHeight: Boolean = false,
    horizontalCenter: Boolean = false,
    content: @Composable () -> Unit,
) {
    val horizontalSpacePx =
        with(LocalDensity.current) {
            horizontalSpace.roundToPx()
        }
    val verticalSpacePx =
        with(LocalDensity.current) {
            verticalSpace.roundToPx()
        }
    Layout(
        content = content,
        modifier = modifier,
    ) { measurables, constraints ->
        check(columns > 0)
        val itemWidth =
            if (columns > 1) {
                constraints.maxWidth.minus(horizontalSpacePx.times(columns.minus(1))) / columns
            } else {
                constraints.maxWidth / columns
            }.toInt().coerceAtLeast(0)

        val itemConstraints =
            constraints.copy(
                minWidth = itemWidth,
                maxWidth = itemWidth,
                minHeight = 0,
            )

        val placeables = measurables.map { it.measure(itemConstraints) }
        val totalItems = placeables.size
        val totalRows = (totalItems + columns - 1) / columns

        // 计算每行的高度
        val rowHeights = IntArray(totalRows) { 0 }
        placeables.forEachIndexed { index, placeable ->
            val row = index / columns
            rowHeights[row] = maxOf(rowHeights[row], placeable.height)
        }

        // 计算每列的总高度
        val columnHeights = IntArray(columns) { 0 }
        placeables.forEachIndexed { index, placeable ->
            val column = index % columns
            val row = index / columns
            val height = if (wrapRowHeight) rowHeights[row] else placeable.height
            columnHeights[column] += if (row > 0) height + verticalSpacePx else height
        }

        val height =
            (columnHeights.maxOrNull() ?: constraints.minHeight)
                .coerceIn(constraints.minHeight, constraints.maxHeight)

        layout(
            width = constraints.maxWidth,
            height = height,
        ) {
            val columnY = IntArray(columns) { 0 }

            // 按行分组处理
            placeables.chunked(columns) { rowItems ->
                val rowIndex = placeables.indexOf(rowItems.first()) / columns
                val itemsInRow = rowItems.size

                // 计算当前行的总宽度（包含间距）
                val rowWidth =
                    itemsInRow * itemWidth +
                        maxOf(0, itemsInRow - 1) * horizontalSpacePx

                // 根据对齐方式计算起始X位置
                val startX = if (horizontalCenter) ((constraints.maxWidth - rowWidth) / 2) else 0

                // 放置当前行的元素
                rowItems.forEachIndexed { columnIndex, placeable ->
                    val column =
                        (
                            rowItems.indexOfFirst { it == placeable } +
                                placeables.indexOf(rowItems.first())
                        ) % columns
                    val x = startX + columnIndex * (itemWidth + horizontalSpacePx)

                    placeable.placeRelative(
                        x = x,
                        y = columnY[column],
                    )

                    // 更新列高度
                    val heightToAdd = if (wrapRowHeight) rowHeights[rowIndex] else placeable.height
                    columnY[column] += heightToAdd + verticalSpacePx
                }
            }
        }
    }
}
