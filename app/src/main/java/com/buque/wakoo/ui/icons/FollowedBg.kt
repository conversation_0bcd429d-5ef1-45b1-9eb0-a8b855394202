package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val com.buque.wakoo.ui.icons.WakooIcons.FollowedBg: ImageVector
    get() {
        val current = _followedBg
        if (current != null) return current

        return ImageVector
            .Builder(
                name = "com.buque.wakoo.ui.theme.WakooTheme.FollowedBg",
                defaultWidth = 97.0.dp,
                defaultHeight = 62.0.dp,
                viewportWidth = 97.0f,
                viewportHeight = 62.0f,
            ).apply {
                // M20.72 0 c3.26 0 5.66 2.18 7.1 4.64 A14 14 0 0 1 28.5 6 H82.5 a14 14 0 0 1 14 14 v28 a14 14 0 0 1 -14 14 h-62 a14 14 0 0 1 -14 -14 V26.9 a10 10 0 0 1 -3.75 -3.79 18.6 18.6 0 0 1 -2.25 -9.23 c0 -3.47 .79 -6.74 2.25 -9.24 C4.18 2.18 6.58 0 9.85 0 c2.22 0 4.03 1 5.43 2.44 A7.4 7.4 0 0 1 20.72 0
                path(
                    fill =
                        Brush.linearGradient(
                            0.0f to Color(0xFFA3FF2C),
                            1.0f to Color(0xFF31FFA1),
                            start = Offset(x = 0.5f, y = 31.0f),
                            end = Offset(x = 96.5f, y = 31.0f),
                        ),
                ) {
                    // M 20.72 0
                    moveTo(x = 20.72f, y = 0.0f)
                    // c 3.26 0 5.66 2.18 7.1 4.64
                    curveToRelative(
                        dx1 = 3.26f,
                        dy1 = 0.0f,
                        dx2 = 5.66f,
                        dy2 = 2.18f,
                        dx3 = 7.1f,
                        dy3 = 4.64f,
                    )
                    // A 14 14 0 0 1 28.5 6
                    arcTo(
                        horizontalEllipseRadius = 14.0f,
                        verticalEllipseRadius = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 28.5f,
                        y1 = 6.0f,
                    )
                    // H 82.5
                    horizontalLineTo(x = 82.5f)
                    // a 14 14 0 0 1 14 14
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 14.0f,
                        dy1 = 14.0f,
                    )
                    // v 28
                    verticalLineToRelative(dy = 28.0f)
                    // a 14 14 0 0 1 -14 14
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -14.0f,
                        dy1 = 14.0f,
                    )
                    // h -62
                    horizontalLineToRelative(dx = -62.0f)
                    // a 14 14 0 0 1 -14 -14
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -14.0f,
                        dy1 = -14.0f,
                    )
                    // V 26.9
                    verticalLineTo(y = 26.9f)
                    // a 10 10 0 0 1 -3.75 -3.79
                    arcToRelative(
                        a = 10.0f,
                        b = 10.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -3.75f,
                        dy1 = -3.79f,
                    )
                    // a 18.6 18.6 0 0 1 -2.25 -9.23
                    arcToRelative(
                        a = 18.6f,
                        b = 18.6f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -2.25f,
                        dy1 = -9.23f,
                    )
                    // c 0 -3.47 0.79 -6.74 2.25 -9.24
                    curveToRelative(
                        dx1 = 0.0f,
                        dy1 = -3.47f,
                        dx2 = 0.79f,
                        dy2 = -6.74f,
                        dx3 = 2.25f,
                        dy3 = -9.24f,
                    )
                    // C 4.18 2.18 6.58 0 9.85 0
                    curveTo(
                        x1 = 4.18f,
                        y1 = 2.18f,
                        x2 = 6.58f,
                        y2 = 0.0f,
                        x3 = 9.85f,
                        y3 = 0.0f,
                    )
                    // c 2.22 0 4.03 1 5.43 2.44
                    curveToRelative(
                        dx1 = 2.22f,
                        dy1 = 0.0f,
                        dx2 = 4.03f,
                        dy2 = 1.0f,
                        dx3 = 5.43f,
                        dy3 = 2.44f,
                    )
                    // A 7.4 7.4 0 0 1 20.72 0
                    arcTo(
                        horizontalEllipseRadius = 7.4f,
                        verticalEllipseRadius = 7.4f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 20.72f,
                        y1 = 0.0f,
                    )
                }
                // M20.72 3 c-2.34 0 -4.34 2.06 -5.44 5.2 -1.1 -3.14 -3.1 -5.2 -5.44 -5.2 C6.3 3 3.5 7.78 3.5 13.88 s2.79 10.87 6.34 10.87 c2.34 0 4.34 -2.06 5.44 -5.2 1.1 3.14 3.1 5.2 5.44 5.2 3.56 0 6.34 -4.78 6.34 -10.87 C27.06 7.78 24.28 3 20.72 3 m-7.83 17.49 c-.85 1.55 -1.96 2.45 -3.05 2.45 s-2.2 -.9 -3.05 -2.45 a12 12 0 0 1 -1.16 -3.31 3.63 3.63 0 1 0 0 -6.6 A12 12 0 0 1 6.8 7.25 c.85 -1.55 1.96 -2.45 3.05 -2.45 1.1 0 2.2 .9 3.05 2.45 a14 14 0 0 1 1.48 6.62 14 14 0 0 1 -1.48 6.6 m10.88 0 c-.85 1.55 -1.96 2.45 -3.05 2.45 -1.1 0 -2.2 -.9 -3.05 -2.45 a12 12 0 0 1 -1.16 -3.31 3.63 3.63 0 1 0 0 -6.6 12 12 0 0 1 1.16 -3.32 c.85 -1.55 1.96 -2.45 3.05 -2.45 s2.2 .9 3.05 2.45 a14 14 0 0 1 1.48 6.62 14 14 0 0 1 -1.48 6.6 m3.06 14.09 4.22 -.6 -.07 .52 6.39 -.9 .13 -.9 -10.8 1.5 .49 -3.38 15.08 -2.12 -1.05 7.45 -3.73 .52 a9 9 0 0 0 3.77 .41 c-.55 3.94 -.71 4.1 -4.36 4.72 a175 175 0 0 1 -8.35 1.2 c-1.49 .17 -2.9 -.12 -2.64 -1.93z m10.1 2.17 .02 -.17 -6.39 .9 -.14 .99 c-.1 .73 .04 .72 .67 .66 1 -.09 4.27 -.55 4.86 -.68 .83 -.2 .91 -.53 1 -1.7z m21.94 -6.4 -5.26 .75 q-.04 .28 -.06 .58 l5.79 -.82 -.42 2.98 -4.56 .64 c1.15 .83 2.85 .66 4.78 .37 a10.5 10.5 0 0 0 -2.05 4.2 q-4 -.23 -6.4 -2.09 c-2.11 2.16 -4.65 3.09 -7.5 4.04 a6.4 6.4 0 0 0 -.93 -3.78 c1.3 -.18 3.92 -.63 5.4 -1.8 l-4.87 .69 .42 -2.98 5.84 -.82 .05 -.36 q0 -.12 .03 -.22 l-5.3 .74 .45 -3.17 1.78 -.25 a5 5 0 0 0 -.16 -.9 l4.02 -.86 a4 4 0 0 1 .15 1.2 l3.58 -.5 a10 10 0 0 0 .48 -1.28 l3.94 -.18 a5 5 0 0 1 -.42 .9 l1.66 -.24z m16.74 1.73 -3.5 .5 -.2 1.3 3.9 -.55 -.44 3.14 -11.58 1.62 .44 -3.13 3.89 -.55 .18 -1.3 -3.5 .5 .2 -1.33 -2.32 6.09 c-1.35 -.08 -2.65 -.17 -4.02 -.16 1.23 -2.32 1.94 -4.27 2.7 -6.05 h1.34 a12 12 0 0 0 -2.95 -.55 l2.3 -2.89 c1.15 .1 2.1 .41 3.18 .68 l.3 -2.2 -1.11 1.64 a18 18 0 0 0 -4.21 -.58 l2.13 -2.99 c1.24 .1 2.78 .5 4.05 .7 l-.17 .23 2.86 -.4 -1.05 -.29 3.33 -1.86 c1.03 .3 2.08 .58 2.8 1.03 l-.96 .54 3.58 -.5 -.44 3.13 -3.65 .52 -.14 1.04 3.5 -.5z m-10.6 .07 .24 -1.71 3.5 -.5 .14 -1.04 -3.61 .5 .3 .08 -2.08 2.66z
                path(
                    fill = SolidColor(Color(0xFF111111)),
                ) {
                    // M 20.72 3
                    moveTo(x = 20.72f, y = 3.0f)
                    // c -2.34 0 -4.34 2.06 -5.44 5.2
                    curveToRelative(
                        dx1 = -2.34f,
                        dy1 = 0.0f,
                        dx2 = -4.34f,
                        dy2 = 2.06f,
                        dx3 = -5.44f,
                        dy3 = 5.2f,
                    )
                    // c -1.1 -3.14 -3.1 -5.2 -5.44 -5.2
                    curveToRelative(
                        dx1 = -1.1f,
                        dy1 = -3.14f,
                        dx2 = -3.1f,
                        dy2 = -5.2f,
                        dx3 = -5.44f,
                        dy3 = -5.2f,
                    )
                    // C 6.3 3 3.5 7.78 3.5 13.88
                    curveTo(
                        x1 = 6.3f,
                        y1 = 3.0f,
                        x2 = 3.5f,
                        y2 = 7.78f,
                        x3 = 3.5f,
                        y3 = 13.88f,
                    )
                    // s 2.79 10.87 6.34 10.87
                    reflectiveCurveToRelative(
                        dx1 = 2.79f,
                        dy1 = 10.87f,
                        dx2 = 6.34f,
                        dy2 = 10.87f,
                    )
                    // c 2.34 0 4.34 -2.06 5.44 -5.2
                    curveToRelative(
                        dx1 = 2.34f,
                        dy1 = 0.0f,
                        dx2 = 4.34f,
                        dy2 = -2.06f,
                        dx3 = 5.44f,
                        dy3 = -5.2f,
                    )
                    // c 1.1 3.14 3.1 5.2 5.44 5.2
                    curveToRelative(
                        dx1 = 1.1f,
                        dy1 = 3.14f,
                        dx2 = 3.1f,
                        dy2 = 5.2f,
                        dx3 = 5.44f,
                        dy3 = 5.2f,
                    )
                    // c 3.56 0 6.34 -4.78 6.34 -10.87
                    curveToRelative(
                        dx1 = 3.56f,
                        dy1 = 0.0f,
                        dx2 = 6.34f,
                        dy2 = -4.78f,
                        dx3 = 6.34f,
                        dy3 = -10.87f,
                    )
                    // C 27.06 7.78 24.28 3 20.72 3
                    curveTo(
                        x1 = 27.06f,
                        y1 = 7.78f,
                        x2 = 24.28f,
                        y2 = 3.0f,
                        x3 = 20.72f,
                        y3 = 3.0f,
                    )
                    // m -7.83 17.49
                    moveToRelative(dx = -7.83f, dy = 17.49f)
                    // c -0.85 1.55 -1.96 2.45 -3.05 2.45
                    curveToRelative(
                        dx1 = -0.85f,
                        dy1 = 1.55f,
                        dx2 = -1.96f,
                        dy2 = 2.45f,
                        dx3 = -3.05f,
                        dy3 = 2.45f,
                    )
                    // s -2.2 -0.9 -3.05 -2.45
                    reflectiveCurveToRelative(
                        dx1 = -2.2f,
                        dy1 = -0.9f,
                        dx2 = -3.05f,
                        dy2 = -2.45f,
                    )
                    // a 12 12 0 0 1 -1.16 -3.31
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.16f,
                        dy1 = -3.31f,
                    )
                    // a 3.63 3.63 0 1 0 0 -6.6
                    arcToRelative(
                        a = 3.63f,
                        b = 3.63f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        dx1 = 0.0f,
                        dy1 = -6.6f,
                    )
                    // A 12 12 0 0 1 6.8 7.25
                    arcTo(
                        horizontalEllipseRadius = 12.0f,
                        verticalEllipseRadius = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        x1 = 6.8f,
                        y1 = 7.25f,
                    )
                    // c 0.85 -1.55 1.96 -2.45 3.05 -2.45
                    curveToRelative(
                        dx1 = 0.85f,
                        dy1 = -1.55f,
                        dx2 = 1.96f,
                        dy2 = -2.45f,
                        dx3 = 3.05f,
                        dy3 = -2.45f,
                    )
                    // c 1.1 0 2.2 0.9 3.05 2.45
                    curveToRelative(
                        dx1 = 1.1f,
                        dy1 = 0.0f,
                        dx2 = 2.2f,
                        dy2 = 0.9f,
                        dx3 = 3.05f,
                        dy3 = 2.45f,
                    )
                    // a 14 14 0 0 1 1.48 6.62
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.48f,
                        dy1 = 6.62f,
                    )
                    // a 14 14 0 0 1 -1.48 6.6
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.48f,
                        dy1 = 6.6f,
                    )
                    // m 10.88 0
                    moveToRelative(dx = 10.88f, dy = 0.0f)
                    // c -0.85 1.55 -1.96 2.45 -3.05 2.45
                    curveToRelative(
                        dx1 = -0.85f,
                        dy1 = 1.55f,
                        dx2 = -1.96f,
                        dy2 = 2.45f,
                        dx3 = -3.05f,
                        dy3 = 2.45f,
                    )
                    // c -1.1 0 -2.2 -0.9 -3.05 -2.45
                    curveToRelative(
                        dx1 = -1.1f,
                        dy1 = 0.0f,
                        dx2 = -2.2f,
                        dy2 = -0.9f,
                        dx3 = -3.05f,
                        dy3 = -2.45f,
                    )
                    // a 12 12 0 0 1 -1.16 -3.31
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.16f,
                        dy1 = -3.31f,
                    )
                    // a 3.63 3.63 0 1 0 0 -6.6
                    arcToRelative(
                        a = 3.63f,
                        b = 3.63f,
                        theta = 0.0f,
                        isMoreThanHalf = true,
                        isPositiveArc = false,
                        dx1 = 0.0f,
                        dy1 = -6.6f,
                    )
                    // a 12 12 0 0 1 1.16 -3.32
                    arcToRelative(
                        a = 12.0f,
                        b = 12.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.16f,
                        dy1 = -3.32f,
                    )
                    // c 0.85 -1.55 1.96 -2.45 3.05 -2.45
                    curveToRelative(
                        dx1 = 0.85f,
                        dy1 = -1.55f,
                        dx2 = 1.96f,
                        dy2 = -2.45f,
                        dx3 = 3.05f,
                        dy3 = -2.45f,
                    )
                    // s 2.2 0.9 3.05 2.45
                    reflectiveCurveToRelative(
                        dx1 = 2.2f,
                        dy1 = 0.9f,
                        dx2 = 3.05f,
                        dy2 = 2.45f,
                    )
                    // a 14 14 0 0 1 1.48 6.62
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = 1.48f,
                        dy1 = 6.62f,
                    )
                    // a 14 14 0 0 1 -1.48 6.6
                    arcToRelative(
                        a = 14.0f,
                        b = 14.0f,
                        theta = 0.0f,
                        isMoreThanHalf = false,
                        isPositiveArc = true,
                        dx1 = -1.48f,
                        dy1 = 6.6f,
                    )
                    close()
                }
            }.build()
            .also { _followedBg = it }
    }

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.FollowedBg,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((97.0).dp)
                        .height((62.0).dp),
            )
        }
    }
}

@Suppress("ObjectPropertyName")
private var _followedBg: ImageVector? = null
