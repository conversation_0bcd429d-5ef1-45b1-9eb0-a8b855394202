package com.buque.wakoo.ui.screens.recharge

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooLightGrayBg
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth

/**
 * 充值消费记录页面
 */
@Composable
fun ConsumptionRecordScreen(onBackClick: () -> Unit = {}) {
    Scaffold(
        topBar = {
            ConsumptionRecordTopBar(onBackClick = onBackClick)
        },
    ) { paddingValues ->
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .background(color = WakooLightGrayBg)
                    .verticalScroll(rememberScrollState()),
        ) {
            SizeHeight(24.dp)
            RecordList()
        }
    }
}

/**
 * 顶部导航栏
 */
@Composable
private fun ConsumptionRecordTopBar(onBackClick: () -> Unit) {
    Surface(
        shadowElevation = 0.dp,
    ) {
        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(44.dp),
        ) {
            // 返回按钮
            Box(
                modifier =
                    Modifier
                        .size(44.dp)
                        .clickable { onBackClick() }
                        .padding(10.dp),
                contentAlignment = Alignment.Center,
            ) {
//                Icon(
//                    painter = painterResource(id = R.drawable.ic_arrow_left),
//                    contentDescription = "返回",
//                    modifier = Modifier.size(24.dp),
//                )
            }

            // 标题
            Text(
                text = "充值消费记录",
                style =
                    MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Medium,
                        fontSize = 18.sp,
                    ),
                modifier = Modifier.align(Alignment.Center),
            )
        }
    }
}

/**
 * 记录列表
 */
@Composable
private fun RecordList() {
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
    ) {
        // 示例数据
        RecordItem(
            description = "文案文案文案文案",
            time = "04-10 21:23",
            amount = "+999",
            isPositive = true,
        )

        HorizontalDivider(
            modifier = Modifier.fillMaxWidth(),
            thickness = 0.5.dp,
            color = WakooLightGrayBg,
        )

        RecordItem(
            description = "文案文案文案文案",
            time = "04-10 21:23",
            amount = "+999",
            isPositive = true,
        )

        HorizontalDivider(
            modifier = Modifier.fillMaxWidth(),
            thickness = 0.5.dp,
            color = WakooLightGrayBg,
        )

        RecordItem(
            description = "文案文案文案文案",
            time = "04-10 21:23",
            amount = "+999",
            isPositive = true,
        )

        HorizontalDivider(
            modifier = Modifier.fillMaxWidth(),
            thickness = 0.5.dp,
            color = WakooLightGrayBg,
        )

        RecordItem(
            description = "文案文案文案文案",
            time = "04-10 21:23",
            amount = "-999",
            isPositive = false,
        )
    }
}

/**
 * 单个记录项
 */
@Composable
private fun RecordItem(
    description: String,
    time: String,
    amount: String,
    isPositive: Boolean,
) {
    Row(
        modifier =
            Modifier
                .fillMaxWidth()
                .height(38.dp)
                .padding(vertical = 11.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Column(
            modifier = Modifier.weight(1f),
        ) {
            Text(
                text = description,
                style =
                    MaterialTheme.typography.bodyLarge.copy(
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Normal,
                    ),
                color = MaterialTheme.colorScheme.onBackground,
            )

            SizeHeight(12.dp)

            Text(
                text = time,
                style = MaterialTheme.typography.labelSmall.copy(fontSize = 12.sp),
                color = WakooGrayText,
            )
        }

        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_green_diamond_straight),
                contentDescription = "绿钻图标",
                modifier = Modifier.size(16.dp),
            )

            SizeWidth(4.dp)

            Text(
                text = amount,
                style =
                    MaterialTheme.typography.bodyLarge.copy(
                        fontWeight = FontWeight.Medium,
                        fontSize = 16.sp,
                    ),
                color = MaterialTheme.colorScheme.onBackground,
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun ConsumptionRecordScreenPreview() {
    WakooTheme {
        ConsumptionRecordScreen()
    }
}
