package com.buque.wakoo.im_business.message.types

import androidx.core.text.HtmlCompat
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.inter.IUCMessage

data class UCTextMessage constructor(
    override val base: UCMessage,
    val text: String,
) : UCInstanceMessage,
    IUCMessage by base {
    val htmlTextSpan: CharSequence by lazy(LazyThreadSafetyMode.NONE) {
        try {
            HtmlCompat.fromHtml(text, HtmlCompat.FROM_HTML_MODE_LEGACY)
        } catch (e: Exception) {
            text
        }
    }

    override fun getSummaryString(): String = "文本消息：$text"
}
