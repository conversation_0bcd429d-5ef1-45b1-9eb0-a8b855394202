package com.buque.wakoo.im_business.wigets

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.AnimationVector
import androidx.compose.animation.core.SpringSpec
import androidx.compose.animation.core.TwoWayConverter
import androidx.compose.animation.core.VectorConverter
import androidx.compose.animation.core.VisibilityThreshold
import androidx.compose.animation.core.spring
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imeAnimationTarget
import androidx.compose.foundation.layout.isImeVisible
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.produceState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

object AudioPanel : Panel.Key<Panel>

object EmojiPanel : Panel.Key<Panel>

interface Panel {
    interface Key<P : Panel>

    val key: Key<*>

    /**
     * 面板的真实高度，不确定要返回-1
     */
    val realHeight: Int

    /**
     * 自动触发的隐藏操作是否能把此面板隐藏（自动触发的操作：1. 面板展开时点击列表区域。2. 面板展开时点击返回键）
     */
    val autoHideEnable: Boolean
        get() = true

    /**
     * 是否应用导航栏间距
     */
    val applyNavigationBarsPadding: Boolean
        get() = false

    /**
     * 是否是键盘面板
     */
    val isKeyboard: Boolean
        get() = false

    /**
     * 面板展开时是否需要输入框强制请求焦点
     */
    val forceRequestFocus: Boolean
        get() = false

    /**
     * 面板展开时是否需要输入框强制失去焦点
     */
    val forceClearFocus: Boolean
        get() = false

    companion object KeyboardPanel : Key<Panel> {
        @Stable
        val Keyboard =
            object : Panel {
                override val key: Key<*> = this@KeyboardPanel

                override val realHeight: Int = -1

                override val isKeyboard: Boolean = true

                override fun equals(other: Any?): Boolean {
                    if (this === other) return true
                    if (javaClass != other?.javaClass) return false

                    other as IMPanel

                    return key == other.key
                }

                override fun hashCode(): Int = key.hashCode()

                override fun toString(): String =
                    "Keyboard(key=$key, realHeight=$realHeight, autoHideEnable=$autoHideEnable, applyNavigationBarsPadding=$applyNavigationBarsPadding, isKeyboard=$isKeyboard)"
            }
    }
}

@Stable
data class IMPanel(
    override val key: Panel.Key<*>,
    override var realHeight: Int = -1,
    override val autoHideEnable: Boolean = true,
    override val applyNavigationBarsPadding: Boolean = false,
    override val forceRequestFocus: Boolean = false,
    override val forceClearFocus: Boolean = false,
) : Panel {
    init {
        check(!isKeyboard)
        check(forceRequestFocus != forceClearFocus || (!forceRequestFocus))
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as IMPanel

        return key == other.key
    }

    override fun hashCode(): Int = key.hashCode()
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun rememberPanelState(
    listState: LazyListState,
    panels: Array<IMPanel> = emptyArray(),
    minNavigationBarsHeight: Int = 0,
    visibleIndex: Int = -1,
): KeyboardPanelState {
    val density = LocalDensity.current
    val ime = WindowInsets.ime
    val imeAnimationTarget = WindowInsets.imeAnimationTarget
    val navigationBars = WindowInsets.navigationBars
    val scope = rememberCoroutineScope()
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusManager = LocalFocusManager.current
    return remember(panels, density, ime, imeAnimationTarget, navigationBars, scope, keyboardController, focusManager) {
        KeyboardPanelState(
            visibleIndex,
            panels,
            minNavigationBarsHeight,
            density,
            ime,
            imeAnimationTarget,
            navigationBars,
            scope,
            keyboardController,
            focusManager,
        )
    }.apply {
        val current = targetVisiblePanel
        if (current != null) {
            LaunchedEffect(key1 = current) {
                listState.scrollToItem(0)
            }
        }
        LaunchedEffect(key1 = this) {
            startMonitor()
        }
    }
}

fun Modifier.withPanelSize(
    panelState: KeyboardPanelState,
    panel: Panel,
): Modifier =
    panelState.run {
        withPanelSize(panel)
    }

fun Modifier.withInputField(panelState: KeyboardPanelState): Modifier =
    panelState.run {
        withInputField()
    }

fun Modifier.withAutoHidePanel(panelState: KeyboardPanelState): Modifier =
    panelState.run {
        withAutoHidePanel()
    }

/**
 * 总共9种面板和键盘切换场景
 *
 * 1: null to keyboard (keyboard动画)
 *
 * 2: null to panel (animatable动画)
 *
 * 3: keyboard to panel (height变大，animatable动画)
 *
 * 4: keyboard to panel (height变小，keyboard动画)
 *
 * 5: keyboard to null (keyboard动画)
 *
 * 6: panel to keyboard (height变大，keyboard动画)
 *
 * 7: panel to keyboard (height变小，animatable动画)
 *
 * 8: panel to panel (animatable动画)
 *
 * 9: panel to null (animatable动画)
 */
@Stable
class KeyboardPanelState(
    visibleIndex: Int,
    val panels: Array<IMPanel>,
    private val minNavigationBarsHeight: Int,
    private val density: Density,
    private val ime: WindowInsets,
    private val imeAnimationTarget: WindowInsets,
    private val navigationBars: WindowInsets,
    private val scope: CoroutineScope,
    private val keyboardController: SoftwareKeyboardController? = null,
    private val focusManager: FocusManager,
) {
    private sealed class VisiblePanel {
        abstract val from: Panel?

        abstract val to: Panel?

        @androidx.compose.runtime.Stable
        data class Stable(
            override val from: Panel?,
            override val to: Panel?,
            val finalHeight: Int,
        ) : VisiblePanel() // 稳态

        @androidx.compose.runtime.Stable
        data class Keyboard(
            override val from: Panel?,
            override val to: Panel?,
            val min: Int = Int.MIN_VALUE,
            val max: Int = Int.MAX_VALUE,
        ) : VisiblePanel() // ime动画

        @androidx.compose.runtime.Stable
        data class Animatable(
            override val from: Panel?,
            override val to: Panel?,
            val overrideHeight: Float? = null,
        ) : VisiblePanel() { // 自定义动画

            val targetValue: Float
                get() = overrideHeight ?: to?.realHeight?.toFloat() ?: 0f
        }
    }

    private val focusRequester = FocusRequester()

    private val minKeyboardHeight =
        with(density) {
            10.dp.roundToPx()
        }

    private val animatable = Animatable(0f)

    private var visiblePanel by mutableStateOf<VisiblePanel>(
        VisiblePanel.Stable(
            from = null,
            to = panels.getOrNull(visibleIndex),
            finalHeight = -1,
        ),
    )

    private var job: Job? = null

    var inputFieldHasFocus = false
        private set

    val paddingHeight by derivedStateOf {
        when (val current = visiblePanel) {
            is VisiblePanel.Stable ->
                current.finalHeight.takeIf { it != -1 }
                    ?: run {
                        // -1 的情况极为少见，应该是初始化展示面板才有可能[visibleIndex!=-1]
                        val toPanel = current.to
                        when {
                            toPanel == null -> 0
                            toPanel.isKeyboard -> ime.getBottom(density)
                            else -> toPanel.realHeight
                        }
                    }

            is VisiblePanel.Keyboard -> {
                ime.getBottom(density).coerceIn(current.min, current.max)
            }

            is VisiblePanel.Animatable -> animatable.value.toInt()
        }
    }

    val targetVisiblePanel by derivedStateOf {
        visiblePanel.to
    }

    val navigationBarsPadding by derivedStateOf {
        with(density) {
            if (targetVisiblePanel?.applyNavigationBarsPadding != false) {
                navigationBars.getBottom(this).coerceAtLeast(minNavigationBarsHeight)
            } else {
                0
            }.toDp()
        }
    }

    operator fun <P : Panel> get(key: Panel.Key<P>): P =
        panels.find {
            it.key == key
        } as P

    fun CoroutineScope.startMonitor() {
        snapshotFlow {
            imeAnimationTarget.getBottom(density) to ime.getBottom(density)
        }.map { pair ->
            val current = visiblePanel
            val toPanel = current.to
            val (targetImeHeight, currentImeHeight) = pair
            val imeVisible = targetImeHeight >= minKeyboardHeight
            if (imeVisible) {
                if (toPanel?.isKeyboard != true) {
                    val currentPaddingHeight = paddingHeight
                    return@map when {
                        currentPaddingHeight == targetImeHeight -> { // 面板和键盘高度一样，直接切换到键盘
                            VisiblePanel.Stable(from = toPanel, to = Panel.Keyboard, finalHeight = targetImeHeight)
                        }

                        currentPaddingHeight < targetImeHeight -> { // 面板或空白切换到键盘
                            VisiblePanel.Keyboard(from = toPanel, to = Panel.Keyboard, min = currentPaddingHeight)
                        }

                        else -> { // 面板切换到键盘
                            VisiblePanel.Animatable(
                                from = toPanel,
                                to = Panel.Keyboard,
                                overrideHeight = targetImeHeight.toFloat(),
                            )
                        }
                    }
                } else if (current is VisiblePanel.Stable && current.finalHeight != targetImeHeight) { //  键盘高度调整
                    return@map if (currentImeHeight == targetImeHeight) { // 需要一个动画更顺滑点
                        VisiblePanel.Stable(from = toPanel, to = Panel.Keyboard, finalHeight = targetImeHeight)
                    } else { // 键盘高度调整系统带动画，没见过
                        VisiblePanel.Keyboard(from = toPanel, to = Panel.Keyboard)
                    }
                } else if (current is VisiblePanel.Keyboard && currentImeHeight >= targetImeHeight) { // 键盘打开或者是键盘调整高度结束
                    return@map current.toStable()
                }
            } else if (toPanel != null) {
                if (toPanel.isKeyboard) { // 键盘收起
                    return@map VisiblePanel.Keyboard(from = toPanel, to = null)
                } else if (current is VisiblePanel.Keyboard && currentImeHeight <= toPanel.realHeight) { // 键盘切换到面板结束
                    return@map current.toStable()
                }
            } else if (current is VisiblePanel.Keyboard && currentImeHeight == targetImeHeight) { // 键盘收起结束
                return@map current.toStable()
            }
            null
        }.filterNotNull()
            .onEach {
                updateVisiblePanel(it)
            }.launchIn(this)
    }

    /**
     * @param panel 指定隐藏的panel, 当panel == null时，默认隐藏当前的
     */
    fun hidePanel(panel: Panel? = null) {
        val current = visiblePanel
        if (current.to == null) {
            return
        }
        if (panel == null || isShowing(panel)) {
            if (panel?.isKeyboard == true) {
                keyboardController?.hide()
            } else {
                updateVisiblePanel(VisiblePanel.Animatable(from = current.to, to = null))
            }
        }
    }

    /**
     * @param panel 指定显示的panel
     */
    fun showPanel(panel: Panel) {
        if (panel.isKeyboard) {
            if (inputFieldHasFocus) { // 输入法有焦点才能使用keyboardController弹出键盘
                keyboardController?.show()
            } else {
                focusRequester.requestFocus()
            }
        } else {
            val current = visiblePanel
            val toPanel = current.to
            if (toPanel == panel) { // 已展开忽律
                return
            }
            val showPanel =
                panels
                    .find {
                        it == panel
                    }?.takeIf { it.realHeight != -1 } ?: return
            if (toPanel?.isKeyboard == true) {
                val currentImeHeight = ime.getBottom(density)
                updateVisiblePanel(
                    when {
                        showPanel.realHeight == currentImeHeight -> {
                            VisiblePanel.Stable(from = toPanel, to = showPanel, finalHeight = showPanel.realHeight)
                        }

                        showPanel.realHeight < currentImeHeight -> {
                            VisiblePanel.Keyboard(from = toPanel, to = showPanel, min = showPanel.realHeight)
                        }

                        else -> {
                            VisiblePanel.Animatable(from = toPanel, to = showPanel)
                        }
                    },
                )
                keyboardController?.hide()
            } else {
                updateVisiblePanel(VisiblePanel.Animatable(from = toPanel, to = showPanel))
            }
        }
    }

    /**
     * @param panel 指定要切换的panel
     * @param hide2Keyboard 是否支持普通panel和keyboard互相切换
     */
    fun switchPanel(
        panel: Panel,
        hide2Keyboard: Boolean = true,
    ): Boolean =
        if (isShowing(panel)) {
            if (panel.isKeyboard) {
                keyboardController?.hide()
            } else if (hide2Keyboard) {
                if (inputFieldHasFocus) {
                    keyboardController?.show()
                } else {
                    focusRequester.requestFocus()
                }
            } else {
                updateVisiblePanel(VisiblePanel.Animatable(from = panel, to = null))
            }
            false
        } else {
            showPanel(panel)
            true
        }

    /**
     * @param auto 是否是自动触发的隐藏操作，比如触摸非键盘和非输入区域，有些panel不支持自动隐藏
     */
    fun hideAll(auto: Boolean = false) {
        targetVisiblePanel
            ?.takeIf {
                !auto || it.autoHideEnable // 非自动，或者支持自动隐藏
            }?.also {
                hidePanel(it)
            }
    }

    fun isShowing(panel: Panel) = targetVisiblePanel == panel

    fun hasPanelShowing() = targetVisiblePanel != null

    fun imeVisible() = targetVisiblePanel?.isKeyboard == true

    fun isAllowPanelShowing(panel: Panel) =
        run {
            val current = visiblePanel
            if (current is VisiblePanel.Stable) {
                visiblePanel.to == panel
            } else {
                visiblePanel.from == panel || visiblePanel.to == panel
            }
        }

    fun Modifier.withPanelSize(panel: Panel) =
        onSizeChanged { size ->
            panels
                .find {
                    it == panel
                }?.also {
                    it.realHeight = size.height
                    val current = visiblePanel
                    if (current is VisiblePanel.Stable && current.finalHeight == -1 && current.to == it) {
                        // 说明这个面板在没有测出高度之前就想显示，但是没有具体高度显示不了，这里要处理下
                        updateVisiblePanel(current.toStable())
                    }
                }
        }

    fun Modifier.withInputField() =
        focusRequester(focusRequester)
            .onFocusChanged {
                inputFieldHasFocus = it.isFocused
            }

    fun Modifier.withAutoHidePanel() =
        pointerInput(this@KeyboardPanelState) {
            detectTapGestures(
                onPress = {
                    hideAll(true)
                },
            )
        }

    private fun updateVisiblePanel(visiblePanel: VisiblePanel) {
        job?.cancel()
        val newVisiblePanel = visiblePanel.transformVisiblePanel()
        if (newVisiblePanel is VisiblePanel.Animatable) {
            job =
                scope.launch(Dispatchers.Main.immediate) {
                    val currentPaddingHeight = paddingHeight.toFloat()
                    if (animatable.value != currentPaddingHeight) {
                        animatable.snapTo(currentPaddingHeight)
                    }
                    applyVisiblePanel(newVisiblePanel)
                    animatable.animateTo(targetValue = newVisiblePanel.targetValue)
                    applyVisiblePanel(newVisiblePanel.toStable())
                }
        } else {
            applyVisiblePanel(newVisiblePanel)
        }
    }

    /**
     * 只在[updateVisiblePanel]中调用，其他地方不准直接调用[applyVisiblePanel], 请使用[updateVisiblePanel]
     */
    private fun applyVisiblePanel(newVisiblePanel: VisiblePanel) {
        visiblePanel = newVisiblePanel
        if (newVisiblePanel is VisiblePanel.Stable) {
            val showPanel = newVisiblePanel.to
            if (showPanel != null) {
                if (showPanel.forceRequestFocus && !inputFieldHasFocus) { // 有些面板弹出时(比如表情面板)，输入框没有焦点没法正常工作，这里强制请求焦点
                    job =
                        scope.launch {
                            delay(100)
                            focusRequester.requestFocus()
                            if (!showPanel.isKeyboard) { // 非键盘面板弹出，不能出现键盘，输入框请求焦点可能会自动弹出键盘，这里要离开隐藏
                                keyboardController?.hide()
                            }
                        }
                }

                if (showPanel.forceClearFocus && inputFieldHasFocus) { // 有些面板弹出时(比如录音面板)，输入框可能显示异常，这里强制释放焦点
                    focusManager.clearFocus(true)
                }
            }
        }
    }

    private fun VisiblePanel.transformVisiblePanel(): VisiblePanel {
        if (this is VisiblePanel.Stable && from?.isKeyboard == true && to?.isKeyboard == true) { // 检查是否键盘高度调整，尝试转换成一个动画过渡
            val currentPaddingHeight = paddingHeight
            val current = visiblePanel
            if (current.to?.isKeyboard == true && currentPaddingHeight != finalHeight) {
                return VisiblePanel.Animatable(
                    from = Panel.Keyboard,
                    to = Panel.Keyboard,
                    overrideHeight = finalHeight.toFloat(),
                )
            }
        }
        return this
    }

    private fun VisiblePanel.toStable(): VisiblePanel.Stable {
        val finalHeight = to.finalHeight
        if (this is VisiblePanel.Stable) {
            return if (this.finalHeight == finalHeight) {
                this
            } else {
                copy(finalHeight = finalHeight)
            }
        }
        return VisiblePanel.Stable(from = from, to = to, finalHeight = finalHeight)
    }

    private val Panel?.finalHeight
        get() =
            when {
                this == null -> 0
                this.isKeyboard -> ime.getBottom(density)
                else -> this.realHeight
            }
}

fun Modifier.keyboardSystemBarsPadding(panelState: KeyboardPanelState): Modifier =
    composed {
        val animatedPadding by animateDpAsState2(
            targetValue = panelState.navigationBarsPadding,
            label = "keyboardSystemBarsPadding",
        )
        padding(bottom = animatedPadding)
    }

@Composable
fun animateDpAsState2(
    targetValue: Dp,
    animationSpec: AnimationSpec<Dp> = dpDefaultSpring,
    label: String = "DpAnimation",
    finishedListener: ((Dp) -> Unit)? = null,
): State<Dp> =
    animateValueAsState2(
        targetValue,
        Dp.VectorConverter,
        animationSpec,
        label = label,
        finishedListener = finishedListener,
    )

private val dpDefaultSpring = spring(visibilityThreshold = Dp.VisibilityThreshold)

@Composable
private fun <T, V : AnimationVector> animateValueAsState2(
    targetValue: T,
    typeConverter: TwoWayConverter<T, V>,
    animationSpec: AnimationSpec<T> = remember { spring() },
    visibilityThreshold: T? = null,
    label: String = "ValueAnimation",
    finishedListener: ((T) -> Unit)? = null,
): State<T> {
    var firstAnim by remember { mutableStateOf(true) }
    val toolingOverride = remember { mutableStateOf<State<T>?>(null) }
    val animatable = remember { Animatable(targetValue, typeConverter, visibilityThreshold, label) }
    val listener by rememberUpdatedState(finishedListener)
    val animSpec: AnimationSpec<T> by rememberUpdatedState(
        animationSpec.run {
            if (visibilityThreshold != null &&
                this is SpringSpec &&
                this.visibilityThreshold != visibilityThreshold
            ) {
                spring(dampingRatio, stiffness, visibilityThreshold)
            } else {
                this
            }
        },
    )
    val channel = remember { Channel<T>(Channel.CONFLATED) }
    SideEffect {
        channel.trySend(targetValue)
    }
    LaunchedEffect(channel) {
        for (target in channel) {
            // This additional poll is needed because when the channel suspends on receive and
            // two values are produced before consumers' dispatcher resumes, only the first value
            // will be received.
            // It may not be an issue elsewhere, but in animation we want to avoid being one
            // frame late.
            val newTarget = channel.tryReceive().getOrNull() ?: target
            launch {
                if (newTarget != animatable.targetValue) {
                    if (firstAnim) {
                        firstAnim = false
                        animatable.snapTo(newTarget)
                    } else {
                        animatable.animateTo(newTarget, animSpec)
                    }
                    listener?.invoke(animatable.value)
                }
            }
        }
    }
    return toolingOverride.value ?: animatable.asState()
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun imeVisible(minKeyboardHeight: Dp): State<Boolean> {
    val density = LocalDensity.current
    val imeAnimationTarget = WindowInsets.imeAnimationTarget
    return produceState(initialValue = WindowInsets.isImeVisible, imeAnimationTarget, density, minKeyboardHeight) {
        snapshotFlow {
            with(density) {
                imeAnimationTarget.getBottom(this) >= minKeyboardHeight.roundToPx()
            }
        }.collectLatest {
            value = it
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun imeHeight(): State<Int> {
    val density = LocalDensity.current
    val ime = WindowInsets.ime
    return produceState(initialValue = ime.getBottom(density), density) {
        snapshotFlow {
            ime.getBottom(density)
        }.collectLatest {
            value = it
        }
    }
}
