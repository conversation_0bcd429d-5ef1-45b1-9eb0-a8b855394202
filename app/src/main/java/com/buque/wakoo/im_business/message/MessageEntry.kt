package com.buque.wakoo.im_business.message

import com.buque.wakoo.bean.BasicUser
import com.buque.wakoo.bean.User
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im_business.message.ui.entry.MsgUIEntry
import com.buque.wakoo.utils.TypeRefresh

/**
 * 消息的ui显示需要的额外字段, 根据业务需求可自行扩展
 * @param expansionExtra 额外的扩展字段，可以使用时获取，也可以提前获取
 */
data class MsgUIExtra(
    val expansionExtra: MsgExpansionExtra? = null,
) {
    companion object {
        val CACHE = MsgUIExtra()
    }
}

/**
 * 扩展字段发生变化，可能会引起下列字段发生变化，需要重新生成, 可以根据业务自行扩展
 * @param hint 是否显示hint扩展
 */
data class MsgExpansionExtra(
    val hint: HintExpansionExtra? = null,
)

/**
 * hint相关的数据
 */
data class HintExpansionExtra(
    val hintVisible: Boolean = false,
    val isSelf: Boolean = true,
//    val hintRichList: List<RichItem>? = null,
    val hintText: String? = null,
    val hintIcon: String? = null,
)

data class UIMessageEntry(
    val message: UCInstanceMessage,
    val uiEntry: MsgUIEntry,
    val isFakerMessage: Boolean = false,
    /**
     * 某些情况一条消息要裂变成多个item显示，通过这个索引去对应item
     * 默认为null，如果有裂变
     */
    val msgIndex: Int? = null,
    val refresh: TypeRefresh = TypeRefresh.Init,
    val user: User = BasicUser(message.sender, "", "", ""), // 为兼容写的，会删除的
) {
    val key: Any
        get() {
            return if (isFakerMessage) {
                var id = message.id
                if (id == "0") {
                    id = message.timestamp.toString()
                }
                if (msgIndex == null) id else "index_${msgIndex}_$id"
            } else {
                message.id
            }
        }

    val requireMsgIndex: Int
        get() = msgIndex ?: 0

    val isSelf: Boolean
        get() = message.isSelf

    /**
     * 只改变refresh字段，其他字段不变，目前就是为了让ui自动刷新
     */
    fun onlyRefreshCopy() = copy(refresh = refresh.nextTypeRefresh())
}
//
// sealed interface MessageEntry<out Message : UCInstanceMessage> : TypeEntry {
//    @Deprecated(
//        message = "请使用message",
//        replaceWith = ReplaceWith("message"),
//        level = DeprecationLevel.HIDDEN,
//    )
//    override val content: Any
//        get() = message
//
//    /**
//     * 消息主体
//     */
//    val message: Message
//
//    val key: Any
//
//    val isSelf: Boolean
//        get() = message.isSelf
// }
//
// data class UIMessageEntry<out Message : UCInstanceMessage>(
//    /**
//     * 消息的发送owner
//     */
//    val user: User,
//    /**
//     * 消息主体
//     */
//    override val message: Message,
//    /**
//     * 从扩展字段解析出来的类型。
//     * 个人认为，如果数据要放在列表容器中显示，可能会面临多次刷新，不应该每次都重新读取，甚至还要重新序列化(原始数据为string)，对于性能上不友好。
//     * 正确的做法应该是，读取数据的时候提前读取，在ui容器中直接读取就行了。
//     */
//    val extra: MsgUIExtra? = null,
//    /**
//     * 消息是否需要刷新的标记, 只要改变就表示需要刷新
//     */
//    override val refresh: TypeRefresh = TypeRefresh.Init,
//    /**
//     * 某些情况一条消息要裂变成多个item显示，通过这个索引去对应item
//     * 默认为null，如果有裂变
//     */
//    val msgIndex: Int? = null,
// ) : MessageEntry<Message> {
//    val requireMsgIndex: Int
//        get() = msgIndex ?: 0
//
//    override val key: Any
//        get() {
//            var id = message.id
//            if (id == "0") {
//                id = message.timestamp.toString()
//            }
//            return if (msgIndex == null) id else "index_${msgIndex}_$id"
//        }
//
//    /**
//     * 只改变refresh字段，其他字段不变，目前就是为了让ui自动刷新
//     */
//    fun onlyRefreshCopy() = copy(refresh = refresh.nextTypeRefresh())
// }
//
// data class FakerMessageEntry<out Message : UCFakerMessage>(
//    override val message: Message,
//    override val refresh: TypeRefresh = TypeRefresh.Init,
// ) : MessageEntry<UCFakerMessage> {
//    override val key: Any
//        get() = message.id
// }
