package com.buque.wakoo.im_business.message

import android.graphics.Rect
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.Stable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.ext.click
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.getExtraBoolean
import com.buque.wakoo.im.isFailure
import com.buque.wakoo.im.isSending
import com.buque.wakoo.im_business.UIMessageUtils
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage

enum class MessageSceneType {
    C2C,
}

/**
 * 消息UI管理者, 只在给Compose的LazyList中调用
 *
 * 通过不同的[MessageSceneType]来给出默认的用户信息样式和气泡样式
 * 例如C2C使用[MessageC2CUserScaffold]
 * 而CHATROOM使用[MessageChatroomUserScaffold]
 *
 * 但是最终使用何种信息样式,气泡样式 由消息本身来决定
 */
// abstract class MessageUIManager {
// //    // 支持的消息类型
// //    protected abstract val supportMessageTypes: Map<KClass<out UCInstanceMessage>, MsgLayoutContent>
// //
// //    // 支持的自定义消息类型
// //    protected abstract val supportCustomMessageTypes: Map<String, MsgLayoutContent>
//
//    // 消息key的前缀
//    protected abstract val prefixKey: String
//
//    protected abstract val sceneType: MessageSceneType
//
//    @Composable
//    protected abstract fun Render(
//        entry: UIMessageEntry,
//        onAction: IIMAction,
//    )
//
// //    /**
// //     * 获取指定消息的Content对象, 用来显示
// //     */
// //    protected fun getMsgLayout(key: UCInstanceMessage): MsgLayoutContent? {
// //        val content =
// //            if (key is UCCustomMessage) {
// //                supportCustomMessageTypes[key.cmd]
// //            } else {
// //                supportMessageTypes[key::class]
// //            }
// //
// //        if (content == null) {
// //            IMLogUtils.w("未知的消息类型${key.toMsgString()}\n${if (key is UCCustomMessage) key.rawContent else ""}")
// //        }
// //
// //        return content
// //    }
//
//    /**
//     * 加载消息列表UI(item UI)
//     *
//     * @param messageList 消息实体列表
//     * @param onAction 消息事件回调
//     */
// //    fun LazyListScope.loadMsgListLayout(
// //        messageList: List<UIMessageEntry>,
// //        onAction: IIMAction,
// //    ) {
// //        items(items = messageList, key = {
// //            "$prefixKey-${it.key}"
// //        }, contentType = {
// //            if (it.isFakerMessage) {
// //                (it.message as UCFakerMessage).contentType
// //            } else {
// //                getMsgLayout(it.message) ?: "not_find_layout_content"
// //            }
// //        }) {
// //            it.refresh
// //            if (it.isFakerMessage) {
// //                if (it.message is UCTimestampMessage) {
// //                    TimeLineContent.Render(sceneType, onAction)
// //                } else {
// //                    NoProviderMessageContent.Render(sceneType, onAction)
// //                }
// //            } else {
// //                Render(it, onAction)
// //            }
// //        }
// //    }
//
// //    object C2C : MessageUIManager() {
// ////        override val supportMessageTypes =
// ////            mapOf<KClass<out UCInstanceMessage>, MsgLayoutContent>(
// ////                UCTextMessage::class to TextMessageContent,
// ////                UCImageMessage::class to ImageMessageContent,
// ////                UCVoiceMessage::class to VoiceMessageContent,
// ////            )
// ////
// ////        override val supportCustomMessageTypes =
// ////            mapOf<String, MsgLayoutContent>(
// ////                "audioroom_share" to AudioRoomShareContent,
// ////                "give_gift" to GiftContent,
// ////            )
// //        override val prefixKey: String = "c2c"
// //        override val sceneType: MessageSceneType = MessageSceneType.C2C
// //
// //        @Composable
// //        override fun Render(
// //            entry: UIMessageEntry,
// //            onAction: IIMAction,
// //        ) {
// //            val message = entry.message
// //
// ////            // 1. 先判断消息是否已撤回, 撤回的时候直接显示撤回消息UI
// ////            if (message.isRevoked) {
// ////                RecallMessageContent.apply {
// ////                    Render(sceneType, onAction)
// ////                }
// ////                return
// ////            }
// //            // 2. 获取指定的UI
// //            val uiProvider = getMsgLayout(message)
// //
// //            // 3. 用指定的UI进行显示, 是否需要显示bubble和userInfo直接由MsgLayoutContent来判断
// //            if (uiProvider != null) {
// //                uiProvider.apply {
// //                    Render(
// //                        {
// //                            MessageC2CUserScaffold(entry = entry, onAction) {
// //                                it()
// //                            }
// //                        },
// //                        {
// //                            MessageThemeBubble(
// //                                entry = entry,
// //                            ) {
// //                                it()
// //                            }
// //                        },
// //                        MessageSceneType.C2C,
// //                        onAction,
// //                    )
// //                }
// //            } else {
// //                // 5. 没有找到layout的时候直接显示默认未找到布局
// //                NoProviderMessageContent.apply {
// //                    Render(message, sceneType, IIMAction.EMPTY)
// //                }
// //            }
// //        }
// //    }
// }

//region 消息脚手架 用户信息. 可以根据自己的需求定制在MessageContent中使用

/**
 * 消息UI脚手架
 *
 * @param entry 消息实体
 * @param onAction 动作回调
 * @param edgeSpace 消息最后的留白距离
 * @param showReadStatus 是否显示发送状态
 * @param content 消息详细UI
 */
@Composable
fun MessageC2CUserScaffold(
    entry: UIMessageEntry,
    onAction: IIMAction,
    edgeSpace: Dp = 35.dp,
    showReadStatus: Boolean = false,
    content: @Composable RowScope.(MenuActionScope) -> Unit,
) {
    val message = entry.message
    val isSelf = message.isSelf
    val context = LocalContext.current
    CompositionLocalProvider(LocalLayoutDirection provides if (isSelf) LayoutDirection.Rtl else LayoutDirection.Ltr) {
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(end = edgeSpace),
        ) {
            val menuActionScope =
                remember(context) {
                    MenuActionScope(context)
                }
            Spacer(modifier = Modifier.width(16.dp))
            AvatarNetworkImage(entry.user, size = 40.dp)
            Spacer(modifier = Modifier.width(8.dp))
            Column(modifier = Modifier.weight(1f, false)) {
                Row(verticalAlignment = Alignment.Bottom) {
                    Row(
                        modifier =
                            Modifier.combinedClickable(
                                interactionSource = remember { MutableInteractionSource() },
                                indication = null,
                                onLongClick = {
                                    menuActionScope.showMessageMenu(message)
                                },
                                onClick = {},
                            ),
                    ) {
                        CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Ltr) {
                            content(menuActionScope)
                        }
                        MessageContextMenu(menuActionScope.menuListState)
                    }
                    MessageStatus(
                        message = message,
                        showReadStatus = showReadStatus && isSelf,
                        onAction = onAction,
                        modifier =
                            Modifier
                                .align(Alignment.Bottom)
                                .padding(start = 4.dp, bottom = 4.dp),
                    )
                }
                MessageReminder(entry, Modifier.padding(top = 5.dp))
            }
        }
    }
}

/**
 * 消息UI脚手架
 *
 * @param entry 消息实体
 * @param onAction 动作回调
 * @param edgeSpace 消息最后的留白距离
 * @param showReadStatus 是否显示发送状态
 * @param content 消息详细UI
 */
@Composable
fun MessageChatroomUserScaffold(
    entry: UIMessageEntry,
    onAction: IIMAction,
    edgeSpace: Dp = 35.dp,
    content: @Composable RowScope.(MenuActionScope) -> Unit,
) {
    val message = entry.message
    val context = LocalContext.current
    Row(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(end = edgeSpace),
    ) {
        val menuActionScope =
            remember(context) {
                MenuActionScope(context)
            }
        AvatarNetworkImage(entry.user, size = 28.dp)
        Spacer(modifier = Modifier.width(8.dp))
        Column(modifier = Modifier.weight(1f, false)) {
            FlowRow(
                modifier =
                    Modifier
                        .heightIn(min = 28.dp),
            ) {
                Text(
                    entry.user.name,
                    style =
                        TextStyle(
                            color = Color.White,
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center,
                        ),
                    modifier = Modifier.heightIn(min = 28.dp),
                )
                // todo IM 添加勋章什么什么什么
            }
            Row(verticalAlignment = Alignment.Bottom) {
                Row(
                    modifier =
                        Modifier.combinedClickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null,
                            onLongClick = {
                                menuActionScope.showMessageMenu(message)
                            },
                            onClick = {},
                        ),
                ) {
                    content(menuActionScope)
                    MessageContextMenu(menuActionScope.menuListState)
                }
                MessageStatus(
                    message = message,
                    showReadStatus = false,
                    onAction = onAction,
                    modifier =
                        Modifier
                            .align(Alignment.Bottom)
                            .padding(start = 4.dp, bottom = 4.dp),
                )
            }
            MessageReminder(entry, Modifier.padding(top = 5.dp))
        }
    }
}

//endregion

//region 消息气泡皮肤

val UCInstanceMessage.bubbleColor: Color
    get() = if (isSelf) Color(0xFF84F288) else Color.White

val UCInstanceMessage.bubbleContentColor
    get() = Color(0xff111111)

val UCInstanceMessage.bubbleShape
    get() =
        RoundedCornerShape(
            topStart = if (!isSelf) 0.dp else 12.dp,
            topEnd = if (!isSelf) 12.dp else 0.dp,
            bottomEnd = 12.dp,
            bottomStart = 12.dp,
        )

@Stable
data class MessageTheme(
    val painter: Painter,
    val paddingValues: PaddingValues,
    val shape: Shape?,
    val contentColor: Color,
    val fontSize: TextUnit,
    val left: Boolean = true,
)

data class MessageSkin(
    val background: String,
    val paddingRect: Rect,
    val textColor: String,
)

//region 普通气泡

@Composable
inline fun RowMessageBubble(
    message: UCInstanceMessage,
    modifier: Modifier = Modifier,
    bubbleColor: Color = message.bubbleColor,
    bubbleContentColor: Color = message.bubbleContentColor,
    bubbleShape: Shape = message.bubbleShape,
    crossinline content: @Composable RowScope.() -> Unit,
) {
    Row(
        modifier =
            modifier
                .background(bubbleColor, bubbleShape)
                .padding(horizontal = 16.dp, vertical = 9.5.dp),
        content = {
            CompositionLocalProvider(LocalContentColor provides bubbleContentColor) {
                content()
            }
        },
        verticalAlignment = Alignment.CenterVertically,
    )
}

@Composable
inline fun ColumnMessageBubble(
    message: UCInstanceMessage,
    modifier: Modifier = Modifier,
    bubbleColor: Color = message.bubbleColor,
    bubbleContentColor: Color = message.bubbleContentColor,
    bubbleShape: Shape = message.bubbleShape,
    crossinline content: @Composable ColumnScope.() -> Unit,
) {
    Column(
        modifier =
            modifier
                .background(bubbleColor, bubbleShape)
                .padding(horizontal = 16.dp, vertical = 9.5.dp),
        content = {
            CompositionLocalProvider(LocalContentColor provides bubbleContentColor) {
                content()
            }
        },
        horizontalAlignment = Alignment.CenterHorizontally,
    )
}
//endregion

@Composable
inline fun MessageThemeBubble(
    entry: UIMessageEntry? = null,
    modifier: Modifier = Modifier,
    childModifier: Modifier = Modifier,
    bubbleColor: Color = entry?.message?.bubbleColor ?: Color(0xFF84F288),
    bubbleContentColor: Color = entry?.message?.bubbleContentColor ?: Color(0xff111111),
    bubbleShape: Shape = entry?.message?.bubbleShape ?: RoundedCornerShape(0.dp),
    crossinline content: @Composable RowScope.() -> Unit,
) {
    MessageThemeBubble(
        entry = entry,
        defaultMsgTheme =
            MessageTheme(
                painter = ColorPainter(bubbleColor),
                paddingValues = PaddingValues(horizontal = 16.dp, vertical = 9.5.dp),
                shape = bubbleShape,
                contentColor = bubbleContentColor,
                fontSize = 14.sp,
                left = entry?.isSelf ?: false,
            ),
        modifier = modifier,
        childModifier = childModifier,
        content = content,
    )
}

@Composable
inline fun MessageThemeBubble(
    entry: UIMessageEntry?,
    defaultMsgTheme: MessageTheme,
    modifier: Modifier = Modifier,
    childModifier: Modifier = Modifier,
    crossinline content: @Composable RowScope.() -> Unit,
) {
//    val chatBubble = if (entry.message is UCTextMessage) {
//        entry.user.bubble
//    } else {
//        null
//    }
    MessageThemeBubble(defaultMsgTheme, modifier, childModifier, content)
}

@Composable
inline fun MessageThemeBubble(
    defaultMsgTheme: MessageTheme,
    modifier: Modifier = Modifier,
    childModifier: Modifier = Modifier,
    crossinline content: @Composable RowScope.() -> Unit,
) {
//    val left = defaultMsgTheme.left
//    var hasSkin = false
//    val messageTheme = run {
//        if (chatBubble != null) {
//            val url = if (left) {
//                chatBubble.leftImg
//            } else {
//                chatBubble.rightImg
//            }
//            if (url.isNotEmpty()) {
//                LaunchOnceEffect {
//                    NinePathLoader.recordChatBubble(chatBubble)
//                }
//                hasSkin = true
//                return@run rememberSkinMessageTheme(
//                    MessageSkin(
//                        url, if (left) {
//                            chatBubble.leftPadding
//                        } else {
//                            chatBubble.rightPadding
//                        }, chatBubble.fontColor
//                    ),
//                    defaultMsgTheme
//                ).value
//            }
//        }
//        defaultMsgTheme
//    }

    Box(modifier) {
        MessageThemeBubble(childModifier, defaultMsgTheme, content)

//        if (hasSkin) {
//            val options = remember {
//                RequestOptions.bitmapTransform(SourceDensity(NinePathLoader.PIC_DENSITY_DPI))
//                    .override(Target.SIZE_ORIGINAL)
//            }
//
//            val images = if (left) chatBubble?.startImages else chatBubble?.endImages
//
//            images?.getOrNull(0)?.takeIf {
//                it.isNotEmpty()
//            }?.let {
//                rememberPainterByUrl(it, options).value
//            }?.also { painter ->
//                Image(
//                    painter = painter,
//                    contentDescription = null,
//                    modifier = Modifier.align(Alignment.TopStart),
//                    contentScale = ContentScale.Fit
//                )
//            }
//
//            images?.getOrNull(1)?.takeIf {
//                it.isNotEmpty()
//            }?.let {
//                rememberPainterByUrl(it, options).value
//            }?.also { painter ->
//                Image(
//                    painter = painter,
//                    contentDescription = null,
//                    modifier = Modifier.align(Alignment.TopEnd),
//                    contentScale = ContentScale.Fit
//                )
//            }
//
//            images?.getOrNull(2)?.takeIf {
//                it.isNotEmpty()
//            }?.let {
//                rememberPainterByUrl(it, options).value
//            }?.also { painter ->
//                Image(
//                    painter = painter,
//                    contentDescription = null,
//                    modifier = Modifier.align(Alignment.BottomStart),
//                    contentScale = ContentScale.Fit
//                )
//            }
//
//            images?.getOrNull(3)?.takeIf {
//                it.isNotEmpty()
//            }?.let {
//                rememberPainterByUrl(it, options).value
//            }?.also { painter ->
//                Image(
//                    painter = painter,
//                    contentDescription = null,
//                    modifier = Modifier.align(Alignment.BottomEnd),
//                    contentScale = ContentScale.Fit
//                )
//            }
//        }
    }
}

@Composable
inline fun MessageThemeBubble(
    modifier: Modifier,
    messageTheme: MessageTheme,
    crossinline content: @Composable RowScope.() -> Unit,
) {
    Row(
        modifier =
            modifier
                .heightIn(min = 40.dp)
                .run {
                    val shape = messageTheme.shape
                    if (shape != null) {
                        clip(shape)
                    } else {
                        this
                    }
                }.run {
                    if (LocalInspectionMode.current) {
                        background((messageTheme.painter as ColorPainter).color)
                    } else {
                        paint(messageTheme.painter, contentScale = ContentScale.FillBounds)
                    }
                }.padding(messageTheme.paddingValues),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        val mergedStyle =
            LocalTextStyle.current.merge(TextStyle(color = messageTheme.contentColor, fontSize = messageTheme.fontSize))
        CompositionLocalProvider(
            LocalContentColor provides messageTheme.contentColor,
            LocalTextStyle provides mergedStyle,
        ) {
            content()
        }
    }
}

// @Composable
// fun rememberSkinMessageTheme(msgSkin: MessageSkin, defaultMsgTheme: MessageTheme): State<MessageTheme> {
//    val context = LocalContext.current
//    val density = LocalDensity.current
//    val scope = rememberCoroutineScope()
//    return remember(msgSkin) {
//        val cache = NinePathLoader.getChatBubbleCache(msgSkin.background)
//        if (cache != null) {
//            mutableStateOf(cache.bitmapToNinePatchPainter(context, density).toMessageTheme(msgSkin, defaultMsgTheme))
//        } else {
//            mutableStateOf(defaultMsgTheme).also { state ->
//                scope.launch {
//                    val ninePatch = NinePathLoader.load(msgSkin.background, msgSkin.paddingRect)
//                        ?.bitmapToNinePatchPainter(context, density)
//                    if (ninePatch != null) {
//                        state.value = ninePatch.toMessageTheme(msgSkin, defaultMsgTheme)
//                    }
//                }
//            }
//        }
//    }
// }
//
// private fun NinePatchPainter.toMessageTheme(msgSkin: MessageSkin, defaultMsgTheme: MessageTheme) = MessageTheme(
//    DrawablePainter(drawable),
//    padding,
//    null,
//    try {
//        Color(msgSkin.textColor.toColorInt())
//    } catch (e: Throwable) {
//        defaultMsgTheme.contentColor
//    },
//    defaultMsgTheme.fontSize,
// )

//endregion

//region 消息状态UI
@Composable
fun MessageStatus(
    message: UCInstanceMessage,
    showReadStatus: Boolean,
    modifier: Modifier = Modifier,
    onAction: IIMAction,
) {
    if (LocalInspectionMode.current) {
        return
    }

    when {
        message.isSending -> {
            CircularProgressIndicator(
                modifier = modifier.size(16.dp),
                color = MaterialTheme.colorScheme.primary,
                strokeWidth = 2.dp,
            )
        }

        message.isFailure -> {
            Image(
                painter = painterResource(id = R.drawable.ic_send_msg_failure),
                contentDescription = null,
                modifier =
                    modifier
                        .size(16.dp)
                        .click {
                            onAction.onResendMessage(message)
                        },
            )
        }

        message.getExtraBoolean(UIMessageUtils.CONTENT_ILLEGAL, false) -> Unit

        showReadStatus && message.isC2CRead -> {
            Image(
                painter = painterResource(id = R.drawable.ic_cpd_msg_read),
                contentDescription = null,
                modifier = modifier.size(16.dp),
            )
        }

        showReadStatus && message.isSelf -> {
            Image(
                painter = painterResource(id = R.drawable.ic_cpd_msg_unread),
                contentDescription = null,
                modifier = modifier.size(16.dp),
            )
        }
    }
}

@Composable
fun MessageReminder(
    entry: UIMessageEntry,
    modifier: Modifier,
) {
//    val extra = entry.extra?.expansionExtra?.hint
//    if (extra?.hintVisible == true) {
//        val hintRichList = extra.hintRichList
//        if (hintRichList != null) {
//            RichText(
//                rich = hintRichList,
//                modifier = modifier,
//                color = if (entry.isSelf) Color(0xFFFF5E8B) else Color(0xFF86909C),
//                fontSize = 11.sp,
//                textAlign = if (entry.isSelf) TextAlign.End else TextAlign.Start,
//                lineHeight = 13.5.sp
//            )
//        } else {
//            val text = extra.hintText
//            if (text != null) {
//                Text(
//                    text = text,
//                    modifier = modifier,
//                    color = if (entry.isSelf) Color(0xFFFF5E8B) else Color(0xFF86909C),
//                    fontSize = 11.sp,
//                    textAlign = if (entry.isSelf) TextAlign.End else TextAlign.Start,
//                    lineHeight = 13.5.sp
//                )
//            }
//        }
//    }
}
//endregion
