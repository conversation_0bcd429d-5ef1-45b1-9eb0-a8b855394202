package com.buque.wakoo.im_business.wigets

import android.Manifest
import android.os.SystemClock
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.ext.composeClick
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im_business.interf.IC2CAction
import com.buque.wakoo.ui.icons.Emoji
import com.buque.wakoo.ui.icons.Keyboard
import com.buque.wakoo.ui.icons.MicLine
import com.buque.wakoo.ui.icons.Picture
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.utils.PermissionUtils

private var recorderStartTime = 0L

private var requestPermissionStartTime = 0L

const val PERMISSION_DIALOG_THRESHOLD = 200L

@Composable
fun ColumnScope.C2CBottomBar(
    panelState: KeyboardPanelState,
    textFieldValue: MutableState<TextFieldValue>,
    hiddenModules: List<String>?,
    onAction: IC2CAction,
) {
    val audioPanel = panelState[AudioPanel]
    val emojiPanel = panelState[EmojiPanel]

    val showSendBtn by remember {
        derivedStateOf {
            textFieldValue.value.text.isNotEmpty() &&
                !panelState.isShowing(audioPanel) &&
                !panelState.isShowing(emojiPanel)
        }
    }

    val scope = rememberCoroutineScope()

    Spacer(modifier = Modifier.height(12.dp))

    Column {
        Row(
            modifier = Modifier.padding(horizontal = 16.dp),
            verticalAlignment = Alignment.Bottom,
        ) {
            Box(
                modifier =
                    Modifier
                        .clip(RoundedCornerShape(20.dp))
                        .weight(1f)
                        .heightIn(min = 40.dp)
                        .background(Color(0xFFF6F7FB)),
                contentAlignment = Alignment.CenterStart,
            ) {
                // 不能隐藏会抛异常
                BasicTextField(
                    value = textFieldValue.value,
                    onValueChange = {
                        textFieldValue.value = it
                    },
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 10.dp)
                            .withInputField(panelState),
                    textStyle =
                        TextStyle.Default.copy(
                            color = Color(0xFF1D2129),
                            fontSize = 14.sp,
                        ),
                    maxLines = 6,
                    cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
                    decorationBox = { innerTextField ->
                        innerTextField()
                        if (textFieldValue.value.text.isEmpty()) {
                            Text(text = "输入新消息", fontSize = 14.sp, color = Color(0xFF86909C))
                        }
                    },
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Send),
                    keyboardActions =
                        KeyboardActions(
                            onSend = {
                                val text = textFieldValue.value.text
                                if (text.isNotBlank()) {
                                    onAction.onSendMessage(MessageBundle.Text.create(text))
                                    textFieldValue.value = TextFieldValue()
                                } else {
//                            toastRes(R.string.不能发送空白消息)
                                }
                            },
                        ),
                )
            }

            Row(
                modifier = Modifier.height(40.dp),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                IconButton(
                    onClick =
                        composeClick {
                            panelState.switchPanel(emojiPanel)
                        },
                    modifier =
                        Modifier
                            .padding(start = 6.dp)
                            .size(36.dp),
                ) {
                    Image(
                        imageVector = if (panelState.isShowing(emojiPanel)) WakooIcons.Keyboard else WakooIcons.Emoji,
                        contentDescription = "",
                    )
                }

                AnimatedVisibility(visible = showSendBtn) {
                    SolidButton(
                        text = "发送",
                        onClick = {
                            val text = textFieldValue.value.text
                            if (text.isNotBlank()) {
                                onAction.onSendMessage(MessageBundle.Text.create(text))
                                textFieldValue.value = TextFieldValue()
                            } else {
                                //                            toastRes(R.string.不能发送空白消息)
                            }
                        },
                        height = 30.dp,
                        minWidth = 66.dp,
                        paddingValues = PaddingValues(horizontal = 24.dp),
                        fontSize = 14.sp,
                    )
                }
            }
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceAround,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            val context = LocalContext.current
            val launcher =
                rememberLauncherForActivityResult(ActivityResultContracts.RequestPermission()) {
                    if (!it) {
                        if (
                            SystemClock
                                .elapsedRealtime()
                                .minus(requestPermissionStartTime) < PERMISSION_DIALOG_THRESHOLD &&
                            !PermissionUtils.hasAudioPermission(context)
                        ) {
                            showToast("请到设置中授予录音权限")
                        } else {
                            showToast("需要录音权限才能使用语音消息")
                        }
                    }
                }
            IconButton(
                onClick =
                    composeClick {
                        if (!PermissionUtils.hasAudioPermission(context)) {
                            launcher.launch(Manifest.permission.RECORD_AUDIO)
                        }
                        panelState.switchPanel(audioPanel)
                    },
                modifier =
                    Modifier
                        .size(28.dp),
            ) {
                Image(
                    imageVector = WakooIcons.MicLine,
                    contentDescription = "",
                )
            }
            IconButton(
                onClick =
                    composeClick {
                        onAction.onGoMediaSelector()
                    },
                modifier =
                    Modifier
                        .size(36.dp),
            ) {
                Image(
                    imageVector = WakooIcons.Picture,
                    contentDescription = "",
                )
            }
            IconButton(
                onClick =
                    composeClick {
                        onAction.onShowGiftPanel()
                    },
                modifier =
                    Modifier
                        .size(36.dp),
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_gift_open),
                    contentDescription = "",
                )
            }
        }
    }

    Spacer(modifier = Modifier.height(12.dp))
}

@Composable
@Preview
private fun C2CBottomBarPreview() {
    val textFieldValue =
        rememberSaveable(stateSaver = TextFieldValue.Saver) {
            mutableStateOf(TextFieldValue())
        }
    val listState = rememberLazyListState()
    val panelState = rememberPanelState(listState)

    Column {
        C2CBottomBar(panelState, textFieldValue, null, IC2CAction.Empty)
    }
}
