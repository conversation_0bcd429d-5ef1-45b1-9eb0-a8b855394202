package com.buque.wakoo.im_business.viewmodel

import android.util.Log
import androidx.annotation.CallSuper
import androidx.annotation.IntRange
import androidx.collection.ArraySet
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.lifecycle.compose.LifecycleStartEffect
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.bean.BasicUser
import com.buque.wakoo.bean.User
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.bean.ConversationAtInfo
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.bean.ConversationUnreadInfo
import com.buque.wakoo.im.bean.IIMEnvironmentScope
import com.buque.wakoo.im.bean.MessageAtInfo
import com.buque.wakoo.im.bean.MessageReadReceipt
import com.buque.wakoo.im.bean.SendParams
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.compat.IMCompatCore.match
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.inter.MsgFilter
import com.buque.wakoo.im.isNeedReadReceipt
import com.buque.wakoo.im.isRevoked
import com.buque.wakoo.im.isSent
import com.buque.wakoo.im.sequence
import com.buque.wakoo.im_business.UIMessageUtils
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.MsgUIExtra
import com.buque.wakoo.im_business.message.UIMessageEntry
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.message.types.UCGiftMessage
import com.buque.wakoo.im_business.message.types.UCNewMsgTagMessage
import com.buque.wakoo.im_business.message.types.UCTimestampMessage
import com.buque.wakoo.im_business.message.ui.entry.GiftMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.MsgUIEntry
import com.buque.wakoo.im_business.message.ui.entry.NoProviderEntry
import com.buque.wakoo.im_business.message.ui.entry.RecallMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.TimeMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.TimeTagMsgEntry
import com.buque.wakoo.ui.widget.pagination.LoadResult
import com.buque.wakoo.ui.widget.pagination.PaginateState
import com.buque.wakoo.utils.TypeRefresh
import com.buque.wakoo.viewmodel.BaseViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import kotlin.math.abs

object IMFetchException : IllegalStateException() {
    private fun readResolve(): Any = IMFetchException
}

data class IMMessageConfig(
    // 是否自动加载历史记录
    val loadHistoryEnable: Boolean = true,
    /**
     * 是否在消息列表中插入时间戳
     */
    val insetTimeLine: Boolean = true,
    /**
     * 显示时间戳最小间隔，单位毫秒
     */
    val eachTimeLineGap: Int = 300000, // 5分钟
    /**
     * 历史消息分页加载每页数量
     */
    val eachPageSize: Int = 20,
    /**
     * 收到新消息自动清除会话未读数
     */
    val autoCleanUnreadCount: Boolean = true,
    /**
     * 收到新消息，是否自动滚动到新消息位置
     */
    val autoScrollToLatestMsgIndex: Boolean = true,
    /**
     * 新消息距离底部第一个可见item的个数
     */
    @IntRange(from = 2) val autoScrollLatestAndFirstVisibleCount: Int = 6,
    /**
     * 是否平滑滚动
     */
    val smoothScroll: Boolean = true,
    /**
     * 是否自动播放未播放的礼物
     */
    val autoPlayNoPlayedGiftEffect: Boolean = false,
    /**
     * 群组是否加未读消息信息
     */
    val hasLoadUnreadMsgInfo: Boolean = false,
    /**
     * 群组显示以下是新消息tag
     */
    val hasAddNewMsgTag: Boolean = false,
    /**
     * 已读回执发送
     */
    val autoMarkReadReceipt: Boolean = false,
    /**
     * 多收礼人礼物消息分裂
     */
    val fissionGiftMessageEnable: Boolean = true,
)

abstract class ListStateMessageViewModel(
    sendParams: SendParams,
    config: IMMessageConfig = IMMessageConfig(),
) : IMMessageViewModel(sendParams, config) {
    val paginateState by lazy {
        PaginateState<UCMessage>(nextEnabled = false, prevEnabled = false)
    }

    private var reverseList = false

    private var reverseLayout = true

    init {
        super.init()
    }

    final override fun resetNextPageKey(
        oldEnable: Boolean?,
        oldMsgKey: UCInstanceMessage?,
        newEnable: Boolean?,
        newKey: UCInstanceMessage?,
    ) {
        if (oldEnable == null && newEnable == null) {
            return
        }
        if (reverseLayout) {
            paginateState.resetState(newEnable, newKey?.base, oldEnable, oldMsgKey?.base)
        } else {
            paginateState.resetState(oldEnable, oldMsgKey?.base, newEnable, newKey?.base)
        }
    }

    /**
     * compose 重的分页组件
     */
    @Composable
    fun bindListState(
        listState: LazyListState,
        reverseList: Boolean = false,
        reverseLayout: Boolean = true,
    ): PaginateState<UCMessage> {
        this.reverseList = reverseList
        this.reverseLayout = reverseLayout

        if (config.loadHistoryEnable) {
            paginateState.ConnectToState(listState) { pagingScope ->
                val list =
                    getHistoryMessages(key = pagingScope.key, descendPullOrder = pagingScope.next, enableNotify = true)
                delay(300)
                if (list == null) {
                    LoadResult.Error(IMFetchException)
                } else if (list.isEmpty()) {
                    LoadResult.Page(null)
                } else {
                    if (pagingScope.next) {
                        LoadResult.Page(list.last().base)
                    } else { // 拉取新消息
                        LoadResult.Page(list.first().base)
                    }
                }
            }
        }

        if (config.autoCleanUnreadCount) {
            LifecycleStartEffect(Unit) {
                onStopOrDispose {
                    cleanConversationUnreadCount()
                }
            }
        }

        if (config.autoScrollToLatestMsgIndex) {
            LaunchedEffect(Unit) {
                launch {
                    uiEffect.filterIsInstance<Effect.OnScrollToLatestMsg>().collectLatest {
                        delay(100)
                        val totalCount = listState.layoutInfo.totalItemsCount
                        if (totalCount <= 0) {
                            return@collectLatest
                        }

                        val index =
                            if (reverseLayout) {
                                listState.firstVisibleItemIndex
                            } else {
                                val lastVisibleItemIndex =
                                    listState.layoutInfo.visibleItemsInfo
                                        .lastOrNull()
                                        ?.index ?: 0
                                totalCount - 1 - lastVisibleItemIndex
                            }
                        if (index > config.autoScrollLatestAndFirstVisibleCount) {
                            return@collectLatest
                        }

                        val position =
                            if (reverseList) {
                                totalCount - 1
                            } else {
                                0
                            }

                        if (config.smoothScroll) {
                            listState.animateScrollToItem(position)
                        } else {
                            listState.scrollToItem(position)
                        }
                    }
                }

                launch {
                    uiEffect.filterIsInstance<Effect.OnScrollToIndex>().collectLatest {
                        val totalCount = listState.layoutInfo.totalItemsCount
                        if (totalCount <= 0) {
                            return@collectLatest
                        }

                        val position =
                            if (reverseList) {
                                totalCount - 1 - it.indexOfMsgList
                            } else {
                                it.indexOfMsgList
                            }

                        if (position in 0..<totalCount) {
                            if (listState.layoutInfo.reverseLayout) {
                                listState.animateScrollToItem(position, -listState.layoutInfo.viewportSize.height + 150)
                            } else {
                                listState.animateScrollToItem(position)
                            }
                        }
                    }
                }
            }
        }

        if (config.autoMarkReadReceipt || _unreadMsgInfo != null || _unreadAtInfo != null) {
            LaunchedEffect(listState) {
                snapshotFlow {
                    val totalCount = listState.layoutInfo.totalItemsCount
                    if (totalCount <= 0 || msgList.isEmpty()) {
                        return@snapshotFlow null
                    }

                    // reverseLayout
                    // 无论布局是反转 topIndex <= bottomIndex
                    var topIndex =
                        listState.layoutInfo.visibleItemsInfo
                            .firstOrNull()
                            ?.index ?: -1
                    var bottomIndex =
                        listState.layoutInfo.visibleItemsInfo
                            .lastOrNull()
                            ?.index ?: -1

                    if (topIndex != -1 && bottomIndex != -1) {
                        if (reverseList) {
                            val position = topIndex
                            topIndex = totalCount - 1 - bottomIndex
                            bottomIndex = totalCount - 1 - position
                        }
                    }
                    topIndex to bottomIndex
                }.filterNotNull().collectLatest { (topIndex, bottomIndex) ->
                    var markReadMessage: UCInstanceMessage? = null
                    try {
                        msgList.subList(topIndex.coerceAtLeast(0), bottomIndex.plus(1).coerceAtMost(msgList.size))
                    } catch (e: Exception) {
                        return@collectLatest
                    }.forEach {
                        if (!it.isFakerMessage) {
                            val unreadMsgInfo = _unreadMsgInfo
                            if (unreadMsgInfo != null) {
                                if (unreadMsgInfo.sequence == it.message.sequence) {
                                    _unreadMsgInfo = null
                                }
                            }
                            val unreadAtInfo = _unreadAtInfo
                            if (unreadAtInfo != null) {
                                if (unreadAtInfo.sequence == it.message.sequence) {
                                    _unreadAtInfo = null
                                }
                            }

                            if (config.autoMarkReadReceipt && markReadMessage == null && !it.isSelf && it.message.isNeedReadReceipt) {
                                markReadMessage = it.message
                            }
                        }
                    }

                    if (markReadMessage != null) {
                        IMCompatCore.markMessageReadReceipts(markReadMessage)
                    }
                }
            }
        }
        return paginateState
    }
}

abstract class IMMessageViewModel(
    override val sendParams: SendParams,
    /**
     * 会话消息配置
     */
    protected val config: IMMessageConfig = IMMessageConfig(),
) : BaseViewModel(),
    IIMEnvironmentScope,
    IMCompatListener {
    /**
     * 改变分页key
     */
    protected abstract fun resetNextPageKey(
        oldEnable: Boolean?,
        oldMsgKey: UCInstanceMessage?,
        newEnable: Boolean?,
        newKey: UCInstanceMessage?,
    )

    /**
     * 过滤出需要显示的消息
     */
    protected open fun filterShownMessage(message: UCInstanceMessage): Boolean =
        if (message is UCCustomMessage) {
            !message.base.isExcludedFromLastMessage
        } else {
            true
        }

    /**
     * im id
     */
    protected val imId: String
        get() = sendParams.receiver

    /**
     * 会话类型
     */
    protected val type: ConversationType
        get() = sendParams.type

    // 目前主要用来滚到到最新消息索引
    protected val uiEffect: MutableSharedFlow<Effect> = MutableSharedFlow()

    protected var onlyLocal = true

    protected var _unreadMsgInfo by mutableStateOf<ConversationUnreadInfo?>(null)

    protected var _unreadAtInfo by mutableStateOf<ConversationAtInfo?>(null)

    // 新消息索引靠前，旧消息索引靠后, 包括时间戳
    protected val msgList = mutableStateListOf<UIMessageEntry>()

    private val msgKeySet = HashSet<String>()

    // 消息
    private var messageAtInfo: MessageAtInfo? = null

    // 标记的最新已读消息时间戳
    private var latestReadTime = 0L

    private var addedNewMsgTag = false

    val unreadMsgInfo
        get() = _unreadMsgInfo

    val unreadAtInfo
        get() = _unreadAtInfo

    val messageList: List<UIMessageEntry>
        get() = msgList

    override val filter: MsgFilter = MsgFilter(imId)

    /**
     * 初始化
     */
    protected fun init() {
        IMCompatCore.addIMListener(this)
        if (config.loadHistoryEnable) {
            viewModelScope.launch(Dispatchers.Unconfined) {
                // 加载第一页历史数据
                val list = getHistoryMessages(null, descendPullOrder = true, enableNotify = true)
                if (!list.isNullOrEmpty()) {
                    // 还有更多数据，重置分页标志
                    resetNextPageKey(true, list.last(), null, null)
                    if (config.hasLoadUnreadMsgInfo) {
                        loadUnreadInfo()
                    }
                }
            }
        }
    }

    /**
     * at用户
     */
    fun addAtUser(user: User): Boolean {
        var ret = false
        val atUsers =
            buildList {
                messageAtInfo?.atUsers?.also {
                    addAll(it)
                }
                if (!contains(user.id)) {
                    ret = true
                    add(user.id)
                }
            }
        messageAtInfo = messageAtInfo?.copy(atUsers = atUsers) ?: MessageAtInfo(atUsers = atUsers)
        return ret
    }

    fun useAtInfo() =
        messageAtInfo?.also {
            messageAtInfo = null
        }

    /**
     * 点击跳转到未读消息处
     */
    fun scrollToUnreadMessage() {
        viewModelScope.launch {
            if (!scrollToUnreadMsg(_unreadMsgInfo?.sequence ?: 0)) {
                _unreadMsgInfo = null
            }
        }
    }

    /**
     * 点击跳转到第一条@消息处
     */
    fun scrollToAtMessage() {
        viewModelScope.launch {
            if (!scrollToUnreadMsg(_unreadAtInfo?.sequence ?: 0)) {
                _unreadAtInfo = null
            }
        }
    }

    @CallSuper
    override fun onCleared() {
        IMCompatCore.removeIMListener(this)
    }

    @CallSuper
    override fun onSendNewMessage(
        message: UCInstanceMessage,
        onlyLocal: Boolean,
    ) {
        if (filter(message)) {
            addEntries(entries = convertToUIMessageEntries(message, false), index = 0)
            tryScrollToLatestMsgIndex()
        }
    }

    @CallSuper
    override fun onSendMessageResult(
        message: UCInstanceMessage,
        success: Boolean,
    ) {
        val updated =
            updateMessage(message.id) {
                convertToUIMessageEntry(message, true, it)
            }
        if (updated) {
            tryScrollToLatestMsgIndex()
        }
    }

    @CallSuper
    override fun onResendMessage(message: UCInstanceMessage) {
        updateMessage(message.id) {
            convertToUIMessageEntry(message, false, it)
        }
    }

    @CallSuper
    override fun onRecvNewMessage(
        message: UCInstanceMessage,
        offline: Boolean,
    ) {
        if (filter(message)) {
            addEntries(entries = convertToUIMessageEntries(message, true), index = 0)
            tryScrollToLatestMsgIndex()
        }
    }

    @CallSuper
    override fun onFetchHistoryMessagesSuccess(
        messages: List<UCInstanceMessage>,
        descendPullOrder: Boolean,
    ) {
        val entries =
            messages.flatMap {
                if (filter(it)) {
                    convertToUIMessageEntries(it, true)
                } else {
                    emptyList()
                }
            }

        addEntries(entries, if (descendPullOrder) -1 else 0)
        tryAddNewMsgTag()
    }

    @CallSuper
    override fun onMessageRecalled(message: UCInstanceMessage) {
        // 这里的消息不包括扩展字段
        updateMessage(message.id) {
            convertToUIMessageEntry(message, false, it)
        }
    }

    @CallSuper
    override fun onMessageDeleted(message: UCInstanceMessage) {
        removeMessageById(message.id)
    }

    @CallSuper
    override fun onMessageExpansionUpdate(
        message: UCInstanceMessage,
        add: Boolean,
        expansions: Map<String, String>,
    ) {
        // 这里的消息包括扩展字段
        updateMessage(message.id) {
            convertToUIMessageEntry(message, true, it)
        }
    }

    @CallSuper
    override fun onMessageExpansionDelete(
        message: UCInstanceMessage,
        deleteKeys: Array<String>,
    ) {
        // 这里的消息包括扩展字段
        updateMessage(message.id) {
            convertToUIMessageEntry(message, true, it)
        }
    }

    @CallSuper
    override fun onReadReceiptReceived(readReceipt: MessageReadReceipt) {
        when (readReceipt) {
            is MessageReadReceipt.Full -> {
                if (readReceipt.timestamp <= latestReadTime) {
                    return
                }
                updateMessages({ combo, _, message ->
                    if (!combo && message.timestamp <= latestReadTime) { // 已经标记过无需再处理
                        0
                    } else if (message.isSelf && message.isSent && !message.isC2CStaticRead && message.timestamp <= readReceipt.timestamp) {
                        if (message.isC2CRead) {
                            1
                        } else { // tim sdk会修改消息缓存，疑似弱引用持有消息对象
                            -1
                        }
                    } else {
                        -1
                    }
                }) { entry ->
                    entry.onlyRefreshCopy()
                }
                latestReadTime = readReceipt.timestamp
            }

            is MessageReadReceipt.Each -> {
                // 暂时废弃了，不会有这个类型的消息了
                var changedCount = 0
                updateMessages({ combo, _, message ->
                    if (!combo && changedCount >= readReceipt.ids.size) {
                        0
                    } else if (message.isSelf &&
                        message.isSent &&
                        !message.isC2CStaticRead &&
                        readReceipt.ids.contains(
                            message.id,
                        )
                    ) {
                        changedCount++
                        1
                    } else {
                        -1
                    }
                }) { entry ->
                    val message = entry.message
                    if (!message.isC2CRead) {
                        // 说明没改过来，要重新查询一遍消息，因为腾讯的消息是没法修改的
                    }
                    entry.onlyRefreshCopy()
                }
            }
        }
    }

    //region 消息转换
    protected fun defaultConvertToUIMessageEntry(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
        oldEntry: UIMessageEntry?,
    ): MsgUIEntry = NoProviderEntry

    protected open fun sceneConvertToUIMessageEntry(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
        oldEntry: UIMessageEntry?,
    ): MsgUIEntry? = null

    /**
     * 将[message]转换为[UIMessageEntry], 更新消息忽略裂变, 只在updateMessage中被调用
     *
     * @param message 需要转换的消息
     * @param includeExtensions [message]中是否包括扩展信息，腾讯的消息可能不包含扩展，此时可以从[oldEntry]复制过来
     * @param oldEntry [msgList]中的老entry，如果[message]中没有扩展信息，则从[oldEntry]复制扩展信息
     */
    protected fun convertToUIMessageEntry(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
        oldEntry: UIMessageEntry?,
    ): UIMessageEntry {
        val uiEntry =
            if (message.isRevoked) {
                createRecallUIMessageEntry(message, includeExtensions, oldEntry)
            } else {
                sceneConvertToUIMessageEntry(message, includeExtensions, oldEntry) ?: defaultConvertToUIMessageEntry(
                    message,
                    includeExtensions,
                    oldEntry,
                )
            }
        return UIMessageEntry(
            message = message,
            uiEntry = uiEntry,
            refresh = oldEntry?.refresh?.nextTypeRefresh() ?: TypeRefresh.Init,
            user = message.user ?: oldEntry?.user ?: BasicUser(message.sender, "", "", ""),
        )
    }

    protected fun defaultConvertToUIMessageEntries(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
    ): List<MsgUIEntry> =
        message.let {
            val user = message.user ?: BasicUser(message.sender, "", "", "")
            if (config.fissionGiftMessageEnable && message is UCGiftMessage) {
                val list = message.gift.receivers
                List(list.size.coerceAtLeast(1)) { index ->
                    GiftMsgEntry(
                        sendUser = user,
                        targetUser = list.getOrNull(index),
                        giftModel = message.gift,
                    )
                }
            } else if (message is UCCustomMessage &&
                (message.cmd == IMEvent.COMMON_CHATROOM_PUBLIC_MESSAGES || message.cmd == IMEvent.FOLLOW_CHATROOM_PUBLIC_MESSAGES)
            ) {
//                val list = message.getJsonValue<List<UserRichList>>("messages")
//                if (list.isNullOrEmpty()) {
//                    emptyList()
//                } else {
//                    val isSingle = list.size == 1
//                    List(list.size) { index ->
//                        UIMessageEntry(
//                            user = list[index].sender ?: user,
//                            message = message,
//                            extra = extra,
//                            refresh = TypeRefresh.Init,
//                            msgIndex = if (isSingle) null else index,
//                        )
//                    }
//                }
                error("")
            } else {
                listOf(
                    if (message.isRevoked) {
                        createRecallUIMessageEntry(message, includeExtensions, null)
                    } else {
                        sceneConvertToUIMessageEntry(message, includeExtensions, null)
                            ?: defaultConvertToUIMessageEntry(
                                message,
                                includeExtensions,
                                null,
                            )
                    },
                )
            }
        }

    protected open fun sceneConvertToUIMessageEntries(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
    ): List<MsgUIEntry>? = null

    protected fun convertToUIMessageEntries(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
    ): List<UIMessageEntry> =
        if (message.isRevoked) {
            listOf(convertToUIMessageEntry(message, includeExtensions, null))
        } else {
            (
                sceneConvertToUIMessageEntries(message, includeExtensions) ?: defaultConvertToUIMessageEntries(
                    message,
                    includeExtensions,
                )
            ).run {
                val isSingle = size < 2
                mapIndexed { index, it ->
                    UIMessageEntry(
                        message = message,
                        uiEntry = it,
                        msgIndex = if (isSingle) null else index,
                        refresh = TypeRefresh.Init,
                        user = message.user ?: BasicUser(message.sender, "", "", ""),
                    )
                }
            }
        }

    protected open fun createRecallUIMessageEntry(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
        oldEntry: UIMessageEntry?,
    ): MsgUIEntry = RecallMsgEntry(if (message.isSent) "你撤回了一条消息" else "对方撤回了一条消息")
    //endregion

    protected fun updateMessage(
        id: String,
        transform: (UIMessageEntry) -> UIMessageEntry,
    ): Boolean {
        var combo = false
        var updated = false
        kotlin.run {
            msgList.forEachIndexed { index, entry ->
                if (entry.isFakerMessage) {
                    if (combo) {
                        // combo模式，没有连续命中则退出遍历
                        return@run
                    }
                    return@forEachIndexed
                }
                if (entry.message.id == id) {
                    updated = true
                    msgList[index] = transform(entry)
                    if (entry.msgIndex == null) { // 可能存在一条消息分裂成多条的情况
                        return@run
                    }
                    combo = true
                } else if (combo) {
                    // combo模式，没有连续命中则退出遍历
                    return@run
                }
            }
        }
        return updated
    }

    protected fun updateMessages(
        predicate: (Boolean, Int, UCInstanceMessage) -> Int, // 第一参数combo, 返回值大于0则更新，等于0则不更新直接返回，小于0则跳过
        transform: (UIMessageEntry) -> UIMessageEntry,
    ): Boolean {
        var combo = false
        var updated = false
        kotlin.run {
            msgList.forEachIndexed { index, entry ->
                if (entry.isFakerMessage) {
                    combo = false
                    return@forEachIndexed
                }
                val message = entry.message
                val filter = predicate(combo, index, message)
                if (filter == 0) { // 直接结束遍历，无需再更新
                    return@run
                }
                if (filter > 0) {
                    updated = true
                    msgList[index] = transform(entry)
                    if (entry.msgIndex != null) {
                        combo = true
                    }
                } else {
                    combo = false
                }
            }
        }
        return updated
    }

    protected fun removeMessageById(id: String) {
        val removedSet = ArraySet<Any>()
        val removedList =
            msgList.filter {
                val message = it.message
                val removed =
                    if (message is UCTimestampMessage) {
                        message.base.id
                    } else {
                        message.id
                    } == id
                if (removed) {
                    removedSet.add(it.key)
                }
                removed
            }
        if (removedList.isNotEmpty()) {
            msgList.removeAll(removedList)
            msgKeySet.removeAll(removedSet)
        }
    }

    protected suspend fun getHistoryMessages(
        key: UCMessage?,
        descendPullOrder: Boolean,
        enableNotify: Boolean,
    ): List<UCInstanceMessage>? {
        if (type == ConversationType.CHATROOM) {
            onlyLocal = true
        }
        var list =
            IMCompatCore.getHistoryMessages(
                id = imId,
                type = type,
                count = config.eachPageSize,
                lastMsg = key,
                descendPullOrder = descendPullOrder,
                onlyLocal = onlyLocal,
                enableNotify = enableNotify,
            )
        if (type != ConversationType.CHATROOM && onlyLocal && list.isNullOrEmpty()) {
            onlyLocal = false
            list =
                IMCompatCore.getHistoryMessages(
                    id = imId,
                    type = type,
                    count = config.eachPageSize,
                    lastMsg = key,
                    descendPullOrder = descendPullOrder,
                    onlyLocal = false,
                    enableNotify = enableNotify,
                )
        }

        if (!list.isNullOrEmpty()) {
            callbackPlayGiftEffects(list)
        }
        return list
    }

    /**
     * 获取message ui上需要显示的额外字段，可以自行实现
     * @return 扩展信息，如果为[MsgUIExtra.CACHE], 则使用消息缓存的扩展, 默认使用缓存
     */
    private fun getMessageUIExtra(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
    ): MsgUIExtra? {
//        return if (includeExtensions && message.isSupportMessageExtension) {
//            UIMessageUtils.parseMessageExtra(message)?.let {
//                MsgUIExtra(expansionExtra = it)
//            }
//        } else {
//            MsgUIExtra.CACHE
//        }
        return null
    }

    private fun callbackPlayGiftEffects(list: List<UCInstanceMessage>) {
        if (config.autoPlayNoPlayedGiftEffect) {
            val giftMessageList =
                list.mapNotNull {
                    if (it is UCGiftMessage && !it.isSelf && !it.isPlayed) {
                        it
                    } else {
                        null
                    }
                }
            if (giftMessageList.isNotEmpty()) {
                IMCompatCore.dispatcher.synchronizedDispatchListener {
                    if (it.match(sendParams.receiver)) {
                        it.onPlayHistoryGiftEffect(giftMessageList)
                    }
                }
            }
        }
    }

    private fun filter(message: UCInstanceMessage): Boolean {
        if (msgKeySet.contains(message.id)) {
            if (message.id != "0") {
                return false
            }
        }
        if (!UIMessageUtils.isVisible(message)) {
            return false
        }
        return filterShownMessage(message).also {
            if (it) {
                msgKeySet.add(message.id)
            }
        }
    }

    private fun addEntries(
        entries: List<UIMessageEntry>,
        index: Int = -1,
    ) {
        if (entries.isEmpty()) {
            return
        }

        val oldSize = msgList.size

        val safeIndex =
            if (index < 0 || index >= oldSize) {
                -1
            } else {
                index
            }

        if (safeIndex == -1) {
            msgList.addAll(entries)
        } else {
            msgList.addAll(safeIndex, entries)
        }

        if (!config.insetTimeLine) {
            return
        }

        // 对于这个列表最老的一条消息处理，老消息在视觉上是在上方的
        var lastEntry = entries.last()
        if (safeIndex == -1) { // 最老的一条消息，也需要显示时间戳
            addTimestampMsgEntry(lastEntry, msgList.size)
        } else {
            val nextTopEntry = msgList[safeIndex + entries.size] // 通过逻辑控制，在线程安全的情况下不可能为空
            if (abs(lastEntry.message.timestamp.minus(nextTopEntry.message.timestamp)) >= config.eachTimeLineGap) {
                addTimestampMsgEntry(lastEntry, safeIndex + entries.size)
            }
        }

        // 对于这个[entries]的其他消息处理，添加是否需要显示时间戳
        var i = entries.size - 2
        while (i >= 0) {
            val nextTopEntry = lastEntry
            lastEntry = entries[i]
            if (abs(lastEntry.message.timestamp.minus(nextTopEntry.message.timestamp)) >= config.eachTimeLineGap) {
                addTimestampMsgEntry(lastEntry, (if (safeIndex == -1) oldSize else safeIndex) + i + 1)
            }
            i--
        }

        if (safeIndex == -1 && oldSize > 0) { // 如果添加的消息都是老消息，那么需要检查是否移除前最老消息时间戳
            val lastTopEntry = msgList[oldSize - 1]
            if (lastTopEntry.message is UCTimestampMessage &&
                abs(
                    entries
                        .first()
                        .message.timestamp
                        .minus(lastTopEntry.message.timestamp),
                ) < config.eachTimeLineGap
            ) {
                msgList.removeAt(oldSize - 1)
            }
        }
    }

    /**
     * 添加时间戳
     */
    private fun addTimestampMsgEntry(
        entry: UIMessageEntry,
        index: Int,
    ) {
        msgList.add(
            index,
            UIMessageEntry(
                message = UCTimestampMessage(entry.message.base),
                uiEntry = TimeMsgEntry(entry.message.timestamp),
                isFakerMessage = true,
            ),
        )
    }

    private fun tryScrollToLatestMsgIndex() {
        if (config.autoScrollToLatestMsgIndex) {
            viewModelScope.launch {
                uiEffect.emit(Effect.OnScrollToLatestMsg)
            }
        }
    }

    private suspend fun loadUnreadInfo() {
        if (sendParams.type == ConversationType.GROUP) {
            val conversation = IMCompatCore.getIMConversation(imId, type, false)

            if (conversation != null && conversation.unreadCount > 4) { // 未读数太少第一屏渲染就在视野里面了，可以不显示
                val firstUnreadMsgSequence = conversation.firstUnreadMsgSequence
                if (firstUnreadMsgSequence <= 0) {
                    return
                }

                _unreadMsgInfo = ConversationUnreadInfo(conversation.unreadCount, firstUnreadMsgSequence)

                val atInfoList = conversation.atInfoList

                if ((atInfoList?.atMeCount ?: 0) > 0) {
                    _unreadAtInfo = atInfoList
                }

                tryAddNewMsgTag()
            }
        }

        if (config.autoCleanUnreadCount) {
            cleanConversationUnreadCount()
        }
    }

    private fun tryAddNewMsgTag() {
        if (!addedNewMsgTag && config.hasAddNewMsgTag) {
            val unreadMsgInfo = _unreadMsgInfo
            if (unreadMsgInfo != null) {
                val index =
                    msgList.indexOfLast {
                        it.message.sequence == unreadMsgInfo.sequence
                    }
                if (index != -1) {
                    addedNewMsgTag = true
                    msgList.add(
                        index + 1,
                        UIMessageEntry(
                            message = UCNewMsgTagMessage(msgList[index].message.base),
                            uiEntry = TimeTagMsgEntry("以下为新消息"),
                            isFakerMessage = true,
                        ),
                    )
                }
            }
        }
    }

    private suspend fun scrollToUnreadMsg(sequence: Long): Boolean {
        if (sequence <= 0) {
            return false
        }

        var index =
            msgList.indexOfLast {
                it.message.sequence == sequence
            }

        if (index > -1) {
            if (index < msgList.lastIndex) {
                index++
            }
            uiEffect.emit(Effect.OnScrollToIndex(index))
            return true
        }

        onlyLocal = true

        val list =
            IMCompatCore.getHistoryMessages(imId, type, sequence, config.eachPageSize - 1, config.eachPageSize)?.also {
                // 回调未播放礼物特效
                callbackPlayGiftEffects(it)
            }

        if (!list.isNullOrEmpty()) {
            // 先禁用分页
            resetNextPageKey(false, null, false, null)

            // 更新消息列表
            updateMessageList(list)

            delay(300)
            index =
                msgList.indexOfLast {
                    it.message.base.sequence == sequence
                }

            if (index > -1) {
                if (index < msgList.lastIndex) {
                    index++
                }
                uiEffect.emit(Effect.OnScrollToIndex(index))
                return true
            }
        }

        // 没有找到目标消息
        return false
    }

    /**
     * 要检查新消息和现有消息的连续性，如果不连续应该直接替换掉
     */
    private fun updateMessageList(messages: List<UCInstanceMessage>) {
        fun addMessages(index: Int = -1) {
            val entries =
                messages.flatMap {
                    if (filter(it)) {
                        convertToUIMessageEntries(it, true)
                    } else {
                        emptyList()
                    }
                }
            addEntries(entries, index)
        }

        fun changeNextPageKey(
            oldEnable: Boolean?,
            oldMsgKey: UCInstanceMessage?,
            newEnable: Boolean?,
            newKey: UCInstanceMessage?,
        ) {
            viewModelScope.launch {
                delay(500)
                resetNextPageKey(oldEnable, oldMsgKey, newEnable, newKey)
            }
        }

        if (msgList.isEmpty()) {
            addMessages()
            changeNextPageKey(true, messages.lastOrNull(), true, messages.firstOrNull())
            return
        }

        val curNewer = msgList.first().message
        val curOlder = msgList.last().message

        val newer = messages.first()
        val older = messages.last()

        if (older.timestamp > curNewer.timestamp) {
            msgKeySet.clear()
            msgList.clear()
            addMessages()
            changeNextPageKey(true, messages.lastOrNull(), true, messages.firstOrNull())
        } else if (newer.timestamp < curOlder.timestamp) {
            msgKeySet.clear()
            msgList.clear()
            addMessages()
            changeNextPageKey(true, messages.lastOrNull(), true, messages.firstOrNull())
        } else if (newer.timestamp >= curNewer.timestamp && older.timestamp <= curOlder.timestamp) {
            msgKeySet.clear()
            msgList.clear()
            addMessages()
            changeNextPageKey(true, messages.lastOrNull(), true, messages.firstOrNull())
        } else if (newer.timestamp > curNewer.timestamp && older.timestamp > curOlder.timestamp) {
            addMessages(0)
            changeNextPageKey(null, null, true, messages.firstOrNull())
        } else if (newer.timestamp < curNewer.timestamp && older.timestamp < curOlder.timestamp) {
            addMessages()
            changeNextPageKey(true, messages.lastOrNull(), null, null)
        } else {
            addMessages()
            msgList.sortByDescending {
                it.message.timestamp
            }
            changeNextPageKey(true, messages.lastOrNull(), true, messages.firstOrNull())
        }

        tryAddNewMsgTag()
    }

    @Stable
    protected sealed interface Effect {
        data object OnScrollToLatestMsg : Effect

        data class OnScrollToIndex(
            val indexOfMsgList: Int,
        ) : Effect
    }
}
