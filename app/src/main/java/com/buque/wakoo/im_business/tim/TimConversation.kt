package com.buque.wakoo.im_business.tim

import com.buque.wakoo.im.inter.UCConversation
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.bean.ConversationAtInfo
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im_business.tim.TIMConverter.convId2Id
import com.buque.wakoo.im_business.tim.TIMConverter.toUCInstanceMessage
import com.buque.wakoo.im.utils._isAppRelease
import com.tencent.imsdk.v2.V2TIMConversation
import com.tencent.imsdk.v2.V2TIMGroupAtInfo
import com.tencent.imsdk.v2.V2TIMManager

class TIMConversation constructor(conversation: V2TIMConversation) : UCConversation {

    /**
     * 原始消息
     */
    val rawConversation: V2TIMConversation = conversation

    /**
     * 会话id，和[IUCMessage.targetId]含义相同
     */
    override val id: String = rawConversation.userID ?: rawConversation.groupID.orEmpty()

    /**
     * 会话类型
     */
    override val type: ConversationType = if (rawConversation.type == V2TIMConversation.V2TIM_C2C) {
        ConversationType.C2C
    } else {
        when (rawConversation.groupType) {
            V2TIMManager.GROUP_TYPE_MEETING -> ConversationType.CHATROOM
            V2TIMManager.GROUP_TYPE_PUBLIC -> ConversationType.GROUP
            V2TIMManager.GROUP_TYPE_AVCHATROOM -> ConversationType.RTM
            else -> {
                if (_isAppRelease) {
                    ConversationType.GROUP
                } else {
                    error("not supported group type ${rawConversation.conversationID} ${rawConversation.groupType}")
                }
            }
        }
    }

    override val timestamp: Long
        get() = lastMessage?.timestamp ?: 0

    /**
     * 会话展示名称
     */
    override val name: String
        get() = rawConversation.showName.orEmpty()

    /**
     * 会话展示图标
     */
    override val iconUrl: String
        get() = rawConversation.faceUrl.orEmpty()

    /**
     * 未读消息数量
     */
    override val unreadCount: Int
        get() = rawConversation.unreadCount

    /**
     * 会话草稿文本
     */
    override val draftText: String?
        get() = rawConversation.draftText

    /**
     * 会话是否置顶
     */
    override val isPinned: Boolean
        get() = rawConversation.isPinned

    /**
     * 会话排序字段
     */
    override val orderKey: Long
        get() = rawConversation.orderKey

    /**
     * 第一条未读消息的Sequence
     */
    override val firstUnreadMsgSequence: Long
        get() = if (type == ConversationType.C2C) {
            rawConversation.c2CReadTimestamp
        } else {
            rawConversation.groupReadSequence.plus(1)
        }

    override val atInfoList: ConversationAtInfo
        get() = rawConversation.groupAtInfoList.run {
            var count = 0
            val sequenceList = this?.mapNotNull {
                if (it.atType == V2TIMGroupAtInfo.TIM_AT_ME || it.atType == V2TIMGroupAtInfo.TIM_AT_ALL || it.atType == V2TIMGroupAtInfo.TIM_AT_ALL_AT_ME) {
                    count++
                    it.seq
                } else {
                    null
                }
            }
            ConversationAtInfo(count, sequenceList?.takeIf { it.isNotEmpty() })
        }

    /**
     * 会话最后一条消息
     */
    override val lastMessage: UCInstanceMessage? = rawConversation.lastMessage?.let {
        TIMMessage(it).toUCInstanceMessage()
    }
    override val stableId: String
        get() = id.convId2Id()

    override fun toString(): String {
        return "TIMConversation(id:${id}, name:${name} ,content: ${lastMessage?.toMsgString()})"
    }
}
