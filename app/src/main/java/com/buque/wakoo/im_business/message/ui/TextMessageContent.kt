package com.buque.wakoo.im_business.message.ui

import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.tooling.preview.Preview
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.types.UCTextMessage
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent

data class TextMessageContent(val message: UCInstanceMessage) : MsgLayoutContent() {

    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction
    ) {
        baseBox {
            bubbleBox {
                val msg = message as UCTextMessage
                Text(text = msg.text)
            }
        }
    }
}


@Preview
@Composable
private fun PreviewText() {

}