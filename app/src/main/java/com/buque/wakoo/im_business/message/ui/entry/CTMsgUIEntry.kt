package com.buque.wakoo.im_business.message.ui.entry

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import com.buque.wakoo.im_business.interf.IIMAction

@Stable
abstract class MsgLayoutContent : MsgUIEntry {
    /**
     * 默认绘制样式
     *
     * @param message 消息实体
     * @param baseBox 最外部的包裹, 包含用户头像/昵称等信息, 不需要可以不用
     * @param bubbleBox 气泡包裹, 不需要可以不用
     * @param onAction 点击事件回调
     */
    @Composable
    protected abstract fun RenderDefault(
        baseBox: @Composable (@Composable () -> Unit) -> Unit,
        bubbleBox: @Composable (@Composable () -> Unit) -> Unit,
        onAction: IIMAction,
    )

    /**
     * 渲染UI
     *
     * @param message 消息实体
     * @param baseBox 最外部的包裹, 包含用户头像/昵称等信息, 不需要可以不用
     * @param bubbleBox 气泡包裹, 不需要可以不用
     * @param type 消息场景, 可以根据不同的消息场景来变更不同的样式
     * @param onAction 点击事件回调
     */
    @Composable
    open fun Render(
        userInfoWrapper: @Composable (content: @Composable () -> Unit) -> Unit,
        bubbleWrapper: @Composable (content: @Composable () -> Unit) -> Unit,
        onAction: IIMAction,
    ) {
        RenderDefault(userInfoWrapper, bubbleWrapper, onAction)
    }

    @Composable
    fun Render(
        onAction: IIMAction,
    ) {
        Render({ it() }, { it() }, onAction)
    }
}
