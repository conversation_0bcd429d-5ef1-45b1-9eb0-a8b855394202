package com.qyqy.cupid.im.messages

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent


data class RecallMessageContent(val message: UCInstanceMessage) : MsgLayoutContent() {
    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction
    ) {
        val text = if (message.isSelf) {
            stringResource(id = R.string.你撤回了一条消息)
        } else if (message.isC2CMsg) {
            stringResource(id = R.string.对方撤回了一条消息)
        } else {
            stringResource(id = R.string.撤回了一条消息, message.user?.name ?: "")
        }
        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            Text(
                text = text,
                color = Color(0xFF86909C),
                fontSize = 12.sp
            )
        }
    }

}