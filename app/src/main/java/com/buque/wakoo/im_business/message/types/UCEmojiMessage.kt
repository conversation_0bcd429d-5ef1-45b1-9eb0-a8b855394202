package com.buque.wakoo.im_business.message.types

import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.inter.IUCCustomMessage
import com.buque.wakoo.im.inter.IUCMessage


data class UCEmojiMessage(
    val rawInstanceMessage: UCCustomMessage,
    val type: Int,
    val value: String,
    override val base: UCMessage = rawInstanceMessage.base,
) : UCInstanceMessage, IUCMessage by rawInstanceMessage, IUCCustomMessage by rawInstanceMessage {

    companion object {
        const val TYPE_DICE = 1
        const val TYPE_NUMBER_1 = 2
        const val TYPE_NUMBER_3 = 3
        const val TYPE_NUMBER_5 = 4
        const val TYPE_GUESSING_FIST = 5
    }

    override fun getSummaryString(): String {
        val name = when (type) {
            TYPE_DICE -> "骰子"
            in TYPE_NUMBER_1..TYPE_NUMBER_5 -> "幸运数字"
            TYPE_GUESSING_FIST -> "猜拳"
            else -> "未知"
        }
        return "表情消息(${name}), type: $type, name: $value"
    }
}
