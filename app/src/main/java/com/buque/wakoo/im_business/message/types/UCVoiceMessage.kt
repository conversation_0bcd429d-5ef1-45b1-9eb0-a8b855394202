package com.buque.wakoo.im_business.message.types

import android.net.Uri
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.inter.IUCMessage

data class UCVoiceMessage(
    override val base: UCMessage,
    val duration: Int,
    val url: String,
    val localPath: String? = null,
    val uuid: String? = null,
) : UCInstanceMessage,
    IUCMessage by base {
    override fun getSummaryString(): String = "语音消息, 时长: $duration, url: $url"
}
