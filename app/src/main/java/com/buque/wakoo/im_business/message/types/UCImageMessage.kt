package com.buque.wakoo.im_business.message.types

import android.net.Uri
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.inter.IUCMessage

data class ImageElem(
    val width: Int,
    val height: Int,
    val url: String,
    val localUri: Uri?,
    val uuid: String?,
)

data class UCImageMessage(
    override val base: UCMessage,
    val thumbElem: ImageElem?,
    val largeElem: ImageElem?,
    val originElem: ImageElem?,
) : UCInstanceMessage, IUCMessage by base {

    init {
        require(thumbElem != null || largeElem != null || originElem != null)
    }

    val previewElem: ImageElem
        get() {
            if (largeElem?.localUri != null) {
                return largeElem
            }
            if (originElem?.localUri != null) {
                return originElem
            }
            return (largeElem ?: originElem ?: thumbElem)!!
        }

    override fun getSummaryString(): String {
        return "图片消息, 宽: ${previewElem.width}, 高: ${previewElem.height}, url: ${previewElem.url}"
    }

//    fun toPhotoPreviewModel(): PhotoPreviewModel {
//        val transitionElem = thumbElem ?: previewElem
//
//        val source = transitionElem.toImageSource()
//
//        val aspectRatio = if (transitionElem.width > 0 && transitionElem.height > 0) {
//            transitionElem.width.toFloat().div(transitionElem.height)
//        } else {
//            -1f
//        }
//
//        return PhotoPreviewModel(
//            key = id,
//            aspectRatio = aspectRatio,
//            transitionSource = source,
//            previewSource = previewElem.toImageSource(),
//            originSource = originElem?.toImageSource(),
//        )
//    }
//
//    private fun ImageElem.toImageSource(): IImageSource {
//        return if (localUri != null) {
//            IImageSource.Uri(localUri)
//        } else {
//            IImageSource.Url(url)
//        }
//    }
}
