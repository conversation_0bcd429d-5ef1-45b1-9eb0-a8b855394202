package com.buque.wakoo.im_business.message.ui.custom

//object UserEnteranceContent : MsgLayoutContent() {
//    @Composable
//    override fun RenderDefault(
//        message: UCInstanceMessage,
//        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
//        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
//        onAction: IIMAction
//    ) {
////        {"user": {"userid": 4481, "public_id": "106238", "nickname": "Default Nickname", "avatar_url": "https://s.test.ucoofun.com/aaceGV?x-oss-process=image/format,webp", "gender": 1, "age": 18, "height": 0, "avatar_frame": "", "medal": null, "medal_list": [], "level": 0, "country_flag": "https://media.wakooclub.com/opsite%2Fcountryflag%2FL_slices%2FCN.png", "exp_level_info": {"charm_level": 1, "wealth_level": 1}, "have_certified": false, "is_member": false, "is_newbie": false, "public_cp": null, "cp_extra_info": null, "special_effect_file": "", "entrance_banner": null}, "happen_timestamp": 1751421963400, "digest": "", "is_hidden": false, "audience_cnt": 1}
//        val msg = message.asInstance<UCCustomMessage>()!!
//        val user = msg.user!!
//        MessageThemeBubble(
//            defaultMsgTheme = MessageTheme(
//                painter = ColorPainter(Color(0x1AFFFFFF)),
//                paddingValues = PaddingValues(horizontal = 8.dp, vertical = 4.dp),
//                shape = RoundedCornerShape(8.dp),
//                contentColor = Color.White,
//                fontSize = 14.sp,
//                left = false,
//            )
//        ) {
//            Row(verticalAlignment = Alignment.CenterVertically) {
//                AvatarNetworkImage(user, size = 28.dp)
//                Spacer(Modifier.width(4.dp))
//                Text(buildAnnotatedString {
//                    withStyle(
//                        SpanStyle(color = Color(0xffffd683))
//                    ) {
//                        append(user.name)
//                    }
//                    append(" ")
//                    append("进入了房间")
//                }, style = TextStyle(color = Color.White, fontSize = 14.sp))
//            }
//        }
//
//
//    }
//}