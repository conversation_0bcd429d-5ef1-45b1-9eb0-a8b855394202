package com.buque.wakoo.im_business.interf

import androidx.compose.runtime.Stable
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im.UCInstanceMessage
import java.lang.reflect.InvocationHandler
import java.lang.reflect.Proxy

interface IIMAction {
    companion object {
        val EMPTY = object : IIMAction {}
    }

    fun onSendMessage(message: MessageBundle) = Unit

    fun onSendMultipleMessage(messages: List<MessageBundle>) = Unit

    fun onResendMessage(message: UCInstanceMessage) = Unit

    fun onPreview(message: UCInstanceMessage) = Unit

    fun onGoMediaSelector() = Unit

}

interface IUserOperationAction {
    fun onForbidUser() = Unit

    fun onReportUser() = Unit
}

@Stable
interface IC2CAction :
    IIMAction,
    IUserOperationAction {
    companion object {
        val Empty = object : IC2CAction by noOpDelegate() {}
    }

    fun onShowGiftPanel()
}

private val NO_OP_HANDLER =
    InvocationHandler { _, _, _ ->
        // no op
    }

private inline fun <reified T : Any> noOpDelegate(): T {
    val javaClass = T::class.java
    return Proxy.newProxyInstance(
        javaClass.classLoader,
        arrayOf(javaClass),
        NO_OP_HANDLER,
    ) as T
}
