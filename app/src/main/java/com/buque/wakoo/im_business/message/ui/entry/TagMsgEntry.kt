package com.buque.wakoo.im_business.message.ui.entry

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.buque.wakoo.bean.User
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.panel.LiveRoomUserInfoPanel
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage

data class TimeTagMsgEntry(
    val tag: AnnotatedString,
) : MsgUIEntry {
    constructor(tag: String) : this(buildAnnotatedString { append(tag) })
}

data class LiveRoomSystemMsgEntry(
    val content: AnnotatedString,
) : MsgUIEntry {
    constructor(tag: String) : this(buildAnnotatedString { append(tag) })
}

@Composable
fun LiveRoomSystemMsgEntry.RoomContent(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    val density = LocalDensity.current
    val fontSize =
        with(density) {
            14.dp.toPx().toSp()
        }

    Box(
        modifier =
            modifier
                .padding(end = 35.dp)
                .background(Color(0x1AFFFFFF), RoundedCornerShape(8.dp))
                .padding(horizontal = 8.dp, vertical = 4.dp),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = content,
            fontSize = fontSize,
            lineHeight = fontSize * 1.3f,
            color = Color.White.copy(alpha = 0.5f),
        )
    }
}

data class LiveRoomUserSystemMsgEntry(
    val content: AnnotatedString,
    val user: User,
) : MsgUIEntry {
    constructor(tag: String, user: User) : this(buildAnnotatedString { append(tag) }, user)
}

@Composable
fun LiveRoomUserSystemMsgEntry.RoomContent(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    val density = LocalDensity.current
    val fontSize =
        with(density) {
            14.dp.toPx().toSp()
        }

    Row(
        modifier =
            modifier
                .padding(end = 35.dp)
                .background(Color(0x1AFFFFFF), RoundedCornerShape(8.dp))
                .padding(horizontal = 8.dp, vertical = 4.dp),
    ) {
        AvatarNetworkImage(
            user = user,
            size = 28.dp,
            onClick = {
                roomInfoState.sendEvent(
                    RoomEvent.PanelDialog {
                        LiveRoomUserInfoPanel(user, roomInfoState)
                    },
                )
            },
        )
        SizeWidth(4.dp)

        Text(
            text = content,
            modifier = Modifier.padding(top = 5.dp),
            fontSize = fontSize,
            lineHeight = fontSize * 1.3f,
            color = Color.White.copy(alpha = 0.5f),
        )
    }
}
