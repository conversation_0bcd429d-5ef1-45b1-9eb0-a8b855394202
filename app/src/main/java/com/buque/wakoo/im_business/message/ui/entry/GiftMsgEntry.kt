package com.buque.wakoo.im_business.message.ui.entry

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.unit.dp
import com.buque.wakoo.bean.GiftWrapper
import com.buque.wakoo.bean.User
import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.im_business.message.MessageTheme
import com.buque.wakoo.im_business.message.MessageThemeBubble
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.panel.LiveRoomUserInfoPanel
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.VipCrownTag
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.richtext.RichText

data class GiftMsgEntry(
    val sendUser: User,
    val targetUser: User?,
    val giftModel: GiftWrapper,
) : MsgUIEntry

@Composable
fun GiftMsgEntry.RoomContent(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    val density = LocalDensity.current
    val fontSize =
        with(density) {
            14.dp.toPx().toSp()
        }

    Column(modifier = modifier.padding(end = 35.dp)) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            AvatarNetworkImage(
                user = sendUser,
                size = 28.dp,
                onClick = {
                    roomInfoState.sendEvent(
                        RoomEvent.PanelDialog {
                            LiveRoomUserInfoPanel(sendUser, roomInfoState)
                        },
                    )
                },
            )

            SizeWidth(8.dp)

            RichText(
                fontSize = fontSize,
                color = Color.White,
            ) {
                append(sendUser.name)
                if (sendUser.isVip) {
                    append("  ")
                    InlineSizedContent(48.dp, 18.dp) {
                        VipCrownTag()
                    }
                }
                if (sendUser is UserInfo) {
                    sendUser.extra.medalList?.forEach {
                        append(" ")
                        InlineSizedContent(it.width.dp, it.height.dp) {
                            NetworkImage(
                                data = it.icon,
                                modifier = Modifier.size(it.width.dp, it.height.dp),
                            )
                        }
                    }
                }
            }
        }

        MessageThemeBubble(
            defaultMsgTheme =
                MessageTheme(
                    painter = ColorPainter(Color(0x1AFFFFFF)),
                    paddingValues = PaddingValues(horizontal = 8.dp, vertical = 10.dp),
                    shape = RoundedCornerShape(8.dp),
                    contentColor = Color.White,
                    fontSize = fontSize,
                    left = true,
                ),
            modifier = Modifier.padding(start = 36.dp),
        ) {
            RichText {
                val receiverName = targetUser?.name ?: "所有人"
                val count = giftModel.count.coerceAtLeast(1).toString()
                val giftName = giftModel.gift.name
                val content =
                    if (giftModel.isBlinxBox) {
//                        "送给 %1$s %2$s个%3$s，开出了限定礼物%4$s"
                        "赠送了 $receiverName ${count}个${giftModel.blindboxName.orEmpty()}，开出了限定礼物$giftName"
                    } else if (giftModel.isCupidLuckyGift) {
//                        "送给 %1$s %2$s个%3$s, 抽中了%4$s金币"
                        "赠送了 $receiverName ${count}个$giftName, 抽中了${giftModel.comboCoin}钻石"
                    } else {
//                        "送给 %1$s %2$s个%3$s"
                        "赠送了 $receiverName ${count}个$giftName"
                    }

                withBuilder {
                    append(content)

                    var start = content.indexOf(receiverName, 0)
                    var end = start.plus(receiverName.length)

                    addStyle(SpanStyle(color = Color(0xFF66FE6B)), start, end)

                    if (giftModel.isBlinxBox) {
                        start = content.indexOf(giftName, end)
                        end = start.plus(giftName.length)
                        addStyle(SpanStyle(color = Color(0xFFFFEE56)), start, end)
                    }
                }

                if (!giftModel.isCupidLuckyGift) {
                    append(" ")
                    InlineSizedContent(20.dp, 20.dp) {
                        NetworkImage(data = giftModel.gift.icon)
                    }
                }

                append(" ")
            }
        }
    }
}
