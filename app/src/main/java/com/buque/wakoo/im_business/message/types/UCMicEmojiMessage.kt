package com.buque.wakoo.im_business.message.types

import com.buque.wakoo.im.bean.EmojiMessageContent
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.inter.IUCCustomMessage
import com.buque.wakoo.im.inter.IUCMessage


data class UCMicEmojiMessage(
    val rawInstanceMessage: UCCustomMessage,
    val content: EmojiMessageContent,
    override val base: UCMessage = rawInstanceMessage.base,
) : UCInstanceMessage, IUCMessage by rawInstanceMessage, IUCCustomMessage by rawInstanceMessage {

    companion object {
        const val TYPE_WEBP = 1
        const val TYPE_WEBP_AND_RESULT = 2

        fun isSupportEmoji(content: EmojiMessageContent) = content.type == 1 || content.type == 2
    }

    val type: Int
        get() = content.type

//    override val user: AppUser
//        get() = content.user

    override fun getSummaryString(): String {
        val type = when (content.type) {
            TYPE_WEBP -> "普通动态表情"
            TYPE_WEBP_AND_RESULT -> "带结果的动态表情"
            else -> "${content.type}"
        }
        return "表情消息(${content.emojiEffect.name}), type: $type"
    }
}
