package com.buque.wakoo.im_business.panel.emoji

import android.view.KeyEvent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.awaitLongPressOrCancellation
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.AwaitPointerEventScope
import androidx.compose.ui.input.pointer.PointerEventPass
import androidx.compose.ui.input.pointer.PointerInputScope
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.getTextAfterSelection
import androidx.compose.ui.text.input.getTextBeforeSelection
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.fastAny
import com.buque.wakoo.R
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.wigets.EmojiPanel
import com.buque.wakoo.im_business.wigets.KeyboardPanelState
import com.buque.wakoo.ui.widget.SolidButton
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlin.coroutines.cancellation.CancellationException


@Composable
fun EmojiPanel(
    panelState: KeyboardPanelState,
    textFieldValue: MutableState<TextFieldValue>,
    modifier: Modifier = Modifier,
    emojiSource: Array<String>? = null,
    onAction: IIMAction,
) {
    val emojiPanel = panelState[EmojiPanel]

    val sources = emojiSource?.takeIf { it.isNotEmpty() } ?: with(LocalContext.current) {
        remember {
            resources.getIntArray(R.array.rc_emoji_code).map {
                val chars = Character.toChars(it)
                buildString {
                    chars.forEach { c -> append(c) }
                }
            }.toTypedArray()
        }
    }


    val showBtn by remember {
        derivedStateOf {
            textFieldValue.value.text.isNotEmpty() && panelState.isShowing(emojiPanel)
        }
    }

    Column(modifier = modifier.fillMaxWidth()) {
        LazyVerticalGrid(
            columns = GridCells.Fixed(7),
            modifier = Modifier.weight(1f),
        ) {
            items(sources) {
                Box(
                    modifier = Modifier
                        .height(36.dp)
                        .clickable {
                            textFieldValue.value = textFieldValue.value.insertText(it)
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = it,
                        fontSize = 25.sp,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }

        Row(
            modifier = Modifier
                .padding(bottom = 15.dp)
                .fillMaxWidth()
                .height(52.dp)
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(36.dp)
                    .clip(MaterialTheme.shapes.small)
                    .background(Color(0x0D000000))
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_emoji_default_tab),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .size(24.dp)
                )
            }

            Spacer(modifier = Modifier.weight(1f))

            AnimatedVisibility(visible = showBtn) {
                Row {

                    var deleting by remember {
                        mutableStateOf(false)
                    }

                    val composeView = LocalView.current

                    if (deleting) {
                        LaunchedEffect(key1 = Unit) {
                            while (isActive) {
                                composeView.dispatchKeyEvent(KeyEvent(KeyEvent.ACTION_DOWN, KeyEvent.KEYCODE_DEL))
                                delay(80)
                            }
                        }
                    }

                    Image(
                        painter = painterResource(id = R.drawable.ic_emoji_del),
                        contentDescription = null,
                        modifier = Modifier
                            .size(58.dp, 28.dp)
                            .clip(CircleShape)
                            .clickable {
                                composeView.dispatchKeyEvent(KeyEvent(KeyEvent.ACTION_DOWN, KeyEvent.KEYCODE_DEL))
                            }
                            .pointerInput(key1 = Unit) {
                                awaitEachGesturePlus({
                                    deleting = false
                                }) {
                                    val down = awaitFirstDown(requireUnconsumed = false)
                                    awaitLongPressOrCancellation(down.id)?.also {
                                        deleting = true
                                    }
                                }
                            }
                    )

                    SolidButton(
                        text = "发送",
                        modifier = Modifier
                            .padding(start = 8.dp)
                            .size(58.dp, 28.dp),
                        fontSize = 13.sp,
//                        textStyle = LocalTextStyle.current.copy(fontWeight = FontWeight.Medium)
                        onClick = {
                            val text = textFieldValue.value.text
                            if (text.isNotBlank()) {
                                onAction.onSendMessage(MessageBundle.Text.create(text))
                                textFieldValue.value = TextFieldValue()
                            } else {
//                                    toastRes(R.string.不能发送空白消息)
                            }
                        }
                    )
                }
            }
        }
    }
}

private fun TextFieldValue.insertText(text: String): TextFieldValue {
    val max = this.text.length
    val textBeforeSelection = getTextBeforeSelection(max)
    val textAfterSelection = getTextAfterSelection(max)
    val t = textBeforeSelection + AnnotatedString(text) + textAfterSelection
    val sel = if (selection.collapsed) {
        TextRange(selection.start + text.length)
    } else {
        selection
    }
    return this.copy(t, sel)
}

private fun AwaitPointerEventScope.allPointersUp(): Boolean =
    !currentEvent.changes.fastAny { it.pressed }

private suspend fun AwaitPointerEventScope.awaitAllPointersUp() {
    if (!allPointersUp()) {
        do {
            val events = awaitPointerEvent(PointerEventPass.Final)
        } while (events.changes.fastAny { it.pressed })
    }
}

private suspend fun PointerInputScope.awaitEachGesturePlus(
    upOrCancel: AwaitPointerEventScope.() -> Unit = {},
    block: suspend AwaitPointerEventScope.() -> Unit = {},
) {
    val currentContext = currentCoroutineContext()
    awaitPointerEventScope {
        while (currentContext.isActive) {
            try {
                block()

                // Wait for all pointers to be up. Gestures start when a finger goes down.
                awaitAllPointersUp()
            } catch (e: CancellationException) {
                if (currentContext.isActive) {
                    // The current gesture was canceled. Wait for all fingers to be "up" before
                    // looping again.
                    awaitAllPointersUp()
                } else {
                    // detectGesture was cancelled externally. Rethrow the cancellation exception to
                    // propagate it upwards.
                    throw e
                }
            } finally {
                upOrCancel()
            }
        }
    }
}