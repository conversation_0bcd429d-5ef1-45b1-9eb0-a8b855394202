package com.buque.wakoo.im_business.message.ui.entry

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState

data class RecallMsgEntry(
    val content: AnnotatedString,
) : MsgUIEntry {
    constructor(tag: String) : this(buildAnnotatedString { append(tag) })
}

@Composable
fun RecallMsgEntry.RoomContent(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    val density = LocalDensity.current
    val fontSize =
        with(density) {
            14.dp.toPx().toSp()
        }

    Box(
        modifier =
            modifier
                .padding(end = 35.dp)
                .background(Color(0x1AFFFFFF), RoundedCornerShape(8.dp))
                .padding(horizontal = 8.dp, vertical = 4.dp),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = content,
            fontSize = fontSize,
            lineHeight = fontSize * 1.3f,
            color = Color.White.copy(alpha = 0.5f),
        )
    }
}
