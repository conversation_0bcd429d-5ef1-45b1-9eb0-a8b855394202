package com.buque.wakoo.im_business.message.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.sp
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.types.UCTimestampMessage
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
import java.util.Locale


data class TimeLineContent(val message: UCInstanceMessage) : MsgLayoutContent() {

    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction
    ) {
        val msg = message as UCTimestampMessage
        val context = LocalContext.current
        val locale = Locale.getDefault()
        val time = remember(context, locale) {
            message.getMessageTimeFormatText(context, locale)
        }
        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            Text(
                text = time,
                color = Color(0xFF86909C),
                fontSize = 12.sp
            )
        }
    }
}
