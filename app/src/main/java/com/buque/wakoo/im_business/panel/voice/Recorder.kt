package com.buque.wakoo.im_business.panel.voice

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.verticalDrag
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.manager.AudioRecordManager
import com.buque.wakoo.manager.RecordingEndReason
import com.buque.wakoo.manager.RecordingState
import com.buque.wakoo.ui.icons.Delete
import com.buque.wakoo.ui.icons.MicLine
import com.buque.wakoo.ui.icons.MicRecording
import com.buque.wakoo.ui.icons.WakooIcons
import kotlinx.coroutines.launch
import kotlin.coroutines.cancellation.CancellationException

@Composable
fun AudioRecordPanel(
    modifier: Modifier = Modifier,
    onAction: IIMAction,
) {
    val context = LocalContext.current

    val scope = rememberCoroutineScope()

    val cancelOffset =
        with(LocalDensity.current) {
            remember {
                -40.dp.toPx()
            }
        }

    val recordManager = AudioRecordManager.singletonInstance
    DisposableEffect(key1 = recordManager) {
        onDispose {
//            AudioRecorder.cancelRecord()
            recordManager.cancelRecording()
            recordManager.cleanup()
        }
    }

    var state by remember {
        // 0：未开始 -》按住说话
        // 1：录音中上滑取消 -》上滑取消
        // 2：录音中松手取消 -》松手取消
        mutableIntStateOf(0)
    }

    var duration by remember {
        mutableIntStateOf(0)
    }

    var sweepAngle by remember {
        mutableFloatStateOf(0f)
    }

    // 监听录音管理器的状态变化
    val recordingState by recordManager.recordingState.collectAsStateWithLifecycle()

    LaunchedEffect(recordingState) {
        when (recordingState) {
            is RecordingState.Completed -> {
                state = 0
                val recordingStateCompleted = recordingState as? RecordingState.Completed
                if (recordingStateCompleted != null) {
                    scope.launch {
                        val audioFile = recordingStateCompleted.finalOutputFile
                        val duration = recordingStateCompleted.duration
                        if (duration <= 1010) {
                            showToast("录音时间太短")
                        } else {
                            onAction.onSendMessage(MessageBundle.Voice.create(audioFile.absolutePath, duration / 1000))
                        }
                    }
                }
            }

            is RecordingState.Error -> {
                state = 0
                (recordingState as? RecordingState.Error)?.apply {
                    showToast(message)
                }
            }

            RecordingState.Idle -> {
                state = 0
            }

            is RecordingState.Paused -> {
            }

            is RecordingState.Recording -> {
            }
        }
    }

    val tipText by remember {
        derivedStateOf {
            when (state) {
                0 -> {
                    "按住 说话"
                }

                1 -> {
                    "松开 发送"
                }

                else -> {
                    "松手 取消"
                }
            }
        }
    }

    val color by remember {
        derivedStateOf {
            if (state == 0) {
                listOf(
                    Color(0xFFA3FF2C),
                    Color(0xFF31FFA1),
                )
            } else {
                listOf(
                    Color(0xFF82CC25),
                    Color(0xFF28CC80),
                )
            }
        }
    }

    val imageVector by remember {
        derivedStateOf {
            if (state == 2) {
                WakooIcons.MicLine
            } else {
                WakooIcons.MicLine
            }
        }
    }

    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Box(
            modifier =
                Modifier
                    .padding(top = 16.dp)
                    .size(88.dp)
                    .clip(CircleShape)
                    .background(brush = Brush.horizontalGradient(color))
                    .pointerInput(key1 = Unit) {
                        awaitEachGesture {
                            val id = awaitFirstDown().id
                            state = 1
//                            onStartRecord()
                            recordManager.startRecording(minDurationMillis = 1000L)
                            verticalDrag(id) {
                                if (state != 0) {
                                    state =
                                        if (it.position.y < cancelOffset) { // 已到可以取消的距离
                                            recordManager.changeRecordingStatus(true)
                                            2
                                        } else {
                                            recordManager.changeRecordingStatus(false)
                                            1
                                        }
                                } else { // 录音已结束
                                    throw CancellationException()
                                }
                            }
                            if (state != 0) {
                                if (state == 2) {
                                    recordManager.cancelRecording()
                                } else {
                                    recordManager.stopRecording(RecordingEndReason.USER_STOPPED)
                                }
                                state = 0
                            }
                        }
                    },
        ) {
            Icon(
                imageVector = imageVector,
                contentDescription = null,
                modifier =
                    Modifier
                        .align(Alignment.Center)
                        .size(32.dp),
            )
        }
        Text(text = tipText, modifier = Modifier.padding(top = 30.dp), color = Color(0xFF999999), fontSize = 14.sp)
    }
}

@Composable
fun BoxScope.AudioCenterStatusWidget() {
    val state by AudioRecordManager.singletonInstance.recordingState.collectAsStateWithLifecycle()

    if (state is RecordingState.Recording) {
        val isPreparingCancel = (state as RecordingState.Recording).preparingCancel
        Column(
            modifier =
                Modifier
                    .align(alignment = Alignment.Center)
                    .size(160.dp, 150.dp)
                    .background(Color(0x80000000), shape = RoundedCornerShape(12.dp))
                    .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
        ) {
            Text(state.formattedDuration, fontSize = 14.sp, color = Color.White)
//            SizeHeight(16.dp)
            Spacer(Modifier.weight(1f))
            if (isPreparingCancel) {
                Icon(
                    imageVector = WakooIcons.Delete,
                    contentDescription = null,
                    modifier = Modifier.size(32.dp),
                    tint = Color.White,
                )
            } else {
                Image(
                    imageVector = WakooIcons.MicRecording,
                    contentDescription = null,
                    modifier = Modifier.size(32.dp),
                )
            }
//            SizeHeight(16.dp)
            Spacer(Modifier.weight(1f))
            Text(
                if (isPreparingCancel) "松开手指 取消发送" else "手指上划 取消发送",
                fontSize = 14.sp,
                color = Color.White,
                modifier =
                    Modifier
                        .background(
                            if (isPreparingCancel) Color(0xFFFD6C6C) else Color.Transparent,
                            shape = RoundedCornerShape(4.dp),
                        ).padding(4.dp),
            )
        }
    }
}
