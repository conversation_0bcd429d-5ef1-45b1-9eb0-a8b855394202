package com.buque.wakoo.im_business.message.ui.custom

import android.R.id.message
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.BasicUser
import com.buque.wakoo.bean.isSelf
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
import com.buque.wakoo.ui.widget.ButtonStyles
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.image.NetworkImage
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class AudioRoom(
    @SerialName("id") val id: Int = 0,
    @SerialName("title") val title: String = "",
    @SerialName("owner") val owner: BasicUser,
)

data class AudioRoomShareContent(val room: AudioRoom) : MsgLayoutContent() {

    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction
    ) {

        baseBox {
            Row(
                modifier = Modifier
                    .widthIn(max = 220.dp)
                    .background(
                        color = Color.White, shape = RoundedCornerShape(15.dp)
                    )
                    .padding(horizontal = 8.dp)
            ) {
                NetworkImage(
                    room.owner.avatar, modifier = Modifier
                        .padding(vertical = 16.dp)
                        .padding(end = 8.dp)
                        .size(48.dp)
                        .clip(CircleShape)
                )

                Column(
                    horizontalAlignment = Alignment.End,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        verticalArrangement = Arrangement.SpaceAround,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 8.dp)
                            .heightIn(64.dp)
                    ) {
                        Text(
                            "邀请加入语音房",
                            style = TextStyle(fontSize = 14.sp, color = Color(0xff111111), lineHeight = 14.sp)
                        )
                        Text(
                            text = "房间标题: ${room?.title} ",
                            style = TextStyle(fontSize = 12.sp, color = Color(0xff999999), lineHeight = 16.sp),
                            overflow = TextOverflow.Ellipsis,
                            maxLines = 2,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }

                    if (!message.isSelf) {
                        HorizontalDivider(color = Color(0xffe5e5e5), modifier = Modifier.padding(vertical = 8.dp))
                        SolidButton(
                            text = "点击进入",
                            onClick = {

                            },
                            height = 32.dp,
                            textColor = Color(0xFF111111),
                            backgroundColor = Color(0xFF66FE6B),
                            fontSize = 14.sp,
                            config = ButtonStyles.Solid.copy(minWidth = 72.dp),
                            paddingValues = PaddingValues(horizontal = 12.dp),
                        )
                        Spacer(Modifier.height(8.dp))
                    }
                }
            }
        }
    }
}