package com.buque.wakoo.im_business.tim

import com.buque.wakoo.ext.getStringOrNull
import com.buque.wakoo.im.api.IMApi
import com.buque.wakoo.im.utils.takeIsNotEmpty
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.executeApiCallExpectingData
import kotlinx.serialization.json.JsonObject
import retrofit2.http.GET


internal object TimTokenProvider {
    suspend fun requestTimToken(): String? {
        val ret = executeApiCallExpectingData {
            IMApi.instance.getTimToken()
        }
        return ret.getOrNull()?.getStringOrNull("tim_token").takeIsNotEmpty()
    }
}