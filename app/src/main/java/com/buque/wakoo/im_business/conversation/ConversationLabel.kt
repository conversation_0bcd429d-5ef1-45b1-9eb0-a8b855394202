package com.buque.wakoo.im_business.conversation

import android.content.Context
import android.graphics.drawable.Drawable
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.im.inter.IUCConversation

enum class ConvLabelType {
    Hidden, VisibleLeft, VisibleRight,
}

sealed interface ConvLabel {

    val type: ConvLabelType

    interface Left : ConvLabel {

        override val type: ConvLabelType
            get() = ConvLabelType.VisibleLeft

        fun getText(context: Context): String? = null

        fun getBackground(context: Context, conversation: IUCConversation): Drawable? = null
    }

    interface Right : ConvLabel {
        override val type: ConvLabelType
            get() = ConvLabelType.VisibleRight
    }

    open class ComposeUILabel(
        @StringRes val textRes: Int,
        val style: TextStyle = TextStyle(Color.White, fontSize = 10.sp),
        val backgroundColor: Color = Color(0xFF3FBAFF),
    ) : ConvLabel {
        override val type: ConvLabelType = ConvLabelType.VisibleRight

        @Composable
        fun LabelContent(modifier: Modifier = Modifier) {
            Box(
                modifier = modifier
                    .height(20.dp)
                    .background(backgroundColor, RoundedCornerShape(4.dp))
                    .padding(horizontal = 10.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(text = stringResource(id = textRes), style = style)
            }
        }
    }

    data object OfficialLabel : ComposeUILabel(
        R.string.官方,
        backgroundColor = Color(0xFF66FE6B),
        style = TextStyle(
            color = Color(0xFF111111),
            fontWeight = FontWeight.Medium,
            fontSize = 10.sp,
            lineHeight = 10.sp
        )
    )

    // 我的好友
    data object MyFriends : ConvLabel {

        override val type: ConvLabelType
            get() = ConvLabelType.Hidden
    }

    // 客服
    data object SupportStaff : ConvLabel {

        override val type: ConvLabelType
            get() = ConvLabelType.Hidden
    }

}