package com.buque.wakoo.im_business

import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.manager.UserManager
import com.buque.wakoo.navigation.AppNavController
import com.buque.wakoo.navigation.dialog.DialogController


/**
 * 全局app信令处理
 */
class AppEventMessageHandler(val appNavController: AppNavController, val dialogController: DialogController) : IMCompatListener {


    override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
        when (message.cmd) {

            IMEvent.MEMBER_JOIN -> {
                //自己加入群组，更新userInfo
                if (message.user?.sIsSelf == true) {
                    UserManager.refreshSelfUserInfo()
                }
            }

//            MsgEventCmd.FIRSTCLASS_WELCOME_MESSAGE -> {
//                val data = message.parseDataJson<FirstClassNotificationBean>() ?: return
//                showComposeDialog { dialog ->
//                    FirstClassWelcomeWidget(data) {
//                        dialog.dismiss()
//                    }
//                }
//            }
//
//            MsgEventCmd.FIRSTCLASS_ACTIVE_MESSAGE -> {
//                val data = message.parseDataJson<FirstClassNotificationBean>() ?: return
//                showComposeDialog { dialog ->
//                    FirstClassActiveWidget(data) {
//                        dialog.dismiss()
//                    }
//                }
//            }

            else -> {}
        }
    }
}