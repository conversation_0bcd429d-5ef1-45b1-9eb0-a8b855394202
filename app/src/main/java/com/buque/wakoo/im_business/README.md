# im_business

这目录下都是和业务相关的代码

## conversation 会话相关

- [AppConversationManger.kt](conversation/AppConversationManger.kt):包含所有会话的全局flow,
  如果需要定义多种Conversation(也许不是会话,是其他部落/群组/附近的人之类的), 需要在这里面加逻辑
- [ConversationsTypes.kt](conversation/ConversationsTypes.kt): 包含不同类型的Conversation,
  也许是伪Conversation(其他部落/群组/附近的人之类的)

## interf

此目录下存放所有IM业务相关的接口

## [message](message) 消息相关

此目录下存放所有消息相关的类

- [types](message/types): 存放所有可识别的消息类型
- [ui](message/ui): 存放所有消息对应的UI绘制逻辑
- [MessageContexMenu.kt](message/MessageContexMenu.kt): 消息长按菜单
- [MessageEntry.kt](message/MessageEntry.kt): 消息实体, 对UCMessage的又一次封装
- [MessageUI.kt](message/MessageUI.kt): 消息Compose UI入口

## [panel](panel) 操作面板

此目录下存放语音面板(待做) 和 emoji表情面板

## [viewmodel](viewmodel): 和业务相关的ViewModel, 在里面已经对消息拉取/收到消息等做了精细化处理

## [AppEventMessageHandler.kt](AppEventMessageHandler.kt): 全局处理从IM处收到的event事件回调

## [UIMessageUtils.kt](UIMessageUtils.kt): 和messageUI相关的工具类