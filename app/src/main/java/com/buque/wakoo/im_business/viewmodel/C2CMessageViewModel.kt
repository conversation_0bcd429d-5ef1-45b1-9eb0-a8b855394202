package com.buque.wakoo.im_business.viewmodel

import androidx.lifecycle.viewModelScope
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.User
import com.buque.wakoo.bean.chatgroup.ChatGroupBean
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.bean.SendParams
import com.buque.wakoo.im.getExtraString
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.UIMessageEntry
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.message.types.UCGiftMessage
import com.buque.wakoo.im_business.message.types.UCImageMessage
import com.buque.wakoo.im_business.message.types.UCTextMessage
import com.buque.wakoo.im_business.message.types.UCTimestampMessage
import com.buque.wakoo.im_business.message.types.UCVoiceMessage
import com.buque.wakoo.im_business.message.ui.ImageMessageContent
import com.buque.wakoo.im_business.message.ui.NoProviderMessageContent
import com.buque.wakoo.im_business.message.ui.TextMessageContent
import com.buque.wakoo.im_business.message.ui.TimeLineContent
import com.buque.wakoo.im_business.message.ui.VoiceMessageContent
import com.buque.wakoo.im_business.message.ui.custom.AudioRoom
import com.buque.wakoo.im_business.message.ui.custom.AudioRoomShareContent
import com.buque.wakoo.im_business.message.ui.custom.GiftContent
import com.buque.wakoo.im_business.message.ui.custom.GroupShareContent
import com.buque.wakoo.im_business.message.ui.entry.MsgUIEntry
import com.buque.wakoo.im_business.wigets.AudioPanel
import com.buque.wakoo.im_business.wigets.EmojiPanel
import com.buque.wakoo.im_business.wigets.IMPanel
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.UserManager
import com.buque.wakoo.network.api.service.ChatGroupApi
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.qyqy.cupid.im.messages.RecallMessageContent
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class C2CChatViewModel(
    private val user: User,
) : ListStateMessageViewModel(
    SendParams(receiver = user.id, type = ConversationType.C2C),
    IMMessageConfig(autoPlayNoPlayedGiftEffect = true, autoMarkReadReceipt = true),
) {
    val panels: Array<IMPanel> =
        arrayOf(
            IMPanel(AudioPanel, forceClearFocus = true), // 录音面板
            IMPanel(EmojiPanel, forceRequestFocus = true), // 表情面板
        )

    //    private val chatRepo = ChatRepository()
//
//    private val userRepo = UserRepository()
//
//    private val accountRepo = AccountRepository()
//
    private val selfUserFlow = AccountManager.userStateFlow.map { it?.basic }

    val targetUserFlow = UserManager.getUserInfoFlow(user.id).stateIn(viewModelScope, SharingStarted.Eagerly, user)

//    override fun filterShownMessage(message: UCInstanceMessage): Boolean {
//        return getMsgLayoutContent(message, false) != null
//    }

    override fun onRecvNewCustomMessage(
        message: UCCustomMessage,
        offline: Boolean,
    ) {
        when (message.cmd) {
//            MsgEventCmd.CONFIRM_CP -> {
//                refreshCoupleInfo()
//            }
        }
    }

//    fun handleAction(action: Int) {
//        when (action) {
//            C2CChatConst.ACTION_SAY_HI -> sayHi()
//            else -> {}
//        }
//    }

    fun blackUser(userId: String) {
        viewModelScope.launch {
            executeApiCallExpectingData {
                UserApiService.instance.blackUser(fields = mapOf("userid" to userId, "black" to "true"))
            }
        }
    }

    private fun getUser(id: String): User? =
        when (id) {
            targetUserFlow.value.id -> targetUserFlow.value
            else -> SelfUser
        }

    override fun createRecallUIMessageEntry(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
        oldEntry: UIMessageEntry?,
    ): MsgUIEntry = RecallMessageContent(message)

    override fun sceneConvertToUIMessageEntry(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
        oldEntry: UIMessageEntry?,
    ): MsgUIEntry? =
        when (message) {
            is UCTextMessage -> {
                TextMessageContent(message)
            }

            is UCImageMessage -> {
                ImageMessageContent(message)
            }

            is UCTimestampMessage -> {
                TimeLineContent(message)
            }

            is UCGiftMessage -> {
                GiftContent(message)
            }

            is UCVoiceMessage -> {
                VoiceMessageContent(message)
            }

            is UCCustomMessage -> {
                when (message.cmd) {
                    IMEvent.INVITE_TO_ROOM -> {
                        val audioRoom = message.getJsonValue<AudioRoom>("audioroom")!!
                        AudioRoomShareContent(audioRoom)
                    }

                    IMEvent.INVITE_TO_TRIBE -> {
                        val audioRoom = message.getJsonValue<ChatGroupBean>("tribe")!!
                        val inviteCode = message.getJsonString("invite_code") ?: ""
                        val isSelf = message.isSelf
//                        val hasAccepted = message.getExtraBoolean("invite_accepted") ?: false
                        val receiverExtra = message.getExtraString("receiver_extra")
                        GroupShareContent(audioRoom, isSelf, receiverExtra) {
                            acceptJoinGroup(audioRoom.id)
                        }
                    }

                    else -> {
                        NoProviderMessageContent(message)
                    }
                }
            }

            else -> NoProviderMessageContent(message)
        }

    private fun acceptJoinGroup(groupId: String) {
        //todo 申请 or 直接加入?
//        viewModelScope.launch {
//            executeApiCallExpectingData {
//                ChatGroupApi.instance.acceptInvite(mapOf("invite_code" to inviteCode))
//            }.onSuccess {
//
//            }
//        }
        viewModelScope.launch {
            executeApiCallExpectingData {
                ChatGroupApi.instance.applyJoinChatGroup(mapOf("group_id" to groupId))
            }
        }
    }

//    suspend fun getExchangeLineConfig() = chatRepo.getExchangeLineConfig()
//        .map {
//            it.getStringOrNull("msg") ?: app.getString(R.string.为了保持社交礼仪)
//        }.toastError().getOrNull()
//
//    suspend fun requestExchangeLine(): Pair<Boolean, String> {
//        chatRepo.requestExchangeLine(user.id).onFailure {
//            if (it is ApiException && it.code == -20) {
//                return false to it.msg
//            }
//        }.toastError()
//        return true to ""
//    }
//
//    suspend fun getIntimacyRule(): Pair<String?, String?>? {
//        return chatRepo.fetchIntimacyRule().toastError().map {
//            it.getStringOrNull("title") to it.getStringOrNull("content")
//        }.getOrNull()
//    }
//
//    suspend fun fetchIntimacyLevelTable(userId: String) = chatRepo.fetchIntimacyLevelTable(userId)
//
//    suspend fun fetchIntimacyTaskList(userId: String) = chatRepo.fetchIntimacyTaskList(userId)
//
//    fun getHiddenModulesFlow() = UIConfig.configFlow.map {
//        it.getOrNull()?.hideJapanEntries
//    }.combine(flow {
//        emit(chatRepo.getCurrencyGiftConfig(user.id).getOrNull()?.map { it.toString() })
//    }) { hide: List<String>?, show: List<String>? ->
//        if (hide == null && show == null) {
//            null
//        } else {
//            buildList<String> {
//                if (hide != null) {
//                    addAll(hide)
//                }
//                if (show != null) {
//                    addAll(show)
//                }
//            }
//        }
//    }.stateIn(viewModelScope, SharingStarted.Eagerly, UIConfig.userConf.hideJapanEntries)
//
//    suspend fun getUserAccount() = accountRepo.fetchUserAccount().map {
//        it.accountInfo
//    }
//
//    suspend fun createCurrencyGiftOrder(count: Int, type: Int) = accountRepo.createCurrencyGiftOrder(user.id, count, type)
//
//    suspend fun confirmCurrencyGiftOrder(orderId: String) = accountRepo.confirmCurrencyGiftOrder(orderId)

//    private fun sayHi() {
//        viewModelScope.launch {
//            chatRepo.sayHiJapan(user.id)
//        }
//    }

//    private fun refreshCoupleInfo() {
//        if (user.gender != sUser.gender) {
//            viewModelScope.launch {
//                userRepo.getCoupleInfoById(user.userId).onSuccess {
//                    coupleInfo = it
//                }
//            }
//        }
//    }
//
//    suspend fun getCPRightPageInfo(): CPRightPageInfo? {
//        return userRepo.getHaveCpInfo(user.id).onFailure { refreshCoupleInfo() }.toastError().getOrNull()
//    }
//
//    suspend fun agreeHaveCp(code: String) {
//        userRepo.agreeHaveCp(code, Scene.NONE, 0)
//            .toastError()
//    }
}
