package com.buque.wakoo.im_business.message.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.sp
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent


data class NoProviderMessageContent(val message: UCInstanceMessage) : MsgLayoutContent() {

    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction
    ) {
        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            Text(
                text = message.toMsgString(),
                color = Color(0xFF86909C),
                fontSize = 12.sp
            )
        }
    }
}