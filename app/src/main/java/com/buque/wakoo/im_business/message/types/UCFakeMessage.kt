package com.buque.wakoo.im_business.message.types

import android.content.Context
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.im.UCFakerMessage
import com.buque.wakoo.im.inter.IUCMessage
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.bean.MsgSendStatus
import com.buque.wakoo.im_business.UIMessageUtils
import java.util.Locale
import kotlin.math.abs

/**
 * 这不是一个真实的消息，只是某一个消息上方显示的时间戳，包装了这一条消息
 */
data class UCTimestampMessage constructor(
    override val base: UCMessage,
) : UCFakerMessage, IUCMessage by base {

    private var formatText: String = ""

    private var cacheTimestamp = 0L

    override val contentType: String = "UCTimestampMessage"

    override val id: String = "timestamp-${base.id}"

    /**
     * 消息发送状态恒定为[MsgSendStatus.Idea]
     */
    override val sendStatus: MsgSendStatus = MsgSendStatus.Idea

    override fun getSummaryString(): String {
        return "时间戳：${getMessageTimeFormatText(WakooApplication.instance)}"
    }

    override fun equalsSame(other: UCFakerMessage?): Boolean {
        return timestamp == other?.timestamp && !checkInValid()
    }

    fun getMessageTimeFormatText(
        context: Context,
        locale: Locale = Locale.getDefault(),
    ): String {
        if (formatText.isEmpty() || checkInValid()) {
            formatText =
                UIMessageUtils.getMessageTimeFormatText(context, timestamp, locale)
            cacheTimestamp = System.currentTimeMillis()
        }
        return formatText
    }

    private fun checkInValid() = abs(System.currentTimeMillis().minus(cacheTimestamp)) >= 60_000
}


/**
 * 这不是一个真实的消息，只是某一个消息上方显示的时间戳，包装了这一条消息
 */
data class UCNewMsgTagMessage constructor(
    override val base: UCMessage,
) : UCFakerMessage, IUCMessage by base {

    override val contentType: String = "UCNewMsgTagMessage"

    override val id: String = "new-message-tag-${base.id}"

    override fun getSummaryString(): String {
        return "以下为新消息"
    }

    override fun equalsSame(other: UCFakerMessage?): Boolean {
        return true
    }
}
