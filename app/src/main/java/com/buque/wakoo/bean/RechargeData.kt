package com.buque.wakoo.bean

import kotlinx.serialization.Serializable

/**
 * 充值套餐数据模型
 */
@Serializable
data class RechargePackage(
    val id: String,
    val diamonds: Int, // 钻石数量
    val price: String, // 价格（元）
    val isSelected: Boolean = false,
    val isPopular: Boolean = false, // 是否为热门套餐
    val unit:String = "￥"
) {
    /**
     * 格式化价格显示
     */
    val formattedPrice: String
        get() = "${unit}${price}"

    /**
     * 格式化钻石数量显示
     */
    val formattedDiamonds: String
        get() = diamonds.toString()
}

/**
 * 用户钻石余额信息
 */
@Serializable
data class DiamondBalance(
    val balance: Int, // 当前钻石余额
    val totalRecharged: Int = 0, // 累计充值钻石数
    val totalConsumed: Int = 0, // 累计消费钻石数
) {
    /**
     * 格式化余额显示
     */
    val formattedBalance: String
        get() = balance.toString()
}

/**
 * 充值订单信息
 */
@Serializable
data class RechargeOrder(
    val orderId: String,
    val packageId: String,
    val diamonds: Int,
    val amount: Double,
    val status: RechargeOrderStatus,
    val createTime: Long,
    val payTime: Long? = null,
    val payMethod: String? = null,
)

/**
 * 充值订单状态
 */
@Serializable
enum class RechargeOrderStatus {
    PENDING, // 待支付
    PAID, // 已支付
    CANCELLED, // 已取消
    FAILED, // 支付失败
}

/**
 * 充值记录
 */
@Serializable
data class RechargeRecord(
    val id: String,
    val description: String, // 描述，如"充值钻石"
    val diamonds: Int, // 钻石变化数量（正数为充值，负数为消费）
    val timestamp: Long, // 时间戳
    val type: RechargeRecordType,
) {
    /**
     * 格式化时间显示
     */
    val formattedTime: String
        get() {
            val date = java.util.Date(timestamp)
            val format = java.text.SimpleDateFormat("MM-dd HH:mm", java.util.Locale.getDefault())
            return format.format(date)
        }

    /**
     * 格式化钻石数量显示
     */
    val formattedDiamonds: String
        get() = if (diamonds > 0) "+$diamonds" else diamonds.toString()

    /**
     * 是否为正数（充值）
     */
    val isPositive: Boolean
        get() = diamonds > 0
}

/**
 * 充值记录类型
 */
@Serializable
enum class RechargeRecordType {
    RECHARGE, // 充值
    CONSUME, // 消费
    GIFT, // 赠送
    REFUND, // 退款
}

/**
 * 预定义的充值套餐
 */
object RechargePackages {
    val defaultPackages =
        listOf(
            RechargePackage(
                id = "package_300",
                diamonds = 300,
                price = "30.0",
            ),
            RechargePackage(
                id = "package_1980",
                diamonds = 1980,
                price = "198.0",
            ),
            RechargePackage(
                id = "package_5880",
                diamonds = 5880,
                price = "588.0",
            ),
            RechargePackage(
                id = "package_9980",
                diamonds = 9980,
                price = 998.0.toString(),
                isPopular = true, // 标记为热门
            ),
            RechargePackage(
                id = "package_19980",
                diamonds = 19980,
                price = 1998.0.toString(),
            ),
            RechargePackage(
                id = "package_99980",
                diamonds = 99980,
                price = 9998.0.toString(),
            ),
        )
}
