package com.buque.wakoo.bean

import kotlinx.serialization.Serializable

@Serializable
data class AccountInfo(
    val tokenInfo: TokenInfo,
    val userInfo: UserInfo,
) : Token by tokenInfo,
    User by userInfo,
    BasicUserOwner by userInfo {
    companion object {
        val preview
            get() =
                AccountInfo(
                    tokenInfo =
                        TokenInfo(
                            apiAuthToken = "123",
                            apiRefreshToken = "456",
                            imToken = "DDSD",
                        ),
                    userInfo = UserInfo.previewBoy,
                )
    }
}
