package com.buque.wakoo.bean

import kotlinx.serialization.Serializable

@Serializable
data class AppConfig(
    val defaultEnvironment: String,
    val environments: Map<String, Environment>,
)

@Serializable
data class Environment(
    val name: String,
    val apiUrl: String,
    val apiSignAccessKey: String,
    val apiSignSecretKey: String,
    val smOrganization: String,
    val smAppId: String,
    val smPubKey: String,
    val enableAnalytics: Boolean,
    val enableLog: Boolean,
    val googleClientId: String,
    val tencentAppId: Int,
    val tencentAppkey: String,
    val tencentRtcId: Int,
)
