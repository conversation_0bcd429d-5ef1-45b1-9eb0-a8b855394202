package com.buque.wakoo.bean.chatgroup

import com.buque.wakoo.bean.BasicUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class JoinApplyItem(
    @SerialName("apply_id")
    val applyId: Int = 0,
    val user: BasicUser,
)

@Serializable
data class ChatGroupMember(
    @SerialName("member_id")
    val memberId: Int = 0,
    val role: Int = 0,
    @SerialName("is_online")
    val isOnline: Boolean = false,
    val user: BasicUser,
) {
    companion object {
        const val ROLE_OWNER = 10
        const val ROLE_ADMIN = 5
        const val ROLE_COMMON = 0
    }
}

val ChatGroupMember.isOwner: Boolean
    get() = role == ChatGroupMember.ROLE_OWNER

val ChatGroupMember.isAdmin: Boolean
    get() = role == ChatGroupMember.ROLE_ADMIN

val ChatGroupMember.isCommon: Boolean
    get() = role == ChatGroupMember.ROLE_COMMON
