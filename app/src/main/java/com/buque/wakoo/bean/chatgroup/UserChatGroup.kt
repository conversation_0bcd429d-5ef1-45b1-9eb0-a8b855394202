package com.buque.wakoo.bean.chatgroup

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class UserChatGroup(
    @SerialName("avatar_url")
    override val avatarUrl: String = "",
    @SerialName("bulletin")
    override val bulletin: String = "",
    @SerialName("i_am_admin")
    override val iAmAdmin: Boolean = false,
    @SerialName("i_am_owner")
    override val iAmOwner: Boolean = false,
    @SerialName("i_enable_dont_disturb")
    override val iEnableDontDisturb: Boolean = false,
    @SerialName("id")
    override val id: String = "0",
    @SerialName("join_day_cnt")
    val joinDayCnt: Int = 0,
    @SerialName("member_cnt")
    override val memberCnt: Int = 0,
    @SerialName("name")
    override val name: String = "",
    @SerialName("public_id")
    override val publicId: String = "",
    @SerialName("show_box_icon")
    val showBoxIcon: Boolean = false,
    @SerialName("tim_group_id")
    override val timGroupId: String = "",
    @SerialName("tribe_type")
    val tribeType: Int = 0,
    @SerialName("user_role")
    override val relationWithMe: Int = 0,
) : WakooChatGroup {
    @Transient
    override val joinState: GroupJoinState = GroupJoinState(relationWithMe)
}

interface WakooChatGroup : GroupRoleHolder {
    val id: String
    val publicId: String
    val timGroupId: String
    val avatarUrl: String
    val bulletin: String
    val name: String
    val memberCnt: Int
    val iAmOwner: Boolean
    val iAmAdmin: Boolean
    val iEnableDontDisturb: Boolean
    val relationWithMe: Int

}
