package com.buque.wakoo.bean.boost


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * ```
 * {
 *     "is_show_invite_code_banner": false,        // 是否显示邀请码 banner
 *     "is_show_exchange_jpy_button": false,        // 是否显示兑换现金按钮
 *     "is_show_gain_point_rule_entry": false,    // 是否显示如何获取积分规则入口
 *     "is_show_extract": false,                   // 是否显示提现模块
 *     "is_show_distribution": false,                // 是否显示邀请好友得奖励模块
 *     "can_exchange_coin": false,                    // 是否可以兑换钻石 判定点击兑换道具按钮后是弹窗还是直接打开道具商城
 *     "invite_code_banner_url": "",                // 填写邀请码 banner url
 *     "invite_code_award_text": ""，                 // 点击填写邀请码 banner 弹窗文案
 *     "gain_point_rule_content": "",        // 如何获得积分弹窗内容
 *     "distribution_url": "",                // 邀请好友链接
 *     "distribution_content": "",            // 邀请好友内容
 *     "distribution_title": "",               // 邀请好友标题
 *     "distribution_btn": "",                // 邀请好友按钮
 *     "exchange_jpy_btn": "",                // 兑换现金按钮
 *     "extract_btn": "",                    // 出金 按钮
 *     "my_extract": ""                    // 我的现金
 *     "extract_history_url": "",            // 提现记录链接
 * }
 * ```
 */
@Serializable
data class BoostConfig(
    @SerialName("can_exchange_coin")
    val canExchangeCoin: Boolean = false,
    @SerialName("distribution_btn")
    val distributionBtn: String = "",
    @SerialName("distribution_content")
    val distributionContent: String = "",
    @SerialName("distribution_title")
    val distributionTitle: String = "",
    @SerialName("distribution_url")
    val distributionUrl: String = "",
    @SerialName("exchange_jpy_btn")
    val exchangeJpyBtn: String = "",
    @SerialName("extract_btn")
    val extractBtn: String = "",
    @SerialName("extract_history_url")
    val extractHistoryUrl: String = "",
    @SerialName("gain_point_rule_content")
    val gainPointRuleContent: String = "",
    @SerialName("invite_code_award_text")
    val inviteCodeAwardText: String = "",
    @SerialName("invite_code_banner_url")
    val inviteCodeBannerUrl: String = "",
    @SerialName("is_show_distribution")
    val isShowDistribution: Boolean = false,
    @SerialName("is_show_exchange_jpy_button")
    val isShowExchangeJpyButton: Boolean = false,
    @SerialName("is_show_extract")
    val isShowExtract: Boolean = false,
    @SerialName("is_show_gain_point_rule_entry")
    val isShowGainPointRuleEntry: Boolean = false,
    @SerialName("is_show_invite_code_banner")
    val isShowInviteCodeBanner: Boolean = false,
    @SerialName("my_extract")
    val myExtract: String = ""
)