package com.buque.wakoo.bean

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OssToken(
    @SerialName("access_key_id") val accessKeyId: String,
    @SerialName("access_key_secret") val accessKeySecret: String,
    val bucket: String,
    val domain: String,
    val expiration: String,
    @SerialName("expire_timestamp")
    val expireTimestamp: Long,
    @SerialName("start_timestamp")
    val startTimestamp: Long,
    @SerialName("endpoint") val endPoint: String,
    @SerialName("cos_region")
    val cosRegion: String,
    @SerialName("path_prefix") val pathPrefix: String,
    @SerialName("security_token") val securityToken: String,
    @SerialName("sdk_type") val sdkType: Int, // 对象存储的 sdk， 1： aliyun oss sdk， 2：tencent cos sdk
) {
    fun getUniqueId(): String = "${accessKeyId}${accessKeySecret}${bucket}${startTimestamp}$expireTimestamp"
}