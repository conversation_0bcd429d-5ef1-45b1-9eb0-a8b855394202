package com.buque.wakoo.bean

import com.buque.wakoo.im.utils.takeIsNotEmpty
import com.buque.wakoo.network.api.bean.LiveRoomItemResponse
import com.buque.wakoo.network.api.bean.VoiceItemResponse
import com.buque.wakoo.network.api.bean.VoiceTag
import com.buque.wakoo.utils.DateTimeUtils
import kotlinx.serialization.Serializable

sealed interface FeedItemData {
    val type: Int
    val id: String

    val identityId get() = "${type}_$id"
}

@Serializable
data class VoiceCardItem(
    val background: String = "", // https://media.ucoofun.com/xya/sound/background/%E5%8D%A1%E7%89%87032x.png
    val favoriteCount: Int = 0, // 2
    val duration: Int = 0, // 8
    override val type: Int = 1, // 10
    override val id: String = "0", // 10
    val isFavorite: Boolean = false, // false
    val isLike: Boolean = false, // false
    val likeCount: Int = 0, // 3
    val resource: String = "", // https://media.ucoofun.com/mobileclient/ios/2025-05-22/audio_2571857145.wav
    val tags: List<VoiceTag> = listOf(),
    val createdAt: Int = 0, // 1747880345
    val visibility: Int = 1, // 1
    val title: String = "", // Ssssddsdsfsds
    val user: BasicUser, // 1
    val isFollow: Boolean = false,
    // 非接口返回数据，文件名
    var fileName: String = "",
    var filePath: String = "",
    // 非接口返回数据，是否正在点赞请求
    var likeRequesting: Boolean = false,
    // 非接口返回数据，是否正在收藏请求
    var favoriteRequesting: Boolean = false,
    // 非接口返回数据，是否正在关注请求
    var followRequesting: Boolean = false,
) : FeedItemData {
    companion object {
        val preview
            get() =
                VoiceCardItem(
                    title = "有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話",
                    user = UserInfo.previewBoy.basic,
                    isFollow = false,
                    tags =
                        listOf(
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈a"),
                            VoiceTag(1, "哈哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈哈"),
                        ),
                )

        fun fromResponse(response: VoiceItemResponse): VoiceCardItem =
            VoiceCardItem(
                background = response.background,
                favoriteCount = response.collectCount,
                duration = response.duration,
                id = response.id.toString(),
                isFavorite = response.isCollect,
                isLike = response.isLike,
                likeCount = response.likeCount,
                createdAt = response.createdAt,
                visibility = response.visibility,
                title = response.title,
                tags = response.tags,
                resource = response.resource,
                user = BasicUser.fromResponse(response.user),
                isFollow = response.user.iHaveFollowed,
            )
    }

    val formatCreateTime = DateTimeUtils.secondsToFormattedString(createdAt)
}

@Serializable
data class LiveRoomCardItem(
    val background: String = "",
    override val type: Int = 2, // 10
    override val id: String = "0", // 10
    val tags: List<VoiceTag> = listOf(),
    val title: String = "", // Ssssddsdsfsds
    val desc: String = "", // Ssssddsdsfsds
    val user: BasicUser,
    val visibility: Int = 0,
    val roomMode: Int = 0,
    val roomAudioStream: String = "",
) : FeedItemData {
    val previewText = desc.takeIsNotEmpty() ?: title

    companion object {
        val preview
            get() =
                LiveRoomCardItem(
                    title = "有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話",
                    desc = "有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話",
                    user = UserInfo.previewBoy.basic,
                    tags =
                        listOf(
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈a"),
                            VoiceTag(1, "哈哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈哈"),
                        ),
                )

        fun fromResponse(response: LiveRoomItemResponse): LiveRoomCardItem =
            LiveRoomCardItem(
                background = response.background,
                id = response.id.toString(),
                visibility = response.visibility,
                roomMode = response.roomMode,
                title = response.title,
                tags = response.tags,
                desc = response.desc,
                roomAudioStream = response.roomAudioStream,
                user = BasicUser.fromResponse(response.user),
            )
    }
}
