package com.buque.wakoo.bean.chatgroup

import androidx.annotation.Keep
import com.buque.wakoo.bean.BasicUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ChatGroupSquareResponse(
    @SerialName("tribes")
    val tribes: List<ChatGroupSquareListBean> = listOf(),
)

@Keep
@Serializable
data class ChatGroupSquareListBean(
    @SerialName("avatar_frame")
    val avatarFrame: String = "",
    @SerialName("avatar_url")
    val avatarUrl: String = "",
    @SerialName("bulletin")
    val bulletin: String = "",
    @SerialName("id")
    val id: Int = 0,
    @SerialName("list_item_bg_img")
    val listItemBgImg: String = "",
    @SerialName("member_cnt")
    val memberCnt: Int = 0,
    @SerialName("name")
    val name: String = "",
    @SerialName("public_id")
    val publicId: String = "",
    @SerialName("rc_group_id")
    val rcGroupId: String = "",
    @SerialName("relation_with_me")
    val relationWithMe: Int = 0,
    @SerialName("sample_members")
    val sampleMembers: List<BasicUser> = listOf(),
    @SerialName("tim_group_id")
    val timGroupId: String = "",

    val useInterfaceRequesting: Boolean = false
)
