package com.buque.wakoo.bean

import androidx.compose.ui.text.input.KeyboardType
import kotlinx.serialization.Serializable

sealed interface Edit

@Serializable
data class InputEdit(
    val originText: String = "",
    val title: String = "",
    val inputTip: String? = null,
    val maxLength: Int = Int.MAX_VALUE,
    val keyboardType: Int = 0, // KeyboardType.Unspecified
) : Edit {
    companion object {
        fun createNickNameEdit(content: String) =
            InputEdit(
                originText = content,
                title = "修改昵称",
                inputTip = "最多可输入9个字，支持中英文、数字、表情符号",
                maxLength = 9,
                keyboardType = 1,
            )

        fun toKeyboardType(edit: InputEdit): KeyboardType =
            when (edit.keyboardType) {
                1 -> KeyboardType.Text
                2 -> KeyboardType.Ascii
                3 -> KeyboardType.Number
                4 -> KeyboardType.Phone
                5 -> KeyboardType.Uri
                6 -> KeyboardType.Email
                7 -> KeyboardType.Password
                8 -> KeyboardType.NumberPassword
                9 -> KeyboardType.Decimal
                else -> KeyboardType.Unspecified // Fallback for unknown values
            }
    }
}
