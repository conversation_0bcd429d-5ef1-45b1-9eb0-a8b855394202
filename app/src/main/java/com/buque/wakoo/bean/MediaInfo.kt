package com.buque.wakoo.bean

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import retrofit2.http.Query

@Serializable
data class MediaInfo(
    @SerialName("url") @Query("url")
    val mediaUrl: String? = "",
    @Query("width") val width: Int = 0,
    @Query("height") val height: Int = 0,
    @Query("size") val size: Long? = 0L,
)

data class ImageInfo(
    val width: Int,
    val height: Int,
    val sizeInBytes: Long,
)
