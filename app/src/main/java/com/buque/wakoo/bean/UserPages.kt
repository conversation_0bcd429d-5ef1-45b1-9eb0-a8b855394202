package com.buque.wakoo.bean

import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.Serializable

@Serializable
data class UserRelations(
    override val basic: BasicUser,
    val hasRelations: Boolean,
    val pageKey: Int,
    // 非接口返回数据，是否正在改变关系
    var relationRequesting: Boolean = false,
) : BasicUserOwner,
    User by basic {
    companion object {
        fun fromResponse(
            response: UserResponse,
            hasRelations: Boolean,
            pageKey: Int = response.relationId,
        ): UserRelations =
            UserRelations(
                basic = BasicUser.fromResponse(response),
                hasRelations = hasRelations,
                pageKey = pageKey,
            )
    }
}

@Serializable
data class UserPages(
    override val basic: BasicUser,
    val pageKey: Int,
) : BasicUserOwner,
    User by basic {
    companion object {
        fun fromResponse(
            response: UserResponse,
            pageKey: Int = response.relationId,
        ): UserPages =
            UserPages(
                basic = BasicUser.fromResponse(response),
                pageKey = pageKey,
            )
    }
}
