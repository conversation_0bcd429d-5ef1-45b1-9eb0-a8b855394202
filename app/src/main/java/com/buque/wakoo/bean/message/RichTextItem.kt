package com.buque.wakoo.bean.message

import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class RichTextItem(
    @SerialName("android_jump_link")
    val androidJumpLink: String = "",
    @SerialName("client_action_type")
    val clientActionType: String = "",
    @SerialName("client_jump_link")
    val clientJumpLink: String = "",
    @SerialName("ios_jump_link")
    val iosJumpLink: String = "",
    @SerialName("rich_text")
    val richText: String = "",
    @SerialName("type")
    val type: Int = 0,
)

fun AnnotatedString.Builder.appendWithStyle(
    text: String,
    style: SpanStyle,
) {
    pushStyle(style)
    append(text)
    pop()
}
