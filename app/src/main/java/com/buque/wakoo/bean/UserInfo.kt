@file:JvmName("UserPagesKt")

package com.buque.wakoo.bean

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.chatgroup.UserChatGroup
import com.buque.wakoo.ext.keepLastNonNullState
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.network.api.bean.Medal
import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
sealed interface User {
    val id: String
    val publishId: String
    val name: String
    val age: Int
    val gender: Int
    val avatar: String
    val birthday: String
    val isVip: Boolean
    val level: Int // 用户等级
    val followingCount: Int
    val followersCount: Int
    val type: Int // 0 非主播、1 普通主播、2 台湾素人主播

    val isSelf
        @Composable get() = id == LocalSelfUserProvider.current.id

    val sIsSelf
        get() = id == SelfUser?.id

    val isBoy
        get() = gender == 1

    val isGirl
        get() = gender == 2

    val genderIsSet
        get() = isBoy || isGirl

    val ageIsSet
        get() = age > 0

    val displayGender: String
        @Composable get() =
            if (genderIsSet) {
                if (isBoy) {
                    "男"
                } else {
                    "女"
                }
            } else {
                "未选择"
            }
}

fun User.toBasic(): BasicUser =
    when (this) {
        is BasicUser -> this
        is BasicUserOwner -> this.basic
    }

sealed interface BasicUserOwner {
    val basic: BasicUser
}

@Entity(tableName = "users")
@Serializable
data class BasicUser(
    @PrimaryKey
    @ColumnInfo(name = "userid")
    @SerialName("userid")
    override val id: String,
    @ColumnInfo(name = "public_id")
    @SerialName("public_id")
    override val publishId: String,
    @ColumnInfo(name = "nickname")
    @SerialName("nickname")
    override val name: String,
    @ColumnInfo(name = "avatar_url")
    @SerialName("avatar_url")
    override val avatar: String,
    @ColumnInfo(name = "gender")
    @SerialName("gender")
    override val gender: Int = 0,
    @ColumnInfo(name = "age")
    @SerialName("age")
    override val age: Int = -1,
    @ColumnInfo(name = "birthday")
    @SerialName("birthday")
    override val birthday: String = "",
    @ColumnInfo(name = "is_member")
    @SerialName("is_member")
    override val isVip: Boolean = false,
    @ColumnInfo(name = "level")
    @SerialName("level")
    override val level: Int = 0,
    @ColumnInfo(name = "follow_cnt")
    @SerialName("follow_cnt")
    override val followingCount: Int = 0,
    @ColumnInfo(name = "fans_cnt")
    @SerialName("fans_cnt")
    override val followersCount: Int = 0,
    @ColumnInfo(name = "type")
    @SerialName("type")
    override val type: Int = 0, // 0 非主播、1 普通主播、2 台湾素人主播
) : User {
    companion object {
        val sampleBoy = BasicUser("1", "10001", "小帅", "https://s.test.wakooclub.com/aaceIm", 1, 22, isVip = true)
        val sampleGirl =
            BasicUser(
                "2",
                "10002",
                "幼儿园班花",
                "https://s.test.wakooclub.com/aaceHw?x-oss-process=image/format,webp",
                2,
                20,
                isVip = true,
            )

        fun fromUid(uid: String) =
            BasicUser(
                id = uid,
                publishId = "",
                name = "--",
                avatar = "",
            )

        fun fromResponse(response: UserResponse): BasicUser =
            BasicUser(
                id = response.id.toString(),
                publishId = response.publicId,
                name = response.nickname,
                gender = response.gender,
                avatar = response.avatarUrl,
                age = response.age,
                birthday = response.birthday,
                isVip = response.isMember,
                level = response.level,
                followingCount = response.followCnt,
                followersCount = response.fansCnt,
                type = if (response.isHighQuality) 1 else 0,
            )
    }
}

@Serializable
data class UserExtraInfo(
    val avatarFrame: String? = null,
    val medalList: List<Medal>? = null,
    val isBlacked: Boolean = false, // false
    val isFollowed: Boolean = false, // false
    val isMember: Boolean = false,
    val expireAt: Int = 0,
    val balance: Int = 0,
    val group: UserChatGroup? = null,
    val cash: String = "0",//现金
    val pt: String = "0"//积分
) {
    companion object {
        val empty = UserExtraInfo()
    }
}

@Serializable
data class LiveRoomInUserExtraInfo(
    val myLiveRoom: MyLiveRoomInfo? = null,
)

@Serializable
data class Medal(
    val icon: String = "",
    val width: Int = 0,
    val height: Int = 0,
) {
    companion object {
        val EMPTY = Medal()
    }
}

@Serializable
data class UserInfo(
    @SerialName("basic") override val basic: BasicUser,
    @SerialName("extra") val extra: UserExtraInfo = UserExtraInfo.empty,
    @SerialName("liveroom") val roomExtra: LiveRoomInUserExtraInfo? = null,
) : BasicUserOwner,
    User by basic {
    companion object {
        val previewBoy
            get() =
                UserInfo(
                    basic =
                        BasicUser(
                            id = "001",
                            publishId = "123",
                            name = "涂鸦冒险家",
                            age = 18,
                            gender = 1,
                            avatar = "https://picsum.photos/200",
                            birthday = "2025-01-01",
                        ),
                )

        val previewGirl
            get() =
                previewBoy.copy(
                    basic =
                        previewBoy.basic.copy(
                            id = "002",
                            publishId = "456",
                            name = "涂鸦少女",
                            age = 16,
                            gender = 2,
                        ),
                )

        fun fromResponse(response: UserResponse): UserInfo =
            UserInfo(
                basic = BasicUser.fromResponse(response),
                extra =
                    UserExtraInfo(
                        avatarFrame = response.avatarFrame,
                        medalList = response.medalList,
                        isBlacked = response.iHaveBlacked,
                        isFollowed = response.iHaveFollowed,
                        balance = response.balance,
                        expireAt = response.member.expireAt,
                        group = response.group,
                        pt = response.diamond,
                        cash = response.cash
                    ),
                roomExtra =
                    LiveRoomInUserExtraInfo(
                        myLiveRoom = if (response.canCreateAudioroom) null else response.room,
                    ),
            )
    }

    fun copyUser(
        id: String = basic.id,
        publishId: String = basic.publishId,
        name: String = basic.name,
        age: Int = basic.age,
        gender: Int = basic.gender,
        avatar: String = basic.avatar,
        birthday: String = basic.birthday,
        isVip: Boolean = basic.isVip,
        level: Int = basic.level,
        followingCount: Int = basic.followingCount,
        followersCount: Int = basic.followersCount,
        type: Int = basic.type,
    ): UserInfo =
        copy(
            basic =
                basic.copy(
                    id = id,
                    publishId = publishId,
                    name = name,
                    age = age,
                    gender = gender,
                    avatar = avatar,
                    birthday = birthday,
                    isVip = isVip,
                    level = level,
                    followingCount = followingCount,
                    followersCount = followersCount,
                    type = type,
                ),
        )

    fun isZhubo() = basic.type != 0

    val group = extra.group
}

object LocalSelfUserProvider {
    private val LocalSelfUser =
        compositionLocalOf<UserInfo> { error("CompositionLocal LocalSelfUser not present") }

    val current: UserInfo
        @Composable get() = LocalSelfUser.current

    val currentId: String
        @Composable get() = current.id

    @Composable
    fun Provide(content: @Composable () -> Unit) {
        val selfUser =
            if (LocalInspectionMode.current) {
                UserInfo.previewBoy
            } else {
                val userState = AccountManager.userStateFlow.collectAsStateWithLifecycle()
                keepLastNonNullState(userState.value)
            }
                ?: return
        CompositionLocalProvider(LocalSelfUser provides selfUser, content = content)
    }
}

val String.isSelf
    @Composable get() = this == LocalSelfUserProvider.currentId

val Int.isSelf
    @Composable get() = this.toString() == LocalSelfUserProvider.currentId
