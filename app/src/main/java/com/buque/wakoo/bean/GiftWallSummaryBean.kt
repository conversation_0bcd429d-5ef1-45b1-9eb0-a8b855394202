package com.buque.wakoo.bean

import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class GiftWallSummaryBean(
    @SerialName("star_cnt")
    val starCnt: Int = 0,
    @SerialName("tabs")
    val tabs: List<Tab> = listOf(),
    @SerialName("total_cnt")
    val totalCnt: Int = 0,
) {
    @Keep
    @Serializable
    data class Tab(
        @SerialName("name")
        val name: String = "",
        @SerialName("star_cnt")
        val starCnt: Int = 0,
        @SerialName("t")
        val t: Int = 0,
        @SerialName("total_cnt")
        val totalCnt: Int = 0,
    )

    fun updateTabCount(
        tabId: Int,
        count: Int,
    ): GiftWallSummaryBean =
        copy(
            starCnt = starCnt + 1,
            tabs =
                tabs.toMutableList().apply {
                    (find { it.t == tabId })?.let {
                        val pos = indexOf(it)
                        set(pos, it.copy(starCnt = it.starCnt + 1))
                    }
                },
        )
}
