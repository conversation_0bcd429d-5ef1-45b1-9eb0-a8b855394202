package com.buque.wakoo.bean.chatgroup

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class ChatGroupBean(
    @SerialName("avatar_url")
    override val avatarUrl: String = "",
    @SerialName("bulletin")
    override val bulletin: String = "",
    @SerialName("i_am_admin")
    override val iAmAdmin: Boolean = false,
    @SerialName("i_am_owner")
    override val iAmOwner: Boolean = false,
    @SerialName("i_enable_dont_disturb")
    override val iEnableDontDisturb: Boolean = false,
    @SerialName("id")
    override val id: String = "0",
    @SerialName("join_day_cnt")
    val joinDayCnt: Int = 0,
    @SerialName("member_apply_wait_cnt")
    val memberApplyWaitCnt: Int = 0,
    @SerialName("online_member_cnt")
    val onlineMemberCnt: Int = 0,
    @SerialName("member_cnt")
    override val memberCnt: Int = 0,
    @SerialName("name")
    override val name: String = "",
    @SerialName("public_id")
    override val publicId: String = "",
    @SerialName("show_box_icon")
    val showBoxIcon: Boolean = false,
    @SerialName("tim_group_id")
    override val timGroupId: String = "",
    @SerialName("tribe_type")
    val tribeType: Int = 0,
    @SerialName("relation_with_me")
    override val relationWithMe: Int = 0,
    @SerialName("first_page_members")
    val firstPageMembers: List<ChatGroupMember> = emptyList(),
) : GroupRoleHolder,
    WakooChatGroup {
    @Transient
    override val joinState: GroupJoinState = GroupJoinState(relationWithMe)



    val applyJoinCount: Int
        get() =
            if (iAmAdmin || iAmOwner) {
                memberApplyWaitCnt
            } else {
                0
            }

    val imId: String
        get() = timGroupId
}

data class GroupJoinState(
    val relationWithMe: Int,
) {
    val isMember: Boolean
        get() = relationWithMe == 10

    val isApplying: Boolean
        get() = relationWithMe == 5

    val isNone: Boolean
        get() = relationWithMe == 0
}

interface GroupRoleHolder {
    val joinState: GroupJoinState
}
