package com.buque.wakoo.bean

import kotlinx.serialization.Serializable

interface Token {
    val apiAuthToken: String
    val apiRefreshToken: String
    val imToken: String
}

@Serializable
data class TokenInfo(
    override val apiAuthToken: String,
    override val apiRefreshToken: String,
    override val imToken: String,
) : Token {
    companion object {
        val Empty =
            TokenInfo(
                apiAuthToken = "",
                apiRefreshToken = "",
                imToken = "",
            )
    }
}

val TokenInfo.isEmpty
    get() = apiAuthToken.isEmpty() || apiRefreshToken.isEmpty()
