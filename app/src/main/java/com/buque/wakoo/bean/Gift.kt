package com.buque.wakoo.bean

import androidx.annotation.Keep
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.SaverScope
import com.buque.wakoo.WakooApplication.Companion.isPreviewOnCompose
import com.buque.wakoo.app.AppJson
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

interface IGift {
    val id: Int
    val name: String
    val icon: String
    val price: Int
    val priceType: Int

    companion object {
        const val PRICE_TYPE_GOLD = 1 // 金币
        const val PRICE_TYPE_SLIVER = 2 // 银币
    }
}

@Serializable
data class GiftInfo(
    @SerialName("balance")
    val balance: Int = 0,
    @SerialName("silver_balance") val silverBalance: Int = 0,
    val tabs: List<GiftTab> = emptyList(),
)

@Serializable
data class GiftTab(
    // - "tab_id": 2,  # -1: 背包, 1: 热门 2: 幸运 3: CP礼物 5: 新礼物 6:亲友团
    @SerialName("tab_id")
    val tabId: Int = -100,
    @SerialName("tab_name")
    val tabName: String,
    // tab_type : 0-普通,1-CP礼物,2-背包礼物,3-幸运礼物,
    @SerialName("tab_type")
    val tabType: Int = 0,
    @SerialName("unread_cnt")
    val unreadCount: Int = 0,
    @SerialName("gifts")
    val gifts: List<GiftBean>,
) {
    //    0普通 1cp礼物 2背包
    companion object {
        const val TYPE_COMMON = 0
        const val TYPE_CP = 1
        const val TYPE_BAG = 2
        const val TYPE_LUCKY = 3
    }

    val name: String
        get() = tabName
}

/**
 *
 * @property t  礼物类型
 *      *   0-普通
 *      *   1-表白礼物
 *      *   2-时长积分兑换票礼物
 *      *   3-CP官宣礼物,
 *      *   4-盲盒礼物,
 *      *   5-七夕限定礼物
 *      *   6-幸运礼物
 *      *   7-亲友团礼物
 *      *   8-新年祝福礼物
 *      *   9-头等舱
 *      *   10-婚礼礼物
 */
@Serializable
data class GiftBean(
    val t: Int = 0,
    @SerialName("blindbox_cnt")
    val blindBoxCount: Int? = 0,
    @SerialName("desc_icon")
    val descIcon: String = "",
    @SerialName("desc_link")
    val descLink: String = "",
    @SerialName("desc_text")
    val descText: String = "",
    @SerialName("effect_file")
    val effectFile: String = "",
    @SerialName("icon")
    override val icon: String,
    @SerialName("id")
    override val id: Int,
    @SerialName("name")
    override val name: String,
    @SerialName("price")
    override val price: Int,
    @SerialName("price_type")
    override val priceType: Int = 1,
    @SerialName("count")
    var giftCount: Int = 0,
    @SerialName("superscript_icon")
    val superscriptIcon: String = "",
    @SerialName("banner_img")
    val bannerImg: String = "",
    @SerialName("banner_link")
    val bannerLink: String = "",
    @SerialName("jackpot")
    val jackpot: Int = -1,
    @SerialName("is_locked")
    val isLocked: Boolean = false,
    val bless: BlessEntity? = null,
) : IGift {
    companion object {
        const val GIFT_TYPE_BLESS = 8
        val Saver = object : Saver<GiftBean?, Any> {
            override fun SaverScope.save(value: GiftBean?): Any? {
                return value?.let {
                    AppJson.encodeToString(value)
                }
            }

            override fun restore(value: Any): GiftBean? {
                return runCatching { AppJson.decodeFromString<GiftBean>(value as String) }.getOrNull()
            }
        }
    }

    val showPlay: Boolean
        get() = descIcon.isNotEmpty() && descLink.isNotEmpty()

    val isLuckyBall: Boolean
        get() = t == 6
}

@Serializable
data class BlessEntity(
    @SerialName("cnt_per_receiver_limit")
    val cntPerReceiverLimit: Int = 0,
    val hint: String = "",
    val title: String = "",
    @SerialName("limit_desc")
    val limitDesc: String = "",
    @SerialName("max_length")
    val maxLength: Int = 0,
    val placeholder: String = "",
    @SerialName("receiver_cnt_limit")
    val receiverCntLimit: Int = 0,
)

//region 礼物墙礼物
@Serializable
sealed interface GiftWall {
    @Serializable
    data class BlindboxGift(
        @SerialName("series_id")
        val seriesId: Int? = 0,
        @SerialName("series_name")
        val seriesName: String = "",
        val gifts: List<GiftWrapper> = emptyList(),
    )

    @Serializable
    data class SeriesGift(
        @SerialName("series_id")
        val seriesId: Int? = null,
        @SerialName("series_name")
        val seriesName: String = "",
        @SerialName("total_cnt")
        val totalCount: Int = 0,
        @SerialName("star_cnt")
        val starCount: Int = 0,
        @SerialName("gifts")
        val gifts: List<GiftWrapper> = emptyList(),
    )

    @Serializable
    data class GiftWrapper(
        @SerialName("gift") val gift: Gift,
        @SerialName("count") val count: Int,
    ) {
        @Serializable
        data class Gift(
            val t: Int = 0,
            @SerialName("effect_file") val effectFile: String = "",
            @SerialName("icon") override val icon: String = "",
            @SerialName("price_type")
            override val priceType: Int = 1, // 1金币 2银币
            @SerialName("id") override val id: Int = 0,
            @SerialName("name") override val name: String = "",
            @SerialName("price") override val price: Int = 0,
            val desc: String = if (isPreviewOnCompose) "" else "赠送可升级为钻石CP",
            @SerialName("cross_out_price")
            val crossOutPrice: Int = -1,
            @SerialName("price_label")
            val priceLabel: String = "",
            @SerialName("dynamic_effect")
            val dynamicEffect: String = "",
        ) : IGift
    }
}

/**
{
"id": 34,
"name": "空中花园",
"icon": "https://xxxx",
"price_type": 1,
"price": 45,
"is_blindbox_gift": false, // 如果是盲盒礼物，交互上是拉起礼物面板，定位到盲盒。
"blindbox_id": 0,  // 非盲盒礼物时为0
"packet_cnt": 20,  // 背包中该礼物数量，当该数量大于等于赠送数量时，用背包礼物赠送，当前需求中，赠送数量都是1
"star_cnt": 35,
"action_btn_txt": "赠送/求打赏/打赏礼物",
"can_action": true,
"cant_action_hint": "该礼物已下线", # can_give为False时，按钮点击提示，可能为"", 为""则点击无反应
"hint": "背包礼物可赠送",

"beg_cnt": 5
}
 */
@Keep
@Serializable
data class GiftWallItemDetail(
    @SerialName("action_btn_txt")
    val actionBtnTxt: String = "",
    @SerialName("blindbox_id")
    val blindboxId: Int = 0,
    @SerialName("can_action")
    val canAction: Boolean = false,
    @SerialName("cant_action_hint")
    val cantActionHint: String = "",
    @SerialName("hint")
    val hint: String = "",
    @SerialName("icon")
    override val icon: String = "",
    @SerialName("id")
    override val id: Int = 0,
    @SerialName("is_blindbox_gift")
    val isBlindboxGift: Boolean = false,
    @SerialName("name")
    override val name: String = "",
    @SerialName("packet_cnt")
    val packetCnt: Int = 0,
    @SerialName("price")
    override val price: Int = 0,
    @SerialName("price_type")
    override val priceType: Int = 0,
    @SerialName("star_cnt")
    val starCnt: Int = 0,
    @SerialName("beg_cnt")
    val begCount: Int? = null,
    @SerialName("popup_type")
    val popupType: Int = 3,
    @SerialName("t")
    val type: Int = 0,
    @SerialName("bless")
    val bless: BlessEntity? = null,
) : IGift,
    java.io.Serializable {
    constructor(gift: IGift) : this(
        id = gift.id,
        icon = gift.icon,
        name = gift.name,
        price = gift.price,
        priceType = gift.priceType,
    )
}

//endregion

//region 消息里的礼物bean类

@Serializable
data class GiftWrapper constructor(
    @Transient
    var giftType: Int = 0, // 0:普通礼物，1:cp礼物，2:幸运礼物
    val sender: BasicUser,
    val receivers: List<BasicUser>,
    val gift: GiftBean,
    @SerialName("cnt_per_receiver")
    var cnt: Int = 0,
    @SerialName("is_from_blindbox")
    val isBlinxBox: Boolean = false,
    @SerialName("blindbox_effect_file")
    val blindboxEffectFile: String = "",
    @SerialName("blindbox_digest")
    val blindboxDigest: String = "",
    @SerialName("blindbox_name")
    val blindboxName: String? = "",
    @SerialName("reward_type")
    val rewardType: Int = 1,
    @SerialName("total_gift_cnt")
    val totalGiftCnt: Int = 0,
    @SerialName("total_reward_cnt")
    val totalRewardCnt: Int = 0,
    @SerialName("subscript_msg")
    val subscriptMsg: String = "",
    @SerialName("reward_object")
    val rewardObject: Int = 1,
    @SerialName("total_coin")
    val comboCoin: Int = 0,
    @SerialName("sender_extra_hint")
    val sender_extra_hint: String = "",
    @SerialName("receiver_extra_hint")
    val receiver_extra_hint: String = "",
    @SerialName("is_dynamic_effect")
    val isDynamicEffect: Boolean = false, // 是否为动态特效
    @SerialName("dynamic_render_type")
    val dynamicRenderType: Int = 0, // 2.41.0 添加 动态渲染类型,此次需求场景,值固定为1
    val showBannerAnim: Boolean = true, // 是否显示礼物banner动画
) {
    //    "total_gift_cnt": 20,  # 送出总礼物数
    //    "total_reward_cnt": 50,  # 奖励总币数
    //    "reward_type": 1,  # 奖励币类型 1金币 2银币
    //    "reward_object": 1,  # 奖励对象 1自己 2对方
    //    "digest": "xxxxx",
    //    "subscript_msg": "xxxx",  # 下标提示语
//    companion object {
//        const val room = 1
//        const val tribe = 2
//        const val private = 3
//    }

    val receiverName = receivers.joinToString(separator = "、") { it.name }

    val isCpGift: Boolean
        get() = giftType == 1

    val isLuckGift: Boolean
        get() = giftType == 2

    val isCupidLuckyGift: Boolean
        get() = giftType == 6

    val count: Int
        get() = cnt

    fun isSender(userid: Int): Boolean = sender.id.toInt(0) == userid

    fun isReceiver(userid: Int): Boolean = receivers.any { it.id.toInt(0) == userid }

    val isRewardReceiver
        get() = rewardObject == 2
}

// fun GiftWrapper?.giftExtra(t: Int): String {
//    return this?.let {
//        if (it.isBlinxBox) {
//            it.blindboxDigest
//        } else {
//            LuckBalls.getSubscript(this, t)
//        }
//    } ?: ""
// }
//
// /**
// * 2025-1-23
// * 新增的背包礼物说明
// * 有sender和receiver两个字段，分别代表发送者和接收者
// *
// * @param t
// * @return
// */
// fun GiftWrapper?.giftExtraV2(t: Int): String {
//    val giftExtra = this.giftExtra(t) ?: ""
//    return giftExtra.ifBlank {
//        this?.let { model ->
//            if (model.isSender(sUser.userId)) {
//                model.sender_extra_hint
//            } else if (model.isReceiver(sUser.userId)) {
//                model.receiver_extra_hint
//            } else {
//                ""
//            }
//        } ?: ""
//    }
// }
//
// fun GiftWrapper?.hasGiftExtra(t: Int): Boolean {
//    return giftExtra(t).isNotEmpty()
// }

//endregion
