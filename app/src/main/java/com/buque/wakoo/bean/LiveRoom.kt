package com.buque.wakoo.bean

import androidx.compose.runtime.IntState
import com.buque.wakoo.network.api.bean.RtcConfig
import com.buque.wakoo.ui.screens.liveroom.LiveMicMode
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class MyLiveRoomInfo(
    @SerialName("id") val roomId: String = "0",
    @SerialName("public_id") val publicId: String = "",
    @SerialName("title") val roomName: String = "",
)

@Serializable
data class LiveRoomEditInfo(
    val title: String,
    val desc: String,
    val tagIds: List<Int>,
)

/**
 * rtc相关的配置
 */
data class RoomRtcToken(
    val channelName: String = "", // audioroom_375_rtc_trtc
    val channelType: Int = 0, // 3
    val config: RtcConfig = RtcConfig(),
    val token: String = "",
)

data class RoomOnlineInfo(
    val totalCount: IntState,
    val previewList: List<User>,
)

@Serializable
data class BasicRoomInfo(
    val id: String,
    val publicId: String,
    val title: String,
    val owner: UserInfo,
    val roomMode: LiveRoomMode,
    val micMode: LiveMicMode,
    val desc: String?, // 公告
    val notice: String?, // 公告
    val background: String?,
    val tagIds: List<Int>?,
    val updateTimestamp: Long = 0,
) {
    companion object {
        val preview: BasicRoomInfo
            get() =
                BasicRoomInfo(
                    id = "375",
                    publicId = "10086",
                    title = "你要约会吗?",
                    owner = UserInfo.previewGirl,
                    roomMode = LiveRoomMode.Normal,
                    micMode = LiveMicMode.Free,
                    desc = "大家来约会吧",
                    notice = "想要上麦请扣1",
                    tagIds = null,
                    background = null,
                )
    }

    val ownerId: String
        get() = owner.id

    val micSeatCount =
        when (roomMode) {
            LiveRoomMode.Radio -> 5
            else -> 8
        }

    val ownerIsInvalid: Boolean
        get() = owner.id == "-1"
}
