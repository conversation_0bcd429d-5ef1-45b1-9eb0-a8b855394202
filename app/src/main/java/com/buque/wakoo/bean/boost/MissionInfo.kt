package com.buque.wakoo.bean.boost


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class MissionInfo(
    @SerialName("mission_series_list")
    val missionSeriesList: List<MissionSeries> = listOf()
) {
    @Serializable
    data class MissionSeries(
        @SerialName("all_finished")
        val allFinished: Boolean = false,
        @SerialName("current_light_count")
        val currentLightCount: Int = 0,
        @SerialName("finished_totips")
        val finishedTotips: String = "",
        @SerialName("light_prize_infos")
        val lightPrizeInfos: List<String> = listOf(),
        @SerialName("light_prize_rule_content")
        val lightPrizeRuleContent: String = "",
        @SerialName("light_prize_rule_title")
        val lightPrizeRuleTitle: String = "",
        @SerialName("multiplier_help")
        val multiplierHelp: String = "",
        @SerialName("prize_type")
        val prizeType: Int = 0,
        @SerialName("series_id")
        val seriesId: Int = 0,
        @SerialName("series_slug")
        val seriesSlug: String = "",
        @SerialName("series_title")
        val seriesTitle: String = "",
        @SerialName("series_type")
        val seriesType: Int = 0,
        @SerialName("tasks")
        val tasks: List<Task> = listOf(),
        @SerialName("today_finished")
        val todayFinished: Boolean = false,
        @SerialName("treasure_totips")
        val treasureTotips: String = ""
    ) {
        companion object {
            const val SERIES_TYPE_ONCE_TASK = 2
            const val SERIES_TYPE_SIGN = 3
            const val SERIES_TYPE_DAILY = 4
        }

        @Serializable
        data class Task(
            @SerialName("condition_times")
            val conditionTimes: Int = 0,
            @SerialName("condition_type")
            val conditionType: Int = 0,
            @SerialName("desc")
            val desc: String = "",
            @SerialName("extra")
            val extra: Extra = Extra(),
            @SerialName("finished")
            val finished: Boolean = false,
            @SerialName("id")
            val id: Int = 0,
            @SerialName("prize")
            val prize: String = "",
            @SerialName("prize_type")
            val prizeType: Int = 0,
            @SerialName("progress")
            val progress: String = "",
            @SerialName("title")
            val title: String = ""
        ) {
            @Serializable
            data class Extra(
                @SerialName("audit_only")
                val auditOnly: Boolean = false,
                @SerialName("big_gold_box")
                val bigGoldBox: Boolean = false,
                @SerialName("finish_times_limit")
                val finishTimesLimit: String = "",
                @SerialName("hint")
                val hint: String = "",
                @SerialName("min_version")
                val minVersion: String = "",
                @SerialName("predecessors")
                val predecessors: String = "",
                @SerialName("prize_icon")
                val prizeIcon: String = "",
                @SerialName("task_icon")
                val taskIcon: String = "",
                @SerialName("twd_prize_value")
                val twdPrizeValue: String = ""
            )
        }
    }
}