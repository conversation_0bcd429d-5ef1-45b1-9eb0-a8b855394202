package com.buque.wakoo.bean

import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class RichItem(
    // 1 text;2:icon
    val type: Int,
    @SerialName("rich_text") val text: String = "",
    val icon: String = "",
    val width: Int = 0,
    val height: Int = 0,
    @SerialName("client_jump_link")
    val link: String? = null,
    @SerialName("android_jump_link")
    val androidLink: String? = "",
    @SerialName("client_action_type")
    val action: String = "",
    @SerialName("border_radius")
    val radius: Int = 0,
) {
    companion object {
        private const val TYPE_TEXT = 1
        private const val TYPE_ICON = 2
    }
}

@Serializable
data class UserRichList(
    val sender: UserResponse? = null,
    @SerialName("rich_text") val richText: List<RichItem>,
)
