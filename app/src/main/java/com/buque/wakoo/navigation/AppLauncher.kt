package com.buque.wakoo.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.saveable.rememberSaveable
import java.util.UUID

/**
 * 一个轻量级的启动器，负责用特定的请求键来启动导航。
 */
class AppLauncher<I : AppNavKey> internal constructor(
    private val resultKey: String,
    private val controller: AppNavController,
) {
    fun launch(input: I) {
        controller.launchForResult(input, resultKey)
    }
}

/**
 * 模仿 rememberLauncherForActivityResult 的 Composable 函数。
 * @param onResult 当目标页面通过 setResult 返回时调用的回调。
 * @return 返回一个 AppLauncher，用于启动导航。
 */
@Composable
fun <I : AppNavKey, O> AppNavController.rememberLauncherForResult(onResult: (O) -> Unit): AppLauncher<I> {
    val resultKey = rememberSaveable { UUID.randomUUID().toString() }

    val latestOnResult by rememberUpdatedState(onResult)

    LaunchedEffect(Unit) {
        val pendingResult = <EMAIL><O>(resultKey)
        if (pendingResult != null) {
            latestOnResult(pendingResult)
        }
    }

    DisposableEffect(resultKey) {
        registerResultCallback(resultKey) { result ->
            @Suppress("UNCHECKED_CAST")
            latestOnResult(result as O)
        }
        onDispose {
            unregisterResultCallback(resultKey)
        }
    }

    // 5. 返回一个稳定的 Launcher 实例。
    return remember { AppLauncher(resultKey, this) }
}
