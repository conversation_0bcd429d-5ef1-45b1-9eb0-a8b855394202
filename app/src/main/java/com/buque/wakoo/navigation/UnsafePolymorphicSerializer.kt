package com.buque.wakoo.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.saveable.rememberSerializable
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.runtime.toMutableStateList
import androidx.savedstate.serialization.SavedStateConfiguration
import kotlinx.serialization.InternalSerializationApi
import kotlinx.serialization.KSerializer
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.descriptors.buildClassSerialDescriptor
import kotlinx.serialization.descriptors.serialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.encoding.decodeStructure
import kotlinx.serialization.encoding.encodeStructure
import kotlinx.serialization.serializer

class UnsafePolymorphicSerializer<T : Any> : KSerializer<T> {
    override val descriptor =
        buildClassSerialDescriptor("PolymorphicData") {
            element(elementName = "type", serialDescriptor<String>())
            element(elementName = "payload", buildClassSerialDescriptor("Any"))
        }

    @OptIn(InternalSerializationApi::class)
    @Suppress("UNCHECKED_CAST")
    override fun deserialize(decoder: Decoder): T =
        decoder.decodeStructure(descriptor) {
            val className = decodeStringElement(descriptor, decodeElementIndex(descriptor))
            val classRef = Class.forName(className).kotlin
            val serializer = classRef.serializer()

            decodeSerializableElement(descriptor, decodeElementIndex(descriptor), serializer) as T
        }

    @OptIn(InternalSerializationApi::class)
    @Suppress("UNCHECKED_CAST")
    override fun serialize(
        encoder: Encoder,
        value: T,
    ) {
        encoder.encodeStructure(descriptor) {
            val className = value::class.java.name
            encodeStringElement(descriptor, index = 0, className)
            val serializer = value::class.serializer() as KSerializer<T>
            encodeSerializableElement(descriptor, index = 1, serializer, value)
        }
    }
}

/**
 * 一个用于 SnapshotStateList<T> 的 KSerializer。
 * 它通过将核心序列化/反序列化任务委托给内置的 ListSerializer 来工作。
 *
 * @param T 列表中元素的类型。
 * @param elementSerializer 用于列表中元素的 KSerializer。
 */
class SnapshotStateListSerializer<T>(
    private val elementSerializer: KSerializer<T>,
) : KSerializer<SnapshotStateList<T>> {
    // 创建一个标准的 ListSerializer 来处理列表的通用逻辑
    private val listSerializer = ListSerializer(elementSerializer)

    /**
     * 描述符（Descriptor）也直接委托给 listSerializer。
     * 这会告诉序列化框架，我们正在处理一个列表结构。
     */
    override val descriptor: SerialDescriptor = listSerializer.descriptor

    /**
     * 序列化时，将 SnapshotStateList<T> 当作一个普通的 List<T>，
     * 并使用 listSerializer 进行编码。
     */
    override fun serialize(
        encoder: Encoder,
        value: SnapshotStateList<T>,
    ) {
        encoder.encodeSerializableValue(listSerializer, value)
    }

    /**
     * 反序列化时，首先使用 listSerializer 将数据解码为一个普通的 List<T>，
     * 然后将这个 List<T> 转换为一个 SnapshotStateList<T>。
     */
    override fun deserialize(decoder: Decoder): SnapshotStateList<T> {
        val list = decoder.decodeSerializableValue(listSerializer)
        return list.toMutableStateList()
    }
}

@Composable
fun <T> rememberSerializableList(
    vararg inputs: Any?,
    elementSerializer: KSerializer<T>,
    configuration: SavedStateConfiguration = SavedStateConfiguration.DEFAULT,
    init: () -> SnapshotStateList<T>,
): SnapshotStateList<T> {
    // 在内部创建 SnapshotStateListSerializer
    val listSerializer = SnapshotStateListSerializer(elementSerializer)

    // 调用通用的 rememberSerializable 函数
    return rememberSerializable(
        *inputs,
        serializer = listSerializer,
        configuration = configuration,
        init = init,
    )
}
