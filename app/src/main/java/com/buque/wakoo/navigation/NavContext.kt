package com.buque.wakoo.navigation

import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.runtime.ProvidableCompositionLocal
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.navigation3.runtime.NavKey
import kotlinx.serialization.Serializable

// controller的key
interface CtrlKey<T : AppNavController>

val CtrlKey<*>.isRoot
    get() = this is RootNavCtrlKey

// 根controller的key
object RootNavCtrlKey : CtrlKey<RootNavController>

// 路由的NavKey
@Serializable
sealed interface AppNavKey : NavKey {
    val isRequiresLogin: Boolean
        get() = true
}

/**
 * 已登录页面的容器
 */
@Serializable
data object LoggedInHost : AppNavKey {
    val rootNavKey = Route.Home
}

interface BottomNavKey<T : ITabItemContent> : AppNavKey {
    val tab: T
}

typealias AppNavBackStack = SnapshotStateList<AppNavKey>

val AppNavController.isRoot
    get() = key.isRoot

val AppNavController.useRoot: RootNavController?
    get() = this as? RootNavController ?: LocalAppNavController.useRoot

val AppNavController.requireRoot: RootNavController
    get() = requireNotNull(useRoot)

fun <C : AppNavController> AppNavController.asCtrl(key: CtrlKey<C>): C? =
    if (this.key === key) {
        @Suppress("UNCHECKED_CAST")
        this as C
    } else {
        null
    }

@OptIn(ExperimentalSharedTransitionApi::class)
val LocalNavSharedTransitionScope: ProvidableCompositionLocal<SharedTransitionScope> =
    compositionLocalOf {
        throw IllegalStateException(
            "Unexpected access to LocalNavSharedTransitionScope. You must provide a " +
                "SharedTransitionScope from a call to SharedTransitionLayout() or " +
                "SharedTransitionScope()",
        )
    }
