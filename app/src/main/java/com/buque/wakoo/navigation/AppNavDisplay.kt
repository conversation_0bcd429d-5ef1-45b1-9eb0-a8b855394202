package com.buque.wakoo.navigation

import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.ContentTransform
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.SizeTransform
import androidx.compose.animation.core.tween
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.togetherWith
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.lifecycle.viewmodel.navigation3.rememberViewModelStoreNavEntryDecorator
import androidx.navigation3.runtime.EntryProviderBuilder
import androidx.navigation3.runtime.NavEntry
import androidx.navigation3.runtime.NavEntryDecorator
import androidx.navigation3.runtime.entry
import androidx.navigation3.runtime.entryProvider
import androidx.navigation3.runtime.rememberSavedStateNavEntryDecorator
import androidx.navigation3.ui.NavDisplay
import androidx.navigation3.ui.SceneStrategy
import androidx.navigation3.ui.SinglePaneSceneStrategy
import androidx.navigation3.ui.rememberSceneSetupNavEntryDecorator
import androidx.navigationevent.NavigationEventDispatcher
import androidx.navigationevent.NavigationEventDispatcherOwner
import com.buque.wakoo.ui.theme.FixPreview

private val NoOpNavigator =
    object : NavigationEventDispatcherOwner {
        override val navigationEventDispatcher: NavigationEventDispatcher
            get() = NavigationEventDispatcher(null, null)
    }

@Composable
fun <T : Any> AppNavDisplay(
    backStack: List<T>,
    modifier: Modifier = Modifier,
    contentAlignment: Alignment = Alignment.TopStart,
    onBack: (Int) -> Unit = {
        if (backStack is MutableList<T>) {
            repeat(it) { backStack.removeAt(backStack.lastIndex) }
        }
    },
    entryDecorators: List<NavEntryDecorator<*>> =
        listOf(
            rememberSceneSetupNavEntryDecorator(),
            rememberSavedStateNavEntryDecorator(),
            rememberViewModelStoreNavEntryDecorator(),
        ),
    sceneStrategy: SceneStrategy<T> = SinglePaneSceneStrategy(),
    sizeTransform: SizeTransform? = null,
    transitionSpec: AnimatedContentTransitionScope<*>.() -> ContentTransform = {
        slideInHorizontally(initialOffsetX = { it }) togetherWith
            slideOutHorizontally(targetOffsetX = { -it })
    },
    popTransitionSpec: AnimatedContentTransitionScope<*>.() -> ContentTransform = {
        slideInHorizontally(initialOffsetX = { -it }) togetherWith
            slideOutHorizontally(targetOffsetX = { it })
    },
    predictivePopTransitionSpec: AnimatedContentTransitionScope<*>.() -> ContentTransform = {
        slideInHorizontally(initialOffsetX = { -it }) togetherWith
            slideOutHorizontally(targetOffsetX = { it })
    },
    entryProvider: (key: T) -> NavEntry<T>,
) {
    if (LocalInspectionMode.current) {
        CompositionLocalProvider(
            FixPreview.generateProvidedValue(NoOpNavigator),
        ) {
            NavDisplay(
                backStack = backStack,
                modifier = modifier,
                contentAlignment = contentAlignment,
                onBack = onBack,
                entryDecorators = entryDecorators,
                sceneStrategy = sceneStrategy,
                sizeTransform = sizeTransform,
                transitionSpec = transitionSpec,
                popTransitionSpec = popTransitionSpec,
                predictivePopTransitionSpec = predictivePopTransitionSpec,
                entryProvider = entryProvider,
            )
        }
    } else {
        NavDisplay(
            backStack = backStack,
            modifier = modifier,
            contentAlignment = contentAlignment,
            onBack = onBack,
            entryDecorators = entryDecorators,
            sceneStrategy = sceneStrategy,
            sizeTransform = sizeTransform,
            transitionSpec = transitionSpec,
            popTransitionSpec = popTransitionSpec,
            predictivePopTransitionSpec = predictivePopTransitionSpec,
            entryProvider = entryProvider,
        )
    }
}

@Composable
fun getNoneTransitionMetadata(): Map<String, Any> =
    NavDisplay.transitionSpec {
        EnterTransition.None togetherWith ExitTransition.None
    } +
        NavDisplay.popTransitionSpec {
            EnterTransition.None togetherWith ExitTransition.None
        } +
        NavDisplay.predictivePopTransitionSpec {
            EnterTransition.None togetherWith ExitTransition.None
        }

@Composable
fun getVerticalTransitionMetadata(): Map<String, Any> =
    NavDisplay.transitionSpec {
        // Slide new content up, keeping the old content in place underneath
        slideInVertically(
            initialOffsetY = { it },
            animationSpec = tween(DEFAULT_TRANSITION_DURATION_MILLISECOND),
        ) togetherWith ExitTransition.KeepUntilTransitionsFinished
    } +
        NavDisplay.popTransitionSpec {
            // Slide old content down, revealing the new content in place underneath
            EnterTransition.None togetherWith
                slideOutVertically(
                    targetOffsetY = { it },
                    animationSpec = tween(DEFAULT_TRANSITION_DURATION_MILLISECOND),
                )
        } +
        NavDisplay.predictivePopTransitionSpec {
            // Slide old content down, revealing the new content in place underneath
            EnterTransition.None togetherWith
                slideOutVertically(
                    targetOffsetY = { it },
                    animationSpec = tween(DEFAULT_TRANSITION_DURATION_MILLISECOND),
                )
        }

inline fun <T : Any> appEntryProvider(
    noinline fallback: (unknownScreen: T) -> NavEntry<T> = {
        throw IllegalStateException("Unknown screen $it")
    },
    builder: EntryProviderBuilder<T>.() -> Unit,
): (T) -> NavEntry<T> = entryProvider(fallback, builder)

inline fun <reified T : Any> EntryProviderBuilder<*>.appEntry(
    noinline clazzContentKey: (key: @JvmSuppressWildcards T) -> Any = { defaultContentKey(it) },
    metadata: Map<String, Any> = emptyMap(),
    noinline content: @Composable (T) -> Unit,
) {
    entry(clazzContentKey, metadata, content)
}

fun <T : Any> EntryProviderBuilder<T>.appEntry(
    key: T,
    contentKey: Any = defaultContentKey(key),
    metadata: Map<String, Any> = emptyMap(),
    content: @Composable (T) -> Unit,
) {
    entry(key, contentKey, metadata, content)
}

@PublishedApi
internal fun defaultContentKey(key: Any): Any = key.toString()

internal const val DEFAULT_TRANSITION_DURATION_MILLISECOND = 350
