package com.buque.wakoo.navigation.dialog

import androidx.compose.runtime.Composable
import com.buque.wakoo.ui.dialog.AnyPopDialogProperties

/**
 * 快速使用弹窗，有返回值可以用来关闭弹窗，发送事件，获取返回值。
 * @param needKeepInConfigChange 是否需要在配置变更的时候保留，默认是true，但不建议使用[DialogHandle.await]获取返回结果
 *                               如果为false，配置变更则弹窗消失。
 * @param dialogProperties 弹窗属性，可以配置弹窗底部弹出弹窗[AnyPopDialogProperties.useSystemDialog == false生效效]。
 * 如果需要进程重建也保留弹窗需要实现[DialogDestination.Restorable]接口并`kotlinx.serialization`序列化
 * 更建议使用[easyPost]
 */
suspend fun DialogController.easyShow(
    needKeepInConfigChange: Boolean = true,
    useSystemDialog: Boolean = true,
    dialogProperties: AnyPopDialogProperties = AnyPopDialogProperties(useSystemDialog = useSystemDialog),
    content: OnDialogContent,
): DialogHandle =
    show(
        object : DialogDestination {
            override val content: OnDialogContent
                get() = content

            override val needKeepInConfigChange: Boolean = needKeepInConfigChange

            override val dialogProperties: AnyPopDialogProperties = dialogProperties
        },
    )

/**
 * 快速使用弹窗，没有返回值，只能通过event获取事件，包括返回结果。使用从方法建议[needKeepInConfigChange]保持为true
 * @param needKeepInConfigChange 是否需要在配置变更的时候保留，默认是true，不建议更改（触发弹窗不适合保留）。
 * @param dialogProperties 弹窗属性，可以配置弹窗底部弹出弹窗[AnyPopDialogProperties.useSystemDialog == false生效效]。
 * 如果需要进程重建也保留弹窗需要实现[DialogDestination.Restorable]接口并`kotlinx.serialization`序列化
 */
fun DialogController.easyPost(
    needKeepInConfigChange: Boolean = true,
    useSystemDialog: Boolean = true,
    dialogProperties: AnyPopDialogProperties = AnyPopDialogProperties(useSystemDialog = useSystemDialog),
    content: OnDialogContent,
) = post(
    object : DialogDestination {
        override val content: OnDialogContent
            get() = content

        override val needKeepInConfigChange: Boolean = needKeepInConfigChange

        override val dialogProperties: AnyPopDialogProperties = dialogProperties
    },
)

/**
 * 底部弹窗
 */
fun DialogController.easyPostBottomPanel(
    needKeepInConfigChange: Boolean = true,
    useSystemDialog: Boolean = true,
    dialogProperties: AnyPopDialogProperties =
        AnyPopDialogProperties(
            useSystemDialog = useSystemDialog,
            useCustomAnimation = true,
        ),
    content: OnDialogContent,
) = post(
    object : DialogDestination {
        override val content: OnDialogContent
            get() = content

        override val needKeepInConfigChange: Boolean = needKeepInConfigChange

        override val dialogProperties: AnyPopDialogProperties = dialogProperties
    },
)

inline fun <reified T : DialogEvent> DialogEventWrapper.filterDo(
    tag: String? = null,
    action: (T) -> Unit,
): DialogEventWrapper? {
    if (this.tag == tag && this.event is T) {
        action(this.event)
        return null
    }
    return this
}

inline fun DialogEventWrapper.filterDoResult(
    tag: String? = null,
    action: (DialogResultEvent) -> Unit,
): DialogEventWrapper? = filterDo(tag, action)

abstract class BottomPanelDialog<Param : Any> : DialogDestination.Restorable {
    override val needKeepInConfigChange: Boolean = true

    override val dialogProperties: AnyPopDialogProperties = AnyPopDialogProperties(useCustomAnimation = true)

    override val content: OnDialogContent = {
        Content(it as Param)
    }

    @Composable
    abstract fun DialogScope.Content(param: Param)
}

abstract class BottomPanelWidgetDialog<Param : Any> : DialogDestination.Restorable {
    override val needKeepInConfigChange: Boolean = true

    override val dialogProperties: AnyPopDialogProperties =
        AnyPopDialogProperties(useSystemDialog = false, useCustomAnimation = true)

    override val content: OnDialogContent = {
        Content(it as Param)
    }

    @Composable
    abstract fun DialogScope.Content(param: Param)
}

abstract class CenterDialog<Param : Any> : DialogDestination.Restorable {
    override val needKeepInConfigChange: Boolean = true

    override val dialogProperties: AnyPopDialogProperties = AnyPopDialogProperties()

    override val content: OnDialogContent = {
        Content(it as Param)
    }

    @Composable
    abstract fun DialogScope.Content(param: Param)
}
