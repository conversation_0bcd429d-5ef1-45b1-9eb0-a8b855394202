package com.buque.wakoo.navigation

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.bean.isSelf
import com.buque.wakoo.ui.icons.MineSelected
import com.buque.wakoo.ui.icons.MineUnselected
import com.buque.wakoo.ui.icons.SquareSelected
import com.buque.wakoo.ui.icons.SquareUnselected
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.MI_SANS
import kotlinx.serialization.Serializable

interface ITabItemContent {
    @Composable
    fun TabContent(
        selected: Boolean,
        modifier: Modifier = Modifier,
        color: Color = Color.Unspecified,
    )
}

sealed interface HomeNavTab : ITabItemContent {
    @Serializable
    data object Square : HomeNavTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Column(
                modifier = modifier,
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(2.dp),
            ) {
                Image(
                    imageVector = if (selected) WakooIcons.SquareSelected else WakooIcons.SquareUnselected,
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                )
                Text(
                    text = "广场",
                    style = MaterialTheme.typography.labelMedium,
                    color = if (selected) Color(0xFF222222) else Color(0xFFB6B6B6),
                )
            }
        }
    }

    @Serializable
    data object Mine : HomeNavTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Column(
                modifier = modifier,
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(2.dp),
            ) {
                Image(
                    imageVector = if (selected) WakooIcons.MineSelected else WakooIcons.MineUnselected,
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                )
                Text(
                    text = "我的",
                    style = MaterialTheme.typography.labelMedium,
                    color = if (selected) Color(0xFF222222) else Color(0xFFB6B6B6),
                )
            }
        }
    }
}

sealed interface VoiceListTab : ITabItemContent {
    sealed interface ISquare : VoiceListTab

    data object Recommend : ISquare {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "推荐",
                color = color,
                modifier = modifier,
                fontFamily = FontFamily.MI_SANS,
                fontSize = 20.sp,
            )
        }
    }

    data object Follow : ISquare {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "关注",
                modifier = modifier,
                color = color,
                fontFamily = FontFamily.MI_SANS,
                fontSize = 20.sp,
            )
        }
    }

    sealed interface IUser : VoiceListTab {
        val userId: String

        val emptyText: String
            @Composable
            get() = ""

        val emptyId: Int
            get() = R.drawable.ic_empty_for_all

        val emptyButton: String?
            @Composable
            get() = null
    }

    data class MyPublish(
        override val userId: String,
    ) : IUser {
        override val emptyText: String
            @Composable
            get() = if (userId.isSelf) "您还没有发布声音，快去发布吧~" else "TA还没有发布声音"

        override val emptyButton: String?
            @Composable
            get() = if (userId.isSelf) "发布声音内容" else null

        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = if (userId.isSelf) "我的发布" else "TA的发布",
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.SemiBold else FontWeight.Normal,
            )
        }
    }

    data class Like(
        override val userId: String,
    ) : IUser {
        override val emptyText: String
            @Composable
            get() = "暂无点赞内容~"

        override val emptyId: Int
            get() = R.drawable.ic_empty_for_like

        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "点赞",
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.SemiBold else FontWeight.Normal,
            )
        }
    }

    data class Favorite(
        override val userId: String,
    ) : IUser {
        override val emptyText: String
            @Composable
            get() = "暂无收藏内容~"

        override val emptyId: Int
            get() = R.drawable.ic_empty_for_favorite

        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "收藏",
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.SemiBold else FontWeight.Normal,
            )
        }
    }
}

sealed interface RelationsKey {
    sealed interface TabContent :
        RelationsKey,
        ITabItemContent

    data object RecentlyChat : TabContent {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "聊天",
                modifier = modifier,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object Following : TabContent {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "关注",
                modifier = modifier,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object Followers : TabContent {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "粉丝",
                modifier = modifier,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object BlackList : RelationsKey
}

sealed interface MessageTab : ITabItemContent {
    data object IMMessage : MessageTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "私信",
                modifier = modifier,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object Notification : MessageTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "通知",
                modifier = modifier,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }
}

sealed interface RoomRankTab : ITabItemContent {
    data object Contribution : RoomRankTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "贡献榜",
                modifier = modifier,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object Charm : RoomRankTab {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "魅力榜",
                modifier = modifier,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }
}

sealed interface RankTimely : ITabItemContent {
    data object Daily : RankTimely {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "日榜",
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }

    data object Weekly : RankTimely {
        @Composable
        override fun TabContent(
            selected: Boolean,
            modifier: Modifier,
            color: Color,
        ) {
            Text(
                text = "周榜",
                modifier = modifier,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            )
        }
    }
}
