package com.buque.wakoo.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.navigation3.runtime.EntryProviderBuilder
import androidx.navigation3.runtime.entry
import com.buque.wakoo.ext.forEachReversedIndexed
import com.buque.wakoo.manager.EnvironmentManager
import kotlinx.serialization.Serializable

@Serializable
private data class ForResultWrapperKey(
    val key: AppNavKey,
    val resultKey: String,
) : AppNavKey by key

private val AppNavKey.rawKey: AppNavKey
    get() =
        if (this is ForResultWrapperKey) {
            key
        } else {
            this
        }

fun <T : AppNavKey> EntryProviderBuilder<*>.appResultEntry(
    clazzContentKey: (key: @JvmSuppressWildcards T) -> Any = { defaultContentKey(it) },
    metadata: Map<String, Any> = emptyMap(),
    content: @Composable (T, String) -> Unit,
) {
    entry<ForResultWrapperKey>(
        clazzContentKey = {
            clazzContentKey(it.key as T)
        },
        metadata = metadata,
    ) {
        content(it.key as T, it.resultKey)
    }
}

open class AppNavController(
    val key: CtrlKey<*>,
    private val defaultBackStack: AppNavBackStack,
    defaultMutableBackStack: AppNavBackStack = defaultBackStack,
    initialPendingResults: Map<String, Any> = emptyMap(),
) {
    protected open val mutableNavBackStack: AppNavBackStack = defaultMutableBackStack

    val backStack: List<AppNavKey> = defaultBackStack

    // 存储活跃的回调。非持久化，因为函数无法序列化。
    private val resultCallbacks = mutableMapOf<String, (Any) -> Unit>()

    // 存储待处理的结果。必须是可持久化的状态。
    // 使用 mutableStateMapOf 以便 Compose 能够观察到其变化。
    val pendingResults =
        mutableStateMapOf<String, Any>().apply {
            putAll(initialPendingResults)
        }

    /**
     * 检查导航键是否存在于返回栈中
     */
    fun isNavKeyInBackStack(navKey: AppNavKey): Boolean =
        mutableNavBackStack.any {
            it.rawKey == navKey
        }

    /**
     * 根据条件检查是否有匹配的导航键存在于返回栈中
     */
    fun hasMatchingNavKey(predicate: (AppNavKey) -> Boolean): Boolean =
        mutableNavBackStack.any {
            predicate(it.rawKey)
        }

    /**
     * 根据条件查找返回栈中的导航键
     */
    fun findNavKeyByPredicate(predicate: (AppNavKey) -> Boolean) =
        mutableNavBackStack.firstOrNull {
            predicate(it.rawKey)
        }

    /**
     * 由 rememberLauncherForResult 内部调用，用于注册回调。
     */
    internal fun registerResultCallback(
        key: String,
        callback: (Any?) -> Unit,
    ) {
        resultCallbacks[key] = callback
    }

    /**
     * 由 rememberLauncherForResult 内部调用，用于注销回调。
     */
    internal fun unregisterResultCallback(key: String) {
        resultCallbacks.remove(key)
    }

    /**
     * 由 rememberLauncherForResult 内部调用，用于消费已恢复的结果。
     */
    internal fun <T> consumePendingResult(key: String): T? {
        @Suppress("UNCHECKED_CAST")
        return pendingResults.remove(key) as? T
    }

    /**
     * 由 AppLauncher 调用，启动一个以获取结果为目的的导航。
     */
    internal fun launchForResult(
        navKey: AppNavKey,
        resultKey: String,
    ) {
        // 将原始 key 和 resultKey 包装起来再推入堆栈
        val wrapperKey = ForResultWrapperKey(navKey, resultKey)
        push(wrapperKey)
    }

    /**
     * 由目标页面调用，以返回结果。
     */
    fun setResult(
        resultKey: String,
        result: Any,
    ) {
        // 尝试立即调用回调。如果调用方已不存在（例如进程重启后），
        // 回调将不存在，此时我们将结果存入 pendingResults。
        val callback = resultCallbacks[resultKey]
        if (callback != null) {
            callback(result)
            // 调用后移除，防止重复调用
            unregisterResultCallback(resultKey)
        } else {
            pendingResults[resultKey] = result
        }
    }

    fun pop() {
        if (mutableNavBackStack.isNotEmpty()) {
            mutableNavBackStack.removeLastOrNull()
        }
    }

    fun popIf(navKey: AppNavKey) {
        if (getLastNavKey() == navKey) {
            pop()
        }
    }

    inline fun <reified T : AppNavKey> popIs() {
        if (getLastNavKey() is T) {
            pop()
        }
    }

    fun removeIf(filter: (AppNavKey) -> Boolean): Boolean =
        mutableNavBackStack.removeIf {
            filter(it.rawKey)
        }

    fun removeAll(predicate: (AppNavKey) -> Boolean) = mutableNavBackStack.removeAll { predicate(it.rawKey) }

    fun popUnit(filter: (AppNavKey) -> Boolean): Boolean {
        var fromIndex = -1
        mutableNavBackStack.forEachReversedIndexed { index, item ->
            if (filter(item.rawKey)) {
                fromIndex = index
            }
        }
        if (fromIndex != -1 && fromIndex < mutableNavBackStack.lastIndex) {
            mutableNavBackStack.removeRange(fromIndex + 1, mutableNavBackStack.size)
        }
        return fromIndex != -1
    }

    fun moveToTop(navKey: AppNavKey) {
        if (mutableNavBackStack.remove(navKey)) {
            mutableNavBackStack.add(navKey)
        }
    }

    fun clear() {
        mutableNavBackStack.clear()
    }

    fun getLastNavKey() = mutableNavBackStack.lastOrNull()?.rawKey

    open fun push(navKey: AppNavKey) {
        mutableNavBackStack.add(navKey)
    }

    open fun replaceLast(navKey: AppNavKey) {
        if (mutableNavBackStack.isEmpty()) {
            push(navKey)
        } else {
            mutableNavBackStack[mutableNavBackStack.lastIndex] = navKey
        }
    }
}

class RootNavController(
    private val loginNavKey: AppNavKey,
    private val backStackWithNotLoggedIn: AppNavBackStack,
    private val backStackWithLoggedIn: AppNavBackStack,
    initialPendingResults: Map<String, Any> = emptyMap(),
    isLoggedIn: Boolean = false,
    loginSuccessNavKey: AppNavKey? = null,
) : AppNavController(
        key = RootNavCtrlKey,
        defaultBackStack = backStackWithNotLoggedIn,
        defaultMutableBackStack = if (isLoggedIn) backStackWithLoggedIn else backStackWithLoggedIn,
        initialPendingResults = initialPendingResults,
    ) {
    private var isLoggedIn by mutableStateOf(isLoggedIn)

    val loggedInBackStack: List<AppNavKey> = backStackWithLoggedIn

    override val mutableNavBackStack: AppNavBackStack
        get() = if (isLoggedIn) backStackWithLoggedIn else backStackWithNotLoggedIn

    var onLoginSuccessNavKey: AppNavKey? = loginSuccessNavKey
        private set(value) {
            // If the user explicitly requested the login AppNavKey, don't redirect them after login
            if (value !== loginNavKey && value !== LoggedInHost && value !== LoggedInHost.rootNavKey) {
                field = value
            }
        }
//
//    override fun push(navKey: AppNavKey) {
//        if (navKey.isRequiresLogin && !isLoggedIn) {
//            // Store the intended destination and redirect to login
//            onLoginSuccessNavKey = navKey
//            val existsLoginNav =
//                backStackWithNotLoggedIn.any {
//                    it.rawKey == loginNavKey
//                }
//            if (!existsLoginNav) {
//                super.push(loginNavKey)
//            }
//        } else {
//            checkAddLoggedInHost()
//            super.push(navKey)
//        }
//    }
//
//    override fun replaceLast(navKey: AppNavKey) {
//        if (navKey.isRequiresLogin && !isLoggedIn) {
//            // Store the intended destination and redirect to login
//            onLoginSuccessNavKey = navKey
//            val existsLoginNav =
//                backStackWithNotLoggedIn.any {
//                    it.rawKey == loginNavKey
//                }
//            if (!existsLoginNav) {
//                super.replaceLast(loginNavKey)
//            }
//        } else {
//            checkAddLoggedInHost()
//            super.replaceLast(navKey)
//        }
//    }

    /**
     * 登录
     */
    fun login() {
        if (isLoggedIn) {
            return
        }

        // 标记为已登录
        isLoggedIn = true

        // 移除其他页面
        backStackWithLoggedIn.clear()
        // 向已登录容器添加根页面
        backStackWithLoggedIn.add(LoggedInHost.rootNavKey)
        onLoginSuccessNavKey
            ?.takeIf { it.rawKey !== LoggedInHost.rootNavKey }
            ?.let {
                backStackWithLoggedIn.add(it)
            }

        // 移除其他页面
        backStackWithNotLoggedIn.clear()
        // 添加已登录容器
        backStackWithNotLoggedIn.add(LoggedInHost)
    }

    /**
     * 退出登录
     */
    fun logout() {
        if (!isLoggedIn) {
            return
        }
        // 标记为未登录
        isLoggedIn = false

        // 这里其实可以尝试保存之前的返回栈，然后再登录后检查是否是同一个用户的，可以直接还原
        backStackWithNotLoggedIn.add(loginNavKey)
        backStackWithNotLoggedIn.removeAll { it.isRequiresLogin }

//        立刻清空目前会报错，不清空也没事，登录的时候会清空的
//        loggedBackStack.clear()
    }

    /**
     * 登录状态要进行检查，确保根页面存在，并且要放在最下面
     */
    private fun checkAddLoggedInHost() {
        if (isLoggedIn) {
            if (backStackWithNotLoggedIn.singleOrNull()?.rawKey !== LoggedInHost) {
                // 移除其他页面
                backStackWithNotLoggedIn.clear()
                // 添加已登录容器
                backStackWithNotLoggedIn.add(LoggedInHost)
            }
            val index =
                backStackWithLoggedIn.indexOfFirst {
                    it.rawKey === LoggedInHost.rootNavKey
                }
            if (index != 0) {
                if (index != -1) {
                    backStackWithLoggedIn.removeAt(index)
                }
                backStackWithLoggedIn.add(
                    index = 0,
                    element = LoggedInHost.rootNavKey,
                )
            }
        }
    }
}

class BottomNavController<T : BottomNavKey<*>>(
    key: CtrlKey<*>,
    navBackStack: AppNavBackStack,
) : AppNavController(
        key,
        navBackStack,
    ) {
    @Suppress("UNCHECKED_CAST")
    var topNavKey: T by mutableStateOf(navBackStack.last() as T)
        private set

    fun pushToTop(navKey: T) {
        if (navKey == topNavKey) {
            return
        }
        mutableNavBackStack.apply {
            remove(navKey)
            add(navKey)
        }
        topNavKey = navKey
    }

    override fun push(navKey: AppNavKey) {
        if (!EnvironmentManager.isProdRelease) {
            error("不支持，请使用pushTopLevel")
        }
    }

    override fun replaceLast(navKey: AppNavKey) {
        if (!EnvironmentManager.isProdRelease) {
            error("不支持，请使用pushTopLevel")
        }
    }
}
