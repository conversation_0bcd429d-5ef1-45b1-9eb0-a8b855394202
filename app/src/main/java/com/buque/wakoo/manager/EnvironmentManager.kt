package com.buque.wakoo.manager

import android.content.Context
import com.buque.wakoo.BuildConfig
import com.buque.wakoo.bean.AppConfig
import com.buque.wakoo.bean.Environment

object EnvironmentManager {
    val isReleaseBuild = BuildConfig.BUILD_TYPE == "release"

    val isProfileBuild = BuildConfig.BUILD_TYPE == "profile"

    val isDebugBuild = BuildConfig.BUILD_TYPE == "debug"

    val isProdRelease = isReleaseBuild && BuildConfig.FLAVOR == "production"

    private val fullAppConfig: AppConfig by lazy {
        // 从 BuildConfig 构建开发环境对象
        val devEnvironment =
            Environment(
                name = BuildConfig.APP_DEV_NAME,
                apiUrl = BuildConfig.APP_DEV_API_URL,
                apiSignAccessKey = BuildConfig.APP_DEV_API_SIGN_ACCESS_KEY,
                apiSignSecretKey = BuildConfig.APP_DEV_API_SIGN_SECRET_KEY,
                smOrganization = BuildConfig.APP_DEV_SM_ORGANIZATION,
                smAppId = BuildConfig.APP_DEV_SM_APP_ID,
                smPubKey = BuildConfig.APP_DEV_SM_PUBLISH_KEY,
                enableAnalytics = BuildConfig.APP_DEV_ENABLE_ANALYTICS,
                enableLog = BuildConfig.APP_DEV_ENABLE_LOG,
                googleClientId = BuildConfig.APP_DEV_GOOGLE_WEB_CLIENT_ID,
                tencentAppId = BuildConfig.APP_DEV_TENCENT_APPID,
                tencentAppkey = BuildConfig.APP_DEV_TENCENT_APPKEY,
                tencentRtcId = BuildConfig.APP_DEV_TENCENT_RTC_APPID,
            )

        // 从 BuildConfig 构建生产环境对象
        val prodEnvironment =
            Environment(
                name = BuildConfig.APP_PROD_NAME,
                apiUrl = BuildConfig.APP_PROD_API_URL,
                apiSignAccessKey = BuildConfig.APP_PROD_API_SIGN_ACCESS_KEY,
                apiSignSecretKey = BuildConfig.APP_PROD_API_SIGN_SECRET_KEY,
                smOrganization = BuildConfig.APP_PROD_SM_ORGANIZATION,
                smAppId = BuildConfig.APP_PROD_SM_APP_ID,
                smPubKey = BuildConfig.APP_PROD_SM_PUBLISH_KEY,
                enableAnalytics = BuildConfig.APP_PROD_ENABLE_ANALYTICS,
                enableLog = BuildConfig.APP_PROD_ENABLE_LOG,
                googleClientId = BuildConfig.APP_PROD_GOOGLE_WEB_CLIENT_ID,
                tencentAppId = BuildConfig.APP_PROD_TENCENT_APPID,
                tencentAppkey = BuildConfig.APP_PROD_TENCENT_APPKEY,
                tencentRtcId = BuildConfig.APP_PROD_TENCENT_RTC_APPID,
            )

        // 创建包含所有环境的 Map
        val environmentsMap =
            mapOf(
                "development" to devEnvironment,
                "production" to prodEnvironment,
            )

        // 组装最终的、包含所有信息的 AppConfig 对象
        AppConfig(
            // 使用 BuildConfig.FLAVOR 来确定哪个是当前默认环境
            defaultEnvironment = BuildConfig.FLAVOR,
            environments = environmentsMap,
        )
    }

    // 1. 直接暴露当前激活的 Environment 对象，这是最常用的。
    val current: Environment by lazy {
        val currentFlavor = BuildConfig.FLAVOR // "development" 或 "production"
        fullAppConfig.environments[currentFlavor]
            ?: throw IllegalStateException("Current flavor '$currentFlavor' does not have a matching environment in the config map.")
    }

    fun initialize(
        context: Context,
        initialEnvKey: String? = null,
    ) {
    }

    /**
     * 获取包含所有环境配置的完整 AppConfig 对象。
     */
    fun getConfig(): AppConfig = fullAppConfig

    /**
     * 获取所有可用环境的列表。
     * 非常适合用于构建调试菜单中的环境选择列表。
     * @return List<Environment>
     */
    fun getAllEnvironments(): List<Environment> = fullAppConfig.environments.values.toList()

    /**
     * 切换到另一个环境。
     * @param envKey 要切换到的环境的 Key (例如 "development", "production")
     * @return 切换成功返回 true，如果环境不存在则返回 false。
     */
    fun switchEnvironment(envKey: String): Boolean = false
}
