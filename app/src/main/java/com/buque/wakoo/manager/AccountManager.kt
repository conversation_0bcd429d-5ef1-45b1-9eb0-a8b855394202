package com.buque.wakoo.manager

import android.content.Context
import android.webkit.CookieManager
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.bean.AccountInfo
import com.buque.wakoo.bean.BasicUser
import com.buque.wakoo.bean.UserExtraInfo
import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.repository.AccountPreferencesRepository
import com.buque.wakoo.repository.PreviewAccountPreferencesRepository
import com.buque.wakoo.utils.preload.AppPreLoader
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object AccountManager {
    private val accountPreferencesRepository =
        try {
            AccountPreferencesRepository()
        } catch (_: Exception) {
            PreviewAccountPreferencesRepository()
        }

    // 公开的、只读的 StateFlow，供外部（如ViewModel）观察用户状态
    val accountStateFlow: StateFlow<AccountInfo?> = accountPreferencesRepository.accountInfoFlow

    val userStateFlow: StateFlow<UserInfo?> =
        accountStateFlow
            .map { it?.userInfo }
            .stateIn(
                appCoroutineScope,
                SharingStarted.Eagerly,
                accountStateFlow.value?.userInfo,
            )

    private var isInitialized = false

    val isLoggedIn: Boolean
        get() = accountStateFlow.value != null

    val selfUser: UserInfo?
        get() = userStateFlow.value

    /**
     * 初始化 UserManager。必须在 Application 的 onCreate 中调用一次。
     * @param context 应用程序上下文，用于初始化 UserPreferencesRepository
     */
    fun initialize(context: Context) {
        if (isInitialized) {
            return
        }
        isInitialized = true
        // 启动一个协程，持续监听来自 DataStore 的数据变化
        appCoroutineScope.launch {
            launch {
                delay(100)
                if (isLoggedIn) { // 如果保存有用户信息，冷启动自动登录，刷新自己的数据
                    UserManager.getRemoteSelfUserInfo()
                }
            }

            accountStateFlow
                .distinctUntilChangedBy { item ->
                    item?.id
                }.collect { accountData ->
                    if (accountData != null) { // 登录成功
                        // 预加载相关资源
                        syncCookieToken(accountData.apiAuthToken)
                        AppPreLoader.loadAfterLoggedIn()
                        IMCompatCore.login(accountData.id, accountData.imToken)
                    } else {
                        clearCookie()
                        IMCompatCore.logout()
                    }
                }
        }
    }

    /**
     * 用户登录
     * @param accountInfo 登录成功后获取的用户信息
     */
    suspend fun login(accountInfo: AccountInfo) {
        checkInitialized()
        accountPreferencesRepository.saveData(accountInfo)
    }

    /**
     * 用户退出登录
     */
    suspend fun logout() {
        checkInitialized()
        accountPreferencesRepository.clearData()
    }

    suspend fun updateUserInfo(action: (user: UserInfo) -> UserInfo) {
        checkInitialized()
        withContext(NonCancellable) {
            accountPreferencesRepository.updateInfo {
                it.copy(userInfo = action(it.userInfo))
            }
        }
    }

    suspend fun setApiTokens(
        accessToken: String,
        refreshToken: String,
    ) {
        checkInitialized()
        syncCookieToken(accessToken)
        accountPreferencesRepository.updateInfo {
            it.copy(
                tokenInfo =
                    it.tokenInfo.copy(
                        apiAuthToken = accessToken,
                        apiRefreshToken = refreshToken,
                    ),
            )
        }
    }

    private fun syncCookieToken(authToken: String?) {
        val value =
            if (authToken.isNullOrBlank()) {
                "Access-Token="
            } else {
                "Access-Token=$authToken"
            }
        val urls =
            arrayOf(
                "https://fastapi.wakooclub.com",
                "https://api.test.wakooclub.com",
                "https://api.wakooclub.com",
            )
        try {
            CookieManager.getInstance().apply {
                for (url in urls) {
                    setAcceptCookie(true)
                    setCookie(url, value)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun clearCookie() {
        CookieManager.getInstance().removeAllCookies(null)
    }

    /**
     * 获取当前的认证Token。
     * 可以在需要发起网络请求的地方（例如 Retrofit 的 Interceptor）调用。
     * @return 当前用户的 authToken，如果未登录则返回 null。
     */
    fun getAccessToken(): String? {
        checkInitialized()
        return accountStateFlow.value?.apiAuthToken
    }

    /**
     * 获取当前的刷新Token。
     * @return 当前用户的 refreshToken，如果未登录则返回 null。
     */
    fun getRefreshToken(): String? {
        checkInitialized()
        return accountStateFlow.value?.apiRefreshToken
    }

    /**
     * 检查 UserManager 是否已初始化。
     */
    private fun checkInitialized() {
        if (!isInitialized) {
            throw IllegalStateException("UserManager must be initialized in Application.onCreate()")
        }
    }

    suspend fun updateUserBasicInfo(updateAction: (BasicUser) -> BasicUser) {
        updateUserInfo {
            it.copy(basic = updateAction(it.basic))
        }
    }

    suspend fun updateUserExtraInfo(updateAction: (UserExtraInfo) -> UserExtraInfo) {
        updateUserInfo {
            it.copy(extra = it.extra.let(updateAction))
        }
    }
}
