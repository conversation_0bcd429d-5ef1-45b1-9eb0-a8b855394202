package com.buque.wakoo.manager

import android.os.Bundle
import androidx.compose.runtime.saveable.Saver
import androidx.savedstate.SavedState
import androidx.savedstate.SavedStateRegistry
import androidx.savedstate.serialization.SavedStateConfiguration
import androidx.savedstate.serialization.decodeFromSavedState
import androidx.savedstate.serialization.encodeToSavedState
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.Const
import com.buque.wakoo.im.utils.takeIsNotBlank
import com.buque.wakoo.navigation.UnsafePolymorphicSerializer

private val saverAny: Saver<Any, SavedState> =
    Saver(
        save = { original ->
            encodeToSavedState(
                UnsafePolymorphicSerializer(),
                original,
                SavedStateConfiguration.DEFAULT,
            )
        },
        restore = { savedState ->
            decodeFromSavedState(
                UnsafePolymorphicSerializer(),
                savedState,
                SavedStateConfiguration.DEFAULT,
            )
        },
    )

object AppSavedStateManager {
    fun bind(savedStateRegistry: SavedStateRegistry) {
        savedStateLiveRoom(savedStateRegistry)
    }

    private fun savedStateLiveRoom(savedStateRegistry: SavedStateRegistry) {
        savedStateRegistry.registerSavedStateProvider(
            Const.SavedState.LIVE_ROOM,
        ) {
            val bundle = Bundle()
            val current = LiveRoomManager.getCurrentSaveStateRoom()
            if (current != null) {
                bundle.putString(Const.SavedState.DATA, AppJson.encodeToString(current))
            }
            bundle
        }

        // 恢复数据
        val restoredBundle = savedStateRegistry.consumeRestoredStateForKey(Const.SavedState.LIVE_ROOM)
        restoredBundle?.let {
            if (it.isEmpty) {
                return
            }
            val saveStateRoom =
                it.getString(Const.SavedState.DATA)?.takeIsNotBlank()?.let { value ->
                    AppJson.decodeFromString<SaveStateRoom?>(value)
                }
            if (saveStateRoom != null) {
                LiveRoomManager.restoreSaveStateRoom(saveStateRoom)
            }
        }
    }
}
