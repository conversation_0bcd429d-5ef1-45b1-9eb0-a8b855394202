package com.buque.wakoo.manager

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.runtime.snapshots.Snapshot
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.bean.BasicUser
import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.ext.keepLastNonNullState
import com.buque.wakoo.navigation.LiveRoomRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.repository.LiveRoomRepository
import com.buque.wakoo.ui.screens.liveroom.LiveMicMode
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.screen.LiveRoomNavCtrlKey
import com.buque.wakoo.viewmodel.liveroom.LiveRoomViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.serialization.Serializable

private class CurrentRoom(
    val id: String,
) {
    val isExpandRoom = mutableStateOf(true)

    var hasCollapsed = false
}

@Serializable
data class SaveStateRoom(
    val info: BasicRoomInfo,
    val isExpandRoom: Boolean,
    val hasCollapsed: Boolean,
)

object LiveRoomManager {
    private var currentRoom by mutableStateOf<CurrentRoom?>(null)

    private var currentRepository by mutableStateOf<LiveRoomRepository?>(null)

    private val onExitRoom: (String, String?) -> Unit = { roomId, reason ->
        Snapshot.withMutableSnapshot {
            if (currentRoom?.id == roomId) {
                currentRoom = null
                currentRepository?.release(reason)
                currentRepository = null
            }
        }
    }

    private val background by derivedStateOf {
        currentRepository?.roomInfoState?.basicInfo?.background
    }

    val isCollapse: Boolean by derivedStateOf {
        currentRoom?.isExpandRoom?.value == false
    }

    fun getCurrentSaveStateRoom(): SaveStateRoom? {
        val currentRoom = currentRoom
        if (currentRoom != null && currentRepository != null) {
            return currentRepository?.roomInfoState?.basicInfo?.let {
                SaveStateRoom(it, currentRoom.isExpandRoom.value, currentRoom.hasCollapsed)
            }
        }
        return null
    }

    fun roomHasCollapseHistory(roomId: String): Boolean =
        if (currentRoom?.id == roomId) {
            currentRoom?.hasCollapsed == true
        } else {
            false
        }

    @Composable
    fun liveRoomBackground() = keepLastNonNullState(background)

    @Composable
    fun AutoCollapse(roomId: String) {
        if (currentRoom != null && currentRoom?.id == roomId) {
            DisposableEffect(Unit) {
                if (currentRoom != null && currentRoom?.id == roomId) {
                    currentRoom?.isExpandRoom?.value = true
                }
                onDispose {
                    collapseRoom(roomId)
                }
            }
        }
    }

    @Composable
    fun attachLiveRoomViewModel(basicInfo: BasicRoomInfo): LiveRoomViewModel {
        val rootNavController = LocalAppNavController.root

        LaunchedEffect(Unit) {
            snapshotFlow {
                currentRoom == null || currentRoom?.id != basicInfo.id
            }.filter { it }
                .collectLatest {
                    rootNavController.removeIf {
                        it is Route.LiveRoom && it.basicInfo.id == basicInfo.id
                    }
                }
        }

        return viewModel<LiveRoomViewModel>(factory = LiveRoomViewModel.Factory(basicInfo)).apply {
            LaunchedEffect(rootNavController, roomInfoState) {
                roomInfoState.events
                    .onEach { event ->
                        if (event is RoomEvent.CollapseRoom) {
                            rootNavController.popIs<Route.LiveRoom>()
                        }
                    }.launchIn(this)
            }
        }
    }

    fun joinRoom(roomId: String) {
        joinRoom(
            BasicRoomInfo(
                id = roomId,
                publicId = "",
                title = "",
                owner =
                    UserInfo(
                        basic =
                            BasicUser(
                                id = "-1",
                                publishId = "0",
                                name = "",
                                age = 0,
                                gender = -1,
                                avatar = "",
                                birthday = "",
                            ),
                    ),
                roomMode = LiveRoomMode.UnKnown(-1),
                micMode = LiveMicMode.Free,
                desc = null,
                notice = null,
                background = null,
                tagIds = null,
            ),
        )
    }

    fun restoreSaveStateRoom(saveStateRoom: SaveStateRoom) {
        if (currentRoom == null && currentRepository == null) {
            currentRepository = LiveRoomRepository(saveStateRoom.info, onExitRoom)
            currentRoom =
                CurrentRoom(saveStateRoom.info.id).also {
                    it.isExpandRoom.value = saveStateRoom.isExpandRoom
                    it.hasCollapsed = saveStateRoom.hasCollapsed
                }
        }
    }

    fun joinRoom(basicInfo: BasicRoomInfo) {
        Snapshot.withMutableSnapshot {
            if (currentRoom?.id != basicInfo.id) {
                currentRepository?.release(null)
                currentRepository = LiveRoomRepository(basicInfo, onExitRoom)
                currentRoom = CurrentRoom(basicInfo.id)
            } else {
                currentRoom?.isExpandRoom?.value = true
            }
            LocalAppNavController.useRoot?.apply {
                val exists =
                    findNavKeyByPredicate {
                        it is Route.LiveRoom && it.basicInfo.id != basicInfo.id
                    }
                if (exists != null) {
                    moveToTop(exists)
                } else {
                    Snapshot.withMutableSnapshot {
                        removeAll { it is Route.LiveRoom }
                        push(Route.LiveRoom(basicInfo))
                    }
                }
            }
        }
    }

    fun expandCurrentRoom() {
        if (currentRoom != null && currentRepository != null) {
            currentRoom?.isExpandRoom?.value = true
            LocalAppNavController.useRoot?.apply {
                if (popUnit { it is Route.LiveRoom }) {
                    LocalAppNavController[LiveRoomNavCtrlKey]?.popUnit {
                        it is LiveRoomRoute.Home
                    }
                } else {
                    push(Route.LiveRoom(currentRepository!!.roomInfoState.basicInfo))
                }
            }
        }
    }

    fun exitCurrentRoom() {
        if (currentRoom != null) {
            onExitRoom(currentRoom?.id.orEmpty(), null)
        }
    }

    fun collapseRoom(id: String) {
        if (currentRoom?.id == id) {
            currentRoom?.hasCollapsed = true
            currentRoom?.isExpandRoom?.value = false
        }
    }

    fun createLiveRoomRepository(basicInfo: BasicRoomInfo): LiveRoomRepository =
        currentRepository?.takeIf { it.id == basicInfo.id } ?: LiveRoomRepository(basicInfo, onExitRoom)
}
