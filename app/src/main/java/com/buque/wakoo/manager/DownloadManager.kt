package com.buque.wakoo.manager

import android.annotation.SuppressLint
import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.produceState
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshotFlow
import androidx.compose.runtime.snapshots.SnapshotStateMap
import com.buque.wakoo.utils.DateTimeUtils
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okio.use
import java.io.File
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.PriorityBlockingQueue

/**
 * 任务状态的密封类 (Sealed Class)。
 * 相比枚举能携带更多信息（如进度、错误），表达力更强，是状态建模的理想选择。
 */
sealed class DownloadStatus {
    /** 任务已加入队列，等待调度器执行。*/
    object PENDING : DownloadStatus()

    /** 任务正在下载中。*/
    data class RUNNING(
        val progress: Int, // 下载进度百分比 (0-100)
        val downloadedBytes: Long, // 已下载的字节数
        val totalBytes: Long, // 文件总字节数
    ) : DownloadStatus()

    /** 任务已被用户手动暂停。*/
    object PAUSED : DownloadStatus()

    /** 任务已成功下载并完成。这是一个终态。*/
    object COMPLETED : DownloadStatus()

    /** 任务因发生错误而失败。这是一个终态。*/
    data class FAILED(
        val error: Throwable,
    ) : DownloadStatus()

    /** 任务已被用户手动取消。这是一个终态。*/
    object CANCELLED : DownloadStatus()
}

/**
 * 任务优先级枚举。
 * 用于决定任务在等待队列中的执行顺序。
 */
enum class Priority {
    LOW,
    NORMAL,
    HIGH,
    HIGHEST,
}

/**
 * 下载任务的数据模型 (Data Class)。
 * 这是一个不可变对象，任何状态的变更都通过创建新的实例 (`copy`方法) 来完成，符合函数式编程思想。
 * @param id 任务的唯一标识符，由 URL 的哈希值生成。
 * @param url 文件的下载地址。
 * @param filePath 文件存储的目录路径。
 * @param fileName 文件名。
 * @param priority 任务优先级。
 * @param status 任务当前的下载状态。
 */
data class DownloadTask(
    val id: String,
    val url: String,
    val filePath: String,
    val fileName: String,
    val priority: Priority = Priority.NORMAL,
    val status: DownloadStatus = DownloadStatus.PENDING,
)

/**
 * 下载管理器 (单例 Object) - 专为 Jetpack Compose 优化。
 *
 * 这是一个全局单例，负责管理所有下载任务的生命周期、调度、状态更新和持久化。
 * 它被设计为应用内所有下载需求的中央枢纽。
 */
object DownloadManager {
    // --- 核心配置 ---
    private const val MAX_CONCURRENT_DOWNLOADS = 3 // 最大并行下载数，防止网络拥塞和资源过度消耗。
    private const val MAX_RETRIES = 3 // 失败后最大重试次数，以应对临时的网络问题。
    private const val RETRY_DELAY_MS = 2000L // 每次重试的基础延迟时间，可能会随重试次数增加。

    // --- 协程与线程管理 ---
    // 使用 SupervisorJob，确保一个下载任务的失败（子协程抛出异常）不会取消管理器本身或其他下载任务。
    // Dispatchers.Default 适合用于管理调度逻辑，因为它不阻塞主线程，并且为CPU密集型操作优化。
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    private lateinit var okHttpClient: OkHttpClient

    @SuppressLint("StaticFieldLeak")
    private lateinit var notificationHelper: NotificationHelper

    @Volatile // 确保 isInitialized 在多线程环境下的可见性，防止因指令重排导致的初始化问题。
    private var isInitialized = false

    // --- 状态管理 ---

    /**
     * 【核心状态】活动任务列表。
     * 使用 Compose 的 `SnapshotStateMap` 作为单一数据源，它对 Compose UI 友好且线程安全。
     * 关键特性：此 Map 只包含 **正在进行中**（排队、下载、暂停）的任务。
     * 一旦任务完成（成功、失败或取消），它将从此 Map 中 **被移除**。
     * 这使得UI层可以简单地通过观察这个 Map 来展示一个干净的“活动任务”列表。
     */
    val tasks = SnapshotStateMap<String, DownloadTask>()

    // --- 任务调度 ---
    // 使用优先级阻塞队列存储等待中的任务。它能自动根据任务的 `priority` 排序，确保高优先级任务先执行。
    private val taskQueue =
        PriorityBlockingQueue<DownloadTask>(10, compareByDescending { it.priority })

    // 存储正在运行的任务的Job，Key是任务ID。用于后续的暂停或取消操作。
    private val activeJobs = ConcurrentHashMap<String, Job>()

    /**
     * 【必须调用】初始化下载管理器。
     * 强烈建议在应用的 `Application` 类的 `onCreate` 方法中调用，以确保在任何地方使用前都已完成初始化。
     * @param context 应用上下文，用于创建通知。最好传入 `applicationContext` 防止内存泄漏。
     * @param client 自定义的 OkHttpClient 实例。允许外部注入，方便进行统一的网络配置（如添加拦截器、配置缓存等）。
     */
    fun initialize(
        context: Context,
        client: OkHttpClient = OkHttpClient(),
    ) {
        if (isInitialized) return // 防止重复初始化
        this.okHttpClient = client
        this.notificationHelper = NotificationHelper(context.applicationContext)
        this.isInitialized = true
        startDispatcher() // 初始化完成后，立即启动任务调度器。
    }

    /**
     * 在所有公开方法前调用，确保管理器已初始化，提供运行时安全。
     */
    private fun checkInitialized() {
        if (!isInitialized) {
            throw IllegalStateException("DownloadManager must be initialized by calling initialize() before use.")
        }
    }

    /**
     * 将 URL 转换为唯一的任务 ID。
     * 使用 URL 的 `hashCode` 是一个简单有效的方法。在整个应用中都应使用此方法来生成和引用任务ID，以保证一致性。
     */
    fun getTaskId(url: String): String = url.hashCode().toString()

    /**
     * 【Compose 专用】为 UI 提供对单个活动任务状态的精确、高效的订阅。
     *
     * @param id 任务ID。
     * @return 一个 `State<DownloadTask?>` 对象。在 Composable 中使用 `val task by getTaskState(id)` 来订阅。
     *         使用 `derivedStateOf` 是一种性能优化，确保只有当 `tasks[id]` 的值真正改变时，才会触发UI重组。
     */
    @Composable
    fun getTaskState(id: String): State<DownloadTask?> = remember(id) { derivedStateOf { tasks[id] } }

    /**
     * 【Compose 专用核心函数】以声明式方式获取或下载文件。
     *
     * 这是为 Compose 设计的理想接口。它封装了“检查本地文件 -> 不存在则下载 -> 观察进度 -> 完成后更新”的全部逻辑。
     * UI层只需调用此函数并观察返回的 `State` 即可。
     *
     * @param url 文件的下载地址。
     * @param filePath 文件存储的目录路径。
     * @param fileName 文件名。
     * @param priority 下载优先级。
     * @return 一个 `State<File?>`。初始值可能是 `null`（如果文件不存在）或 `File`（如果已存在）。下载成功后，`null` 会更新为 `File` 对象。
     */
    @Composable
    fun rememberDownloadableFile(
        url: String,
        filePath: String,
        fileName: String,
        priority: Priority = Priority.NORMAL,
    ): State<File?> {
        checkInitialized()
        val id =
            remember(url) {
                getTaskId(url)
            }
        val targetFile = remember(filePath, fileName) { File(filePath, fileName) }

        // 使用 `produceState`，它是创建能与 Compose 生命周期绑定的 State 的强大工具，非常适合处理异步数据流。
        return produceState<File?>(
            initialValue = if (targetFile.exists()) targetFile else null,
            id,
            targetFile,
        ) {
            // 阶段1: 初始化检查。如果文件已存在，直接设置 state 的 value 并结束。
            if (value != null) return@produceState

            // 阶段2: 文件不存在，触发下载。
            // 检查任务是否已在活动列表中，如果不在，则加入队列。
            enqueue(url, filePath, fileName, priority)

            // 阶段3: 等待结果。
            // `snapshotFlow` 能将 Compose 的 State (`tasks`) 转换成一个 Cold Flow。
            // 我们监听这个 flow，直到我们关心的任务从 `tasks` map 中被移除（这标志着任务结束）。
            snapshotFlow { tasks[id] }
                .filter { it == null && targetFile.exists() } // 我们只对 `null` 值感兴趣，因为它表示任务已从 map 中消失, 且文件已存在才行。
                .first() // 我们只需要等到它第一次消失即可，然后就停止监听。

            // 阶段4: 任务结束，更新最终状态。
            // 任务被移除后，再次检查目标文件是否存在。
            if (targetFile.exists()) {
                value = targetFile // 下载成功，更新 State 为下载好的文件。
            }
            // 如果文件仍不存在（可能下载失败或被取消），State 会保持为 null。
        }
    }

    /**
     * 启动任务调度器协程。
     * 调度器是一个无限循环，是下载引擎的大脑，负责从队列中取出任务并执行。
     */
    private fun startDispatcher() {
        scope.launch {
            while (isActive) {
                // 当活跃任务数小于并发上限，且队列中有等待任务时...
                if (activeJobs.size < MAX_CONCURRENT_DOWNLOADS && taskQueue.isNotEmpty()) {
                    val task = taskQueue.take() // `take()` 会阻塞，直到队列中有元素可用。

                    // 简单的校验：确保任务仍然存在于活动列表中，并且确实是等待状态
                    val currentTask = tasks[task.id]
                    if (currentTask != null && currentTask.status is DownloadStatus.PENDING) {
                        startDownload(currentTask)
                    }
                    // 如果任务不存在或状态不对，就简单地丢弃它，继续循环。
                }
                delay(200) // 短暂延迟以避免在队列为空时 CPU 空转。
            }
        }
    }

    /**
     * 【重要】下载一个文件并挂起协程，直到下载完成、失败或被取消。
     *
     * 这是一个命令式的接口，非常适合在 ViewModel 或非 UI 的业务逻辑中使用，
     * 例如“下载一个配置文件，然后解析它”的场景。
     *
     * @param url 文件的下载地址。
     * @param filePath 文件存储的目录路径。
     * @param fileName 文件名。
     * @param priority 下载优先级。
     * @return `Result<File>`，成功时包含下载好的文件对象，失败时包含具体的异常。
     */
    suspend fun download(
        url: String,
        filePath: String,
        fileName: String,
        priority: Priority = Priority.NORMAL,
    ): Result<File> {
        checkInitialized()
        val targetFile = File(filePath, fileName)

        // 检查1：如果文件已存在，直接返回成功。
        if (targetFile.exists()) {
            return Result.success(targetFile)
        }

        val id = getTaskId(url)

        enqueue(url, filePath, fileName, priority)

        // 使用 snapshotFlow 监听任务状态，直到它从活动列表 `tasks` 中消失。
        return try {
            snapshotFlow { tasks[id] }
                .filter { it == null } // 等待任务完成并被移除的信号
                .first()

            // 任务结束后，检查最终结果
            if (targetFile.exists()) {
                Result.success(targetFile)
            } else {
                // 如果任务结束但文件不存在，说明下载失败或被取消了。
                // 这是一个通用的失败情况，因为具体的失败原因已在下载流程内部处理。
                Result.failure(IOException("Download did not complete successfully, file not found."))
            }
        } catch (e: Exception) {
            // 如果在等待过程中协程被取消，也视为失败。
            Result.failure(e)
        }
    }

    /**
     * 将一个新任务加入下载队列（“即发即忘”模式）。
     * 如果文件已存在或任务已在进行中，则此操作无效。
     * @return 任务的唯一 ID。
     */
    fun enqueue(
        url: String,
        filePath: String,
        fileName: String,
        priority: Priority = Priority.NORMAL,
    ): String {
        checkInitialized()
        val id = getTaskId(url)
        val targetFile = File(filePath, fileName)

        // 防御性编程：如果文件已存在，则不重复下载。
        if (targetFile.exists()) {
            return id
        }

        val existsTask = tasks[id]
        if (existsTask != null) {
            // 如果任务正在运行，或者新请求的优先级不比现在高，则不作处理。
            if (existsTask.status is DownloadStatus.RUNNING || priority <= existsTask.priority) {
                return id
            }

            // 如果任务当前处于等待状态，并且我们收到了一个更高优先级的请求...
            if (existsTask.status is DownloadStatus.PENDING) {
                // 【优化点】实现动态优先级调整
                // 1. 创建一个具有新优先级的新任务对象。
                val updatedTask = existsTask.copy(priority = priority)

                // 2. 尝试从队列中移除旧的、低优先级的任务。
                //    PriorityBlockingQueue.remove(Object) 是线程安全的，但性能是 O(n)。
                //    对于下载队列这种规模不大的场景，这是完全可以接受的。
                val removed = taskQueue.remove(existsTask)

                // 3. 更新我们的“单一数据源”
                tasks[id] = updatedTask

                // 4. 将高优先级的任务重新加入队列，它会排到更靠前的位置。
                //    即使 remove 失败（可能任务已被调度器取走），put 操作也能确保任务不会丢失。
                taskQueue.put(updatedTask)

                // 可以选择性地更新一下通知
                notificationHelper.showNotification(updatedTask)

                return id
            }

            // 如果任务是暂停或失败状态，可以根据产品需求决定是否要用新优先级恢复它。
            // 这里我们简单地返回，让用户手动调用 resume。
            return id
        }

        // 如果任务完全不存在，则创建新任务。
        val task = DownloadTask(id, url, filePath, fileName, priority)
        tasks[id] = task // 将任务添加到活动列表，Compose UI 会立即感知到。
        taskQueue.put(task) // 将任务放入优先级队列等待调度。
        notificationHelper.showNotification(task)
        return id
    }

    /**
     * 启动一个具体的下载任务，包含完整的重试和错误处理逻辑。
     */
    private fun startDownload(initialTask: DownloadTask) {
        val job =
            scope.launch {
                var attempt = 0
                while (attempt <= MAX_RETRIES && isActive) {
                    try {
                        // 只有在首次尝试时才更新为 RUNNING，重试时不重复更新。
                        if (attempt == 0) {
                            updateStatus(initialTask.id, DownloadStatus.RUNNING(0, 0, 0))
                        }
                        executeDownload(initialTask)
                        updateStatus(initialTask.id, DownloadStatus.COMPLETED) // 成功，更新状态并退出循环。
                        return@launch
                    } catch (e: Exception) {
                        if (e is CancellationException) {
                            // 如果是用户手动暂停或取消，状态已在 pause/cancel 方法中处理，直接退出协程。
                            return@launch
                        }

                        // 其他错误（如网络IO异常），准备重试。
                        attempt++
                        if (attempt > MAX_RETRIES) {
                            // 达到最大重试次数，最终置为失败。
                            updateStatus(initialTask.id, DownloadStatus.FAILED(e))
                        } else {
                            // 等待一段时间后重试，等待时间随次数增加。
                            delay(RETRY_DELAY_MS * attempt)
                        }
                    }
                }
            }

        activeJobs[initialTask.id] = job
        job.invokeOnCompletion {
            // 任务完成后（无论成功、失败、取消），从活跃任务 Job Map 中移除。
            activeJobs.remove(initialTask.id)
        }
    }

    /**
     * 【核心】执行网络请求和文件写入，实现了断点续传。
     * 这个函数被标记为 `suspend` 并在 `Dispatchers.IO` 上下文中执行，是处理阻塞式IO操作的最佳实践。
     */
    private suspend fun executeDownload(task: DownloadTask) =
        withContext(Dispatchers.IO) {
            val finalFile = File(task.filePath, task.fileName)
            val tempFile = File(finalFile.absolutePath + ".tmp") // 使用临时文件保证下载的原子性，避免产生不完整的文件。

            // 【断点续传】检查临时文件大小，作为 HTTP Range 请求的起点。
            val downloadedBytes = if (tempFile.exists()) tempFile.length() else 0L

            val request =
                Request
                    .Builder()
                    .url(task.url)
                    .apply {
                        if (downloadedBytes > 0) {
                            addHeader("Range", "bytes=$downloadedBytes-")
                        }
                    }.build()

            okHttpClient.newCall(request).execute().use { response ->
                // 200 OK (首次下载) 或 206 Partial Content (断点续传) 都表示成功。
                if (!response.isSuccessful && response.code != 206) {
                    throw IOException("Unexpected HTTP code: ${response.code} ${response.message}")
                }

                val body = response.body
                // 计算总大小。如果是断点续传，需要加上已下载的部分。
                val totalBytes =
                    if (downloadedBytes > 0) downloadedBytes + body.contentLength() else body.contentLength()

                // 使用追加模式写入临时文件。
                java.io.FileOutputStream(tempFile, downloadedBytes > 0).use { output ->
                    body.byteStream().use { input ->
                        val buffer = ByteArray(8 * 1024) // 8KB 缓冲区，是比较通用的IO操作大小。
                        var bytesRead = 0
                        var currentDownloaded = downloadedBytes
                        var lastUpdateTime = 0L

                        while (isActive && input.read(buffer).also { bytesRead = it } != -1) {
                            // 在写入循环中检查任务是否被外部暂停或取消。
                            val currentStatus = tasks[task.id]?.status
                            if (currentStatus is DownloadStatus.PAUSED || currentStatus is DownloadStatus.CANCELLED) {
                                throw CancellationException("Download was paused or cancelled by user.")
                            }

                            output.write(buffer, 0, bytesRead)
                            currentDownloaded += bytesRead

                            // 【性能优化】节流更新，避免过于频繁地更新UI和通知 (例如每500ms或1%进度更新一次)。
                            val now = DateTimeUtils.currentTimeMillis()
                            if (now - lastUpdateTime > 500) {
                                lastUpdateTime = now
                                val progress =
                                    if (totalBytes > 0) ((currentDownloaded * 100) / totalBytes).toInt() else -1
                                updateStatus(
                                    task.id,
                                    DownloadStatus.RUNNING(progress, currentDownloaded, totalBytes),
                                )
                            }
                        }
                    }
                }
            }
            // 下载成功后，将临时文件重命名为正式文件。这是一个原子操作，能保证文件完整性。
            if (!tempFile.renameTo(finalFile)) {
                throw IOException("Failed to rename temp file to final file.")
            }
        }

    /**
     * 暂停一个正在下载的任务。
     */
    fun pause(id: String) {
        checkInitialized()
        // 通过取消其 Job 来停止下载协程。传入自定义的 CancellationException 以便区分。
        activeJobs[id]?.cancel(CancellationException("Download paused by user."))
        updateStatus(id, DownloadStatus.PAUSED)
    }

    /**
     * 恢复一个已暂停或失败的任务。
     */
    fun resume(id: String) {
        checkInitialized()
        val task = tasks[id]
        if (task != null && (task.status is DownloadStatus.PAUSED || task.status is DownloadStatus.FAILED)) {
            // 将任务状态重置为 PENDING 并重新放入队列，等待调度器执行。
            val updatedTask = task.copy(status = DownloadStatus.PENDING)
            tasks[id] = updatedTask
            taskQueue.put(updatedTask)
        }
    }

    /**
     * 取消一个任务，并触发清理。
     */
    fun cancel(id: String) {
        checkInitialized()
        activeJobs[id]?.cancel(CancellationException("Download cancelled by user."))
        // 标记为取消。这会触发 updateStatus 中的逻辑，从活动列表移除并删除临时文件。
        updateStatus(id, DownloadStatus.CANCELLED)
    }

    /**
     * 内部核心方法，用于更新指定ID任务的状态，并处理后续逻辑。
     * @param id 任务ID。
     * @param status 新的状态。
     */
    private fun updateStatus(
        id: String,
        status: DownloadStatus,
    ) {
        val task = tasks[id] ?: return // 如果任务已不存在（可能已被其他线程处理），则直接返回。

        val updatedTask = task.copy(status = status)
        tasks[id] = updatedTask // 更新 SnapshotStateMap，Compose UI 会自动响应。
        notificationHelper.showNotification(updatedTask)

        // 【关键改动】如果任务已到达最终状态（完成、失败、取消），则执行清理工作。
        if (status is DownloadStatus.COMPLETED || status is DownloadStatus.FAILED || status is DownloadStatus.CANCELLED) {
            // 从活动列表 `tasks` 中移除，这是净化列表的关键步骤。
            tasks.remove(id)

            // 如果是取消或失败，需要额外删除可能存在的临时文件。
            if (status is DownloadStatus.CANCELLED || status is DownloadStatus.FAILED) {
                scope.launch(Dispatchers.IO) {
                    File(task.filePath, task.fileName + ".tmp").delete()
                }
            }

            if (status is DownloadStatus.CANCELLED) {
                notificationHelper.cancelNotification(id.hashCode())
            }
        }
    }
}

/**
 * 通知帮助类
 *
 * 将Android通知相关的逻辑与下载核心逻辑解耦。
 */
class NotificationHelper(
    private val context: Context,
) {
    //    private val notificationManager = NotificationManagerCompat.from(context)
//    private val channelId = "download_channel"

    init {
//        createNotificationChannel()
    }

    private fun createNotificationChannel() {
//        val name = "下载任务"
//        val descriptionText = "显示文件下载进度"
//        val importance = NotificationManager.IMPORTANCE_LOW // 使用低优先级，避免打扰用户
//        val channel = NotificationChannel(channelId, name, importance).apply {
//            description = descriptionText
//        }
//        notificationManager.createNotificationChannel(channel)
    }

    fun showNotification(task: DownloadTask) {
//        val notificationId = task.id.hashCode()
//        val builder = NotificationCompat.Builder(context, channelId)
//                .setSmallIcon(android.R.drawable.stat_sys_download) // 请替换为你的应用图标
//                .setContentTitle(task.fileName)
//                .setPriority(NotificationCompat.PRIORITY_LOW)
//                .setOngoing(true) // 设为true，用户无法轻易滑掉
//
//        when (val status = task.status) {
//            is DownloadStatus.RUNNING -> {
//                val progress = if (status.progress < 0) 0 else status.progress
//                val indeterminate = status.progress < 0 // 如果总大小未知，显示不确定进度条
//                builder.setContentText("下载中... ${progress}%")
//                        .setProgress(100, progress, indeterminate)
//            }
//            DownloadStatus.COMPLETED -> {
//                builder.setContentText("下载完成")
//                        .setSmallIcon(android.R.drawable.stat_sys_download_done)
//                        .setProgress(0, 0, false) // 移除进度条
//                        .setOngoing(false) // 下载完成，允许用户滑掉
//            }
//            is DownloadStatus.FAILED -> {
//                builder.setContentText("下载失败")
//                        .setProgress(0, 0, false)
//                        .setOngoing(false)
//            }
//            DownloadStatus.PAUSED -> {
//                builder.setContentText("已暂停")
//                        .setProgress(0, 0, false)
//            }
//            DownloadStatus.PENDING -> {
//                builder.setContentText("等待下载...")
//                        .setProgress(100, 0, true) // 等待时显示不确定进度条
//            }
//            DownloadStatus.CANCELLED -> {
//                // 如果是取消状态，直接移除通知
//                cancelNotification(notificationId)
//                return
//            }
//        }
//        notificationManager.notify(notificationId, builder.build())
    }

    fun cancelNotification(notificationId: Int) {
//        notificationManager.cancel(notificationId)
    }
}
