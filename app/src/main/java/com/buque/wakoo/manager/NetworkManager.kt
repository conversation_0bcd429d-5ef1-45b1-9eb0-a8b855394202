package com.buque.wakoo.manager

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.LinkProperties
import android.net.Network
import android.net.NetworkCapabilities
import android.text.TextUtils
import com.buque.wakoo.utils.LogUtils
import java.net.InetAddress
import java.net.UnknownHostException

@SuppressLint("MissingPermission")
object NetworkManager {
    const val TAG = "NetworkManager"
    private lateinit var connectivityManager: ConnectivityManager
    private var callbacks: ArrayList<Callback> = ArrayList()
    private var firstReceive: Boolean = false

    @Volatile
    private lateinit var currentCapabilities: NetworkCapabilities

    private val callback =
        object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                LogUtils.i(TAG, "onAvailable")
                callbacks.forEach { it.onNetworkAvailable() }
            }

            override fun onLosing(
                network: Network,
                maxMsToLive: Int,
            ) {
                LogUtils.i(TAG, "onLosing")
            }

            override fun onLost(network: Network) {
                LogUtils.i(TAG, "onLost")
                callbacks.forEach { it.onNetworkLost() }
            }

            override fun onUnavailable() {
                LogUtils.i(TAG, "onUnavailable")
            }

            override fun onCapabilitiesChanged(
                network: Network,
                networkCapabilities: NetworkCapabilities,
            ) {
                LogUtils.i(TAG, "onCapabilitiesChanged")
                synchronized(this) {
                    currentCapabilities = networkCapabilities
                    callbacks.forEach {
                        it.onNetworkChanged()
                    }
                }
            }

            override fun onLinkPropertiesChanged(
                network: Network,
                linkProperties: LinkProperties,
            ) {
                LogUtils.i(TAG, "onLinkPropertiesChanged")
            }

            override fun onBlockedStatusChanged(
                network: Network,
                blocked: Boolean,
            ) {
                LogUtils.i(TAG, "onBlockedStatusChanged")
            }
        }

    interface Callback {
        fun onNetworkAvailable() {
        }

        fun onNetworkLost() {
        }

        fun onNetworkChanged() {
        }
    }

    private class NetworkStateReceiver : BroadcastReceiver() {
        override fun onReceive(
            context: Context?,
            intent: Intent?,
        ) {
            if (intent?.action == "android.net.conn.CONNECTIVITY_CHANGE") {
                onNetStateChanged()
            }
        }
    }

    @JvmStatic
    fun initialize(context: Context) {
        connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        try {
            connectivityManager.registerDefaultNetworkCallback(callback)
        } catch (e: Exception) {
            // 长官说这个地方可能会出现崩溃要try catch住
        }
        makeSureNetworkCapabilities()
    }

    private fun makeSureNetworkCapabilities() {
        if (!::currentCapabilities.isInitialized) {
            synchronized(this) {
                if (!::currentCapabilities.isInitialized) {
                    currentCapabilities = connectivityManager.getNetworkCapabilities(connectivityManager.activeNetwork) ?: return
                }
            }
        }
    }

    fun register(callback: Callback) {
        synchronized(this) {
            if (!callbacks.contains(callback)) {
                callbacks.add(callback)
            }
        }
    }

    fun unregister(callback: Callback) {
        synchronized(this) {
            callbacks.remove(callback)
        }
    }

    fun isNetworkAvailable(): Boolean {
        makeSureNetworkCapabilities()
        if (!::currentCapabilities.isInitialized) {
            return false
        }
        return when {
            currentCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> true
            currentCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> true
            currentCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> true
            currentCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> true
            else -> false
        }
    }

    fun networkType(): String {
        makeSureNetworkCapabilities()
        if (!::currentCapabilities.isInitialized) {
            return "initial"
        }
        if (currentCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
            return "wifi"
        } else if (currentCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
            return "cellular"
        } else if (currentCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)) {
            return "ethernet"
        } else {
            if (isNetworkAvailable()) {
                return "unknown"
            } else {
                return "none"
            }
        }
    }

    fun isUsingVPN() = getNetworkState() == NetworkCapabilities.TRANSPORT_VPN

    fun isUsingProxy(): Boolean {
        val proxyAddress = System.getProperty("http.proxyHost")
        val proxyPort =
            try {
                System.getProperty("http.proxyPort").toInt()
            } catch (e: Exception) {
                0
            }
        return !proxyAddress.isNullOrBlank() && proxyPort != 0
    }

    fun isUsingVpnOrProxy() = isUsingVPN() || isUsingProxy()

    fun getNetworkState(): Int {
        makeSureNetworkCapabilities()
        if (!::currentCapabilities.isInitialized) {
            return -1
        }
        return currentCapabilities.run {
            when {
                hasTransport(NetworkCapabilities.TRANSPORT_VPN) -> NetworkCapabilities.TRANSPORT_VPN
                hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkCapabilities.TRANSPORT_WIFI
                hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> NetworkCapabilities.TRANSPORT_ETHERNET
                hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> NetworkCapabilities.TRANSPORT_CELLULAR
                hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> NetworkCapabilities.TRANSPORT_BLUETOOTH
                else -> -1
            }
        }
    }

    private fun onNetStateChanged() {
        if (!firstReceive) {
            firstReceive = true
            return
        }
        val isAvailable = isNetworkAvailable()
        if (isAvailable) {
            callbacks.forEach { it.onNetworkAvailable() }
        } else {
            callbacks.forEach { it.onNetworkLost() }
        }
    }

    fun isAvailableByDns(domain: String? = ""): Boolean {
        val realDomain = if (TextUtils.isEmpty(domain)) "www.baidu.com" else domain!!
        val inetAddress: InetAddress?
        return try {
            inetAddress = InetAddress.getByName(realDomain)
            inetAddress != null
        } catch (e: UnknownHostException) {
            e.printStackTrace()
            false
        }
    }
}
