package com.buque.wakoo.manager

import androidx.room.Room
import com.buque.wakoo.BuildConfig
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.bean.BasicUser
import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.db.AppDatabase
import com.buque.wakoo.manager.AccountManager.isLoggedIn
import com.buque.wakoo.manager.AccountManager.updateUserInfo
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.launch
import java.util.concurrent.CopyOnWriteArrayList

fun interface UserInfoUpdateListener {
    fun onUserInfoUpdated(userInfo: UserInfo)
}

object UserManager {
    private val DB_NAME = if (BuildConfig.DEBUG) "ucoo-user-debug" else "ucoo-user"

    private val db by lazy {
        Room.databaseBuilder(WakooApplication.instance, AppDatabase::class.java, DB_NAME).build()
    }

    private val userDao by lazy {
        db.userDao()
    }

    private val userInfoUpdateListeners = CopyOnWriteArrayList<UserInfoUpdateListener>()

    fun registerUserInfoUpdateListener(listener: UserInfoUpdateListener) {
        if (!userInfoUpdateListeners.contains(listener)) {
            userInfoUpdateListeners.add(listener)
        }
    }

    fun unregisterUserInfoUpdateListener(listener: UserInfoUpdateListener) {
        userInfoUpdateListeners.remove(listener)
    }

    private fun notifyUserInfoUpdateListeners(userInfo: UserInfo) {
        userInfoUpdateListeners.forEach { it.onUserInfoUpdated(userInfo) }
    }

    fun refreshUserInfo(userId: String): Job =
        appCoroutineScope.launch {
            getRemoteUserInfo(userId)
        }

    fun refreshSelfUserInfo() = refreshUserInfo(SelfUser?.id.orEmpty())

    suspend fun getRemoteSelfUserInfo(): Result<UserInfo> = getRemoteUserInfo(SelfUser?.id.orEmpty())

    suspend fun getRemoteUserInfo(userId: String): Result<UserInfo> {
        if (!isLoggedIn) {
            return Result.failure(IllegalStateException("请先登录"))
        }
        return executeApiCallExpectingData { UserApiService.instance.getUserInfo(userId) }
            .mapCatching {
                UserInfo.fromResponse(it)
            }.onSuccess { user ->
                if (SelfUser?.id == user.id) {
                    updateUserInfo {
                        user
                    }
                } else {
                    userDao.insertAll(user.basic)
                }
                notifyUserInfoUpdateListeners(user)
            }
    }

    suspend fun getLocalUserInfo(userId: String): Result<BasicUser?> =
        runCatching {
            userDao.getUserById(userId)
        }

    fun getUserInfoFlow(
        userId: String,
        fromLocal: Boolean = true,
    ): Flow<UserInfo> =
        callbackFlow {
            if (fromLocal) {
                getLocalUserInfo(userId).getOrNull()?.also {
                    send(UserInfo(it))
                }
            }
            val listener =
                UserInfoUpdateListener {
                    launch {
                        send(it)
                    }
                }
            registerUserInfoUpdateListener(listener)
            refreshUserInfo(userId)
            awaitClose {
                unregisterUserInfoUpdateListener(listener)
            }
        }
}
