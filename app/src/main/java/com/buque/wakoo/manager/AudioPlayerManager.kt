package com.buque.wakoo.manager

import android.annotation.SuppressLint
import android.content.Context
import android.media.MediaPlayer
import android.net.Uri
import com.buque.wakoo.utils.DateTimeUtils
import com.buque.wakoo.utils.LogUtils
import com.tencent.live2.V2TXLivePlayer
import com.tencent.live2.impl.V2TXLivePlayerImpl
import com.tencent.rtmp.TXLiveBase
import com.tencent.rtmp.TXLiveBaseListener
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.io.IOException
import kotlin.math.min

/**
 * 音频播放器状态封装类
 * 将播放状态、位置、时长和标识合并为一个统一的状态管理
 */
sealed interface PlayerState {
    val currentlyPlayingTag: Any?
    val playType: Int

    interface PlayingOrPaused : PlayerState {
        val position: Int
        val duration: Int
    }

    /**
     * 空闲状态（初始、播放完成、停止后）
     */
    data object Idle : PlayerState {
        override val currentlyPlayingTag: Any? = null
        override val playType: Int = 0
    }

    /**
     * 正在播放状态
     */
    data class Playing(
        override val position: Int,
        override val duration: Int,
        override val currentlyPlayingTag: Any,
        override val playType: Int = 1,
    ) : PlayingOrPaused

    /**
     * 已暂停状态
     */
    data class Paused(
        override val position: Int,
        override val duration: Int,
        override val currentlyPlayingTag: Any,
        override val playType: Int = 1,
    ) : PlayingOrPaused

    /**
     * 错误状态
     */
    data class Error(
        override val currentlyPlayingTag: Any,
        val throwable: Throwable,
        override val playType: Int = 1,
    ) : PlayerState

    val currentPosition
        get() = (this as? PlayingOrPaused)?.position ?: 0

    val totalDuration
        get() = (this as? PlayingOrPaused)?.duration ?: 0

    // 格式化时长
    val formattedPosition: String
        @SuppressLint("DefaultLocale")
        get() {
            val totalSeconds = currentPosition / 1000
            val minutes = totalSeconds / 60
            val seconds = totalSeconds % 60
            return String.format(
                "%02d:%02d",
                minutes,
                seconds,
            )
        }

    val formattedDuration: String
        @SuppressLint("DefaultLocale")
        get() {
            val totalSeconds = totalDuration / 1000
            val minutes = totalSeconds / 60
            val seconds = totalSeconds % 60
            return String.format(
                "%02d:%02d",
                minutes,
                seconds,
            )
        }
}

data class MediaPlayerException(
    val what: Int,
    val extra: Int,
) : Exception()

/**
 * 一个简单的音频播放管理器（单例）
 * 使用系统MediaPlayer，为以后替换ExoPlayer做准备
 */
object AudioPlayerManager {
    private var mediaPlayer: MediaPlayer? = null
    private var progressUpdateJob: Job? = null
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    // --- 精确计时状态 ---

    /**
     * 存储之前所有播放片段的累计时长（毫秒）
     * 仅在 pause 和 seekTo 时更新
     */
    private var accumulatedTimeMs: Long = 0

    /**
     * 当前播放片段的起始时间戳，基于 SystemClock.elapsedRealtime()
     */
    private var playbackStartTimeMs: Long = 0

    // 统一的音频播放器状态
    private val _PlayerState = MutableStateFlow<PlayerState>(PlayerState.Idle)

    // 对外暴露的只读状态流，供Compose订阅
    val playerState = _PlayerState.asStateFlow()

    val currentPosition =
        _PlayerState
            .asStateFlow()
            .map { it.currentPosition }

    val totalDuration =
        _PlayerState
            .asStateFlow()
            .map { it.totalDuration }

    val currentlyPlayingTag =
        _PlayerState
            .asStateFlow()
            .map { it.currentlyPlayingTag }

    private var mLivePlayer: V2TXLivePlayer? = null

    fun initialize(context: Context) {
        val licenceURL = "" // 获取到的 licence url
        val licenceKey = "" // 获取到的 licence key
        TXLiveBase.getInstance().setLicence(context, licenceURL, licenceKey)
        TXLiveBase.setListener(
            object : TXLiveBaseListener() {
                override fun onLicenceLoaded(
                    result: Int,
                    reason: String?,
                ) {
                    mLivePlayer = V2TXLivePlayerImpl(context)
                }
            },
        )
    }

    /**
     * 播放新的音频。
     * @param context Context
     * @param uri 音频文件的Uri，使用Uri可以灵活支持raw、assets、本地文件等多种来源
     * @param tag 播放标识，用于区分不同音频
     */
    fun play(
        context: Context,
        uri: Uri,
        tag: Any = "",
    ) {
        LogUtils.d("AudioPlayerManager.play() 开始播放音频, tag=$tag, uri=$uri")
        stopLivePlay()
        val applicationContext = context.applicationContext
        // 先释放之前的资源，确保一个干净的开始
        release()
        try {
            mediaPlayer =
                MediaPlayer().apply {
                    setDataSource(
                        applicationContext,
                        uri,
                    )
                    // 异步准备，防止阻塞UI线程
                    setOnPreparedListener { mp ->
                        val audioDuration = mp.duration
                        LogUtils.d("AudioPlayerManager.onPrepared() 音频准备完成, tag=$tag, duration=${audioDuration}ms")

                        mp.start()

                        // --- 计时重置 ---
                        // 播放新音频时，重置所有计时
                        accumulatedTimeMs = 0
                        playbackStartTimeMs = DateTimeUtils.elapsedRealtime()

                        // 只有在真正开始播放时，才更新状态和Tag
                        _PlayerState.value =
                            PlayerState.Playing(
                                position = 0,
                                duration = audioDuration,
                                currentlyPlayingTag = tag,
                            )
                        LogUtils.d("AudioPlayerManager.onPrepared() 开始播放并更新状态, tag=$tag")
                        startProgressUpdate() // 开始更新进度
                    }
                    // 播放完成监听
                    setOnCompletionListener { mp ->
                        LogUtils.d("AudioPlayerManager.onCompletion() 播放完成, tag=$tag")
                        _PlayerState.value = PlayerState.Idle
                        stopProgressUpdate()
                    }
                    // 跳转完成监听
                    setOnSeekCompleteListener {
                        // 当seek操作完成，用播放器返回的精确位置更新累计时间
                        accumulatedTimeMs = it.currentPosition.toLong()
                        LogUtils.d("AudioPlayerManager.onSeekComplete() 跳转完成, tag=$tag, position=${accumulatedTimeMs}ms")
                        // 如果seek时正在播放，则重置当前片段的起始时间
                        val currentState = _PlayerState.value
                        if (currentState is PlayerState.Playing) {
                            playbackStartTimeMs = DateTimeUtils.elapsedRealtime()
                        }
                    }
                    // 错误监听
                    setOnErrorListener { _, what, extra ->
                        LogUtils.e("AudioPlayerManager.onError() 播放出错, tag=$tag, what=$what, extra=$extra")
                        _PlayerState.value =
                            PlayerState.Error(
                                currentlyPlayingTag = tag,
                                throwable =
                                    MediaPlayerException(
                                        what = what,
                                        extra = extra,
                                    ),
                            )
                        release() // 出错时释放所有资源，包括Tag
                        true // true表示错误已处理
                    }
                    prepareAsync()
                }
        } catch (e: IOException) {
            LogUtils.e(e, "AudioPlayerManager.play() 播放初始化失败, tag=$tag")
            e.printStackTrace()
            _PlayerState.value =
                PlayerState.Error(
                    currentlyPlayingTag = tag,
                    throwable = e,
                )
            release()
        }
    }

    fun startLivePlay(
        url: String,
        tag: Any = "",
    ) {
        stopProgressUpdate()
        mediaPlayer?.release()
        mediaPlayer = null
        // --- 重置计时状态 ---
        accumulatedTimeMs = 0
        playbackStartTimeMs = 0

        mLivePlayer?.also {
            if (it.isPlaying == 1) {
                it.stopPlay()
            }
            it.startLivePlay(url)
        }
        _PlayerState.value =
            PlayerState.Playing(
                position = 0,
                duration = 0,
                currentlyPlayingTag = tag,
                playType = 2,
            )
    }

    private fun stopLivePlay() {
        mLivePlayer?.stopPlay()
    }

    /**
     * 暂停播放
     */
    fun pause() {
        val currentState = _PlayerState.value
        LogUtils.d("AudioPlayerManager.pause() 暂停播放, tag=${currentState.currentlyPlayingTag}, isPlaying=${mediaPlayer?.isPlaying}")

        if (mediaPlayer?.isPlaying == true) {
            if (currentState is PlayerState.Playing) {
                // --- 计算并累加本次播放时长 ---
                val segmentDuration = DateTimeUtils.elapsedRealtime() - playbackStartTimeMs
                accumulatedTimeMs += segmentDuration

                LogUtils.d(
                    "AudioPlayerManager.pause() 累加播放时长, tag=${currentState.currentlyPlayingTag}, segmentDuration=${segmentDuration}ms, totalAccumulated=${accumulatedTimeMs}ms",
                )

                mediaPlayer?.pause()
                stopProgressUpdate() // 暂停时停止计时

                // 更新为暂停状态，保持精确位置
                _PlayerState.value =
                    PlayerState.Paused(
                        position = accumulatedTimeMs.toInt(),
                        duration = currentState.duration,
                        currentlyPlayingTag = currentState.currentlyPlayingTag,
                    )

                LogUtils.d(
                    "AudioPlayerManager.pause() 暂停完成, tag=${currentState.currentlyPlayingTag}, position=${accumulatedTimeMs.toInt()}ms",
                )
            }
        }
    }

    /**
     * 恢复播放
     */
    fun resume() {
        val currentState = _PlayerState.value
        LogUtils.d(
            "AudioPlayerManager.resume() 恢复播放, tag=${currentState.currentlyPlayingTag}, currentState=${currentState::class.simpleName}",
        )

        if (mediaPlayer != null && currentState is PlayerState.Paused) {
            // --- 重置起始时间戳 ---
            // 为新的播放片段设置起始时间
            playbackStartTimeMs = DateTimeUtils.elapsedRealtime()

            LogUtils.d("AudioPlayerManager.resume() 重置播放起始时间, tag=${currentState.currentlyPlayingTag}, startTime=$playbackStartTimeMs")

            mediaPlayer?.start()
            _PlayerState.value =
                PlayerState.Playing(
                    position = currentState.currentPosition,
                    duration = currentState.duration,
                    currentlyPlayingTag = currentState.currentlyPlayingTag,
                )
            startProgressUpdate() // 恢复时继续计时

            LogUtils.d(
                "AudioPlayerManager.resume() 恢复播放完成, tag=${currentState.currentlyPlayingTag}, position=${currentState.currentPosition}ms",
            )
        }
    }

    /**
     * 停止播放并释放所有资源。
     * 在不需要播放器时（例如Compose组件销毁时）调用。
     * @param tag 可选的标识，如果指定则只有当前播放的tag匹配时才释放
     */
    fun release(tag: Any? = null) {
        if (!innerReleaseIf { tag == null || it == tag }) {
            LogUtils.d("AudioPlayerManager.release() 跳过释放，tag不匹配, 指定tag=$tag, 当前tag=$currentlyPlayingTag")
        }
    }

    fun releaseIf(filter: (Any?) -> Boolean) {
        if (!innerReleaseIf(filter)) {
            LogUtils.d("AudioPlayerManager.release() 跳过释放，tag不匹配, 当前tag=$currentlyPlayingTag")
        }
    }

    /**
     * 停止播放并释放所有资源。
     * 在不需要播放器时（例如Compose组件销毁时）调用。
     * @param filter 筛选是否停止当前声音
     */
    private fun innerReleaseIf(filter: (Any?) -> Boolean): Boolean {
        val currentState = _PlayerState.value
        val currentTag = currentState.currentlyPlayingTag

        // 如果指定了tag，只有当前播放的tag匹配时才释放
        if (!filter(currentTag)) {
            return false
        }

        LogUtils.d("AudioPlayerManager.release() 开始释放资源, tag=$currentTag, 当前tag=$currentTag")

        if (currentState.playType == 2) {
            stopLivePlay()
        } else {
            stopProgressUpdate()
            mediaPlayer?.release()
            mediaPlayer = null
            // --- 重置计时状态 ---
            accumulatedTimeMs = 0
            playbackStartTimeMs = 0
        }

        _PlayerState.value = PlayerState.Idle

        LogUtils.d("AudioPlayerManager.release() 释放完成, tag=$currentTag")
        return true
    }

    /**
     * 跳转到指定播放位置
     * @param position 目标位置 (毫秒)
     */
    fun seekTo(position: Int) {
        val currentState = _PlayerState.value
        LogUtils.d(
            "AudioPlayerManager.seekTo() 跳转播放位置, tag=${currentState.currentlyPlayingTag}, position=${position}ms, currentState=${currentState::class.simpleName}",
        )

        // seekTo是异步的，实际的时间更新在 OnSeekCompleteListener 中完成
        mediaPlayer?.seekTo(position)
        // 立即更新UI以提供拖动反馈，最终位置会在 seekComplete 时校准
        when (currentState) {
            is PlayerState.Playing -> {
                _PlayerState.value = currentState.copy(position = position)
                LogUtils.d("AudioPlayerManager.seekTo() 更新Playing状态位置, tag=${currentState.currentlyPlayingTag}, position=${position}ms")
            }

            is PlayerState.Paused -> {
                _PlayerState.value = currentState.copy(position = position)
                LogUtils.d("AudioPlayerManager.seekTo() 更新Paused状态位置, tag=${currentState.currentlyPlayingTag}, position=${position}ms")
            }

            else -> {
                LogUtils.d(
                    "AudioPlayerManager.seekTo() 忽略seekTo，当前状态不支持, tag=${currentState.currentlyPlayingTag}, currentState=${currentState::class.simpleName}",
                )
                // Idle 或 Error 状态下不处理 seekTo
            }
        }
    }

    /**
     * 启动一个协程来定时更新播放进度
     */
    private fun startProgressUpdate() {
        val currentState = _PlayerState.value
        LogUtils.d("AudioPlayerManager.startProgressUpdate() 开始进度更新, tag=${currentState.currentlyPlayingTag}")

        stopProgressUpdate() // 先停止之前的，防止重复
        progressUpdateJob =
            scope.launch {
                while (isActive) {
                    val currentState = _PlayerState.value
                    // 只在播放状态下才更新进度
                    if (currentState is PlayerState.Playing) {
                        // --- 基于时间戳计算当前位置 ---
                        val segmentDuration = DateTimeUtils.elapsedRealtime() - playbackStartTimeMs
                        val newPosition = accumulatedTimeMs + segmentDuration
                        val clampedPosition =
                            min(
                                newPosition,
                                currentState.duration.toLong(),
                            ).toInt()

                        _PlayerState.value = currentState.copy(position = clampedPosition)
                    }
                    delay(250) // 每250毫秒更新一次，UI更流畅
                }
            }
    }

    /**
     * 停止进度更新
     */
    private fun stopProgressUpdate() {
        val currentState = _PlayerState.value
        LogUtils.d("AudioPlayerManager.stopProgressUpdate() 停止进度更新, tag=${currentState.currentlyPlayingTag}")

        progressUpdateJob?.cancel()
        progressUpdateJob = null
    }
}
