package com.buque.wakoo.consts

import androidx.annotation.IntDef

data object Pay {
    const val CHANNEL_GOOGLE_IAP = 1
    const val CHANNEL_APPLE_IAP = 2
    const val CHANNEL_WXPAY = 3
    const val CHANNEL_ALIPAY = 4
    const val CHANNEL_PAYERMAX = 5
    const val CHANNEL_JKOPAY = 6
    const val CHANNEL_LINEPAY = 7
    const val CHANNEL_PAYPAL = 8
    const val CHANNEL_AGENT = 9

    /**
     * ```
     * PayChannel = Enum((  # 支付渠道，程序购买的支付渠道
     *     (1, "GOOGLE_IAP", "Google应用内购买"),
     *     (2, "APPLE_IAP", "Apple应用内购买"),
     *     (3, "WXPAY", "微信支付"),
     *     (4, "ALIPAY", "支付宝"),
     *     (5, "PAYERMAX", "PAYERMAX"),
     *     (6, "JKOPAY", "街口支付"),  # 目前只支持台湾
     *     (7, "LINEPAY", "LinePay"),  # 目前只支持台湾
     *     (8, "PAYPAL", "PayPal"),  # 目前只支持美金，全球
     *     (9, "AGENT", "Agent"),  # 代充
     * ))
     * ```
     */

    @IntDef(
        CHANNEL_GOOGLE_IAP,
        CHANNEL_APPLE_IAP,
        CHANNEL_WXPAY,
        CHANNEL_ALIPAY,
        CHANNEL_PAYERMAX,
        CHANNEL_JKOPAY,
        CHANNEL_LINEPAY,
        CHANNEL_PAYPAL,
        CHANNEL_AGENT
    )
    @Retention(AnnotationRetention.SOURCE)
    annotation class Channel


    const val CALL_TYPE_GOOGLE_SDK = 1
    const val CALL_TYPE_APPLE_SDK = 2
    const val CALL_TYPE_WXSDK = 3
    const val CALL_TYPE_LINK_WEB = 4
    const val CALL_TYPE_LINK_WEB1 = 5
    const val CALL_TYPE_LINK_WEBVIEW = 6
    const val CALL_TYPE_ORDER_LINK = 7
    const val CALL_TYPE_AGENT = 8

    /**
     * ```
     * PayCallType = Enum((
     *     (1, "GOOGLE_SDK", "谷歌SDK"),
     *     (2, "APPLE_SDK", "苹果SDK"),
     *     (3, "WXSDK", "微信SDK"),
     *     (4, "LINK_WEB", "浏览器打开链接"),
     *     (5, "LINK_WEB1", "浏览器打开链接(适配客户端)"),
     *     (6, "LINK_WEBVIEW", "webview打开链接"),
     *     (7, "ORDER_LINK", "获取订单链接"),
     *     (8, "AGENT", "代充"),
     * ))
     * ```
     */
    @IntDef(
        CALL_TYPE_GOOGLE_SDK,
        CALL_TYPE_APPLE_SDK,
        CALL_TYPE_WXSDK,
        CALL_TYPE_LINK_WEB,
        CALL_TYPE_LINK_WEB1,
        CALL_TYPE_LINK_WEBVIEW,
        CALL_TYPE_ORDER_LINK,
        CALL_TYPE_AGENT
    )
    @Retention(AnnotationRetention.SOURCE)
    annotation class CallType


    const val ORDER_TYPE_GOLD = 1
    const val ORDER_TYPE_MEMBERSHIP = 2

    @IntDef(ORDER_TYPE_GOLD, ORDER_TYPE_MEMBERSHIP)
    @Retention(AnnotationRetention.SOURCE)
    annotation class OrderType
}




