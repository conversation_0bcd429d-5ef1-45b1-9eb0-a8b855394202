package com.buque.wakoo.im.bean

import com.buque.wakoo.bean.User
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im.inter.UCConversation
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.compat.IMCompatCore
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

data class MessageAtInfo(
    val atUsers: List<String>? = null,
    val withAtAll: Boolean = false,
)

data class ConversationAtInfo(
    val atMeCount: Int,
    val sequenceList: List<Long>?,
    val visible: Boolean = false,
) {
    val sequence
        get() = sequenceList?.firstOrNull() ?: 0
}

data class ConversationUnreadInfo(
    val unreadCount: Int,
    val sequence: Long,
    val visible: Boolean = false,
)

data class PushInfo(
    val title: String,
    val desc: String,
    val avatar: String,
)

data class SendParams(
    val receiver: String,
    val type: ConversationType,
    val atInfo: MessageAtInfo? = null,
    val onlyLocal: Boolean = false,
    val isExcludedFromUnreadCount: Boolean = false,  // 消息是否不计入会话未读数
    val isExcludedFromLastMessage: Boolean = false,  // 消息是否不计入会话 lastMsg
)

enum class ConversationType {
    C2C,
    GROUP,
    CHATROOM,
    RTM
}

sealed interface MessageReadReceipt {

    val targetId: String

    data class Full(
        override val targetId: String,
        val timestamp: Long, // 最新的一条已读消息时间
    ) : MessageReadReceipt

    data class Each(
        override val targetId: String,
        val ids: Set<String>,
    ) : MessageReadReceipt
}


sealed interface MsgSendStatus {

    data object Idea : MsgSendStatus

    /**
     * 发送中
     */
    data object Sending : MsgSendStatus

    /**
     * 已发送
     */
    data object Sent : MsgSendStatus

    /**
     * 失败
     */
    data object Failure : MsgSendStatus

    /**
     * 已撤回
     */
    data object Revoked : MsgSendStatus
}

data class IMConversationResult(
    val isFinished: Boolean,
    val nextSeq: Long,
    val list: List<UCConversation>,
)

interface IIMEnvironmentScope {

    val sendParams: SendParams

    fun sendMessage(bundle: MessageBundle, overrideSendParams: SendParams? = null) {
        IMCompatCore.sendMessage(overrideSendParams ?: sendParams, bundle)
    }

    fun sendMessages(bundles: List<MessageBundle>, overrideSendParams: SendParams? = null) {
        IMCompatCore.sendMessages(overrideSendParams ?: sendParams, bundles)
    }

    fun sendMessage(ucMessage: UCMessage, overrideSendParams: SendParams? = null) {
        IMCompatCore.sendMessage(overrideSendParams ?: sendParams, ucMessage)
    }

    fun sendMessageWithAt(bundle: MessageBundle, atInfo: MessageAtInfo) {
        IMCompatCore.sendMessage(sendParams.copy(atInfo = atInfo), bundle)
    }

    fun cleanConversationUnreadCount() {
        IMCompatCore.cleanConversationUnreadCount(sendParams.receiver, sendParams.type)
    }
}

@Serializable
data class EmojiMessageContent(
    @SerialName("user")
    val user: User,
    @SerialName("emoji")
    val emojiEffect: EmojiEffect,
    @SerialName("emoji_result")
    val emojiResult: String = "",
    @SerialName("is_public_message")
    val isPublicMessage: Boolean = false
) {
    val type: Int
        get() = emojiEffect.type
}

@Serializable
data class EmojiEffect(
    @SerialName("id")
    val id: Int,
    @SerialName("effect_file")
    val effectFile: String,
    @SerialName("type")
    val type: Int,
    @SerialName("name")
    val name: String
)