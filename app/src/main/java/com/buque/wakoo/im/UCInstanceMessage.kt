package com.buque.wakoo.im

import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.bean.PushInfo
import com.buque.wakoo.im.bean.SendParams
import com.buque.wakoo.im.inter.IUCMessage
import com.buque.wakoo.im_business.message.types.UCTextMessage

interface UCInstanceMessage : IUCMessage {
    val base: UCMessage

    /**
     * 有些消息需要在解析的结构体里面获取user
     */
    val user: UserInfo?
        get() = base.senderUser?.toUser(null)

    fun getSummaryString(): String

    fun toMsgString(): String {
        if (base.isRevoked) {
            return "(已撤回)${getSummaryString()}"
        }
        return getSummaryString()
    }
}

interface UCFakerMessage : UCInstanceMessage {
    val contentType: String

    fun equalsSame(other: UCFakerMessage?): Boolean
}

val UCInstanceMessage.sequence
    get() = base.sequence

val UCInstanceMessage.isNeedReadReceipt
    get() = base.isNeedReadReceipt

fun UCInstanceMessage.toPushInfo(params: SendParams): PushInfo? {
    val desc =
        when (this) {
            is UCTextMessage -> {
                text.take(40)
            }

            // todo IM push消息体
//        is UCImageMessage -> {
//            if (isUCOO) {
//                id2String(R.string.对方发来一张图片)
//            } else {
//                id2String(R.string.cpd对方发来一张图片)
//            }
//        }
//
//        is UCVoiceMessage -> {
//            if (isUCOO) {
//                id2String(R.string.对方发来一段语音)
//            } else {
//                id2String(R.string.cpd对方发来一段语音)
//            }
//        }
//
//        is UCEmojiMessage -> {
//            if (isUCOO) {
//                id2String(R.string.对方发来一个互动表情)
//            } else {
//                id2String(R.string.cpd对方发来一个互动表情)
//            }
//        }
//
//        is IUCCustomMessage -> {
//            summary ?: if (isUCOO) {
//                id2String(R.string._系统通知_)
//            } else {
//                id2String(R.string.cpd_系统通知_)
//            }
//        }

            else -> {
                return null
            }
        }

    return if (params.type == ConversationType.C2C) {
//        PushInfo(title = sUser.nickname, desc = desc, avatar = sUser.avatarUrl)
        // todo IM push消息体
        PushInfo(title = "用户名", desc = desc, avatar = "头像")
    } else {
        PushInfo(
//            title = if (params.receiver.contains("tribe")) {
//                if (isUCOO) {
//                    id2String(R.string.部落消息)
//                } else {
//                    id2String(R.string.cpd_UCOO群聊消息)
//                }
//            } else {
//                id2String(R.string.UCOO群聊消息)
//            },
            title = "", // todo IM push消息头
            desc = desc,
            avatar = "",
        )
    }
}
