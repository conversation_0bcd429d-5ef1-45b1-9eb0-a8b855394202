# 通用IM

目标为了打造拷贝开箱即用的IM抽象sdk

## 碎碎念

1. 目前项目支持的消息是文本/图片/语言/自定义 这四种大类型, 自定义是抽象出来的,又可以往下分
2. 跟消息有关的:

- MessageBundle = 发送消息时的包装类
- UCMessage = 消息基础类
- UCInstanceMessage = 消息实体类, 所有的扩展出来的消息类型都要继承此类
- MsgLayoutContent = 消息Compose UI, 所有消息都要实现此类来保证布局绘制
- MessageEntry = 又一消息包装类, 在ViewModel中使用

3. 所有的Instance就是具体的消息内容, 在ocha中采用的是消息体包裹内容, 在ucoo中的内容和消息体被包裹在一个新的类中

4. 先把消息收发的搞定6.27


5. IMCompatCore当做 数据调度中心 / 操作抽象层, 负责和业务对接

- IMEngine就是接入进来的具体IM sdk, 例如融云/腾讯/容联 等等等等.
  每一个Engine都需要自主实现IIMEngine,实现具体的消息/会话/扩展操作,
  sdk的回调等等全部通过EventDispatcher来回调给IMCompatCore
- IConverter可用可不用, 是项目的实体类 和 sdk的实体类互转的一个工具
- 所有的具体的im sdk操作全部都会被限制在单个包下, 且此包只有IMCompatCore能访问

6. 现在可能存在的问题就是扩展字段这块逻辑有点混乱

7. 想了一下,还是决定把UCInstanceMessage移动到business中

8. 不太想把MessageScaffold写到item content里, item content应该是完整的

## 目标&进度

### 1. 注册&反注册

### 2. 会话列表

### 3. 消息列表

### 4. 全局信令分发处理

## 在wakoo的处理

1. 全局的通用会话列表(包含部落/群组/异形会话)

- 第一次全局会话列表的用户信息批量获取
- 后续每次用户信息的获取
- 用户在线信息的获取



