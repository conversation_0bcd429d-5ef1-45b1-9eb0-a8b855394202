package com.buque.wakoo.im.push

import kotlinx.serialization.json.JsonObject


//interface PushApi {
//    @POST("api/common/v1/pushnotification/jump")
//    suspend fun jump(@Body map: Map<String, String>): ApiResponse<JsonObject>
//}


object PushRouteHandler {


    //        ('user_home', '用户个人主页'),  # 参数：{"user_id": "11111"}
//        ('tribe_detail', '部落详情'),  # 参数：{"tribe_id": "11111"}
//        ('private_chat', '私聊'),  # 参数：{"user_id": "11111", "msg": "要发送的消息"}
//        ('message_list', '消息列表'),  # 参数：{}
//        ('new_user_task', '新人任务页面'),  # 参数：{"user_id": "11111"}
    private const val ACTION_FOR_USER_HOME = "user_home"
    private const val ACTION_FOR_TRIBE_DETAIL = "tribe_detail"
    private const val ACTION_FOR_PRIVATE_CHAT = "private_chat"
    private const val ACTION_FOR_NOTIFICATION_MESSAGE = "message_list"
    private const val ACTION_FOR_NEW_USER_TASK = "new_user_task"
    private const val ACTION_CALL_JUMP_API = "call_jump_api"

    fun handle(ext: String?) {

    }

    fun handle(action: String, jsonObject: JsonObject?) {

    }

//    private val api = createApi<PushApi>()
//
//    fun handle(ext: String?) {
//        if (ext.isNullOrEmpty()) {
//            return
//        }
//        try {
//            val json = sAppJson.parseToJsonElement(ext).jsonObject
//            val action = json.getStringOrNull("open_page")
//            val jsonObject = json.getOrNull("page_params")?.jsonObject
//            if (!action.isNullOrEmpty()) {
//                handle(action, jsonObject)
//            }
//        } catch (e: Exception) {
//            LogUtils.e("PushRouteHandler", "push click route fail, $e")
//        }
//    }
//
//    fun handle(action: String, jsonObject: JsonObject?) {
//        if (!WelcomeActivity.launched) {// Activity没启动，需要拉起入口Activity
//            app.apply {
//                packageManager.getLaunchIntentForPackage(packageName)?.component?.let {
//                    Intent.makeRestartActivityTask(it)
//                }?.also {
//                    startActivity(it)
//                }
//            }
//            if (sIsSignIn) { // 已登录状态，监听主Activity启动后再路由
//                ActivityLifecycle.topActivityFlow.onEach {
//                    val activity = it?.get()
//                    if (activity is HomeActivity || activity is CupidMainActivity) {
//                        delay(200)
//                        handle(action, jsonObject)
//                        throw CancellationException("")
//                    }
//                }.launchIn(loginCoroutineScope)
//            }
//            return
//        }
//        if (!sIsSignIn) {
//            return
//        }
//        when (action) {
//            ACTION_CALL_JUMP_API -> {
//                loginCoroutineScope.launch {
//                    jsonObject?.let {
//                        sAppJson.encodeToString(it)
//                    }.takeIsNotEmpty()?.also {
//                        runApiCatching {
//                            api.jump(mapOf("jump_params" to it))
//                        }.onSuccess { obj ->
//                            val link = obj.getStringOrNull("action_link")
//                            if (!link.isNullOrEmpty()) {
//                                ActivityLifecycle.startTopActivity?.also { context ->
//                                    AppLinkManager.open(context, link)
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//
//            ACTION_FOR_NEW_USER_TASK -> {
//                ActivityLifecycle.startTopActivity?.also { context ->
//                    WelfareCenterNavigator.navigate(context)
//                }
//            }
//
//            ACTION_FOR_USER_HOME -> {
//                loginCoroutineScope.launch {
//                    val id = jsonObject?.getStringOrNull("user_id").orEmpty()
//                    if (id.isEmpty()) {
//                        return@launch
//                    }
//                    val user = UserManager.getUserCacheById(id) ?: AppUser(id)
//                    ActivityLifecycle.startTopActivity?.also { context ->
//                        UserProfileNavigator.navigate(context, user)
//                    }
//                }
//            }
//
//            ACTION_FOR_TRIBE_DETAIL -> {
//                val id = jsonObject?.getStringOrNull("tribe_id").orEmpty()
//                if (id.isEmpty()) {
//                    return
//                }
//                ActivityLifecycle.startTopActivity?.asBase?.apply {
//                    launchTribeActivity(id)
//                }
//            }
//
//            ACTION_FOR_PRIVATE_CHAT -> {
//                val id = jsonObject?.getStringOrNull("user_id").orEmpty()
//                if (id.isEmpty()) {
//                    return
//                }
//                val msg = jsonObject?.getStringOrNull("msg").orEmpty()
//                if (msg.isNotEmpty()) {
//                    IMCompatCore.sendMessage(SendParams(id, ConversationType.C2C), MessageBundle.Text.create(msg))
//                }
//                loginCoroutineScope.launch {
//                    delay(200)
//                    ActivityLifecycle.startTopActivity?.also { context ->
//                        context.startActivity(ChatActivity.createIntent(context, AppUser(id)))
//                    }
//                }
//            }
//
//            ACTION_FOR_NOTIFICATION_MESSAGE -> {
//                MainBroadcast.sendEvent(BroadcastEvent(EventName.KEY_SET_HOME_TAB, HomeActivity.Companion.Tab.Message))
//            }
//        }
//    }
}