package com.buque.wakoo.im.inter

import com.buque.wakoo.im.bean.MessageAtInfo
import com.buque.wakoo.im.bean.MsgSendStatus

interface IUCMessage {

//    val rawMessage: Any

    /**
     * 消息id, 一般消息发送后才有
     */
    val id: String

    /**
     * 目标id, 这条消息和哪个目标的对话
     * 目前可receiver相同
     */
    val targetId: String
        get() = receiver

    /**
     * 消息时间戳
     */
    val timestamp: Long

    /**
     * 发送者id
     */
    val sender: String

    /**
     * 接受者id
     * 单聊为对方的用户id
     * 群聊为群id
     */
    val receiver: String

    /**
     * 是否是自己发送的
     */
    val isSelf: Boolean

    /**
     * 消息的发送状态
     */
    val sendStatus: MsgSendStatus

    /**
     * 消息附带的@信息
     */
    val groupAtInfo: MessageAtInfo?

    /**
     * 单聊消息，消息接收方是否已读
     */
    val isC2CRead: Boolean

    /**
     * 静态是否已读
     */
    val isC2CStaticRead: Boolean

    /**
     * 是否已经播放，比如音频、视频、礼物是否已播放
     */
    var isPlayed: Boolean

    /**
     * 是否已下载，比如文件是否已下载
     */
    var isDownloaded: Boolean

    /**
     * 是否是单聊消息，腾讯消息只能区分单聊和群组
     */
    val isC2CMsg: Boolean

    /**
     * 是否是系统消息
     */
    val isSystemMsg: Boolean

    /**
     * 是否支持消息扩展
     */
    val isSupportMessageExtension: Boolean

    ///////////////////////////////////////////////////////////////////////////
    //  * [融云]
    //  * message.extra 》没有使用，并且不会同步到云端, 只会存在本地
    //  *
    //  * message.content.extra 》目前用到了两个地方
    //  * 1. 图片消息宽高信息（语聊房）
    //  * 2. 消息发送之前的接口拦截，可能需要把后端返回的extra塞进去，具体有什么作用不清楚
    //  *
    //  * message.content.userInfo.extra 》目前用于基本消息的扩展
    //  * 1. 在发消息时会带上
    //  * 2. 在收到消息时会解析
    //  *
    //  * [腾讯]
    //  * cloudCustomData 》计划存userInfo信息，发送时要带上，收到后可以解析出用户信息
    //  *
    //  * localCustomData 》仅对本地生效
    //  *
    //  * localCustomInt 》仅对本地生效，可以用来标记语音、视频消息是否已经播放
    ///////////////////////////////////////////////////////////////////////////

    /**
     * 用于获取扩展字段
     */
    fun getExtraValue(key: String): String?

    /**
     * 获取翻译文本
     * @param code 语言代码，如 zh-CN, en-US
     */
    fun getTranslateTextByCode(code: String): String?

    fun saveTranslateText(code: String, text: String)
}
