package com.buque.wakoo.im

import androidx.collection.ArraySet
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.utils.IMUtils
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.inter.UCConversation
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update

/**
 * 这个是全局的Conversation列表,不包含部落什么东西的
 * 需要在登录的时候load
 * 登录失效时clear
 */
object IMConversationController : IMCompatListener {

    private val _listFlow: MutableStateFlow<List<UCConversation>> = MutableStateFlow(emptyList())

    val listFlow = _listFlow.asStateFlow()

    override fun onNewConversation(conversationList: List<UCConversation>) {
        updateConversationList(conversationList)
    }

    override fun onConversationChanged(conversationList: List<UCConversation>) {
        updateConversationList(conversationList)
    }

    override fun onConversationDeleted(idList: Set<String>) {
        deleteConversation(idList)
    }

    fun loadConversations() {
        IMUtils.launchCoroutine(IMUtils.imConvCoroutineContext) {
            if (listFlow.value.isNotEmpty()) {
                _listFlow.value = emptyList()
            }
            IMCompatCore.addIMListener(this@IMConversationController)
            var result = IMCompatCore.getConversations(0, 30)
            if (result != null) {
                updateConversationList(result.list)
            }

            val list = mutableListOf<UCConversation>()
            while (result?.isFinished == false) {
                result = IMCompatCore.getConversations(result.nextSeq, 60)
                if (result != null) {
                    list.addAll(result.list)
                }
            }

            if (list.isNotEmpty()) {
                updateConversationList(list)
            }
        }
    }

    fun clearConversations() {
        IMUtils.launchCoroutine(IMUtils.imConvCoroutineContext) {
            IMCompatCore.removeIMListener(this@IMConversationController)
            _listFlow.value = emptyList()
        }
    }

    fun updateIMConversations(function: (List<UCConversation>) -> List<UCConversation>) {
        IMUtils.launchCoroutine(IMUtils.imConvCoroutineContext) {
            _listFlow.update(function)
        }
    }

    fun getIMConversations(id: String, type: ConversationType): UCConversation? {
        return _listFlow.value.find { it.id == id && it.type == type }
    }

    private fun updateConversationList(newList: List<UCConversation>) {
        IMUtils.launchCoroutine(IMUtils.imConvCoroutineContext) {
            _listFlow.update { oldList ->
                val setIds = ArraySet<String>()
                buildList {
                    newList.forEach {
                        if (!setIds.contains(it.id)) {
                            setIds.add(it.id)
                            if (it.lastMessage != null) {
                                add(it)
                            }
                        }
                    }

                    oldList.forEach {
                        if (!setIds.contains(it.id)) {
                            setIds.add(it.id)
                            add(it)
                        }
                    }
                }
            }
        }
    }

    private fun deleteConversation(idList: Set<String>) {
        updateIMConversations { oldList ->
            oldList.filter { !idList.contains(it.id) }
        }
    }

}