package com.buque.wakoo.im

import android.net.Uri
import com.buque.wakoo.app.AppJson
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

sealed interface MessageBundle {

    val isEmpty: Boolean

    class Text private constructor(val text: String) : MessageBundle {


        companion object {

            /**
             * 创建文本消息的MessageBundle
             */
            @JvmStatic
            fun create(text: String): Text {
                return Text(text)
            }
        }

        override val isEmpty: Boolean = text.isEmpty()
    }

    class Voice private constructor(val localPath: String, val duration: Int) : MessageBundle {

        companion object {

            /**
             * 创建语音消息的MessageBundle
             */
            @JvmStatic
            fun create(localPath: String, duration: Int): Voice {
                return Voice(localPath, duration)
            }
        }

        override val isEmpty: Boolean = localPath.isEmpty() || duration <= 0
    }

    class Image private constructor(val uri: Uri, val localPath: String?, val width: Int, val height: Int, val isFull: Boolean) :
        MessageBundle {

        companion object {

            /**
             * 创建图片消息的MessageBundle
             */
            @JvmStatic
            fun create(uri: Uri, localPath: String, width: Int, height: Int, isFull: Boolean = false): Image {
                return Image(uri, localPath, width, height, isFull)
            }
        }

        override val isEmpty: Boolean = width <= 0 || height <= 0
    }

    /**
     * 创建自定义消息的MessageBundle
     */
    class Custom private constructor(
        val dataContent: Pair<String, JsonObject?>,
        val summary: String? = null,
    ) : MessageBundle {

        companion object {
            private const val KEY_DIGEST = "digest"

            /**
             * {
             *    "cmd" :"123",
             *    "data": {}
             * }
             */
            fun create(cmd: String, data: JsonObject?, summary: String? = null): Custom {
                return Custom(dataContent = cmd to data, summary = summary)
            }
        }

        override val isEmpty: Boolean = dataContent.first.isEmpty() && dataContent.second == null

        fun getJsonContent(): JsonContent {
            val cmd = dataContent.first
            val data = dataContent.second
            return if (data != null) {
                JsonContent(
                    cmd, if (summary.isNullOrEmpty()) {
                        data
                    } else {
                        buildJsonObject {
                            data.forEach { (key, value) ->
                                put(key, value)
                            }
                            put("digest", summary)
                        }
                    })
            } else {
                JsonContent(
                    cmd, if (summary.isNullOrEmpty()) {
                        JsonObject(emptyMap())
                    } else {
                        buildJsonObject {
                            put(KEY_DIGEST, summary)
                        }
                    })
            }
        }

        fun getCustomData(jsonContent: JsonContent? = null): String {
            return AppJson.encodeToString(jsonContent ?: getJsonContent())
        }
    }

}

@Serializable
data class JsonContent(
    val cmd: String,
    val data: JsonObject,
)
