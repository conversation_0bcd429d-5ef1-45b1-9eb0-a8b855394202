package com.buque.wakoo.im.bean

import com.buque.wakoo.bean.BasicUser
import com.buque.wakoo.bean.User
import com.buque.wakoo.bean.UserExtraInfo
import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.network.api.bean.ExpLevelInfo
import com.buque.wakoo.network.api.bean.Medal
import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject

@Serializable
data class IMUser(
    @SerialName("userid") val id: String = "",
    @SerialName("nickname") val nickname: String = "",
    @SerialName("avatar_url") val avatarUrl: String = "",
    @SerialName("gender") val gender: Int = 1,
    @SerialName("is_member") var isVip: Boolean = false,
    @SerialName("public_cp") val publicCP: JsonObject? = null,
    @SerialName("avatar_frame") val avatarFrame: String? = null,
    @SerialName("medal") val medal: Medal? = null,
    @SerialName("medal_list") val medalList: List<Medal> = emptyList(),
    @SerialName("level") val level: Int = 0,
    @SerialName("age") val age: Int = 18,
    @SerialName("small_img_url") val cpUrl: String? = null,
    @SerialName("chat_bubble") val bubble: JsonObject? = null,
    @SerialName("country_flag") val countryFlag: String = "",
    @EncodeDefault @SerialName("exp_level_info") val expLevelInfo: ExpLevelInfo = ExpLevelInfo.Empty,
    @SerialName("colorfulNicknameGradient") val colorfulNicknameGradient: List<String>? = null,
) {
    fun toUser(base: User? = null): UserInfo {
//    val cpExtraInfo = cpUrl?.let { cpUrl ->
//        base?.cpExtraInfo?.let {
//            it.copy(levelInfo = it.levelInfo?.copy(smallImgUrl = cpUrl) ?: LevelInfo(smallImgUrl = cpUrl))
//        } ?: cpUrl.cpExtraInfo
//    } ?: base?.cpExtraInfo

        return UserInfo(
            basic =
                (base as? BasicUser)?.copy(
                    id = id,
                ) ?: BasicUser(
                    id = id,
                    publishId = "",
                    name = nickname,
                    avatar = avatarUrl,
                    gender = gender,
                    age = age,
                    birthday = "",
                    isVip = isVip,
                    level = level,
                ),
            extra =
                UserExtraInfo(
                    avatarFrame = avatarFrame,
                    medalList = medalList,
                ),
        )
    }
}

fun UserInfo.toIMUser(): IMUser =
    IMUser(
        id = id,
        nickname = this.name,
        avatarUrl = avatar,
        gender = gender,
        isVip = isVip,
        publicCP = null,
        avatarFrame = extra.avatarFrame,
        medal = null,
        medalList = extra.medalList.orEmpty(),
        level = level,
        age = age,
        cpUrl = null,
        bubble = null,
        countryFlag = "",
        colorfulNicknameGradient = null,
    )
