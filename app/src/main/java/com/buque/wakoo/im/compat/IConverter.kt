package com.buque.wakoo.im.compat

import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.inter.UCConversation

interface IConverter<Message, Conv, ImpMessage : UCMessage, ImpConversation : UCConversation> {
    //region 转换方法

    fun parseConvFromOrigin(conversation: Conv): ImpConversation

    fun convertToConversation(conversation: UCConversation?): Conv?

    fun parseMsgFromOrigin(message: Message): ImpMessage

    fun convertToMessage(message: UCMessage?): Message?

    //endregion
}
