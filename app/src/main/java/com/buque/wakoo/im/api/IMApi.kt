package com.buque.wakoo.im.api

import com.buque.wakoo.im.bean.MsgSendCondition
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST

internal interface IMApi {
    @GET("api/ucuser/v1/tim/token")
    suspend fun getTimToken(): ApiResponse<JsonObject>

    /** 注册接口 */
    @POST("api/xya/c2c/v1/msg/grant")
    suspend fun checkMsgCanSend(
        @Body fields: Map<String, String>
    ): ApiResponse<MsgSendCondition>

    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<IMApi>()
        }
    }

}