package com.buque.wakoo.im.inter

import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.bean.MessageReadReceipt
import com.buque.wakoo.im.bean.MsgSendCondition
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.message.types.UCGiftMessage

data class MsgFilter(
    val id: String?,
) {
    companion object {
        val Empty = MsgFilter(null)
    }

    val isEmpty get() = this === Empty
}

/**
 * 回调在ui线程
 */
interface IMCompatListener {
    val filter: MsgFilter
        get() = MsgFilter.Empty

    /**
     * 消息发送事件通知，如果在聊天页面，应该立刻上屏显示发送中的状态
     */
    fun onSendNewMessage(
        message: UCInstanceMessage,
        onlyLocal: Boolean = false,
    ) = Unit

    /**
     * 消息发送结果通知，如果在聊天页面，应该立刻更新消息发送状态
     */
    fun onSendMessageResult(
        message: UCInstanceMessage,
        success: Boolean,
    ) = Unit

    /**
     * 消息检测不通过，不能发送
     */
    fun onMessageCheckFail(
        condition: MsgSendCondition?,
        throwable: Throwable?,
    ) = Unit

    /**
     * 消息重发通知，消息发送失败点击重发
     */
    fun onResendMessage(message: UCInstanceMessage) = Unit

    /**
     * 收到新消息通知
     */
    fun onRecvNewMessage(
        message: UCInstanceMessage,
        offline: Boolean = false,
    ) = Unit

    /**
     * 收到新自定义消息通知，同时会先调用[onRecvNewMessage]
     */
    fun onRecvNewCustomMessage(
        message: UCCustomMessage,
        offline: Boolean = false,
    ) = Unit

    /**
     * 获取历史消息结果通知，只有有数据才会回调
     */
    fun onFetchHistoryMessagesSuccess(
        messages: List<UCInstanceMessage>,
        descendPullOrder: Boolean,
    ) = Unit

    /**
     * 消息删除通知
     */
    fun onMessageDeleted(message: UCInstanceMessage) = Unit

    /**
     * 消息撤回通知
     */
    fun onMessageRecalled(message: UCInstanceMessage) = Unit

    /**
     * 消息扩展更新通知
     */
    fun onMessageExpansionUpdate(
        message: UCInstanceMessage,
        add: Boolean,
        expansions: Map<String, String>,
    ) = Unit

    /**
     * 消息扩展删除通知
     */
    fun onMessageExpansionDelete(
        message: UCInstanceMessage,
        deleteKeys: Array<String>,
    ) = Unit

    /**
     * 消息已读回执，一般不实现这个回调
     */
    fun onReadReceiptsReceived(readReceipts: List<MessageReadReceipt>) = Unit

    /**
     * 消息已读回执，一般通过此回调刷新页面
     */
    fun onReadReceiptReceived(readReceipt: MessageReadReceipt) = Unit

    /**
     * 消息被修改通知
     */
    fun onMessageModified(message: UCInstanceMessage) = Unit

    /**
     * 会话新增通知
     */
    fun onNewConversation(conversationList: List<UCConversation>) = Unit

    /**
     * 会话变更通知
     */
    fun onConversationChanged(conversationList: List<UCConversation>) = Unit

    /**
     * 会话删除通知
     */
    fun onConversationDeleted(idList: Set<String>) = Unit

//    /**
//     * 语音通话消息通知
//     */
//    fun onVoiceCallMessage(message: UCVoiceCallMessage, offline: Boolean = false) = Unit
//
//    /**
//     * 视频通话消息通知
//     */
//    fun onVideoCallMessage(message: UCVideoCallMessage, offline: Boolean = false) = Unit

    /**
     * 播放历史消息中未播放的礼物
     */
    fun onPlayHistoryGiftEffect(messages: List<UCGiftMessage>) = Unit

    fun onCallUserLogin() = Unit

    fun onUserLogout() = Unit

    /**
     * 客户端下线
     */
    fun onClientOffline(reason: String) = Unit
}
