package com.buque.wakoo.im.utils

import android.util.Log
import com.buque.wakoo.BuildConfig

object IMLogUtils {
    fun i(tag: String, content: String) {
        if (BuildConfig.DEBUG) {
            Log.i("xzwzz", "[$tag]$content")
        }
    }

    fun i(content: String) {
        i("IM", content)
    }

    fun w(content: String) {
        if (BuildConfig.DEBUG) {
            Log.w("xzwzz", "[IM]$content")
        }
    }
}