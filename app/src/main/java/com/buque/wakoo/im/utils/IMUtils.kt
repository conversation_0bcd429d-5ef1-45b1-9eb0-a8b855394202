package com.buque.wakoo.im.utils

import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.ext.isMainThread
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.inter.MsgFilter
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.booleanOrNull
import kotlinx.serialization.json.doubleOrNull
import kotlinx.serialization.json.floatOrNull
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.longOrNull
import java.io.File
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

object IMUtils {

    val sendMsgCoroutineContext: CoroutineContext by lazy {
        Dispatchers.IO.limitedParallelism(1)
    }

    val imConvCoroutineContext: CoroutineContext by lazy {
        Dispatchers.IO.limitedParallelism(1)
    }

    val imHandleCoroutineContext: CoroutineContext by lazy {
        Dispatchers.IO.limitedParallelism(1)
    }

    fun isLocalUri(uri: Uri): Boolean {
        val scheme = uri.scheme ?: return false
        return scheme.equals(ContentResolver.SCHEME_FILE, true) || scheme.equals(ContentResolver.SCHEME_CONTENT, true)
    }

    fun isFileExists(path: String): Boolean {
        return File(path).exists()
    }

    fun isFileExists(context: Context, uri: Uri?): Boolean {
        if (uri == null) return false

        val scheme = uri.scheme ?: return false

        if (scheme.equals(ContentResolver.SCHEME_FILE, true)) {
            val path = uri.path
            return path != null && File(path).exists()
        } else if (scheme.equals(ContentResolver.SCHEME_CONTENT, true)) {
            try {
                context.contentResolver?.openFileDescriptor(uri, "r").use {
                    return true
                }
            } catch (e: Exception) {
                return false
            }
        } else {
            return false // 非本地URI或无法处理
        }
    }

    fun copyFileToCache(context: Context, uri: Uri): String? {
        return FileUtils.getPathByCopyFile(context, uri)
    }

    fun launchOnMain(
        block: suspend CoroutineScope.() -> Unit,
    ): Job = IMCompatCore.obtainLoginCoroutineScope()
        .launch(start = if (isMainThread) CoroutineStart.UNDISPATCHED else CoroutineStart.DEFAULT, block = block)


    fun launchOnUnMain(
        block: suspend CoroutineScope.() -> Unit,
    ): Job = launchCoroutine(context = imHandleCoroutineContext, block = block)

    fun launchCoroutine(
        context: CoroutineContext = EmptyCoroutineContext,
        start: CoroutineStart = CoroutineStart.DEFAULT,
        block: suspend CoroutineScope.() -> Unit,
    ): Job = IMCompatCore.obtainLoginCoroutineScope().launch(context, start, block)

    inline fun parseJsonToMap(
        jsonString: String,
        transform: (String, JsonElement) -> Any? = { _, _ -> null }
    ): Map<String, Any?> {
        return when (val jsonElement = AppJson.parseToJsonElement(jsonString)) {
            is JsonObject -> {
                jsonElement.entries.associate { (key, value) ->
                    val result = transform(key, value)
                    if (result != null) {
                        key to result
                    } else {
                        key to when (value) {
                            is JsonPrimitive -> {
                                if (value is JsonNull) {
                                    null
                                } else {
                                    // 如果是基本数据类型，返回其原始值
                                    value.booleanOrNull ?: value.intOrNull ?: value.longOrNull ?: value.floatOrNull
                                    ?: value.doubleOrNull ?: value.content
                                }
                            }

                            is JsonObject -> {
                                // 如果是对象，则根据业务场景进一步解析
                                value
                            }

                            is JsonArray -> {
                                // 如果是数组，则根据业务场景进一步解析
                                value
                            }
                        }
                    }
                }
            }

            else -> emptyMap()  // 如果不是 JsonObject，则返回空 Map
        }
    }

}

@Composable
fun WatchRecvNewMessage(filter: MsgFilter = MsgFilter.Empty, callback: (UCInstanceMessage) -> Unit) {
    val updatedFilter by rememberUpdatedState(newValue = filter)
    val updatedCallback by rememberUpdatedState(newValue = callback)
    WatchMessageEventEffect(object : IMCompatListener {

        override val filter: MsgFilter
            get() = updatedFilter

        override fun onRecvNewMessage(message: UCInstanceMessage, offline: Boolean) {
            updatedCallback(message)
        }
    })
}

/**
 * @param filter 过滤器
 * @param withOffline 是否处理离线消息
 * @param callback 处理回调
 */
@Composable
fun WatchRecvNewCustomMessage(filter: MsgFilter = MsgFilter.Empty, callback: (UCCustomMessage) -> Unit) {
    val updatedFilter by rememberUpdatedState(newValue = filter)
    val updatedCallback by rememberUpdatedState(newValue = callback)
    WatchMessageEventEffect(object : IMCompatListener {

        override val filter: MsgFilter
            get() = updatedFilter

        override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
            updatedCallback(message)
        }
    })
}

@Composable
fun WatchMessageEventEffect(listener: IMCompatListener) {
    DisposableEffect(key1 = listener) {
        IMCompatCore.addIMListener(listener)
        onDispose {
            IMCompatCore.removeIMListener(listener)
        }
    }
}


