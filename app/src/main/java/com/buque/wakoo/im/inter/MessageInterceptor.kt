package com.buque.wakoo.im.inter

import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.bean.SendParams

interface MessageInterceptor {
    suspend fun sendMessage(
        params: SendParams,
        bundle: MessageBundle?,
        mainMessage: UCMessage
    ): <PERSON>olean {
        return true
    }

    suspend fun receiveMessage(message: UCInstanceMessage): UCInstanceMessage {
        return message
    }
}