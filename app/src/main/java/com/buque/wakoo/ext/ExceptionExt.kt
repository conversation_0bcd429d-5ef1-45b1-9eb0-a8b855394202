package com.buque.wakoo.ext

import com.buque.wakoo.network.BusinessException
import com.buque.wakoo.network.DataParseException
import com.buque.wakoo.network.HttpException
import com.buque.wakoo.network.MissingApiResponseDataException
import com.buque.wakoo.network.NetworkException

/**
 * 扩展方法：判断一个 [Throwable] 是否是网络异常。
 *
 * 该方法通过检查 Throwable 是否是 [NetworkException] 的实例来判断。
 * [NetworkException] 通常封装了由 OkHttp 或底层网络操作直接抛出的 [IOException]，
 * 表示网络连接层面出现了问题（例如无网络、DNS 解析失败、超时等）。
 *
 * @return 如果此 Throwable 是 [NetworkException] 的实例，则返回 `true`，否则返回 `false`。
 */
fun Throwable.isNetworkException(): Boolean = this is NetworkException

// 你也可以考虑添加其他类似的判断扩展方法，例如：
// 判断是否是 HTTP 协议异常 (4xx, 5xx)
fun Throwable.isHttpException(): Boolean = this is HttpException

// 判断是否是业务异常 (例如服务器返回的业务错误码)
fun Throwable.isBusinessException(): Boolean = this is BusinessException

// 判断是否是数据解析异常
fun Throwable.isDataParseException(): Boolean = this is DataParseException

// 判断是否是需要非空数据但返回空值的异常
fun Throwable.isMissingApiResponseDataException(): Boolean = this is MissingApiResponseDataException
