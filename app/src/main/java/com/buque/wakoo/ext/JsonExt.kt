package com.buque.wakoo.ext

import com.buque.wakoo.app.AppJson
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.booleanOrNull
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.floatOrNull
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.json.longOrNull

fun JsonObject.getOrNull(key: String): JsonElement? =
    get(key)?.takeIf {
        it !is JsonNull
    }

fun JsonObject.getStringOrNull(key: String): String? = getOrNull(key)?.jsonPrimitive?.contentOrNull

fun JsonObject.getBoolOrNull(key: String): Boolean? = getOrNull(key)?.jsonPrimitive?.booleanOrNull

fun JsonObject.getIntOrNull(key: String): Int? = getOrNull(key)?.jsonPrimitive?.intOrNull

fun JsonObject.getLongOrNull(key: String): Long? = getOrNull(key)?.jsonPrimitive?.longOrNull

fun JsonObject.getFloatOrNull(key: String): Float? = getOrNull(key)?.jsonPrimitive?.floatOrNull

inline fun <reified T> JsonObject.parseValue(key: String): T? =
    try {
        getOrNull(key)?.let { AppJson.decodeFromJsonElement<T>(it) }
    } catch (e: Exception) {
        null
    }

inline fun <reified T> JsonObject.parseValue(
    key: String,
    default: T,
): T = parseValue(key) ?: default
