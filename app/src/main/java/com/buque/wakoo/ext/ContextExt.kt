package com.buque.wakoo.ext

import android.content.Context
import android.content.Intent
import com.buque.wakoo.MainActivity

/**
 * 返回桌面
 */
fun Context.backToHome() {
    if (this is MainActivity) {
        try {
            val homeIntent = Intent(Intent.ACTION_MAIN)
            homeIntent.addCategory(Intent.CATEGORY_HOME)
            homeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(homeIntent)
        } catch (e: Exception) {
            this.finish()
        }
    }
}
