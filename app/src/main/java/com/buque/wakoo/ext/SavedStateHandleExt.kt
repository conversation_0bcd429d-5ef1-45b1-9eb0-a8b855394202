package com.buque.wakoo.ext

import androidx.lifecycle.SavedStateHandle
import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty

// 扩展属性委托，简化SavedStateHandle的使用
fun <T> SavedStateHandle.delegate(
    key: String,
    initialValue: T,
) = object : ReadWriteProperty<Any, T> {
    override fun getValue(
        thisRef: Any,
        property: KProperty<*>,
    ): T = get<T>(key) ?: initialValue

    override fun setValue(
        thisRef: Any,
        property: KProperty<*>,
        value: T,
    ) {
        set(key, value)
    }
}
