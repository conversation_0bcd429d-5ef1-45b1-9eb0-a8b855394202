package com.buque.wakoo.ext

import com.buque.wakoo.utils.LogUtils
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import kotlin.coroutines.resumeWithException

fun <T> CancellableContinuation<T>.resumeIfActive(value: T) {
    if (isActive) {
        try {
            resume(value, null)
        } catch (e: IllegalStateException) {
            //fixed https://console.firebase.google.com/u/1/project/ucoo-386017/crashlytics/app/android:com.qyqy.ucoo/issues/62fef20289e5320bc42dd37d4e3b5c7f?time=last-seven-days&types=crash&sessionEventKey=66E6E70101D700010866C526C50947C0_1993334523905692072
            //可能是线程导致的问题, 简单点直接捕获完了
            LogUtils.w("CancellableContinuation", e)
        }
    }
}

open class ResultContinuation<T> : (CancellableContinuation<T>) -> Unit {

    @Volatile
    private var continuation: CancellableContinuation<T>? = null

    val isActive: Boolean
        get() = continuation != null && continuation?.isActive == true

    override fun invoke(continuation: CancellableContinuation<T>) {
        this.continuation = continuation
        continuation.invokeOnCancellation {
            this.continuation = null
        }
    }

    fun resume(value: T) {
        if (continuation?.isActive == true) {
            continuation?.resumeIfActive(value)
            continuation = null
        }
    }

    fun cancel() {
        if (continuation?.isActive == true) {
            continuation?.resumeWithException(CancellationException())
            continuation = null
        }
    }

    suspend fun suspendUntil(): T {
        return suspendCancellableCoroutine(this)
    }

    suspend fun suspendUntilWithTimeout(timeMillis: Long): T? {
        return withTimeoutOrNull(timeMillis) {
            suspendUntil()
        }
    }

}

class AwaitContinuation : ResultContinuation<Unit>() {

    var shouldSuspend = false
        set(value) {
            field = value
            if (value.not()) {
                resume()
            }
        }


    fun resume() {
        resume(Unit)
    }
}

fun awaitContinuation() = AwaitContinuation()