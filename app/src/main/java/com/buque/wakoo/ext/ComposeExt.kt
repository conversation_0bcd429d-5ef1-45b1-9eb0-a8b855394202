package com.buque.wakoo.ext

import androidx.activity.OnBackPressedDispatcher
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imeAnimationTarget
import androidx.compose.foundation.layout.isImeVisible
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisallowComposableCalls
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.produceState
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.Dp
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.LocalAppNavController
import kotlinx.coroutines.flow.collectLatest
import kotlin.reflect.KProperty

@Composable
fun Modifier.noEffectClick(
    enabled: Boolean = true,
    onClickLabel: String? = null,
    role: Role? = null,
    onClick: () -> Unit,
) = clickable(
    interactionSource = remember { MutableInteractionSource() },
    indication = null,
    enabled = enabled,
    onClickLabel = onClickLabel,
    role = role,
    onClick = onClick,
)

val onBackPressedDispatcher: OnBackPressedDispatcher?
    @Composable
    get() = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher

val onBackInvoker: () -> Unit
    @Composable
    get() =
        with(onBackPressedDispatcher) {
            if (this == null) {
                {}
            } else {
                remember(this) {
                    {
                        this.onBackPressed()
                    }
                }
            }
        }

val onRootPopInvoker: (AppNavKey?) -> Unit
    @Composable
    get() =
        with(LocalAppNavController.root) {
            remember(this) {
                {
                    if (it != null) {
                        popIf(it)
                    } else {
                        pop()
                    }
                }
            }
        }

val onPopInvoker: (AppNavKey?) -> Unit
    @Composable
    get() =
        with(LocalAppNavController.current) {
            remember(this) {
                {
                    if (it != null) {
                        popIf(it)
                    } else {
                        pop()
                    }
                }
            }
        }

@Composable
inline fun <reified T : AppNavKey> typeRootPopInvoker(): () -> Unit =
    with(LocalAppNavController.root) {
        remember(this) {
            {
                popIs<T>()
            }
        }
    }

@Composable
inline fun <reified T : AppNavKey> typePopInvoker(): () -> Unit =
    with(LocalAppNavController.current) {
        remember(this) {
            {
                popIs<T>()
            }
        }
    }

@Composable
fun <T> keepLastNonNullState(newState: T?): T? {
    val lastState =
        remember {
            mutableStateOf<T?>(null)
        }
    return if (newState == null) {
        lastState.value
    } else {
        SideEffect {
            lastState.value = newState
        }
        newState
    }
}

@Composable
inline fun <T> rememberRefWithPrevious(
    key1: Any?,
    crossinline calculation: @DisallowComposableCalls (T?) -> MutableState<T>,
): MutableState<T> {
    val cacheRef = rememberRef<T>()
    val state =
        remember(key1) {
            calculation(cacheRef.value)
        }
    SideEffect {
        cacheRef.value = state.value
    }
    return state
}

@Composable
inline fun <T> rememberSaveableRefWithPrevious(
    key1: Any?,
    crossinline calculation: @DisallowComposableCalls (T?) -> MutableState<T>,
): MutableState<T> {
    val cacheRef = rememberSaveableRef<T>()
    val state =
        rememberSaveable(key1) {
            calculation(cacheRef.value)
        }
    SideEffect {
        cacheRef.value = state.value
    }
    return state
}

@Composable
fun <T> rememberSaveableRef(): MutableState<T?> {
    // for some reason it always recreated the value with vararg keys,
    // leaving out the keys as a parameter for remember for now
    return rememberSaveable {
        mutableStateOf(null)
    }
}

operator fun <T> ConsumableState<T>.getValue(
    thisObj: Any?,
    property: KProperty<*>,
): T = value

/**
 * 一个只能被“消耗”一次的状态。
 * 首次读取 `value` 时，返回 `initialValue` 并将状态标记为已消耗。
 * 后续所有读取都将返回 `consumedValue`。
 *
 * @param T 状态值的类型。
 * @param initialValue 首次读取时返回的值。
 * @param consumedValue 消耗后返回的值。
 */
data class ConsumableState<T>(
    private val initialValue: T,
    private val consumedValue: T,
) {
    private var isConsumed = false

    val value: T
        get() {
            return if (!isConsumed) {
                isConsumed = true
                initialValue
            } else {
                consumedValue
            }
        }
}

/**
 * 创建并记住一个 `ConsumableState`。
 * 主要用于处理那些只需要在首次组合/读取时触发一次的逻辑。
 *
 * 例如，判断是否是首次进入某个 Composable。
 *
 * @param initialValue 首次读取时返回的值。
 * @param consumedValue 消耗后返回的值。
 */
@Composable
fun <T> rememberConsumableState(
    initialValue: T,
    consumedValue: T,
): ConsumableState<T> =
    remember {
        ConsumableState(initialValue, consumedValue)
    }

// 您的原始用例可以这样调用：
@Composable
fun rememberIsFirstComposition(): ConsumableState<Boolean> = rememberConsumableState(initialValue = true, consumedValue = false)

@Composable
fun rememberFirstEnter(): State<Boolean> =
    rememberSaveable {
        mutableStateOf(true)
    }.also {
        SideEffect {
            it.value = false
        }
    }

@Composable
fun rememberIsRestore(): State<Boolean> {
    val isRestore =
        remember {
            mutableStateOf(true)
        }
    rememberSaveable {
        isRestore.value = false
        mutableStateOf(false)
    }
    return isRestore
}

@Composable
fun <T> rememberRef(): MutableState<T?> {
    // for some reason it always recreated the value with vararg keys,
    // leaving out the keys as a parameter for remember for now
    return remember {
        mutableStateOf(null)
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun imeVisible(minKeyboardHeight: Dp): State<Boolean> {
    val density = LocalDensity.current
    val imeAnimationTarget = WindowInsets.imeAnimationTarget
    return produceState(initialValue = WindowInsets.isImeVisible, imeAnimationTarget, density, minKeyboardHeight) {
        snapshotFlow {
            with(density) {
                imeAnimationTarget.getBottom(this) >= minKeyboardHeight.roundToPx()
            }
        }.collectLatest {
            value = it
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun imeHeight(): State<Int> {
    val density = LocalDensity.current
    val ime = WindowInsets.ime
    return produceState(initialValue = ime.getBottom(density), density) {
        snapshotFlow {
            ime.getBottom(density)
        }.collectLatest {
            value = it
        }
    }
}

/**
 * 一个自定义的 Modifier，当在其应用的组件上发生点击时，
 * 会清除当前焦点，通常用于在点击背景时隐藏键盘。
 */
fun Modifier.hideKeyboardOnClickOutside(): Modifier =
    composed {
        val focusManager = LocalFocusManager.current
        this.clickable(
            interactionSource = remember { MutableInteractionSource() },
            indication = null,
        ) {
            focusManager.clearFocus()
        }
    }
