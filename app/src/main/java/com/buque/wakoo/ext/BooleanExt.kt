package com.buque.wakoo.ext

fun <T> Boolean.orNull(block: () -> T): T? = if (this) block() else null

infix fun <T> Boolean.select(that: T): T? =
    if (this) {
        that
    } else {
        null
    }

/**
 * 根据布尔条件从 Pair 中选择一个元素。
 * 如果布尔参数为 true，则返回 Pair 的第一个元素；
 * 否则返回 Pair 的第二个元素。
 *
 * @param condition 用于选择的布尔条件。
 * @return 根据布尔条件选择的元素。
 *
 * 用法示例：
 * `val result1 = ("Apple" to "Banana") by true // result1 是 "Apple"`
 * `val result2 = (10 to 20) by false // result2 是 20`
 */
infix fun <T> Pair<T, T>.by(condition: Boolean): T =
    if (condition) {
        this.first // 字段：Pair 的第一个元素，当布尔参数为 true 时返回。
    } else {
        this.second // 字段：Pair 的第二个元素，当布尔参数为 false 时返回。
    }
