package com.buque.wakoo.ext

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.VectorConverter
import androidx.compose.animation.core.tween
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInWindow
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.toSize
import kotlinx.coroutines.launch

/**
 * 一个自定义 Shape，可以根据一个 Rect 进行裁剪。
 */
private class RectShape(
    var rect: Rect,
) : Shape {
    override fun createOutline(
        size: Size,
        layoutDirection: LayoutDirection,
        density: Density,
    ): Outline = Outline.Rectangle(rect)
}

/**
 * 自定义 Modifier，将 Composable 裁剪到给定的屏幕（窗口）坐标 Rect 内。
 *
 * @param screenRect 相对于窗口的裁剪矩形区域。
 */
fun Modifier.clipToScreenRect(screenRect: Rect) =
    this.composed {
        // 1. 使用 a state 来存储本地裁剪区域。
        //    我们使用 remember 来确保 RectShape 只在需要时重新创建。
        var localClipRect by remember { mutableStateOf(Rect.Zero) }
        val clipShape = remember { RectShape(localClipRect) }

        // 2. onGloballyPositioned 用于获取 Composable 的位置并进行计算。
        val positioner =
            Modifier.onGloballyPositioned { layoutCoordinates ->
                // 获取 Composable 在窗口中的左上角坐标
                val composableOffsetInWindow = layoutCoordinates.positionInWindow()

                // 计算屏幕 Rect 的左上角相对于 Composable 的本地坐标
                val localOffset =
                    Offset(
                        x = screenRect.left - composableOffsetInWindow.x,
                        y = screenRect.top - composableOffsetInWindow.y,
                    )

                // 创建基于 Composable 本地坐标系的裁剪 Rect
                val newClipRect = Rect(offset = localOffset, size = screenRect.size)

                // 如果计算出的裁剪区域有变化，则更新它
                if (localClipRect != newClipRect) {
                    localClipRect = newClipRect
                    clipShape.rect = newClipRect // 更新 shape 中的 rect
                }
            }

        // 3. 将位置计算和裁剪 Modifier 链接起来。
        //    positioner 必须在 clipper 之前，以确保位置先被计算。
        positioner.clip(clipShape)
    }

/**
 * 一个可动画的 Modifier，可以在给定的屏幕 Rect 和完全不裁剪之间平滑过渡。
 *
 * @param clipped 当为 true 时，裁剪到 screenRect；当为 false 时，不进行裁剪。
 * @param screenRect 相对于窗口的裁剪矩形区域。
 * @param animationSpec 动画规格，例如 tween(), spring()。
 */
fun Modifier.animatableClipToScreenRect(
    clipped: Boolean,
    overlayClipRect: Rect,
    animationSpec: AnimationSpec<Rect> = tween(durationMillis = 350),
) = this.composed {
    // 1. 创建 Animatable 和 CoroutineScope
    val coroutineScope = rememberCoroutineScope()
    val animatable = remember { Animatable(Rect.Zero, Rect.VectorConverter) }

    // 2. 状态变量，用于存储计算出的本地 Rect，并在首次定位后触发动画
    var hasBeenPositioned by remember { mutableStateOf(false) }
    var localClippedRect by remember { mutableStateOf(Rect.Zero) }
    var localFullRect by remember { mutableStateOf(Rect.Zero) }

    // 3. 监听 'clipped' 状态的变化来驱动动画
    LaunchedEffect(clipped, hasBeenPositioned) {
        // 确保我们已经获取了组件的正确位置
        if (!hasBeenPositioned) return@LaunchedEffect

        val targetRect = if (clipped) localClippedRect else localFullRect

        // 只有当目标和当前值不同时才启动动画
        if (animatable.value != targetRect) {
            animatable.animateTo(targetRect, animationSpec)
        }
    }

    // 4. 使用 onGloballyPositioned 获取坐标并进行计算
    val positioner =
        Modifier.onGloballyPositioned { layoutCoordinates ->
            val composableSize = layoutCoordinates.size.toSize()
            val composableOffset = layoutCoordinates.positionInWindow()

            // 计算出两个目标 Rect
            localClippedRect =
                Rect(
                    offset = overlayClipRect.topLeft - composableOffset,
                    size = overlayClipRect.size,
                )
            localFullRect = Rect(Offset.Zero, composableSize)

            // 5. 处理首次加载：直接跳转 (snapTo) 到初始状态，不播放动画
            if (!hasBeenPositioned) {
                coroutineScope.launch {
                    val initialTarget = if (clipped) localClippedRect else localFullRect
                    animatable.snapTo(initialTarget)
                }
                hasBeenPositioned = true
            }
        }

    // 6. 应用裁剪
    positioner.clip(RectShape(animatable.value))
}
