package com.buque.wakoo.ext

import android.annotation.SuppressLint

val Int.formatSecondsDuration: String
    @SuppressLint("DefaultLocale")
    get() {
        val totalSeconds = this
        val minutes = totalSeconds / 60
        val seconds = totalSeconds % 60
        return String.format(
            "%02d:%02d",
            minutes,
            seconds,
        )
    }

val Long.formatSecondsDuration: String
    @SuppressLint("DefaultLocale")
    get() = this.toInt().formatSecondsDuration

val Int.formatMillisDuration: String
    @SuppressLint("DefaultLocale")
    get() = this.div(1000).formatSecondsDuration

val Long.formatMillisDuration: String
    @SuppressLint("DefaultLocale")
    get() = this.div(1000).formatSecondsDuration
