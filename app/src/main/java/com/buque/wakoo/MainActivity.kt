package com.buque.wakoo

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionLayout
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import coil3.ImageLoader
import coil3.SingletonImageLoader
import coil3.gif.AnimatedImageDecoder
import coil3.request.crossfade
import com.buque.wakoo.core.webview.AppLinkNavigator
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.AppSavedStateManager
import com.buque.wakoo.navigation.AppNavDisplay
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.LocalNavSharedTransitionScope
import com.buque.wakoo.navigation.LoggedInHost
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.appEntry
import com.buque.wakoo.navigation.appEntryProvider
import com.buque.wakoo.navigation.appResultEntry
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.navigation.rememberRootNavController
import com.buque.wakoo.ui.AppFloatingWidgets
import com.buque.wakoo.ui.dialog.loading.ProvideLoadingManager
import com.buque.wakoo.ui.screens.WebScreen
import com.buque.wakoo.ui.screens.login.LoginHostScreen
import com.buque.wakoo.ui.setEdgeToEdgeConfig
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.media.selector.MediaSelectorScreen
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import java.util.UUID

class MainActivity : ComponentActivity() {
    companion object {
        var activeUUID: String = ""
    }

    init {
        activeUUID = UUID.randomUUID().toString()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        setTheme(R.style.Theme_Wakoo)
        super.onCreate(savedInstanceState)
        AppSavedStateManager.bind(savedStateRegistry)
        SingletonImageLoader.setSafe {
            ImageLoader
                .Builder(this)
                .crossfade(true)
                .components {
                    add(AnimatedImageDecoder.Factory())
                }.build()
        }
        setEdgeToEdgeConfig()
        setContent {
            WakooTheme {
                WakooAppScreen(AccountManager.isLoggedIn)
            }
        }
    }

    @OptIn(ExperimentalSharedTransitionApi::class)
    @Composable
    private fun WakooAppScreen(isLoggedIn: Boolean) {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background,
        ) {
            val controller =
                rememberRootNavController(
                    loginNavKey = Route.Login,
                    isLoggedIn = isLoggedIn,
                    if (isLoggedIn) LoggedInHost else Route.Login,
                )

            LaunchedEffect(controller) {
                AccountManager.accountStateFlow
                    .map {
                        it != null
                    }.distinctUntilChanged()
                    .collectLatest {
                        if (it) {
                            controller.login()
                        } else {
                            controller.logout()
                        }
                    }
            }

            ProvideLoadingManager {
                LocalAppNavController.ProvideController(controller) {
                    SharedTransitionLayout {
                        CompositionLocalProvider(LocalNavSharedTransitionScope provides this) {
                            AppNavDisplay(
                                backStack = controller.backStack,
                                entryProvider =
                                    appEntryProvider {
                                        appEntry<Route.Login> {
                                            LoginHostScreen()
                                        }
                                        appEntry<Route.Web> {
                                            val dc = rememberDialogController()
                                            WebScreen(it, onOpenLink = { link ->
                                                AppLinkNavigator.go(link, controller, dc)
                                            }, onOpenPage = {
                                                controller.push(it)
                                            }) {
                                                controller.pop()
                                            }
                                        }
                                        appResultEntry<Route.MediaSelector> { it, resultKey ->
                                            MediaSelectorScreen(
                                                resultKey,
                                                it.mediaType,
                                                it.maxSelectCount.coerceAtLeast(1),
                                            )
                                        }
                                        appEntry(LoggedInHost) {
                                            LoggedInHostScreen(controller)
                                        }
                                    },
                            )
                        }
                    }
                }
            }

            AppFloatingWidgets()
        }
    }
}
