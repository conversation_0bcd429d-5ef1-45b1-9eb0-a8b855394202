package com.buque.wakoo.utils

import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import kotlin.coroutines.cancellation.CancellationException
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

open class ResultContinuation<T> : (CancellableContinuation<T>) -> Unit {

    @Volatile
    private var continuation: CancellableContinuation<T>? = null

    val isActive: <PERSON>ole<PERSON>
        get() = continuation != null && continuation?.isActive == true

    override fun invoke(continuation: CancellableContinuation<T>) {
        this.continuation = continuation
        continuation.invokeOnCancellation {
            this.continuation = null
        }
    }

    fun resume(value: T) {
        if (continuation?.isActive == true) {
            continuation?.resume(value)
            continuation = null
        }
    }

    fun cancel() {
        if (continuation?.isActive == true) {
            continuation?.resumeWithException(CancellationException())
            continuation = null
        }
    }

    suspend fun suspendUntil(): T {
        return suspendCancellableCoroutine(this)
    }

    suspend fun suspendUntilWithTimeout(timeMillis: Long): T? {
        return withTimeoutOrNull(timeMillis) {
            suspendUntil()
        }
    }

}

class AwaitContinuation : ResultContinuation<Unit>() {

    var shouldSuspend = false
        set(value) {
            field = value
            if (value.not()) {
                resume()
            }
        }


    fun resume() {
        resume(Unit)
    }
}

fun awaitContinuation() = AwaitContinuation()