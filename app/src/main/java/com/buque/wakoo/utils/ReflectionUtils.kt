package com.buque.wakoo.utils

object ReflectionUtils {
    fun modifyPrivateValue(
        className: String,
        fieldName: String,
        value: Any,
    ) {
        try {
            val clazz = Class.forName(className)
            val field = clazz.getDeclaredField(fieldName)
            field.isAccessible = true
            field.set(null, value) // 设置新的值
        } catch (nothing: Exception) {
        }
    }
}
