package com.buque.wakoo.utils

import com.buque.wakoo.app.OnAction
import com.buque.wakoo.ui.widget.state.CState

sealed interface AsyncValue<T> {

    class Loading<T> : AsyncValue<T>

    data class Data<T>(val result: Result<T>) : AsyncValue<T>
}

fun <T> AsyncValue<T>.stateWhen(
    onLoading: OnAction = {},
    onData: (T) -> Unit = {},
    onError: (Throwable?) -> Unit = {}
) {
    when (this) {
        is AsyncValue.Data<T> -> {
            val result = this.result
            if (result.isSuccess) {
                onData(result.getOrThrow())
            } else {
                onError(result.exceptionOrNull())
            }
        }

        is AsyncValue.Loading<*> -> onLoading()
    }
}

fun <T, R> AsyncValue<T>.map(transform: (T) -> R): AsyncValue<R> {
    var value: AsyncValue<R> = AsyncValue.Loading()
    this.stateWhen(
        onData = {
            value = transform(it).toAsyncData()
        },
        onError = {
            value = it.toAsyncError()
        }
    )
    return value
}

fun <T> AsyncValue<T>.toCState(): CState<T> {
    var state: CState<T> = CState.Idle
    this.stateWhen(
        onLoading = {
            state = CState.Loading()
        },
        onData = {
            state = CState.Success(it)
        },
        onError = {
            state = CState.Error(it ?: UNKNOWN)
        }
    )
    return state
}

class AsyncException(message: String, throwable: Throwable?) : IllegalStateException(message, throwable)

private val UNKNOWN = Throwable(message = "unknown error")

fun <T> Throwable?.toAsyncError() = AsyncValue.Data(Result.failure<T>(this ?: UNKNOWN))

fun <T> T.toAsyncData() = AsyncValue.Data(Result.success(this))

fun <T> Result<T>.toAsyncValue() = AsyncValue.Data(this)

fun <T> AsyncValue<T>.getOrNull(): T? = if (this is AsyncValue.Data) {
    this.result.getOrNull()
} else {
    null
}

fun <T> AsyncValue<T>.isSuccess() = (this is AsyncValue.Data) && this.result.isSuccess