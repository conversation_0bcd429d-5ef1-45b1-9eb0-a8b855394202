package com.buque.wakoo.utils.upload

import com.tencent.cos.xml.transfer.COSXMLUploadTask
import com.tencent.cos.xml.transfer.TransferState

/**
 * 表示高级接口上传操作的结果。
 */
sealed class TransferResult {
    /**
     * 表示上传成功。
     * @param result 来自 COS SDK 的成功结果对象。
     */
    data class Success(
        val result: COSXMLUploadTask.COSXMLUploadTaskResult,
        val url: String,
    ) : TransferResult()

    /**
     * 表示上传失败。
     * @param exception 发生的客户端或服务端异常。
     */
    data class Failure(
        val exception: Exception,
    ) : TransferResult()
}

/**
 * 包含上传任务所有回调信息的数据类。
 *
 * @param onProgress 进度回调 (已上传字节, 总字节)。
 * @param onStateChanged 任务状态变化回调。
 * @param onInitUploadId 分块上传初始化成功后，获取 uploadId 的回调，用于续传。
 */
data class UploadCallbacks(
    val onProgress: (Long, Long) -> Unit = { _, _ -> },
    val onStateChanged: (TransferState) -> Unit = {},
    val onInitUploadId: (String) -> Unit = {},
)
