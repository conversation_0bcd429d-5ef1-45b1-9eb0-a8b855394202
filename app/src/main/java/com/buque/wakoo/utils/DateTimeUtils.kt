package com.buque.wakoo.utils

import android.annotation.SuppressLint
import android.os.SystemClock
import kotlinx.datetime.Instant
import kotlinx.datetime.format
import kotlinx.datetime.toLocalDateTime
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone

/**
 * 日期时间工具类，封装常见的日期和时间戳操作
 * 设计为易于迁移到KMP（Kotlin Multiplatform）的形式
 *
 * 在KMP中，可以使用kotlinx-datetime库替换Java的Date相关API
 * 迁移时只需要替换本类的实现，而不需要修改调用的地方
 */
object DateTimeUtils {
    /**
     * 获取设备启动到现在的毫秒数，包含设备深度休眠的时间。
     * 这个时钟是单调递增的，不受用户调整日期或时区的影响，
     * 是测量时间间隔的推荐方式。
     */
    fun elapsedRealtime(): Long = SystemClock.elapsedRealtime()

    /**
     * 获取当前时间的Unix时间戳（秒）
     */
    fun currentTimeSeconds(): Long = System.currentTimeMillis() / 1000

    /**
     * 获取当前时间的Unix时间戳（毫秒）
     */
    fun currentTimeMillis(): Long = System.currentTimeMillis()

    /**
     * 将Unix时间戳（秒）转换为Date对象
     */
    fun fromUnixTimestamp(timestamp: Long): Date = Date(timestamp * 1000)

    /**
     * 将Unix时间戳（毫秒）转换为Date对象
     */
    fun fromUnixTimestampMillis(timestampMillis: Long): Date = Date(timestampMillis)

    /**
     * 将Date对象转换为Unix时间戳（秒）
     */
    fun toUnixTimestamp(date: Date): Long = date.time / 1000

    /**
     * 获取指定格式的日期字符串
     *
     * @param timestamp Unix时间戳（秒）
     * @param pattern 日期格式，例如："yyyy-MM-dd HH:mm:ss"
     * @param locale 地区设置，默认使用系统默认值
     * @param timeZone 时区，默认使用系统默认值
     * @return 格式化的日期字符串
     */
    fun formatDate(
        timestamp: Long,
        pattern: String,
        locale: Locale = Locale.getDefault(),
        timeZone: TimeZone = TimeZone.getDefault(),
    ): String {
        val date = fromUnixTimestamp(timestamp)
        val format = SimpleDateFormat(pattern, locale)
        format.timeZone = timeZone
        return format.format(date)
    }

    /**
     * 获取指定格式的当前日期字符串
     */
    fun formatCurrentDate(
        pattern: String,
        locale: Locale = Locale.getDefault(),
        timeZone: TimeZone = TimeZone.getDefault(),
    ): String = formatDate(currentTimeSeconds(), pattern, locale, timeZone)

    /**
     * 获取相对于当前时间的友好显示
     * 例如："刚刚"、"5分钟前"、"2小时前"等
     *
     * @param timestamp Unix时间戳（秒）
     * @return 相对时间的友好显示
     */
    fun getRelativeTimeSpan(timestamp: Long): String {
        val now = currentTimeSeconds()
        val diff = now - timestamp

        return when {
            diff < 60 -> "刚刚"
            diff < 3600 -> "${diff / 60}分钟前"
            diff < 86400 -> "${diff / 3600}小时前"
            diff < 86400 * 2 -> "昨天"
            diff < 86400 * 7 -> "${diff / 86400}天前"
            else -> formatDate(timestamp, "yyyy-MM-dd")
        }
    }

    /**
     * 判断两个时间戳是否是同一天
     */
    fun isSameDay(
        timestamp1: Long,
        timestamp2: Long,
    ): Boolean {
        val date1 = fromUnixTimestamp(timestamp1)
        val date2 = fromUnixTimestamp(timestamp2)

        val cal1 = Calendar.getInstance()
        val cal2 = Calendar.getInstance()
        cal1.time = date1
        cal2.time = date2

        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
            cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
    }

    /**
     * 获取指定日期的开始时间（0点0分0秒）
     */
    fun getDayStart(timestamp: Long): Long {
        val date = fromUnixTimestamp(timestamp)
        val calendar = Calendar.getInstance()
        calendar.time = date
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis / 1000
    }

    /**
     * 获取指定日期的结束时间（23点59分59秒）
     */
    fun getDayEnd(timestamp: Long): Long {
        val date = fromUnixTimestamp(timestamp)
        val calendar = Calendar.getInstance()
        calendar.time = date
        calendar.set(Calendar.HOUR_OF_DAY, 23)
        calendar.set(Calendar.MINUTE, 59)
        calendar.set(Calendar.SECOND, 59)
        calendar.set(Calendar.MILLISECOND, 999)
        return calendar.timeInMillis / 1000
    }

    /**
     * 获取两个时间戳之间的差值（秒）
     */
    fun getDiffSeconds(
        startTimestamp: Long,
        endTimestamp: Long,
    ): Long = endTimestamp - startTimestamp

    /**
     * 格式化时间间隔为可读字符串
     * 例如：1小时20分钟30秒
     */
    @SuppressLint("DefaultLocale")
    fun formatDuration(seconds: Long): String {
        val hours = seconds / 3600
        val minutes = (seconds % 3600) / 60
        val remainingSeconds = seconds % 60

        return when {
            hours > 0 -> String.format("%d小时%d分钟%d秒", hours, minutes, remainingSeconds)
            minutes > 0 -> String.format("%d分钟%d秒", minutes, remainingSeconds)
            else -> String.format("%d秒", remainingSeconds)
        }
    }

    /**
     * 为API请求生成标准格式的ISO 8601时间戳
     * 格式: 2023-05-15T14:30:00Z (UTC)
     */
    fun formatIso8601(timestamp: Long? = null): String {
        val date = if (timestamp != null) fromUnixTimestamp(timestamp) else Date()
        val format = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US)
        format.timeZone = TimeZone.getTimeZone("UTC")
        return format.format(date)
    }

    /**
     * 定义期望的日期时间输出格式。
     */
    enum class DateTimeOutputFormat {
        /** 格式: YYYY-MM-DD */
        DATE_ONLY,

        /** 格式: YYYY-MM-DD HH:mm */
        DATE_TIME
    }

    /**
     * 将纪元秒（Int类型）转换为指定格式的日期或日期时间字符串。
     *
     * @param epochSeconds 从 1970-01-01T00:00:00Z 开始计算的秒数。
     * @param format 输出格式，可选值见 [DateTimeOutputFormat] 枚举。默认为只输出日期。
     * @param timeZone 用于确定日期和时间的时区。默认为系统当前时区。
     * @return 根据指定格式格式化后的字符串。
     */
    fun secondsToFormattedString(
        epochSeconds: Int,
        format: DateTimeOutputFormat = DateTimeOutputFormat.DATE_ONLY, // 默认只输出日期
        timeZone: kotlinx.datetime.TimeZone = kotlinx.datetime.TimeZone.currentSystemDefault()
    ): String {
        // 步骤 1: 从纪元秒创建 Instant 对象
        val instant = Instant.fromEpochSeconds(epochSeconds.toLong())

        // 步骤 2: 将 Instant 转换为指定时区的 LocalDateTime (这是两种格式都需要的基础)
        val localDateTime = instant.toLocalDateTime(timeZone)

        // 步骤 3: 根据指定的格式进行处理
        return when (format) {
            // 情况一：只需要日期
            DateTimeOutputFormat.DATE_ONLY -> {
                // 直接获取日期部分并调用 toString()
                localDateTime.date.toString()
            }

            // 情况二：需要日期和时间
            DateTimeOutputFormat.DATE_TIME -> {
                // 手动拼接 "YYYY-MM-DD HH:mm" 格式
                val year = localDateTime.year
                val month = localDateTime.monthNumber.toString().padStart(2, '0')
                val day = localDateTime.dayOfMonth.toString().padStart(2, '0')
                val hour = localDateTime.hour.toString().padStart(2, '0')
                val minute = localDateTime.minute.toString().padStart(2, '0')

                "$year-$month-$day $hour:$minute"
            }
        }
    }
}
