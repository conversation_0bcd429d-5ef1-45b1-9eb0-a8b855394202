package com.buque.wakoo.utils

/**
 * 刷新标记
 */
sealed interface TypeRefresh {
    companion object {
        val Init: TypeRefresh = X
    }

    private data object X : TypeRefresh

    private data object Y : TypeRefresh

    private data object Z : TypeRefresh

    private data object R : TypeRefresh

    private data object G : TypeRefresh

    private data object B : TypeRefresh

    private data object C : TypeRefresh

    private data object N : TypeRefresh

    private data object M : TypeRefresh

    fun nextTypeRefresh() =
        when (this) {
            X -> Y
            Y -> Z
            Z -> R
            R -> G
            G -> B
            B -> C
            C -> N
            N -> M
            M -> X
        }
}
