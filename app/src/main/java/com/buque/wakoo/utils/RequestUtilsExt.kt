package com.buque.wakoo.utils

/**
 * 处理乐观更新的通用工具函数。
 * 它不关心数据源的具体形式（列表、单个对象等），只通过 getItem 和 setItem 进行交互。
 *
 * @param T 数据项的类型。
 * @param getItem 一个函数，用于从任何数据源获取当前要操作的数据项。
 * @param setItem 一个函数，用于将更新后的数据项写回数据源。
 * @param getProperty 获取当前状态属性值（例如，是否点赞）的函数。
 * @param isRequesting 检查当前是否已有请求正在进行的函数。
 * @param copy 创建一个数据项更新后的副本的函数。
 * @param apiCall 执行网络请求的挂起函数。它接收新的状态作为参数，并返回一个布尔值表示是否成功。
 */
suspend fun <T, R> handleOptimisticAnyRequest(
    newValue: R,
    getItem: () -> T?,
    setItem: (newItem: T) -> Unit,
    getProperty: (T) -> R,
    isRequesting: (T) -> Boolean,
    copy: (item: T, property: R, requesting: Boolean) -> T,
    apiCall: suspend (setState: R) -> Boolean,
) {
    val item = getItem() ?: return
    val oldValue = getProperty(item)
    if (isRequesting(item)) {
        // 如果已经在请求中，则只更新UI状态，让正在进行的请求流程来处理后续逻辑
        setItem(copy(item, newValue, true))
    } else {
        setItem(copy(item, newValue, true))
        setRealAnyState(
            getItem = getItem,
            setItem = setItem,
            propertyToSet = newValue,
            getProperty = getProperty,
            oldValue = oldValue,
            copy = copy,
            apiCall = apiCall,
        )
    }
}

/**
 * @param getItem 获取item
 * @param setItem 设置item
 * @param propertyToSet 新值
 * @param getProperty 获取值的方法
 * @param copy 拷贝
 * @param apiCall 网络请求
 */
private suspend fun <T, R> setRealAnyState(
    getItem: () -> T?,
    setItem: (newItem: T) -> Unit,
    propertyToSet: R,
    oldValue: R,
    getProperty: (T) -> R,
    copy: (item: T, property: R, requesting: Boolean) -> T,
    apiCall: suspend (setState: R) -> Boolean,
) {
    val isSuccess = apiCall(propertyToSet)

    val latestItem = getItem() ?: return
    val latestPropertyValueOnUI = getProperty(latestItem)

    val realProperty = if (isSuccess) propertyToSet else oldValue

    if (latestPropertyValueOnUI != realProperty) {
        if (!isSuccess) {
            // 请求失败，回滚数据
            setItem(copy(latestItem, realProperty, false))
        } else {
            // 请求成功，但UI状态又变了，说明用户有新的操作
            // 遵循原始逻辑，用UI上最新的状态，再次发起请求
            setRealAnyState(
                getItem = getItem,
                setItem = setItem,
                propertyToSet = latestPropertyValueOnUI,
                oldValue = oldValue,
                getProperty = getProperty,
                copy = copy,
                apiCall = apiCall,
            )
        }
    } else {
        // 状态一致，重置请求标志位
        setItem(copy(latestItem, latestPropertyValueOnUI, false))
    }
}

/**
 * `handleOptimisticRequest` 的一个便捷重载版本，专门用于处理 `MutableList` 数据源。
 * 它通过 ID 自动处理元素的查找和更新，同时保持元素在列表中的原始位置。
 *
 * @param items 数据所在的 `MutableList`。
 * @param itemId 要更新的数据项的唯一ID。
 * @param findItemIdFromData 从一个数据项中提取其ID的函数。
 */
suspend fun <T, ID, R> handleOptimisticAnyRequest(
    newValue: R,
    itemId: ID,
    items: MutableList<T>,
    findItemIdFromData: (T) -> ID,
    getProperty: (T) -> R,
    isRequesting: (T) -> Boolean,
    copy: (item: T, property: R, requesting: Boolean) -> T,
    apiCall: suspend (setState: R) -> Boolean,
) {
    handleOptimisticAnyRequest(
        newValue = newValue,
        getItem = { items.find { findItemIdFromData(it) == itemId } },
        setItem = { newItem ->
            val index = items.indexOfFirst { findItemIdFromData(it) == itemId }
            if (index > -1) {
                items[index] = newItem
            }
        },
        getProperty = getProperty,
        isRequesting = isRequesting,
        copy = copy,
        apiCall = apiCall,
    )
}
