package com.buque.wakoo.utils

import com.buque.wakoo.app.AppJson
import timber.log.Timber

/**
 * 日志工具类，封装Timber
 * 提供统一的日志接口，方便未来替换底层实现
 */
object LogUtils {
    /** 是否启用线程信息 */
    private var enableThreadInfo = false

    /** 是否启用调用位置信息 */
    private var enableCallerInfo = false

    /**
     * 配置日志功能
     * @param showThreadInfo 是否在日志中显示线程信息
     * @param showCallerInfo 是否在日志中显示调用者信息
     */
    fun init(
        tree: Timber.Tree,
        showThreadInfo: Boolean = false,
        showCallerInfo: Boolean = false,
    ) {
        enableThreadInfo = showThreadInfo
        enableCallerInfo = showCallerInfo
        Timber.plant(tree)
    }

    /**
     * 获取额外信息前缀（线程和调用位置）
     * @param showThreadInfo 是否显示线程信息，null表示用全局配置
     * @param showCallerInfo 是否显示调用位置信息，null表示用全局配置
     */
    private fun getExtraInfoPrefix(
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ): String {
        val sb = StringBuilder()
        val threadInfo = showThreadInfo ?: enableThreadInfo
        val callerInfo = showCallerInfo ?: enableCallerInfo
        // 添加线程信息
        if (threadInfo) {
            val threadName = Thread.currentThread().name
            sb.append("[Thread:$threadName] ")
        }
        // 添加调用位置
        if (callerInfo) {
            val stackTrace = Throwable().stackTrace
            if (stackTrace.size >= 4) {
                val element = stackTrace[3]
                sb.append("[${element.fileName}:${element.lineNumber}] ")
            }
        }
        return sb.toString()
    }

    /**
     * 处理日志消息，添加额外信息
     */
    private fun processLogMessage(
        message: String,
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ): String {
        val prefix = getExtraInfoPrefix(showThreadInfo, showCallerInfo)
        return if (prefix.isNotEmpty()) "$prefix$message" else message
    }

    /**
     * 调试级别日志
     */
    fun d(
        message: String,
        vararg args: Any?,
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ) {
        Timber.d(processLogMessage(message, showThreadInfo, showCallerInfo), *args)
    }

    fun dTag(
        tag: String,
        message: String,
        vararg args: Any?,
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ) {
        Timber.tag(tag).d(processLogMessage(message, showThreadInfo, showCallerInfo), *args)
    }

    /**
     * 信息级别日志
     */
    fun i(
        message: String,
        vararg args: Any?,
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ) {
        Timber.i(processLogMessage(message, showThreadInfo, showCallerInfo), *args)
    }

    fun iTag(
        tag: String,
        message: String,
        vararg args: Any?,
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ) {
        Timber.tag(tag).i(processLogMessage(message, showThreadInfo, showCallerInfo), *args)
    }

    /**
     * 警告级别日志
     */
    fun w(
        message: String,
        vararg args: Any?,
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ) {
        Timber.w(processLogMessage(message, showThreadInfo, showCallerInfo), *args)
    }

    fun wTag(
        tag: String,
        message: String,
        vararg args: Any?,
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ) {
        Timber.tag(tag).w(processLogMessage(message, showThreadInfo, showCallerInfo), *args)
    }

    fun w(
        throwable: Throwable,
        message: String,
        vararg args: Any?,
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ) {
        Timber.w(throwable, processLogMessage(message, showThreadInfo, showCallerInfo), *args)
    }

    fun wTag(
        tag: String,
        throwable: Throwable,
        message: String,
        vararg args: Any?,
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ) {
        Timber.tag(tag).w(throwable, processLogMessage(message, showThreadInfo, showCallerInfo), *args)
    }

    /**
     * 错误级别日志
     */
    fun e(
        message: String,
        vararg args: Any?,
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ) {
        Timber.e(processLogMessage(message, showThreadInfo, showCallerInfo), *args)
    }

    fun eTag(
        tag: String,
        message: String,
        vararg args: Any?,
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ) {
        Timber.tag(tag).e(processLogMessage(message, showThreadInfo, showCallerInfo), *args)
    }

    fun e(
        throwable: Throwable,
        message: String,
        vararg args: Any?,
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ) {
        Timber.e(throwable, processLogMessage(message, showThreadInfo, showCallerInfo), *args)
    }

    fun eTag(
        tag: String,
        throwable: Throwable,
        message: String,
        vararg args: Any?,
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ) {
        Timber.tag(tag).e(throwable, processLogMessage(message, showThreadInfo, showCallerInfo), *args)
    }

    /**
     * JSON格式日志（调试级别）
     * 使用Kotlin Serialization格式化并输出JSON字符串
     */
    fun json(
        json: String,
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ) {
        if (json.isBlank()) {
            d("JSON空字符串", showThreadInfo = showThreadInfo, showCallerInfo = showCallerInfo)
            return
        }
        try {
            val prefix = getExtraInfoPrefix(showThreadInfo, showCallerInfo)
            val formattedJson = formatJson(json)
            Timber.d("$prefix\n$formattedJson")
        } catch (e: Exception) {
            e(e, "JSON格式化失败: $json", showThreadInfo = showThreadInfo, showCallerInfo = showCallerInfo)
        }
    }

    fun jsonTag(
        tag: String,
        json: String,
        showThreadInfo: Boolean? = null,
        showCallerInfo: Boolean? = null,
    ) {
        if (json.isBlank()) {
            return
        }
        try {
            val prefix = getExtraInfoPrefix(showThreadInfo, showCallerInfo)
            val formattedJson = formatJson(json)
            Timber.tag(tag).d("$prefix\n$formattedJson")
        } catch (e: Exception) {
            eTag(tag, e, "JSON格式化失败: $json", showThreadInfo = showThreadInfo, showCallerInfo = showCallerInfo)
        }
    }

    /**
     * 使用Kotlin Serialization格式化JSON
     */
    private fun formatJson(json: String): String =
        try {
            if (isJson(json)) {
                // 解析为JsonElement然后重新格式化为带缩进的字符串
                val jsonElement = AppJson.parseToJsonElement(json)
                AppJson.encodeToString(
                    kotlinx.serialization.json.JsonElement
                        .serializer(),
                    jsonElement,
                )
            } else {
                json
            }
        } catch (e: Exception) {
            // 如果解析失败（可能是非法JSON），回退到原始字符串
            e.printStackTrace()
            json
        }

    /**
     * 判断字符串是否为合法JSON格式
     */
    fun isJson(str: String): Boolean {
        val s = str.trim()
        return (s.startsWith("{") && s.endsWith("}")) || (s.startsWith("[") && s.endsWith("]"))
    }
}
