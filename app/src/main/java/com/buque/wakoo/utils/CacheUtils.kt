package com.buque.wakoo.utils

import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.DevicesKV
import com.tencent.mmkv.MMKV

object CacheKeys {
    const val SELF_CHAT_GROUP = "self_chat_group"
}

inline fun <reified T> putCache(
    cacheKey: String,
    mmkv: MMKV = DevicesKV,
    data: T,
) {
    mmkv.putString(cacheKey, AppJson.encodeToString(data))
}

suspend inline fun <reified T> useCache(
    cacheKey: String,
    mmkv: MMKV = DevicesKV,
    noinline fetchFunction: (suspend () -> Result<T>)? = null,
    onUpdate: (Result<T>) -> Unit,
) {
    val localJson = mmkv.getString(cacheKey, null)
    try {
        localJson?.let {
            val t = AppJson.decodeFromString<T>(it)
            onUpdate(Result.success(t))
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
    fetchFunction?.invoke()?.let {
        onUpdate(it)
        it.onSuccess { value ->
            mmkv.putString(cacheKey, AppJson.encodeToString(value))
        }
    }
}
