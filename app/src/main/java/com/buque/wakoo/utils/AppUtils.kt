package com.buque.wakoo.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.net.toUri

object AppUtils {
    fun openAppInPlayStore(context: Context) {
        val packageName = context.packageName // 获取当前应用的包名
        try {
            // 尝试直接通过 market:// URI 跳转
            val marketIntent = Intent(Intent.ACTION_VIEW, "market://details?id=$packageName".toUri())
            marketIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK) // 建议添加，尤其是在非Activity中调用时
            context.startActivity(marketIntent)
        } catch (e: Exception) {
            // 如果没有安装应用市场（极少数情况，比如在某些国内定制ROM），则通过浏览器打开
            runCatching {
                val webIntent =
                    Intent(Intent.ACTION_VIEW, "https://play.google.com/store/apps/details?id=$packageName".toUri())
                webIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(webIntent)
            }
        }
    }

    fun openExternalBrowser(context: Context, link: String) {
        if (link.isEmpty()) return
        runCatching {
            val intent = Intent(Intent.ACTION_VIEW, link.toUri())
            context.startActivity(intent)
        }
    }
}
