package com.buque.wakoo.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat

/**
 * 权限工具类
 * 提供常用权限检查和管理功能
 */
object PermissionUtils {

    val Contracts = ActivityResultContracts.RequestPermission()
    /**
     * 录音相关权限
     */
    val AUDIO_PERMISSIONS =
        arrayOf(
            Manifest.permission.RECORD_AUDIO,
        )

    /**
     * 存储相关权限
     */
    val STORAGE_PERMISSIONS =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(
                Manifest.permission.READ_MEDIA_AUDIO,
            )
        } else {
            arrayOf(
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
            )
        }

    /**
     * 录音和存储权限组合
     */
    val AUDIO_RECORD_PERMISSIONS = AUDIO_PERMISSIONS + STORAGE_PERMISSIONS

    /**
     * 检查单个权限是否已授予
     */
    fun hasPermission(
        context: Context,
        permission: String,
    ): Boolean =
        ContextCompat.checkSelfPermission(
            context,
            permission,
        ) == PackageManager.PERMISSION_GRANTED

    /**
     * 检查多个权限是否都已授予
     */
    fun hasPermissions(
        context: Context,
        permissions: Array<String>,
    ): Boolean = permissions.all { hasPermission(context, it) }

    /**
     * 检查录音权限
     */
    fun hasAudioPermission(context: Context): Boolean = hasPermission(context, Manifest.permission.RECORD_AUDIO)

    /**
     * 检查存储权限
     */
    fun hasStoragePermissions(context: Context): Boolean = hasPermissions(context, STORAGE_PERMISSIONS)

    /**
     * 检查录音和存储权限
     */
    fun hasAudioRecordPermissions(context: Context): Boolean = hasPermissions(context, AUDIO_RECORD_PERMISSIONS)

    /**
     * 获取未授予的权限列表
     */
    fun getDeniedPermissions(
        context: Context,
        permissions: Array<String>,
    ): List<String> = permissions.filter { !hasPermission(context, it) }

    /**
     * 获取未授予的录音相关权限
     */
    fun getDeniedAudioRecordPermissions(context: Context): List<String> = getDeniedPermissions(context, AUDIO_RECORD_PERMISSIONS)

    /**
     * 权限状态枚举
     */
    enum class PermissionStatus {
        GRANTED, // 已授予
        DENIED, // 被拒绝
        PERMANENTLY_DENIED, // 永久拒绝（不再询问）
    }

    /**
     * 权限检查结果
     */
    data class PermissionResult(
        val permission: String,
        val status: PermissionStatus,
        val isGranted: Boolean = status == PermissionStatus.GRANTED,
    )

    /**
     * 批量权限检查结果
     */
    data class MultiplePermissionResult(
        val results: List<PermissionResult>,
        val allGranted: Boolean = results.all { it.isGranted },
        val grantedPermissions: List<String> =
            results
                .filter { it.isGranted }
                .map { it.permission },
        val deniedPermissions: List<String> = results.filter { !it.isGranted }.map { it.permission },
    )

    /**
     * 检查权限状态（详细）
     */
    fun checkPermissionStatus(
        context: Context,
        permission: String,
    ): PermissionResult {
        val isGranted = hasPermission(context, permission)
        val status =
            if (isGranted) {
                PermissionStatus.GRANTED
            } else {
                // 这里简化处理，实际使用时可能需要在Activity中检查shouldShowRequestPermissionRationale
                PermissionStatus.DENIED
            }

        return PermissionResult(permission, status)
    }

    /**
     * 批量检查权限状态
     */
    fun checkMultiplePermissions(
        context: Context,
        permissions: Array<String>,
    ): MultiplePermissionResult {
        val results = permissions.map { checkPermissionStatus(context, it) }
        return MultiplePermissionResult(results)
    }

    /**
     * 检查录音权限状态
     */
    fun checkAudioRecordPermissions(context: Context): MultiplePermissionResult =
        checkMultiplePermissions(context, AUDIO_RECORD_PERMISSIONS)

    /**
     * 获取权限说明文本
     */
    fun getPermissionDescription(permission: String): String =
        when (permission) {
            Manifest.permission.RECORD_AUDIO -> "录音权限：用于录制语音内容"
            Manifest.permission.READ_EXTERNAL_STORAGE -> "存储读取权限：用于读取音频文件"
            Manifest.permission.WRITE_EXTERNAL_STORAGE -> "存储写入权限：用于保存录音文件"
            Manifest.permission.READ_MEDIA_AUDIO -> "媒体音频权限：用于访问音频文件"
            else -> "未知权限：$permission"
        }

    /**
     * 获取权限组说明
     */
    fun getAudioRecordPermissionDescription(): String =
        """
        录音功能需要以下权限：
        
        • 录音权限：用于录制语音内容
        • 存储权限：用于保存和读取录音文件
        
        请授予这些权限以正常使用录音功能。
        """.trimIndent()
}
