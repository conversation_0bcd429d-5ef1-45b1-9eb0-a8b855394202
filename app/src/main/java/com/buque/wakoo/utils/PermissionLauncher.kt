package com.buque.wakoo.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Process
import android.os.SystemClock
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableLongState
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.platform.LocalContext
import androidx.core.app.ActivityCompat
import androidx.core.net.toUri
import com.buque.wakoo.BuildConfig
import com.buque.wakoo.R
import com.buque.wakoo.ext.showToast

@Composable
fun rememberPermissionLauncher(
    toastTip: Boolean = true,
    onDenied: (Array<String>) -> Unit = {},
    onRun: (Array<String>) -> Unit
): PermissionLauncher<Unit> {
    return rememberPermissionLauncher(toastTip, { _, it ->
        onDenied(it)
    }) { _, it ->
        onRun(it)
    }
}

fun hasPermissions(context: Context, vararg permissions: String): Boolean {
    val pid = Process.myPid()
    val uid = Process.myUid()
    for (permission in permissions) {
        val pms = if (permission == android.Manifest.permission.WRITE_EXTERNAL_STORAGE) {
            android.Manifest.permission.READ_EXTERNAL_STORAGE
        } else {
            permission
        }
        if (context.checkPermission(pms, pid, uid) != PackageManager.PERMISSION_GRANTED) {
            return false
        }
    }
    return true
}

@Composable
fun <T> rememberPermissionLauncher(
    toastTip: Boolean = true,
    onDenied: (T?, Array<String>) -> Unit = { _, _ -> },
    onRun: (T?, Array<String>) -> Unit
): PermissionLauncher<T> {

    val context = LocalContext.current

    val currentOnDenied by rememberUpdatedState(newValue = onDenied)

    val currentOnRun by rememberUpdatedState(newValue = onRun)

    val permissionsState = remember {
        mutableStateOf<Array<String>>(arrayOf())
    }

    val requestPermissionStartTimeState = remember {
        mutableLongStateOf(0L)
    }

    val tempFlagBox = remember {
        TempFlagBox<T>(null)
    }

    val settingsLauncher = rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        val permissions = permissionsState.value
        if (hasPermissions(context, *permissions)) {
            currentOnRun(tempFlagBox.value, permissions)
        } else {
            currentOnDenied(tempFlagBox.value, permissions)
            if (toastTip) {
                showToast(context.getString(R.string.need_permission))
            }
        }
        tempFlagBox.value = null
    }

    val permissionLauncher =
        rememberLauncherForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { ret ->
            val permissions = ret.keys.toTypedArray()
            if (ret.values.any { !it }) {
                val activity = context as? Activity
                if (
                    SystemClock.elapsedRealtime().minus(requestPermissionStartTimeState.longValue) < 200L
                    && activity != null && !ActivityCompat.shouldShowRequestPermissionRationale(
                        activity,
                        permissions.first()
                    )
                ) {
                    // toast这个不需要被toastTip控制
                    showToast("请到设置中授予权限")
                    val packageUri = "package:${BuildConfig.APPLICATION_ID}".toUri()
                    settingsLauncher.launch(Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, packageUri))
                } else {
                    if (toastTip) {
                        showToast(context.getString(R.string.need_permission))
                    }
                    currentOnDenied(tempFlagBox.value, permissions)
                }
            } else {
                currentOnRun(tempFlagBox.value, permissions)
            }
            tempFlagBox.value = null
        }

    return remember {
        PermissionLauncher(tempFlagBox, permissionsState, requestPermissionStartTimeState, permissionLauncher)
    }
}

@Stable
class PermissionLauncher<T>(
    private val tempFlagBox: TempFlagBox<T>,
    private val permissionsState: MutableState<Array<String>>,
    private val requestPermissionStartTimeState: MutableLongState,
    private val permissionLauncher: ActivityResultLauncher<Array<String>>,
) {

    fun launch(flag: T, permissions: Array<String>) {
        tempFlagBox.value = flag
        launch(permissions)
    }

    fun launch(permissions: Array<String>) {
        if (permissions.isEmpty()) {
            return
        }
        permissionsState.value = permissions
        requestPermissionStartTimeState.longValue = SystemClock.elapsedRealtime()
        permissionLauncher.launch(permissions)
    }
}

class TempFlagBox<T>(var value: T?)
