package com.buque.wakoo.utils.upload

import android.content.Context
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.bean.OssToken
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.api.service.SettingsApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.tencent.cos.xml.CosXmlServiceConfig
import com.tencent.cos.xml.CosXmlSimpleService
import com.tencent.cos.xml.transfer.TransferConfig
import com.tencent.cos.xml.transfer.TransferManager
import com.tencent.qcloud.core.auth.BasicLifecycleCredentialProvider
import com.tencent.qcloud.core.auth.QCloudLifecycleCredentials
import com.tencent.qcloud.core.auth.SessionQCloudCredentials
import com.tencent.qcloud.core.common.QCloudClientException
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking

object CosTransferProvider {
    private val userApiService = ApiClient.createuserApiService<SettingsApiService>()

    @Volatile
    private var defaultToken: OssToken? = null

    var tempAccessToken: String? = null

    val transferManager: TransferManager by lazy {
        buildTransferManager(WakooApplication.instance)
    }

    fun initialize() {
        appCoroutineScope.launch {
            executeApiCallExpectingData {
                userApiService.getUploadToken(tempAccessToken)
            }.onSuccess {
                defaultToken = it
            }
        }
    }

    internal suspend fun getOssToken(): OssToken =
        defaultToken ?: executeApiCallExpectingData {
            userApiService.getUploadToken(tempAccessToken)
        }.onSuccess {
            defaultToken = it
        }.getOrThrow()

    private fun buildCosXmlService(context: Context): CosXmlSimpleService {
        // 存储桶地域，请在COS控制台指定存储桶的概览页查看，详情：https://cloud.tencent.com/document/product/436/6224
        val region = defaultToken?.cosRegion ?: "ap-singapore"

        val serviceConfig =
            CosXmlServiceConfig
                .Builder()
                .setRegion(region)
                .isHttps(true) // 默认使用 Https 请求
                .builder()

        // 强烈建议使用临时密钥进行身份验证，详情：https://cloud.tencent.com/document/product/436/14048
        val credentialProvider = ServerCredentialProvider()

        return CosXmlSimpleService(context.applicationContext, serviceConfig, credentialProvider)
    }

    private fun buildTransferManager(context: Context): TransferManager {
        val cosService = buildCosXmlService(context)
        // 自定义传输配置
        val transferConfig =
            TransferConfig
                .Builder()
                .setForceSimpleUpload(true) // 暂时的文件大小适合简单上传
                // .setDivisionForUpload(2 * 1024 * 1024) // 设置大于等于 2M 的文件进行分块上传
                .build()
        return TransferManager(cosService, transferConfig)
    }

    /**
     * 用于从您的服务器获取临时密钥的提供程序。
     * **重要提示**：在实际应用中，您必须从自己的后端服务器获取这些凭证。
     */
    class ServerCredentialProvider : BasicLifecycleCredentialProvider() {
        @Throws(QCloudClientException::class)
        override fun fetchNewCredentials(): QCloudLifecycleCredentials =
            runBlocking {
                executeApiCallExpectingData {
                    userApiService.getUploadToken(tempAccessToken)
                }
            }.onSuccess {
                it.accessKeyId
            }.fold(
                {
                    defaultToken = it
                    SessionQCloudCredentials(
                        it.accessKeyId,
                        it.accessKeySecret,
                        it.securityToken,
                        it.startTimestamp,
                        it.expireTimestamp,
                    )
                },
            ) {
                throw QCloudClientException("获取token失败", it)
            }
    }
}
