package com.buque.wakoo.network.api.service

import com.buque.wakoo.bean.NotificationListResponse
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.GET
import retrofit2.http.Query

interface NotificationApiService {
    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<NotificationApiService>()
        }
    }

    @GET("api/xya/sound/v1/unread/badge")
    suspend fun getUnreadBadge(): ApiResponse<JsonObject>

    @GET("api/xya/sound/v1/notification")
    suspend fun getNotificationList(
        @Query("last_id") lastId: Int,
    ): ApiResponse<NotificationListResponse>
}
