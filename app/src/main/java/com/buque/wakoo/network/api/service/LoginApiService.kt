package com.buque.wakoo.network.api.service

import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.LoginRequest
import com.buque.wakoo.network.api.bean.LoginResponse
import com.buque.wakoo.network.api.bean.RegisterRequest
import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Query

interface LoginApiService {
    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<LoginApiService>()
        }
    }

    /**
     * 发送验证码
     */
    @POST("api/xya/user/v1/phone/identify")
    suspend fun sendVerifyCode(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 注册接口 */
    @POST("api/xya/user/v1/register")
    suspend fun register(
        @Header("Access-Token") token: String,
        @Body registerRequest: RegisterRequest,
    ): ApiResponse<JsonObject>

    /** 登录接口 */
    @POST("api/xya/user/v1/login")
    suspend fun login(
        @Body loginRequest: LoginRequest,
    ): ApiResponse<LoginResponse>

    /** 获取用户信息 */
    @GET("api/xya/user/v1/user/info")
    suspend fun getUserInfo(
        @Header("Access-Token") token: String,
        @Query("userid") userId: String,
    ): ApiResponse<UserResponse>

    //
    @POST("/api/xya/user/v1/writeoff")
    suspend fun writeOffUser(): ApiResponse<UserResponse>
}
