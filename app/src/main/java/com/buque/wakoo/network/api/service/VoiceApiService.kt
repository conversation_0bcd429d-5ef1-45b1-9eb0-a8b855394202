package com.buque.wakoo.network.api.service

import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.PublishVoiceRequest
import com.buque.wakoo.network.api.bean.VoiceFeedResponse
import com.buque.wakoo.network.api.bean.VoiceFeedResponseV2
import com.buque.wakoo.network.api.bean.VoiceListResponse
import com.buque.wakoo.network.api.bean.VoicePublishConfig
import com.buque.wakoo.network.api.bean.VoiceTagListResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface VoiceApiService {
    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<VoiceApiService>()
        }
    }

    /** 首页feed推荐 */
    @GET("api/xya/sound/v1/recommend")
    suspend fun getVoiceRecommendList(
        @Query("page_no") page: Int,
        @Query("page_size") size: Int,
    ): ApiResponse<VoiceFeedResponse>

    @GET("api/xya/sound/v2/recommend")
    suspend fun getVoiceRecommendListV2(): ApiResponse<VoiceFeedResponseV2>

    /** 首页feed关注 */
    @GET("api/xya/sound/v1/follow")
    suspend fun getVoiceFollowingList(
        @Query("last_id") lastId: Int,
    ): ApiResponse<VoiceFeedResponse>

    /** 不感兴趣 */
    @POST("api/xya/sound/v1/sound/bored")
    suspend fun markAsBored(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    @POST("api/xya/sound/v1/livehouse/bored")
    suspend fun markAsBoredRoom(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 点赞 */
    @POST("api/xya/sound/v1/sound/like")
    suspend fun likeVoice(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 收藏 */
    @POST("api/xya/sound/v1/sound/collect")
    suspend fun favoriteVoice(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 删除声音 */
    @POST("api/xya/sound/v1/sound/delete")
    suspend fun deleteVoice(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 个人中心我的发布 */
    @GET("api/xya/sound/v1/sound/user/created")
    suspend fun getVoicePublishList(
        @Query("userid") userId: String,
        @Query("last_id") lastId: Int,
    ): ApiResponse<VoiceFeedResponse>

    /** 个人中心我的喜欢 */
    @GET("api/xya/sound/v1/sound/user/liked")
    suspend fun getVoiceLikedList(
        @Query("userid") userId: String,
        @Query("last_id") lastId: Int,
    ): ApiResponse<VoiceListResponse>

    /** 个人中心我的收藏 */
    @GET("api/xya/sound/v1/sound/user/collected")
    suspend fun getVoiceFavoriteList(
        @Query("userid") userId: String,
        @Query("last_id") lastId: Int,
    ): ApiResponse<VoiceListResponse>

    /** 发布声音 */
    @POST("api/xya/sound/v1/sound/create")
    suspend fun publishVoice(
        @Body request: PublishVoiceRequest,
    ): ApiResponse<JsonObject>

    /** 获取官方标签 */
    @GET("api/xya/sound/v1/sound/tag")
    suspend fun getOfficialTags(
        @Query("tag_type") tagType: Int = 1,
    ): ApiResponse<VoiceTagListResponse>

    /** 获取声音配置 */
    @GET("api/xya/sound/v1/conf")
    suspend fun getVoiceConf(): ApiResponse<VoicePublishConfig>
}
