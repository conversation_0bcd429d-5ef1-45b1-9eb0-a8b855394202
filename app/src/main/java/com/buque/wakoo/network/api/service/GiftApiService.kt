package com.buque.wakoo.network.api.service

import com.buque.wakoo.bean.GiftInfo
import com.buque.wakoo.bean.GiftWallSummaryBean
import com.buque.wakoo.manager.NetworkManager
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface GiftApiService {
    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<GiftApiService>()
        }
    }

    /** 礼物面板接口  api/gift/v3/panel/info
     * *  Sence_type 枚举
     *      *    (0, "UNKNOWN", "未知"),
     *      *     (1, "TRIBE", "部落"),
     *      *     (2, "AUDIOROOM", "语音房"),
     *      *     (3, "RPIVATEROOM", "私密小屋"),
     *      *     (4, "RPIVATE_CHAT", "私聊"),
     *      *     (5, "MOMENT", "动态"),
     *      *     (6, "PROFILE", "个人主页"),
     *      *     (7, "CHATGROUP", "群聊"),
     *      *     (8, "AUDIO_CHAT_USER_RECOM", "语音聊天用户推荐"),
     *      *     (9, "CP_ROOM", "CP小屋"),
     *      */
    @GET("api/xya/gift/v1/gift/shelves/list")
    suspend fun getGiftShelvesList(
        @Query("scene_type") scene_type: Int? = null,
        @Query("scene_id") scene_id: String? = null,
    ): ApiResponse<GiftInfo>

    /** 礼物面板Tab标记已读接口 api/gift/v3/panel/tab/markread */
    @POST("api/xya/gift/v1/gift/shelve/read")
    suspend fun markGiftShelveRead(
        @Body body: Map<String, @JvmSuppressWildcards Any>,
    ): ApiResponse<JsonObject>

    //region 送礼接口

    /** 部落送礼接口 api/tribe/v1/gift/give
     *  "tribe_id" to tribeId,
     *  "receiver_ids" to ids,
     *  "gift_id" to giftId,
     *  "count" to count,
     *  "from_packet" to fromPacket.toString(),
     *  "greetings" to greetings,
     * */
    @POST("api/xya/group/v1/present/give/away")
    suspend fun giveGroupGift(
        @Body body: Map<String, @JvmSuppressWildcards Any>,
    ): ApiResponse<JsonObject>

    /** 语音房送礼接口 api/audioroom/v1/gift/give
     * roomId:Int 房间id
     * receiver_ids:String 接受者id
     * gift_id:String 礼物id
     * count:Int 礼物数量
     * from_packet:Bool 是否是背包礼物
     * greetings:String 祝福语 */
    @POST("api/xya/livehouse/v1/present/give/away")
    suspend fun giveChatRoomGift(
        @Body body: Map<String, @JvmSuppressWildcards Any>,
    ): ApiResponse<JsonObject>

    /** 私聊送礼接口 api/privatechat/v1/privatechat/gift/give
     *  "target_user_id" to userId,
     *  "gift_id" to giftId,
     *  "count" to count,
     *  "from_packet" to fromPacket.toString(),
     *  "greetings" to greetings,
     *  "is_intimate_gift" to isIntimate.toString(),
     *
     * */
    @POST("api/xya/c2c/v1/present/give/away")
    suspend fun giveC2CGift(
        @Body body: Map<String, @JvmSuppressWildcards Any>,
    ): ApiResponse<JsonObject>

    //endregion

    @GET("api/xya/gift/v1/gift/wall/structure")
    suspend fun getWallSummaryInfo(
        @Query("userid") userId: Int,
        @Query("scene_type") scene_type: Int? = null,
        @Query("scene_id") scene_id: String? = null,
        @Query("is_using_vpn") usingVpn: String = (NetworkManager.isUsingVpnOrProxy()).toString(),
    ): ApiResponse<GiftWallSummaryBean>

    @GET("api/xya/gift/v1/gift/showcase/list")
    suspend fun getWallTabInfo(
        @Query("userid") userId: Int,
        @Query("t") tabId: Int,
        @Query("scene_type") scene_type: Int? = null,
        @Query("scene_id") scene_id: String? = null,
        @Query("is_using_vpn") usingVpn: String = (NetworkManager.isUsingVpnOrProxy()).toString(),
    ): ApiResponse<JsonObject>
}
