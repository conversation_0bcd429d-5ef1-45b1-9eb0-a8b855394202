package com.buque.wakoo.network.api.bean

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * POST /api/xya/user/v1/register
 * @param nickname 昵称
 * @param gender 性别
 * @param birthday 生日
 * @param avatar_url 头像
 * @param invite_code 邀请码
 * @param ism_device_id 数美设备ID
 * @param is_using_vpn 是否使用VPN
 */
@Serializable
data class RegisterRequest(
    val nickname: String? = null,
    val gender: Int? = null,
    val birthday: String? = null,
    val avatar_url: String? = null,
    val invite_code: String? = null,
    val ism_device_id: String = "",
    val is_using_vpn: Boolean = false,
)

/**
 * POST /api/xya/user/v1/login
 * @param account_type 账号类型
 * (301, "WAKOO_PHONE", "Wakoo手机号登录")
 * (302, "WAKOO_GOOGLE", "Wakoo Google登录")
 * (307, "WAKOO_FACEBOOK2", "Wakoo FB登录")
 * (308, "WAKOO_LINE", "Wakoo Line登录")
 * (305, "WAKOO_FAST_LOGIN_ANDROID", "Wakoo Android快速登录")
 * @param account_id 同 UCOO user_token
 * @param ism_device_id 数美设备ID
 * @param is_using_vpn 是否使用VPN
 * @param is_recover 是否恢复登录
 */
@Serializable
data class LoginRequest(
    val account_type: Int,
    val account_id: String,
    val ism_device_id: String,
    val is_using_vpn: Boolean,
    val is_recover: Boolean,
    val phone_area_code: String? = null,
    val verify_code: String? = null,
)

/*
{
    "access_token": "b3ce6890e99c4539b74d46d0af49f359",
    "refresh_token": "bf2d9a2f1624448e9256b574d15e83f1",
    "access_token_expire_seconds": 7200,
    "refresh_token_expire_seconds": 14400,
    "need_register": true,
    "public_id": "105813",
    "userid": 4393,
    "im_token": "eJw1zMsKgkAUgOFXkVmHnRk9jQZtXISJi8Cgyy6ZKU5iDOOFNHr30nT7f-C-2SHN3FZbtnaYcIEtnLGQ0s*abvQH3wu9WSpVXI0h9etcCg7AfT*YTL8MWT0IIgoAmHpN5VglguBeiDi-6D7s43wZ40luL11v2uycVGndPJqoV8nKhntb7cosOvZSFl2wYZ8v5asyLw__"
}
*/
@Serializable
data class LoginResponse(
    @SerialName("access_token")
    val accessToken: String = "", // b3ce6890e99c4539b74d46d0af49f359
    @SerialName("access_token_expire_seconds")
    val accessTokenExpireSeconds: Int = 0, // 7200
    @SerialName("im_token")
    val imToken: String = "",
    @SerialName("need_register")
    val needRegister: Boolean = true, // true
    @SerialName("public_id")
    val publicId: String = "", // 105813
    @SerialName("refresh_token")
    val refreshToken: String = "", // bf2d9a2f1624448e9256b574d15e83f1
    @SerialName("refresh_token_expire_seconds")
    val refreshTokenExpireSeconds: Int = 0, // 14400
    @SerialName("userid")
    val userid: String = "", // 4393
)
