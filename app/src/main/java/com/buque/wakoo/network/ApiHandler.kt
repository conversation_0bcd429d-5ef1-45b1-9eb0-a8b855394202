package com.buque.wakoo.network

import com.buque.wakoo.ext.showToast
import com.buque.wakoo.utils.LogUtils
import kotlinx.coroutines.CancellationException
import kotlinx.serialization.SerializationException

/**
 * 通用的 API 请求执行器。
 * 该方法旨在封装 API 请求的执行逻辑、处理成功/失败响应，以及统一捕获和处理常见的异常。
 *
 * @param T 预期从 API 响应中解析出的数据类型。
 * @param expectNonNullData `Boolean` 类型，默认为 `false`。
 * - 如果设置为 `true`：当 API 请求成功（即 `ApiResponse.isSuccessful()` 为 `true`）时，
 * 会强制认为 `ApiResponse.data` 字段一定不为空。如果此时 `ApiResponse.data` 实际为空，
 * 将抛出 `NullPointerException`（进而被捕获为 `Result.failure`）。
 * - 如果设置为 `false`：允许 `ApiResponse.data` 为空，并将其作为 `Result.success(null)` 返回。
 * @param apiBlock 一个 suspend lambda 函数，代表实际的 API 请求执行体。
 * 它通常是调用 Retrofit Service 接口中定义的 suspend 方法。
 * 该 lambda 预期返回一个 `ApiResponse<T>` 对象。
 * @return `Result<T?>` 类型。
 * - 如果 API 请求在业务层面上成功（`ApiResponse.isSuccessful()` 为 `true`）：
 * 返回 `Result.success(ApiResponse.data)`。如果 `expectNonNullData` 为 `true`
 * 且 `data` 为 `null`，将导致 `Result.failure`。
 * - 如果 API 请求在业务层面上失败（`ApiResponse.isSuccessful()` 为 `false`）：
 * 返回 `Result.failure(BusinessException)`，其中包含服务器返回的错误码和消息。
 * - 如果在请求执行过程中发生除了 `CancellationException` 之外的任何运行时异常
 * （如网络连接失败、数据解析异常等）：返回 `Result.failure(Exception)`。
 * - `CancellationException`（协程取消异常）会被重新抛出，以确保协程的取消机制正常工作，
 * 不应被视为业务失败或普通异常。
 */
suspend fun <T> executeApiCall(
    showToastIfError: Boolean = true,
    toastWhiteList: IntArray? = null,
    expectNonNullData: Boolean = false,
    apiBlock: suspend () -> ApiResponse<T>,
): Result<T?> =
    try {
        val response = apiBlock()
        if (response.isSuccessful()) {
            Result.success(
                if (expectNonNullData) {
                    response.requireData
                } else {
                    response.data
                },
            )
        } else {
            // API 响应表示业务失败（如服务器返回非 200 的 code）
            Result.failure(BusinessException(response.code, response.message))
        }
    } catch (e: CancellationException) {
        // 协程取消异常，必须重新抛出，以确保协程的取消传播机制正常工作。
        throw e
    } catch (e: SerializationException) {
        LogUtils.eTag("ApiClient", "数据解析异常：$e", showCallerInfo = false, showThreadInfo = false)
        // 捕获其他所有非 CancellationException 的异常，例如网络连接问题、JSON 解析异常等
        Result.failure(DataParseException(originalException = e))
    } catch (e: Exception) {
        // 捕获其他所有非 CancellationException 的异常，例如网络连接问题、JSON 解析异常等
        Result.failure(e)
    }.onFailure {
        if (showToastIfError) {
            if (toastWhiteList?.isNotEmpty() == true && it is BusinessException && it.businessCode in toastWhiteList) {
                return@onFailure
            }
            showToast(it.message.orEmpty())
        }
    }

/**
 * 用于执行 API 请求的便捷方法，明确要求 API 成功响应中的数据字段 (`data`) 必须不为空。
 * 此方法会调用 [executeApiCall] 方法，并强制设置 `expectNonNullData` 为 `true`。
 *
 * 如果 API 请求成功但 `ApiResponse.data` 为 `null`，该方法将抛出 `NullPointerException`，
 * 进而导致返回 `Result.failure`。
 *
 * @param T 预期从 API 响应中解析出的非空数据类型。
 * @param apiBlock 一个 suspend lambda 函数，代表实际的 API 请求执行体。
 * 它应返回一个 `ApiResponse<T>` 对象。
 * @return `Result<T>` 类型。
 * - 如果 API 请求成功且 `ApiResponse.data` 确实不为空，返回 `Result.success(data)`。
 * - 其他请求失败或异常情况（例如网络问题、解析错误，或 `ApiResponse.data` 为空而抛出 `NullPointerException`）
 * 将导致返回 `Result.failure`。
 */
suspend fun <T> executeApiCallExpectingData(
    showToastIfError: Boolean = true,
    toastWhiteList: IntArray? = null,
    apiBlock: suspend () -> ApiResponse<T>,
): Result<T> {
    // 这里调用 executeApiCall，并明确指定 expectNonNullData = true。
    // 由于 executeApiCall 内部对 data!! 的处理，当 data 为 null 时会抛出异常，
    // 异常会被捕获并封装为 Result.failure，因此 Result.success(null) 的情况不会发生。
    // 所以，这里的 'as Result<T>' 强制类型转换在逻辑上是安全的。
    return executeApiCall(
        showToastIfError = showToastIfError,
        toastWhiteList = toastWhiteList,
        expectNonNullData = true,
        apiBlock = apiBlock,
    ) as Result<T>
}

/**
 * 如果 Result 是失败状态且其异常是 [BusinessException] 类型，则执行给定的 [action]。
 *
 * @param action 接收 [BusinessException] 作为参数的 lambda 表达式。
 * @return 返回原始的 [Result] 对象，允许链式调用。
 */
inline fun <T> Result<T>.onBusinessFailure(action: (exception: BusinessException) -> Unit): Result<T> {
    exceptionOrNull()?.let { throwable ->
        if (throwable is BusinessException) {
            action(throwable)
        }
    }
    return this
}

/**
 * 如果 Result 是失败状态且其异常是 [NetworkException] 类型，则执行给定的 [action]。
 *
 * @param action 接收 [NetworkException] 作为参数的 lambda 表达式。
 * @return 返回原始的 [Result] 对象，允许链式调用。
 */
inline fun <T> Result<T>.onNetworkFailure(action: (exception: NetworkException) -> Unit): Result<T> {
    exceptionOrNull()?.let { throwable ->
        if (throwable is NetworkException) {
            action(throwable)
        }
    }
    return this
}

/**
 * 如果 Result 是失败状态且其异常是 [HttpException] 类型（包括其子类如 [UnauthorizedException]），则执行给定的 [action]。
 *
 * @param action 接收 [HttpException] 作为参数的 lambda 表达式。
 * @return 返回原始的 [Result] 对象，允许链式调用。
 */
inline fun <T> Result<T>.onHttpFailure(action: (exception: HttpException) -> Unit): Result<T> {
    exceptionOrNull()?.let { throwable ->
        if (throwable is HttpException) {
            action(throwable)
        }
    }
    return this
}
