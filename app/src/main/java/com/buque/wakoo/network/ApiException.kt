package com.buque.wakoo.network

import java.io.IOException

/**
 * 基础自定义异常类
 */
sealed class ApiException(
    message: String?,
    cause: Throwable? = null,
) : IOException(message, cause)

/**
 * 网络连接相关异常 (对应 OkHttp 直接抛出的 IOException)
 * @param originalException 原始的 IOException
 */
class NetworkException(
    message: String = "网络连接失败，请检查您的网络设置",
    val originalException: IOException,
) : ApiException(message, originalException)

/**
 * HTTP 协议层面的异常 (非2xx状态码)
 * @param httpStatusCode HTTP 状态码
 * @param errorMessage 服务器返回的原始错误信息（可能来自响应体）
 * @param errorBody 服务器返回的原始错误体字符串
 */
open class HttpException(
    val httpStatusCode: Int,
    val errorMessage: String?,
    val errorBody: String? = null,
) : ApiException("HTTP $httpStatusCode: ${errorMessage ?: "未知HTTP错误"}")

class UnauthorizedException(
    // HTTP 401
    errorMessage: String? = "访问未授权，请检查您的凭据或重新登录",
    errorBody: String? = null,
    val isTokenExpiredOrInvalid: Boolean = false, // 标志是否是由于token过期/无效导致的401
) : HttpException(401, errorMessage, errorBody)

class ForbiddenException(
    // HTTP 403
    errorMessage: String? = "禁止访问，您没有权限执行此操作",
    errorBody: String? = null,
) : HttpException(403, errorMessage, errorBody)

class NotFoundException(
    // HTTP 404
    errorMessage: String? = "请求的资源未找到",
    errorBody: String? = null,
) : HttpException(404, errorMessage, errorBody)

class ServerException(
    // HTTP 5xx
    httpStatusCode: Int,
    errorMessage: String? = "服务器内部错误，请稍后重试",
    errorBody: String? = null,
) : HttpException(httpStatusCode, errorMessage, errorBody)

/**
 * 业务逻辑异常 (HTTP 状态码通常是 200，但业务code表示失败)
 * @param businessCode 业务错误码
 * @param businessMessage 业务错误信息
 * @param responseData 原始的业务响应数据（可能包含更多错误细节）
 */
class BusinessException(
    val businessCode: Int,
    val businessMessage: String?,
    val responseData: Any? = null, // 可以是解析后的错误结构体
) : ApiException(businessMessage ?: "未知业务错误")

/**
 * 数据解析异常 (HTTP 200，但响应体无法解析为目标数据结构)
 * @param originalException 原始的解析异常 (如 JsonDecodingException)
 */
class DataParseException(
    message: String = "数据解析异常",
    val originalException: Throwable? = null,
) : ApiException(message, originalException)

class UnknownException(
    message: String?,
    cause: Throwable? = null,
) : ApiException(message, cause)

object MissingApiResponseDataException : ApiException("API 响应数据为空，但此处调用要求数据非空", null) {
    private fun readResolve(): Any = MissingApiResponseDataException
}
