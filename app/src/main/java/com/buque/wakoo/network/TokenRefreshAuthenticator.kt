package com.buque.wakoo.network

import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.utils.LogUtils
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import okhttp3.Authenticator
import okhttp3.Request
import okhttp3.Response
import okhttp3.Route
import java.net.HttpURLConnection

class TokenRefreshAuthenticator : Authenticator {
    companion object {
        private val refreshTokenMutex = Mutex() // 确保只有一个线程/协程在刷新token
    }

    override fun authenticate(
        route: Route?,
        response: Response,
    ): Request? {
        // 如果响应不是401，或者已经尝试过刷新（避免死循环），则不处理
        if (response.code != HttpURLConnection.HTTP_UNAUTHORIZED || response.priorResponse != null) {
            return null // 返回null表示放弃认证，原始请求失败
        }

        LogUtils.d("TokenRefreshAuthenticator: 收到401，尝试刷新Token")

        // 使用 runBlocking + Mutex 来同步刷新过程
        // 注意: 这里的 runBlocking 同样有潜在的阻塞问题
        val newAccessToken: String? =
            runBlocking {
                refreshTokenMutex.withLock {
                    val currentTokenInManager = AccountManager.getAccessToken()
                    val tokenUsedByFailedRequest = response.request.header("Access-Token")

                    // 如果当前AccountManager中的token与失败请求的token不同，
                    // 说明token可能已经被其他请求刷新了，直接用AccountManager中的token重试
                    if (currentTokenInManager != null && currentTokenInManager != tokenUsedByFailedRequest) {
                        LogUtils.d("TokenRefreshAuthenticator: Token似乎已被其他请求刷新，将使用新的Token重试")
                        return@withLock currentTokenInManager
                    }

                    // 执行刷新Token的操作
                    val refreshToken = AccountManager.getRefreshToken()
                    if (refreshToken == null) {
                        LogUtils.e("TokenRefreshAuthenticator: RefreshToken为空，无法刷新")
                        AccountManager.logout()
                        return@withLock null // 无法刷新
                    }

                    try {
                        // 这个刷新调用应该使用一个不带此 Authenticator 的 "干净" OkHttpClient 实例
                        // 或者确保它不会再次触发401。
                        val refreshCallResponse = ApiClient.refreshTokenApiCall(refreshToken) // 这是伪代码

                        if (refreshCallResponse.isSuccessful && refreshCallResponse.body() != null) {
                            val newTokens = refreshCallResponse.body()!!
                            AccountManager.setApiTokens(newTokens.accessToken, newTokens.refreshToken)
                            LogUtils.d("TokenRefreshAuthenticator: Token刷新成功")
                            newTokens.accessToken
                        } else {
                            LogUtils.e(
                                "TokenRefreshAuthenticator: Token刷新失败 - ${refreshCallResponse.code()} ${refreshCallResponse.message()}",
                            )
                            AccountManager.logout() // 刷新失败，清除token
                            null
                        }
                    } catch (e: Exception) {
                        LogUtils.e("TokenRefreshAuthenticator: Token刷新过程中发生异常 - ${e.message}")
                        AccountManager.logout()
                        null
                    }
                }
            }

        return if (newAccessToken != null) {
            LogUtils.d("TokenRefreshAuthenticator: 使用新的AccessToken构建重试请求")
            // 使用新的token构建新的请求
            response.request
                .newBuilder()
                .header("Authorization", "Bearer $newAccessToken")
                .build()
        } else {
            LogUtils.e("TokenRefreshAuthenticator: Token刷新彻底失败，放弃认证")
            null // 刷新失败，放弃认证，原始请求将失败
        }
    }
} 
