package com.buque.wakoo.network.api.bean

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class AppGlobalSettingsResponse(
    @SerialName("update_title")
    val updateTitle: String = "",
    @SerialName("update_describe")
    val updateDescribes: List<String> = emptyList(),
    @SerialName("is_force_update")
    val forceUpdate: Boolean = false,
    @SerialName("jump_link")
    val jumpLink: String = "",
    @SerialName("official_website_link")
    val officialWebsite: String = "",
    @SerialName("android_download_url")
    val androidDownloadUrl: String = "",
    @SerialName("android_newest_version")
    val androidNewestVersion: String = "",
    @SerialName("customer_service_ids")
    val customerServiceIds: List<Int> = emptyList(),
    @SerialName("has_new_version")
    val hasNewVersion: Boolean = false,
    @SerialName("charge_cs_id")
    val chargeCsId: Int = -1,
    @SerialName("default_cs_id")
    val defaultCsId: Int = -1,
    @SerialName("public_service_userids")
    val publicServiceUids: List<Int> = emptyList(),
    @SerialName("tribe_cs_id")
    val tribeCsId: Int = -1,
    @SerialName("love_cs_id")
    val loveCsId: Int = -1,
    @SerialName("charm_value_per_member")
    val charmValue: Int = 1000,
    @SerialName("private_room_time_gift_id")
    val privateRoomTimeGiftId: Int = -1,
    @SerialName("official_userids")
    val officialUserIds: List<Int> = emptyList(),
    @SerialName("android_feature")
    val androidFeature: AndroidFeature = AndroidFeature(false),
    @SerialName("video_coin_cost_per_minute")
    val videoCostPerMinute: Int = 0,
    @SerialName("pendants_config")
    val pendantsConfig: List<PendantConfig> = emptyList(),
    @SerialName("tencent_im_enable") val timWriteEnable: Boolean? = null, // 腾讯写
    @SerialName("tencent_im_client_enable") val readFromTim: Boolean? = null, // im数据是否从腾讯读
)

@Serializable
data class PendantConfig(
    val name: String,
    @SerialName("visible_pos")
    val visiblePos: List<Int>,
)

@Serializable
data class AndroidFeature(
    @SerialName("micsubcribe")
    val micSubscribe: Boolean = false,
    @SerialName("micsubcribe_v2")
    val micsubcribeV2: Boolean = false,
    @SerialName("micsubcribe_v3")
    val micsubcribeV3: Boolean = false,
    @SerialName("videochat")
    val videochat: Boolean = false,
    @SerialName("conversation_page_size")
    val conversationExtraPageSize: Int = 5,
)
