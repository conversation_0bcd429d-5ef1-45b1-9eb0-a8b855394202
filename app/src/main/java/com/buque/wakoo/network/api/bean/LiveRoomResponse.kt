package com.buque.wakoo.network.api.bean

import com.buque.wakoo.bean.BasicUser
import com.buque.wakoo.utils.DateTimeUtils
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject

@Serializable
data class RoomInfoResponse(
    @SerialName("admins")
    val admins: List<BasicUser> = listOf(),
    @SerialName("audience_cnt")
    val audienceCnt: Int = 0, // 包含麦上和麦下的用户户
    @SerialName("audiences")
    val audiences: List<BasicUser> = listOf(),
    @SerialName("blind_box_filled")
    val blindBoxFilled: Boolean = false, // true
    @SerialName("blind_box_filled_note")
    val blindBoxFilledNote: String = "", // 盲盒存储已满10个请尽快送出
    @SerialName("blind_box_task_duration")
    val blindBoxTaskDuration: Int = 0, // 0
    @SerialName("desc")
    val desc: String? = null, // null
    @SerialName("desc_link")
    val descLink: String = "", // https://api.test.ucoofun.com/h5/xya/connection/v1/social/couples/blindbox/introduce
    @SerialName("happen_timestamp")
    val updateTimestamp: Long = DateTimeUtils.currentTimeMillis(), // 1750837057020
    @SerialName("i_am_admin")
    val iAmAdmin: Boolean = false, // false
    @SerialName("i_am_owner")
    val iAmOwner: Boolean = false, // false
    @SerialName("latest_enter_sample_users")
    val latestEnterSampleUsers: List<BasicUser?> = listOf(),
    @SerialName("notice")
    val notice: String? = null, // null
    @SerialName("occupy_mic_mode")
    val occupyMicMode: Int = 0, // 1
    @SerialName("mic_apply_cnt")
    val requestMicCount: Int = 0,
    @SerialName("owner")
    val owner: UserResponse,
    @SerialName("paid_items")
    val paidItems: List<PaidItem> = listOf(),
    @SerialName("pk_info")
    val pkInfo: JsonObject? = null, // null
    @SerialName("public_id")
    val publicId: String = "", // 100398
    @SerialName("room_background")
    val roomBackground: String = "", // https://media.ucoofun.com/audioroom%2Fwakoo_audioroom_background.webp
    @SerialName("room_id")
    val roomId: String = "0", // 375
    @SerialName("room_locked")
    val roomLocked: Boolean = false, // false
    @SerialName("room_mode")
    val roomMode: Int = 0, // 4
    @SerialName("seats")
    val seats: List<Seat> = listOf(),
    @SerialName("show_audience_task_popup")
    val showAudienceTaskPopup: Boolean = false, // false
    @SerialName("show_blind_box_task")
    val showBlindBoxTask: Boolean = false, // false
    @SerialName("show_cross_pk")
    val showCrossPk: Boolean = false, // false
    @SerialName("sud_game_info")
    val sudGameInfo: JsonObject? = null,
    @SerialName("tags")
    val tags: List<VoiceTag>? = null,
    @SerialName("text_only")
    val textOnly: Boolean = false, // false
    @SerialName("title")
    val title: String = "", // 1111
    @SerialName("wedding_info")
    val weddingInfo: WeddingInfo = WeddingInfo(),
)

@Serializable
data class PaidItem(
    @SerialName("id")
    val id: Int = 0, // 1
    @SerialName("name")
    val name: String = "", // 付费上麦
    @SerialName("price")
    val price: Int = 0, // 20
)

@Serializable
data class Seat(
    @SerialName("has_user")
    val hasUser: Boolean = false, // false
    @SerialName("heart_value") val heartValue: Int? = null,
    val user: UserResponse? = null,
)

// @Serializable
// data class SudGameInfo(
//    @SerialName("game_list")
//    val gameList: List<Any?> = listOf(),
//    @SerialName("ongoing_mg")
//    val ongoingMg: Any? = Any(), // null
// )

@Serializable
class WeddingInfo

@Serializable
data class Medal(
    @SerialName("height")
    val height: Int = 0, // 24
    @SerialName("icon")
    val icon: String = "", // https://s.ucoofun.com/aabLen
    @SerialName("width")
    val width: Int = 0, // 72
)

@Serializable
data class RoomTokenResponse(
    @SerialName("room_id")
    val roomId: String = "0", // 375
    @SerialName("rtc_channel_name")
    val rtcChannelName: String = "", // audioroom_375_rtc_trtc
    @SerialName("rtc_channel_type")
    val rtcChannelType: Int = 0, // 3
    @SerialName("rtc_config")
    val rtcConfig: RtcConfig = RtcConfig(),
    @SerialName("rtc_token")
    val rtcToken: String = "",
    @SerialName("userid")
    val userid: String = "0", // 4466
)

@Serializable
data class RtcConfig(
    @SerialName("audio_3a")
    val audio3a: Audio3a = Audio3a(),
    @SerialName("quality")
    val quality: Int = 0, // 3
)

@Serializable
data class Audio3a(
    @SerialName("aec_enabled")
    val aecEnabled: Boolean = false, // true
    @SerialName("agc_enabled")
    val agcEnabled: Boolean = false, // false
    @SerialName("ans_enabled")
    val ansEnabled: Boolean = false, // true
)

@Serializable
data class VoiceRoomCreateReq(
    val title: String,
    val desc: String,
    val tag_ids: String,
)

@Serializable
data class VoiceRoomCreateResponse(
    @SerialName("room_id")
    val roomId: String,
    @SerialName("public_id")
    val publicId: String,
)

@Serializable
data class VoiceRoomTagListResponse(
    @SerialName("tags")
    val tags: List<VoiceTag>,
)
