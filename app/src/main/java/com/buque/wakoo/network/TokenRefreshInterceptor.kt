package com.buque.wakoo.network

import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.utils.LogUtils
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import org.json.JSONObject

class TokenRefreshInterceptor : Interceptor {
    companion object {
        private val refreshTokenMutex = Mutex() // 确保只有一个线程/协程在刷新token
        private const val TOKEN_EXPIRED_STATUS = -3 // 定义token过期的status值
        private const val REFRESH_TOKEN_EXPIRED_STATUS = -4 // 定义refresh_token过期的status值
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val response = chain.proceed(originalRequest)

        // 只处理HTTP 200的响应
        if (response.code == 200) {
            val responseBody = response.peekBody(Long.MAX_VALUE)
            val responseString = responseBody.string()

            try {
                val jsonObject = JSONObject(responseString)
                val status = jsonObject.optInt("status")

                // 检查是否是token过期状态
                if (status == TOKEN_EXPIRED_STATUS) {
                    LogUtils.d("TokenRefreshInterceptor: 检测到Token过期（status=$TOKEN_EXPIRED_STATUS）")

                    // 关闭原始响应
                    response.close()

                    // 刷新token并重试请求
                    return handleTokenExpired(chain, originalRequest)
                }
            } catch (e: Exception) {
                LogUtils.e("TokenRefreshInterceptor: 解析响应发生异常 - ${e.message}")
            }
        }

        return response
    }

    private fun handleTokenExpired(
        chain: Interceptor.Chain,
        originalRequest: Request,
    ): Response {
        // 使用 runBlocking + Mutex 来同步刷新过程
        val newAccessToken: String? =
            runBlocking {
                refreshTokenMutex.withLock {
                    val currentTokenInManager = AccountManager.getAccessToken()
                    val tokenUsedByFailedRequest = originalRequest.header("Access-Token")

                    // 如果当前AccountManager中的token与失败请求的token不同，
                    // 说明token可能已经被其他请求刷新了，直接用AccountManager中的token重试
                    if (currentTokenInManager != null && currentTokenInManager != tokenUsedByFailedRequest) {
                        LogUtils.d("TokenRefreshInterceptor: Token似乎已被其他请求刷新，将使用新的Token重试")
                        return@withLock currentTokenInManager
                    }

                    // 执行刷新Token的操作
                    val refreshToken = AccountManager.getRefreshToken()
                    if (refreshToken == null) {
                        LogUtils.e("TokenRefreshInterceptor: RefreshToken为空，无法刷新")
                        AccountManager.logout()
                        return@withLock null // 无法刷新
                    }

                    try {
                        // 这个刷新调用应该使用一个不带此拦截器的 "干净" OkHttpClient 实例
                        val refreshCallResponse = ApiClient.refreshTokenApiCall(refreshToken)

                        if (refreshCallResponse.isSuccessful && refreshCallResponse.body() != null) {
                            val newTokens = refreshCallResponse.body()!!
                            AccountManager.setApiTokens(newTokens.accessToken, newTokens.refreshToken)
                            LogUtils.d("TokenRefreshInterceptor: Token刷新成功")
                            newTokens.accessToken
                        } else {
                            LogUtils.e(
                                "TokenRefreshInterceptor: Token刷新失败 - ${refreshCallResponse.code()} ${refreshCallResponse.message()}",
                            )
                            AccountManager.logout() // 刷新失败，清除token
                            null
                        }
                    } catch (e: Exception) {
                        LogUtils.e("TokenRefreshInterceptor: Token刷新过程中发生异常 - ${e.message}")
                        AccountManager.logout()
                        null
                    }
                }
            }

        return if (newAccessToken != null) {
            LogUtils.d("TokenRefreshInterceptor: 使用新的AccessToken构建重试请求")
            // 使用新的token构建新的请求
            val newRequest =
                originalRequest
                    .newBuilder()
                    .header("Access-Token", newAccessToken)
                    .build()

            // 使用新请求重试
            chain.proceed(newRequest)
        } else {
            LogUtils.e("TokenRefreshInterceptor: Token刷新彻底失败，返回原始响应")
            // 刷新失败，构建一个新的响应
            chain.proceed(originalRequest)
        }
    }
}
