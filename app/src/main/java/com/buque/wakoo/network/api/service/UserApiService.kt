package com.buque.wakoo.network.api.service

import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.UserListResponse
import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface UserApiService {
    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<UserApiService>()
        }
    }

    /** 更新用户信息 */
    @POST("api/xya/user/v1/info/set")
    suspend fun updateUserInfo(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 获取用户信息 */
    @GET("api/xya/user/v1/user/info")
    suspend fun getUserInfo(
        @Query("userid") userId: String,
        @Query("scene_type") sceneType: Int? = null,
        @Query("scene_id") sceneId: Int? = null,
        @Query("is_using_app") is_using_app: Int? = null,
    ): ApiResponse<UserResponse>

    /** 获取粉丝信息 */
    @GET("api/xya/user/v1/follower/list")
    suspend fun getUserFollowerList(
        @Query("userid") userId: String,
        @Query("last_relation_id") lastId: Int,
    ): ApiResponse<UserListResponse>

    /** 获取关注信息 */
    @GET("api/xya/user/v1/followee/list")
    suspend fun getUserFollowingList(
        @Query("userid") userId: String,
        @Query("last_relation_id") lastId: Int,
    ): ApiResponse<UserListResponse>

    /** 关注用户 */
    @POST("api/xya/user/v1/follow")
    suspend fun followUser(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 拉黑接口 */
    @POST("api/xya/user/v1/user/black")
    suspend fun blackUser(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    /** 拉黑列表 */
    @GET("api/xya/user/v1/black/list")
    suspend fun getBlackList(
        @Query("userid") userId: String,
        @Query("last_relation_id") lastId: Int,
    ): ApiResponse<UserListResponse>
}
