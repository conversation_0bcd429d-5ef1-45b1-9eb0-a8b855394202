package com.buque.wakoo.network.api.service

import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.BillRecord
import com.buque.wakoo.network.api.bean.ChargeDataEntity
import com.buque.wakoo.network.api.bean.VipInfo
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface WalletApi {
    /**
     * 获取商品列表
     */
    @GET("/api/xya/moneybag/v1/diamond/buy/settings")
    suspend fun fetchRechargeList(): ApiResponse<ChargeDataEntity>

    /**
     * 创建订单
     */
    @POST("api/xya/moneybag/v1/transaction/create")
    suspend fun createOrder(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /**
     * 订单详情
     */
    @GET("api/xya/moneybag/v1/transaction/detail")
    suspend fun orderDetails(
        @Query("order_no") orderNo: String,
    ): ApiResponse<JsonObject>

    /**
     * 订单拉起面板
     */
    @POST("api/xya/moneybag/v1/transaction/panel")
    suspend fun recordBillingEvent(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /**
     * 订单完成/确认
     */
    @POST("api/xya/moneybag/v1/transaction/confirm")
    suspend fun orderCompleted(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /**
     * 取消订单
     */
    @POST("api/xya/moneybag/v1/transaction/revoke")
    suspend fun orderCancel(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    // 会员GOODS
    @GET("api/xya/member/v1/member/options ")
    suspend fun getActivateVipConfig(): ApiResponse<VipInfo>

    /**
     * 获取消费记录
     */
    @GET("api/xya/moneybag/v1/diamond/change/history")
    suspend fun getBillList(
        @Query("last_id") lastId: Int? = null,
    ): ApiResponse<BillRecord>

    @POST("api/xya/moneybag/v1/transaction/report-issue")
    suspend fun reportBillingError(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    /**
     * ## 领取金币
     * bonus_index
     */
    @POST("api/ucmember/v1/newmember/coinbonus/retrieve")
    suspend fun getGold(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>
}
