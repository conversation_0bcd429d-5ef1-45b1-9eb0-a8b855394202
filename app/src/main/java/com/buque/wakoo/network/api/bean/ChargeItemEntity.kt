package com.buque.wakoo.network.api.bean


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ChargeItemEntity(
    @SerialName("currency_mark")
    val currencyMark: String = "",
    @SerialName("currency_number")
    val currencyNumber: String = "",
    @SerialName("currency_unit")
    val currencyUnit: String = "",
    @SerialName("dollar")
    val dollar: String = "",
    @SerialName("product_id")
    val productId: String = "",
    @SerialName("ucoin")
    val ucoin: Int = 0
)

@Serializable
data class ChargeDataEntity(
    val balance: Int = 0,
    @SerialName("charge_items")
    val chargeItems: List<ChargeItemEntity>
)