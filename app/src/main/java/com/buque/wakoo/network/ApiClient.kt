package com.buque.wakoo.network

import com.buque.wakoo.app.AppJson
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.utils.LogUtils
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.ResponseBody.Companion.toResponseBody
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.kotlinx.serialization.asConverterFactory
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST
import java.util.concurrent.TimeUnit

object ApiClient {
    // 配置 Kotlinx Serialization 的 Json 解析器
    private val jsonParser = AppJson

    private val loggingInterceptor by lazy {
        fun decodeUnicode(unicodeString: String): String {
            val pattern = "\\\\u([0-9a-fA-F]{4})".toRegex()
            return pattern.replace(unicodeString) { matchResult ->
                val hex = matchResult.groupValues[1]
                Character.toChars(hex.toInt(16)).joinToString("")
            }
        }

        // 1. 日志拦截器 (建议放在最前面或最后面，看你想看原始请求还是处理后请求)
        val loggingInterceptor =
            HttpLoggingInterceptor {
                LogUtils.jsonTag(
                    tag = "ApiClient",
                    json =
                        if (it.contains("\"msg\": ")) { // 后端返回的是Unicode编码
                            decodeUnicode(it)
                        } else {
                            it
                        },
                    showThreadInfo = false,
                    showCallerInfo = false,
                )
            }
        loggingInterceptor.level = HttpLoggingInterceptor.Level.BODY // 打印请求体和响应体
        loggingInterceptor
    }

    // 配置 OkHttpClient
    private val okHttpClient: OkHttpClient by lazy {
        // 使用 lazy 延迟初始化
        val builder =
            OkHttpClient
                .Builder()
                .connectTimeout(30, TimeUnit.SECONDS) // 连接超时时间
                .readTimeout(30, TimeUnit.SECONDS) // 读取超时时间
                .writeTimeout(30, TimeUnit.SECONDS) // 写入超时时间

        // 2. 统一错误处理拦截器
        builder.addInterceptor(ErrorHandlingInterceptor(jsonParser))

        // 3. 认证拦截器 (添加Token)
        builder.addInterceptor(AuthInterceptor())

        // 4. 签名拦截器 (在添加完所有业务参数和Token后进行签名)
        builder.addInterceptor(
            SignatureInterceptor(
                accessKey = EnvironmentManager.current.apiSignAccessKey,
                secretKey = EnvironmentManager.current.apiSignSecretKey,
                jsonParser = jsonParser,
            ),
        )

        // 5. Token刷新拦截器 (用于处理status=-3的token过期情况)
        builder.addInterceptor(TokenRefreshInterceptor())

        if (EnvironmentManager.current.enableLog) {
            builder.addInterceptor(loggingInterceptor)
        }

        builder.build()
    }

    // 配置 Retrofit
    val retrofit: Retrofit by lazy {
        // 使用 lazy 延迟初始化
        val contentType = "application/json".toMediaType() // 指定 Content-Type
        Retrofit
            .Builder()
            .baseUrl(EnvironmentManager.current.apiUrl)
            .client(okHttpClient) // 设置自定义的 OkHttpClient
            .addConverterFactory(jsonParser.asConverterFactory(contentType)) // 添加 Kotlinx Serialization 转换器
            .build()
    }

    inline fun <reified T> createuserApiService(): T = retrofit.create(T::class.java)

    private val refreshRetrofitAdapter: RefreshuserApiService by lazy {
        Retrofit
            .Builder()
            .baseUrl(EnvironmentManager.current.apiUrl) // 或者你的认证服务器URL
            .client(
                OkHttpClient
                    .Builder()
                    .connectTimeout(15, TimeUnit.SECONDS)
                    .readTimeout(15, TimeUnit.SECONDS)
                    .addInterceptor(
                        SignatureInterceptor(
                            accessKey = EnvironmentManager.current.apiSignAccessKey,
                            secretKey = EnvironmentManager.current.apiSignSecretKey,
                            jsonParser = jsonParser,
                        ),
                    ).apply {
                        if (EnvironmentManager.current.enableLog) {
                            addInterceptor(loggingInterceptor)
                        }
                    }.build(),
            ).addConverterFactory(jsonParser.asConverterFactory("application/json".toMediaType()))
            .build()
            .create(RefreshuserApiService::class.java)
    }

    // 实际的刷新Token API调用，应该是一个独立的Retrofit接口方法
    // 这个方法需要是同步的
    suspend fun refreshTokenApiCall(refreshTokenValue: String): retrofit2.Response<TokenRefreshResponse> {
        LogUtils.d("ApiClient 正在尝试使用 RefreshToken: $refreshTokenValue 进行刷新...")
        try {
            val apiResponse = refreshRetrofitAdapter.refreshToken(refreshTokenValue)

            return if (apiResponse.isSuccessful()) {
                retrofit2.Response.success(apiResponse.requireData)
            } else {
                retrofit2.Response.error(
                    apiResponse.code,
                    "{\"error\":\"invalid_grant\",\"error_description\":\"${apiResponse.message}\"}"
                        .toResponseBody("application/json".toMediaType()),
                )
            }
            // --- 模拟代码 End ---
        } catch (e: Exception) {
            LogUtils.e("ApiClient refreshTokenApiCall 异常: ${e.message}")
            return retrofit2.Response.error(
                500, // 或者其他合适的错误码
                "{\"error\":\"network_error\",\"error_description\":\"${e.message}\"}"
                    .toResponseBody("application/json".toMediaType()),
            )
        }
    }

    private interface RefreshuserApiService {
        /**
         * 刷新token
         */
        @FormUrlEncoded
        @POST("api/xya/user/v1/state/refresh")
        suspend fun refreshToken(
            @Field("refresh_token") refreshToken: String,
        ): ApiResponse<TokenRefreshResponse>
    }
}
