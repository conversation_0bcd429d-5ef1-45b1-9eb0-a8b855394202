package com.buque.wakoo.network.api.service

import com.buque.wakoo.BuildConfig
import com.buque.wakoo.bean.OssToken
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.AppGlobalSettingsResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Query

interface SettingsApiService {
    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<SettingsApiService>()
        }
    }

    /**
     * 更新用户信息
     */
    @POST("api/xya/general/v1/advise")
    suspend fun postFeedback(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    /**
     * 上传图片token获取
     */
    @GET("api/xya/general/v1/put/auth")
    suspend fun getUploadToken(
        @Header("Access-Token") token: String?,
    ): ApiResponse<OssToken>

    @GET("api/xya/config/v1/overall/query")
    suspend fun getSystemConfig(
        @Query("version") version: String = BuildConfig.VERSION_NAME,
    ): ApiResponse<AppGlobalSettingsResponse>
}
