package com.buque.wakoo.network.api.service

import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.UserListResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.POST

interface CommonApiService {
    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<CommonApiService>()
        }
    }

    /**
     * 举报接口
     * ```
     * target_type	枚举
     * 1	用户
     * 2	语音房
     * 3	私密小屋
     * 4	瞬间
     * True
     * target_id	整数	True
     * accusation_type	枚举
     * 1	政治相关
     * 2	色情低俗
     * 3	血腥暴力
     * 4	广告营销
     * 5	恶意诈骗
     * 6	不文明语言
     * 0	其他
     * True
     * note	字符串，最长500个字符	False
     * media_list	图片信息列表
     * 基础格式：[{image_info}, {image_info}..]
     * 其中image_info字段信息如下：
     * url	字符串，URL地址
     * width	整数px值
     * height	整数px值
     * size	整数byte值
     * ```
     * https://api.test.ucoofun.com/doc/index#a2u0
     * @return
     */
    @POST("api/xya/general/v1/report")
    suspend fun report(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>
}
