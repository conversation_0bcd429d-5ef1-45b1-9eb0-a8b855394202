package com.buque.wakoo.network

import com.buque.wakoo.BuildConfig
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.DeviceInfo
import okhttp3.Interceptor
import okhttp3.Response
import java.util.TimeZone

class AuthInterceptor : Interceptor {
    companion object {
        private const val HEADER_PLATFORM_KEY = "Client-Platform"
        private const val HEADER_SYSTEM_VERSION_KEY = "System-Version"
        private const val HEADER_DEVICE_MODEL_KEY = "Device-Model"
        private const val HEADER_PACKAGE_NAME_KEY = "Package-Name"
        private const val HEADER_APP_VERSION_KEY = "App-Version"
        private const val HEADER_DEVICE_ID_KEY = "Device-ID"
        private const val HEADER_SYSTEM_LANGUAGE = "System-Language"
        private const val HEADER_APP_CHANNEL = "App-Channel"
        private const val HEADER_FK_CHANNEL = "Fk-Channel"
        private const val HEADER_TIMEZONE = "timezone"
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val currentToken = AccountManager.getAccessToken()

        val requestBuilder = originalRequest.newBuilder()

        // 如果有 token，则添加到请求头
        currentToken?.let { token ->
            // 通常令牌类型是 Bearer，根据你的API要求调整
            requestBuilder.addHeader("Access-Token", token)
        }

        requestBuilder.apply {
            addHeader(HEADER_PLATFORM_KEY, DeviceInfo.OS_PLATFORM)
            addHeader(HEADER_SYSTEM_VERSION_KEY, DeviceInfo.deviceOs)
            addHeader(HEADER_DEVICE_ID_KEY, DeviceInfo.deviceId)
            addHeader(HEADER_DEVICE_MODEL_KEY, DeviceInfo.deviceName)
            addHeader(HEADER_PACKAGE_NAME_KEY, BuildConfig.APPLICATION_ID)
            addHeader(HEADER_APP_VERSION_KEY, BuildConfig.VERSION_NAME)
            addHeader(HEADER_SYSTEM_LANGUAGE, "zh")
            addHeader(HEADER_APP_CHANNEL, "official")
            addHeader(HEADER_FK_CHANNEL, "official")
            addHeader(HEADER_TIMEZONE, TimeZone.getDefault().id)
        }
        return chain.proceed(requestBuilder.build())
    }
}
