package com.buque.wakoo.network.api.bean


import com.buque.wakoo.network.api.bean.RechargeList.Discount
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import java.text.SimpleDateFormat
import java.util.Locale

@Serializable
data class IconItem(
    val text: String,
    val icon: String,
)

@Serializable
data class RechargeList(
    @SerialName("balance")
    val balance: Int,
    @SerialName("goods")
    val goods: List<Good>,
    @SerialName("userid")
    val userid: Int,
) {

    /**
     * @param fkType     (1, "GOOGLE_SDK", "谷歌SDK"),
     *     (2, "APPLE_SDK", "苹果SDK"),
     *     (3, "WXPAY_SDK", "微信SDK"),
     *     (4, "ORDER_LINK", "获取订单链接后打开"),
     *     (5, "LINK_WEBVIEW", "webview打开链接"),    // 目前没有这种方式
     *     (6, "LINK_WEB", "浏览器打开链接"),
     *     (7, "AGENT", "代充"),
     *
     *           "name": "街口支付",                // 支付方式名称
     *       "desc": "台湾用户专享优惠",          // 支付方式描述
     *       "icon": "https://media.ucoofun.com/opsite/cashier/charge_jkopay_icon.png", // 支付
     *       "discount": {                    // 优惠信息 如果没有优惠 为 null
     *           "discount": 0.8,             // 优惠折扣
     *           "name": "优惠20%",           // 优惠活动名
     *           "expired_ts": 123,          // 优惠到期时间
     *       },
     *       "is_others": true,
     */
    @Serializable
    data class Good(
        @SerialName("fk_channel") //# 1 Google IAP、2 APPLE IAP、3 WXPAY、4 ALIPAY
        val fkChannel: Int = -1,
        @SerialName("call_type")
        val fkType: Int = -1,
        val name: String = "",
        val desc: String = "",
        val icon: String = "",
        val discount: Discount? = null,
        @SerialName("is_others")
        val isOther: Boolean = false,
        @SerialName("charge_items")
        val chargeItems: List<ChargeItem> = listOf(),
        @SerialName("selected")
        val selected: Boolean = false,
    )

    @Serializable
    data class Discount(
        val name: String,
    )

    @Serializable
    data class ChargeItem(
        @SerialName("dollar")
        val dollar: String,
        @SerialName("product_id")
        val productId: String,
        @SerialName("ucoin")
        val ucoin: Int = 0,
        @SerialName("japan_coin")
        val jpUcoin: Int = 0,
        @SerialName("currency_mark")
        val currencyMark: String = "",
        @SerialName("currency_number")
        val currencyNumber: String = "",
        @SerialName("fk_link")
        val fkLink: String = "",
        @SerialName("rights")
        val rights: List<IconItem>? = null,
        /**
         * 2.40
         * 返回字段中 goods.charge_items 中每一项，增加字段 bonus_label，字符串，可能为空，也可能不存在，只有有有效值时显示。
         */
        @SerialName("bonus_label")
        val bonusLabel: String = "",
        @Transient
        var transformPrice: String? = null,
    ) {

        val price: String
            get() {
                return if (currencyMark.isNotEmpty() && currencyNumber.isNotEmpty()) {
                    "$currencyMark $currencyNumber"
                } else {
                    "$ $dollar"
                }
            }

        val cupidPrice: String
            get() = if (transformPrice.isNullOrBlank()) {
                if (currencyMark.isNotEmpty() && currencyNumber.isNotEmpty()) {
                    "${currencyMark} ${currencyNumber}"
                } else {
                    "$ ${dollar}"
                }
            } else {
                transformPrice.orEmpty()
            }

    }
}

@Serializable
data class BillRecord(
    @SerialName("records")
    val records: List<Record>,
)

@Serializable
data class Record(
    @SerialName("change_amount")
    val changeAmount: Int,
    @SerialName("change_reason")
    val changeReason: String,
    @SerialName("change_type")
    val changeType: Int,
    @SerialName("create_timestamp")
    val createTimestamp: Int,
    @SerialName("id")
    val id: Int,
    @SerialName("remain_balance")
    val remainBalance: Int,
) {

    private var _formatTime: String? = null

    val formatTime: String
        get() = _formatTime ?: SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(
            createTimestamp.times(
                1000L
            )
        )
            .also {
                _formatTime = it
            }

}

@Serializable
data class MemberRightItem(val icon: String, val name: String, val prompt: String)

@Serializable
data class VipInfo(
    @SerialName("goods")
    val goods: List<Good> = emptyList(),
    @SerialName("activate_options")
    val activateOption: List<ActivateOption>,
    @SerialName("expire_timestamp")
    val expireTimestamp: Long,
    @SerialName("is_member")
    val isVip: Boolean,
    @SerialName("userid")
    val userid: String,
    @SerialName("member_rights_infos")
    val memberRightsInfos: List<MemberRightItem> = emptyList(),
    @SerialName("membership")
    val membership: Int = 0,
    @SerialName("new_member_coin_bonus_detail")
    val bonusList: List<BonusItem>? = emptyList(),
    @SerialName("member_rights_title_icon")
    val memberRightsTitleIcon: String = "",
    @SerialName("page_type")
    val payAB: Int = 0,
    @SerialName("vip_dress_rights_info")
    val vipDressRightsInfo: List<MemberRightItem> = emptyList(),
    @SerialName("vip_social_rights_info")
    val vipSocialRightsInfo: List<MemberRightItem> = emptyList()
) {

    @Serializable
    data class Good(
        @SerialName("fk_channel") //# 1 Google IAP、2 APPLE IAP、3 WXPAY、4 ALIPAY
        val fkChannel: Int,
        @SerialName("call_type")
        val fkType: Int,
        val name: String,
        val desc: String,
        val icon: String,
        val discount: Discount?,
        @SerialName("is_others")
        val isOther: Boolean,
        @SerialName("activate_options")
        val activateOptions: List<ActivateOption>,
    )

}

@Serializable
data class ActivateOption(
    @SerialName("dollar")
    val dollar: String,
    @SerialName("equity")
    val equity: String,
    @SerialName("extra_note")
    val extraNote: String,
    @SerialName("label")
    val label: String,
    @SerialName("product_id")
    val productId: String,
    @SerialName("fk_type")
    val fkType: Int,
    @SerialName("currency_unit")
    val currencyUnit: String? = "",
    @SerialName("currency_mark")
    val currencyMark: String = "",
    @SerialName("currency_number")
    val currencyNumber: String = "",
    @SerialName("fk_link")
    val fkLink: String = "",
    @SerialName("label_type")
    val labelType: Int = 1,
    @SerialName("need_countdown")
    val needCountDown: Boolean = false,
    @SerialName("extra_note_del_line")
    val extraNoteDel: Boolean = false,
    @SerialName("countdown_seconds")
    val countdownSeconds: Int = 0,
    @Transient
    private var googlePrice: String? = null,
) {

    var transformPrice: String = formatPrice()
        private set

    fun setGooglePrice(price: String) {
        googlePrice = price
        transformPrice = formatPrice()
    }

    private fun formatPrice() = run {
        buildString {
            if (googlePrice.isNullOrEmpty()) {
                if (currencyMark.isNotEmpty() && currencyNumber.isNotEmpty()) {
                    append("$currencyMark ")
                    append(currencyNumber)
                } else {
                    append("$ ")
                    append(dollar)
                }
            } else {
                append(googlePrice)
            }
        }
    }

}

@Serializable
data class BonusItem(
    @Transient
    var bonusIndex: Int = 0,
    @SerialName("bonus_label")
    val bonusLabel: String,

    //# 0 未到领取时间 / 5 到达领取时间，等待领取 / 10 已领取
    @SerialName("bonus_status")
    val bonusStatus: Int,

    @SerialName("bonus_status_label")
    val bonusStatusLabel: String,
)

data class BonusList(val list: List<BonusItem>)
