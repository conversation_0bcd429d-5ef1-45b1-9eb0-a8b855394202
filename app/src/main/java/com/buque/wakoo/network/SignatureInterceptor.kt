package com.buque.wakoo.network

import com.buque.wakoo.utils.DateTimeUtils
import com.buque.wakoo.utils.LogUtils
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.boolean
import kotlinx.serialization.json.booleanOrNull
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.json.put
import okhttp3.FormBody
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okio.Buffer
import java.nio.charset.Charset
import java.security.MessageDigest
import java.util.Locale
import java.util.UUID

class SignatureInterceptor(
    private val accessKey: String,
    private val secretKey: String,
    private val jsonParser: Json,
) : Interceptor {
    companion object {
        // ---- 定义所有参数的Key名称 ----
        private const val ACCESS_KEY = "access_key"
        private const val TIMESTAMP = "timestamp"
        private const val NONCE = "nonce"
        private const val SIGN = "sign"
        private const val SECRET_KEY = "secret_key"

        // 用于解析和构建JSON的实例
        private val JSON_MEDIA_TYPE = "application/json; charset=utf-8".toMediaType()
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val requestBuilder = originalRequest.newBuilder()

        // ---- 步骤1: 收集所有原始请求参数，并保留它们的原始JSON值 ----
        // 使用 MutableMap<String, JsonElement> 来保留原始类型
        val originalParamsJson = mutableMapOf<String, JsonElement>()

        // a. 从URL Query中收集 (值视为JsonPrimitive字符串)
        originalRequest.url.queryParameterNames.forEach { key ->
            originalRequest.url.queryParameter(key)?.let { value ->
                originalParamsJson[key] = JsonPrimitive(value)
            }
        }

        // b. 从Body中收集
        val body = originalRequest.body
        if (body != null && (originalRequest.method == "POST" || originalRequest.method == "PUT")) {
            if (body is FormBody) {
                // FormBody的值视为JsonPrimitive字符串
                for (i in 0 until body.size) {
                    originalParamsJson[body.encodedName(i)] = JsonPrimitive(body.encodedValue(i))
                }
            } else if (body.contentType()?.subtype?.contains("json", ignoreCase = true) == true) {
                val bodyString = bodyToString(body)
                if (bodyString.isNotEmpty()) {
                    try {
                        val jsonObject = jsonParser.decodeFromString<JsonObject>(bodyString)
                        // 直接将原始的JsonObject内容合并进来
                        originalParamsJson.putAll(jsonObject)
                    } catch (e: Exception) {
                        LogUtils.e("AuthSignatureInterceptor", "Failed to parse JSON body.", e)
                    }
                }
            }
        }

        // ---- 步骤2: 生成并添加公共参数和签名 ----
        // getAuthenticatedParams现在需要处理JsonElement
        val finalParamsJson = getAuthenticatedParams(originalParamsJson)

        // ---- 步骤3: 将最终参数重新构建到请求中 ----
        when (originalRequest.method) {
            "GET" -> {
                val newUrl =
                    originalRequest.url
                        .newBuilder()
                        .apply {
                            query(null)
                            finalParamsJson.forEach { (key, jsonElement) ->
                                // 对于GET请求，所有参数都必须转为字符串
                                addQueryParameter(key, jsonElement.jsonPrimitive.content)
                            }
                        }.build()
                requestBuilder.url(newUrl)
            }
            "POST", "PUT" -> {
                // 【关键修复】使用保留了原始类型的finalParamsJson来构建新的JSON Body
                val newJsonBody =
                    buildJsonObject {
                        finalParamsJson.forEach { (key, jsonElement) ->
                            put(key, jsonElement)
                        }
                    }
                val newBody = newJsonBody.toString().toRequestBody(JSON_MEDIA_TYPE)
                requestBuilder.method(originalRequest.method, newBody)
            }
        }

        return chain.proceed(requestBuilder.build())
    }

    /**
     * 接收原始参数(JsonElement)，返回包含认证信息的最终参数Map(JsonElement)
     */
    private fun getAuthenticatedParams(originalParams: Map<String, JsonElement>): Map<String, JsonElement> {
        val finalParams = originalParams.toMutableMap()

        // 1. 添加公共参数
        finalParams[ACCESS_KEY] = JsonPrimitive(accessKey)
        finalParams[TIMESTAMP] = JsonPrimitive(DateTimeUtils.currentTimeSeconds().toString())
        finalParams[NONCE] = JsonPrimitive(generateRandomString())

        // 2. 【最终修复】为签名目的，将所有值按服务器规则转换为字符串
        val paramsForSigning =
            finalParams.mapValues {
                // 使用自定义的转换函数
                convertJsonElementToStringForSigning(it.value)
            }

        // 3. 构建待签名字符串
        val stringA =
            paramsForSigning
                .filter { it.value.isNotBlank() }
                .toSortedMap()
                .map { "${it.key}=${it.value}" }
                .joinToString("&")

        // 4. 拼接密钥
        val stringSignTemp = if (stringA.isNotEmpty()) "$stringA&$SECRET_KEY=$secretKey" else ""

        // 5. 计算签名
        val sign =
            if (stringSignTemp.isNotEmpty()) {
                sha1(stringSignTemp).uppercase(Locale.ROOT)
            } else {
                ""
            }

        // 6. 将签名添加到最终参数列表中
        finalParams[SIGN] = JsonPrimitive(sign)

        return finalParams
    }

    /**
     * 【新增】自定义转换函数，以匹配服务器（Python）的规则。
     * -布尔值 -> "True" / "False" (首字母大写)
     * -其他原始类型 -> 直接内容字符串
     */
    private fun convertJsonElementToStringForSigning(element: JsonElement): String {
        if (element is JsonPrimitive) {
            // 检查是否是布尔值
            if (element.isString.not() && element.booleanOrNull != null) {
                // 如果是布尔值，根据其值返回 "True" 或 "False"
                return if (element.boolean) "True" else "False"
            }
        }
        // 对于其他所有情况（字符串、数字等），使用 .content
        return element.jsonPrimitive.content
    }

    // --- 辅助函数 (保持不变) ---
    private fun bodyToString(body: RequestBody): String =
        try {
            val buffer = Buffer()
            body.writeTo(buffer)
            buffer.readString(Charset.forName("UTF-8"))
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }

    private fun sha1(input: String): String =
        try {
            val md = MessageDigest.getInstance("SHA-1")
            val digest = md.digest(input.toByteArray(Charsets.UTF_8))
            digest.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }

    private fun generateRandomString(): String = UUID.randomUUID().toString().replace("-", "")
}
