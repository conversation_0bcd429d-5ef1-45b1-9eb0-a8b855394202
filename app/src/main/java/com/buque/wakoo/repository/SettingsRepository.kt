package com.buque.wakoo.repository

import com.buque.wakoo.BuildConfig
import com.buque.wakoo.WakooApplication.Companion.isPreviewOnCompose
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.app.currentUserKV
import com.buque.wakoo.network.api.bean.AppGlobalSettingsResponse
import com.buque.wakoo.network.api.service.SettingsApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object SettingsRepository {

    private const val KEY_SETTINGS = "key_settings"

    private var _settingsFlow = MutableStateFlow<AppGlobalSettingsResponse?>(null)

    val sSettingsFlow = _settingsFlow.asStateFlow()

    val videoChatCostPerMinute: Int
        get() = sSettingsFlow.value?.videoCostPerMinute ?: 0

    val chargeCsId: Int?
        get() = sSettingsFlow.value.let {
            it?.chargeCsId
        }

    val tribeCsId: Int?
        get() = sSettingsFlow.value.let {
            it?.tribeCsId
        }

    val loveCsId: Int?
        get() = sSettingsFlow.value.let {
            it?.loveCsId
        }

    val charmValue: Int
        get() = sSettingsFlow.value.let {
            it?.charmValue
        } ?: 1000

    val privateRoomTimeGiftId: Int
        get() = sSettingsFlow.value.let {
            it?.privateRoomTimeGiftId
        } ?: -1

    val rtcAudioSubscribe: Boolean by lazy {
        if (!BuildConfig.DEBUG) {
            sSettingsFlow.value.let {
                it?.androidFeature?.micsubcribeV3
            } ?: false
        } else {
            true
        }
    }

    val conversationExtraPageSize: Int by lazy {
        if (!BuildConfig.DEBUG) {
            sSettingsFlow.value.let {
                it?.androidFeature?.conversationExtraPageSize
            } ?: 5
        } else {
            5
        }
    }

    fun isOfficialId(userId: Int): Boolean {
        return sSettingsFlow.value?.officialUserIds?.any {
            it == userId
        } == true
    }

    init {
        if (!isPreviewOnCompose) {
            appCoroutineScope.launch(Dispatchers.IO) {
                currentUserKV.getString(KEY_SETTINGS, "")?.takeIf {
                    it.isNotBlank()
                }?.let {
                    _settingsFlow.emit(AppJson.decodeFromString<AppGlobalSettingsResponse>(it))
                }

            }
        }
    }

    fun fetchSystemSettings() {
        appCoroutineScope.launch {
            executeApiCallExpectingData {
                SettingsApiService.instance.getSystemConfig()
            }.onSuccess {
                withContext(Dispatchers.IO) {
                    currentUserKV.putString(KEY_SETTINGS, AppJson.encodeToString(it))
                }
                _settingsFlow.emit(it)
            }
        }

//        ConcurrencyShare.globalInstance.joinPreviousOrRun("getSystemSettings") {
//            runApiCatching {
//                settingsApi.getSystemSettings()
//            }.also {
//                withContext(Dispatchers.IO) {
//                    IMManager.saveIMEnvConfig(it)
//                    sAppKV.putString(KEY_SETTINGS, sAppJson.encodeToString(it))
//                }
//                _settingsFlow.emit(DataState.Success(it))
//            }
//        }
    }

}