package com.buque.wakoo.repository

import com.buque.wakoo.app.AppJson
import com.buque.wakoo.bean.chatgroup.ChatGroupBean
import com.buque.wakoo.bean.chatgroup.ChatGroupMember
import com.buque.wakoo.bean.chatgroup.JoinApplyItem
import com.buque.wakoo.ext.getOrNull
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.api.service.ChatGroupApi
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.utils.LogUtils
import kotlinx.serialization.json.decodeFromJsonElement
import retrofit2.http.Query

private const val GROUP_ID = "group_id"

// 定义一个聊天群组仓库类
class ChatGroupRepo {
    // 定义一个伴生对象，用于创建ApiService
    companion object {
        // 创建一个ChatGroupApi的ApiService实例
        val api = ApiClient.createuserApiService<ChatGroupApi>()
    }

    // 创建群组
    suspend fun createGroup(
        name: String,
        avatar: String,
    ) = executeApiCallExpectingData {
        // 调用ApiService的createGroup方法，传入群组名称和头像URL
        api.createGroup(mapOf("name" to name, "avatar_url" to avatar))
    }

    // 更新群组信息
    suspend fun updateGroupInfo(
        groupId: String,
        name: String? = null,
        bulletin: String? = null,
        avatar: String? = null,
        enableDontDisturb: Boolean? = null,
    ) = executeApiCallExpectingData {
        // 调用ApiService的updateGroupInfo方法，传入群组ID和需要更新的信息
        api.updateGroupInfo(
            buildMap {
                put(GROUP_ID, groupId)
                if (name != null) {
                    put("name", name)
                }
                if (bulletin != null) {
                    put("bulletin", bulletin)
                }
                if (avatar != null) {
                    put("avatar_url", avatar)
                }
                if (enableDontDisturb != null) {
                    put("enable_dont_disturb", enableDontDisturb.toString())
                }
            },
        )
    }

    // 获取群组详情
    suspend fun getGroupDetail(groupId: String): Result<ChatGroupBean> {
        LogUtils.d("getGroupDetail:$groupId")
        return executeApiCallExpectingData {
            // 调用ApiService的getGroupDetail方法，传入群组ID
            api.getGroupDetail(groupId)
        }
    }

    suspend fun getJoinApplyList(
        groupId: String,
        lastId: Int = 0,
    ) = executeApiCallExpectingData {
        api.getJoinApplyList(groupId, lastId)
    }.map {
        it
            .getOrNull("applies")
            ?.let {
                AppJson.decodeFromJsonElement<List<JoinApplyItem>>(it)
            }.orEmpty()
    }

    // 申请加入群组
    suspend fun applyJoinGroup(groupId: String) =
        executeApiCallExpectingData {
            // 调用ApiService的applyJoinChatGroup方法，传入群组ID
            api.applyJoinChatGroup(mapOf(GROUP_ID to groupId))
        }

    // 处理申请
    suspend fun handleApply(
        groupId: String,
        userId: String,
        accept: Boolean,
    ) = executeApiCallExpectingData {
        // 调用ApiService的handleApply方法，传入群组ID、用户ID和是否接受申请
        api.handleApply(
            buildMap {
                put(GROUP_ID, groupId)
                put("userid", userId)
                put("agreed", accept.toString())
            },
        )
    }

    suspend fun destroyGroup(groupId: String) =
        executeApiCallExpectingData {
            api.destroyGroup(mapOf("group_id" to groupId))
        }

    // 退出群组
    suspend fun exitGroup(groupId: String) =
        executeApiCallExpectingData {
            api.exitGroup(mapOf("group_id" to groupId))
        }

    suspend fun kickOutMember(
        groupId: String,
        fellowId: String,
    ) = executeApiCallExpectingData {
        api.kickOutMember(
            buildMap {
                put(GROUP_ID, groupId)
                put("fellow_id", fellowId)
            },
        )
    }

    // 挂起函数，设置管理员
    suspend fun setAdmin(
        // 组ID
        groupId: String,
        // 同伴ID
        fellowId: String,
        // 是否为管理员
        isAdmin: Boolean,
    ) = executeApiCallExpectingData {
        // 执行API调用，期望返回数据
        api.setAdmin(
            // 构建一个Map，包含组ID、同伴ID和是否为管理员
            buildMap {
                put(GROUP_ID, groupId)
                put("fellow_id", fellowId)
                put("is_admin", isAdmin.toString())
            },
        )
    }

    suspend fun getRoleMemberList(
        groupId: String,
        roles: String,
        lastFellowId: Int,
    ) = executeApiCallExpectingData {
        api.getMemberListWithRoles(groupId, roles, lastFellowId)
    }.map {
        it
            .getOrNull("members")
            ?.let {
                AppJson.decodeFromJsonElement<List<ChatGroupMember>>(it)
            }.orEmpty()
    }

    suspend fun searchMember(
        groupId: String,
        keyword: String,
    ) = executeApiCallExpectingData {
        api.searchMember(groupId, keyword)
    }.map {
        it
            .getOrNull("members")
            ?.let {
                AppJson.decodeFromJsonElement<List<ChatGroupMember>>(it)
            }.orEmpty()
    }

    suspend fun getGroupMembers(
        @Query("group_id") groupId: String,
        @Query("last_fellow_id") lastId: String,
        @Query("last_fellow_is_online") lastMemberIsOnline: Boolean
    ) = executeApiCallExpectingData {
        api.getGroupMemberList(groupId, lastId, lastMemberIsOnline)
    }.map {
        it.getOrNull("members")?.let {
            AppJson.decodeFromJsonElement<List<ChatGroupMember>>(it)
        }.orEmpty()
    }

    //1 部落/家族 2 私聊 3 群聊
    suspend fun inviteJoinGroup(inviteType: Int, targetUid: String) = executeApiCallExpectingData {
        api.inviteToChatGroup(mapOf("invite_type" to inviteType.toString(), "target_user_id" to targetUid))
    }
}
