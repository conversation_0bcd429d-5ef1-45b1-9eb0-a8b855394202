package com.buque.wakoo.repository

import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.api.bean.BillRecord
import com.buque.wakoo.network.api.bean.ChargeDataEntity
import com.buque.wakoo.network.api.bean.RechargeList
import com.buque.wakoo.network.api.bean.VipInfo
import com.buque.wakoo.network.api.service.WalletApi
import com.buque.wakoo.network.executeApiCallExpectingData
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

class WalletRepository {
    private val walletApi = ApiClient.createuserApiService<WalletApi>()

    /**
     * 获取商品列表
     */
    suspend fun fetchRechargeList(): Result<ChargeDataEntity> {
        return executeApiCallExpectingData {
            walletApi.fetchRechargeList()
        }.onSuccess {
            AccountManager.updateUserExtraInfo { extra ->
                extra.copy(balance = it.balance)
            }
        }
    }

    /**
     * 创建订单
     */
    suspend fun createOrder(productId: String, orderType: Int): Result<Pair<String, JsonObject?>> {
        return executeApiCallExpectingData {
            walletApi.createOrder(
                mapOf(
                    "product_id" to productId,
                    "order_type" to orderType.toString()
                )
            )
        }.map {
            it["order_no"]?.jsonPrimitive?.content.orEmpty() to it["preorder_params"]?.jsonObject
        }
    }


    /**
     * 订单详情
     */
    suspend fun orderDetails(orderNo: String): Result<JsonObject> {
        // -5 取消
        // 0 待支付
        // 10 完成
        return executeApiCallExpectingData {
            walletApi.orderDetails(orderNo)
        }
    }

    /**
     * 订单完成
     */
    suspend fun orderCompleted(orderNo: String, productId: String, token: String): Result<JsonObject> {
        return executeApiCallExpectingData {
            walletApi.orderCompleted(
                mapOf(
                    "order_no" to orderNo,
                    "product_id" to productId,
                    "transaction_id" to token
                )
            )
        }
    }

    /**
     * 取消订单
     */
    suspend fun orderCancel(orderNo: String): Result<JsonObject> {
        return executeApiCallExpectingData {
            walletApi.orderCancel(mapOf("order_no" to orderNo))
        }
    }

    suspend fun getBillList(lastId: Int?): Result<BillRecord> {
        return executeApiCallExpectingData {
            walletApi.getBillList(lastId)
        }
    }

    suspend fun getActivateVipConfig(): Result<VipInfo> {
        return executeApiCallExpectingData {
            walletApi.getActivateVipConfig()
        }.onSuccess {
            AccountManager.updateUserBasicInfo {
                it.copy(isVip = it.isVip)
            }
        }
    }


    suspend fun recordBillingEvent(orderNo: String): Result<JsonObject> {
        return executeApiCallExpectingData {
            walletApi.recordBillingEvent(mapOf("order_no" to orderNo))
        }
    }

    suspend fun reportBillingError(message: String): Result<JsonObject> {
        return executeApiCallExpectingData {
            walletApi.reportBillingError(mapOf("content" to message))
        }
    }

    suspend fun getGold(bonusIndex: Int): Result<JsonObject> =
        executeApiCallExpectingData { walletApi.getGold(mapOf("bonus_index" to bonusIndex.toString())) }

//    suspend fun fetchFirstRecharge(): Result<FirstRechargeInfo> {
//        return executeApiCallExpectingData {
//            walletApi.fetchFirstRecharge()
//        }
//    }
}