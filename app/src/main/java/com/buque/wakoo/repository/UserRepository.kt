package com.buque.wakoo.repository

import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.manager.UserManager
import com.buque.wakoo.network.api.bean.UserListResponse
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import kotlinx.serialization.json.JsonObject

class UserRepository {
    private val userApiService
        get() = UserApiService.instance

    /** 刷新永辉信息 */
    suspend fun getUserInfo(userId: String): Result<UserInfo> = UserManager.getRemoteUserInfo(userId)

    suspend fun updateUserInfo(fields: Map<String, String>): Result<JsonObject> =
        executeApiCallExpectingData {
            userApiService.updateUserInfo(fields)
        }

    /** 拉黑状态更新 */
    suspend fun updateBlackState(
        userId: String,
        isBlack: Boolean,
    ): Result<JsonObject> =
        executeApiCallExpectingData {
            userApiService.blackUser(fields = mapOf("userid" to userId, "black" to isBlack.toString()))
        }

    /** 获取拉黑列表 */
    suspend fun getBlackList(
        userId: String,
        lastId: Int,
    ): Result<UserListResponse> = executeApiCallExpectingData { userApiService.getBlackList("", lastId) }

    /** 关注用户 */
    suspend fun updateFollowState(
        userId: String,
        isFollow: Boolean,
    ): Result<JsonObject> =
        executeApiCallExpectingData {
            userApiService.followUser(
                fields = mapOf("userid" to userId, "follow" to isFollow.toString()),
            )
        }

    /** 获取关注列表 */
    suspend fun getFollowingList(
        userId: String,
        lastId: Int,
    ): Result<UserListResponse> = executeApiCallExpectingData { userApiService.getUserFollowingList("", lastId) }

    /** 获取粉丝列表 */
    suspend fun getFollowerList(
        userId: String,
        lastId: Int,
    ): Result<UserListResponse> = executeApiCallExpectingData { userApiService.getUserFollowerList("", lastId) }
}
