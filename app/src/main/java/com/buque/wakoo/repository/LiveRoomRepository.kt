package com.buque.wakoo.repository

import androidx.collection.mutableScatterSetOf
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableIntState
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.mutableStateSetOf
import androidx.compose.runtime.snapshots.Snapshot
import com.buque.wakoo.app.Const
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.app.createSafeCoroutineScope
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.bean.BasicUser
import com.buque.wakoo.bean.RoomOnlineInfo
import com.buque.wakoo.bean.RoomRtcToken
import com.buque.wakoo.bean.User
import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.ext.isNetworkException
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.utils.takeIsNotBlank
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.network.api.bean.RoomInfoResponse
import com.buque.wakoo.network.api.bean.Seat
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.network.api.bean.VoiceTag
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.network.api.service.VoiceRoomApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.rtc.AppRtcHelper
import com.buque.wakoo.rtc.TencentRtcFactory
import com.buque.wakoo.ui.dialog.loading.LoadingManager
import com.buque.wakoo.ui.screens.liveroom.LiveMicMode
import com.buque.wakoo.ui.screens.liveroom.LiveRoomExtraInfo
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import com.buque.wakoo.ui.screens.liveroom.MicSeatsInfo
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.RoomMember
import com.buque.wakoo.ui.screens.liveroom.RoomRole
import com.buque.wakoo.utils.DateTimeUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

private data class StateScope(
    val basicInfoState: MutableState<BasicRoomInfo>,
    val onlineCountState: MutableIntState,
    val onlinePreviewState: MutableList<User>,
    val micListState: MutableList<MicSeatsInfo>,
    val adminIdsState: MutableSet<String>,
    val blackIdsState: MutableSet<String>,
    val extraInfoState: MutableState<LiveRoomExtraInfo>,
    val allUserMapState: MutableMap<String, RoomMember>,
    val loadingState: MutableState<Boolean>,
) {
    val basicInfo: BasicRoomInfo
        get() = basicInfoState.value

    val onlineCount: Int
        get() = onlineCountState.intValue

    val onlinePreviewList: List<User>
        get() = onlinePreviewState

    // 麦位列表
    val micList: List<MicSeatsInfo>
        get() = micListState

    val adminIds: Set<String>
        get() = adminIdsState

    val blackIds: Set<String>
        get() = blackIdsState

    val extraInfo: LiveRoomExtraInfo
        get() = extraInfoState.value

    val loading: Boolean
        get() = loadingState.value

    val allUserMap: Map<String, RoomMember>
        get() = allUserMapState
}

private class RoomMutableState(
    basicInfo: BasicRoomInfo,
    repository: LiveRoomRepository,
) {
    // 基本信息
    val basicInfoState: MutableState<BasicRoomInfo> = mutableStateOf(basicInfo)

    // 在线房间人数(包括房主)
    val onlineCountState = mutableIntStateOf(if (SelfUser != null) 1 else 0)

    // 目前显示最多3个人
    val onlinePreviewState =
        mutableStateListOf<User>().apply {
            SelfUser?.also {
                add(it)
            }
        }

    // 麦位列表
    val micListState: MutableList<MicSeatsInfo> =
        mutableStateListOf<MicSeatsInfo>().apply {
            addAll(List(basicInfo.micSeatCount) { MicSeatsInfo.Empty(it) })
        }

    // 房间管理员列表Id
    val adminIdsState: MutableSet<String> = mutableStateSetOf("")

    // 黑名单列表Id
    val blackIdsState: MutableSet<String> = mutableStateSetOf("")

    // 额外信息
    val extraInfoState: MutableState<LiveRoomExtraInfo> = mutableStateOf(LiveRoomExtraInfo())

    // 计划作为房间所有需要感知用户信息变化ui组件的唯一数据源，包括（麦位，部分观众，房主），以及一些可能离开房间的引用计数不为0的用户
    // 要时刻保证与micListState，owner数据同步
    // 理想情况下一个用户的数据源只有一个
    val allUserMapState: MutableMap<String, RoomMember> = mutableStateMapOf<String, RoomMember>()

    val loadingState: MutableState<Boolean> = mutableStateOf(true)

    val basicInfo: BasicRoomInfo
        get() = basicInfoState.value

    val onlineCount: Int
        get() = onlineCountState.intValue

    val onlinePreviewList: List<User>
        get() = onlinePreviewState

    // 麦位列表
    val micList: List<MicSeatsInfo>
        get() = micListState

    val adminIds: Set<String>
        get() = adminIdsState

    val blackIds: Set<String>
        get() = blackIdsState

    val extraInfo: LiveRoomExtraInfo
        get() = extraInfoState.value

    val loading: Boolean
        get() = loadingState.value

    val allUserMap: Map<String, RoomMember>
        get() = allUserMapState

    val roomInfoState =
        LiveRoomInfoState(
            basicInfoState = basicInfoState,
            onlineInfo = RoomOnlineInfo(onlineCountState, onlinePreviewList),
            allUserMapState = allUserMapState,
            micListState = micListState,
            adminIdsState = adminIdsState,
            blackIdsState = blackIdsState,
            extraInfoState = extraInfoState,
            loadingState = loadingState,
            repository = repository,
        )

    inline fun <R> withMutableSnapshot(block: StateScope.() -> R): R =
        Snapshot.withMutableSnapshot {
            StateScope(
                basicInfoState = basicInfoState,
                onlineCountState = onlineCountState,
                onlinePreviewState = onlinePreviewState,
                allUserMapState = allUserMapState,
                micListState = micListState,
                adminIdsState = adminIdsState,
                blackIdsState = blackIdsState,
                extraInfoState = extraInfoState,
                loadingState = loadingState,
            ).run {
                block()
            }
        }
}

/**
 * 共享于[LiveRoomManager]和[com.buque.wakoo.viewmodel.liveroom.LiveRoomViewModel]
 * 直播房逻辑，收起后还需要运行
 */
class LiveRoomRepository(
    basicInfo: BasicRoomInfo,
    onExitRoom: (String, String?) -> Unit,
) : IMCompatListener {
    val id = basicInfo.id

    private val onExitRoom: (String?) -> Unit = {
        onExitRoom(id, it)
    }

    private val limitedDispatcher = Dispatchers.Default.limitedParallelism(1)

    private val coroutineScope = createSafeCoroutineScope(limitedDispatcher + SupervisorJob())

    private val mutableState = RoomMutableState(basicInfo, this)

    val roomInfoState = mutableState.roomInfoState

    val loadingManager = LoadingManager(coroutineScope)

    // 可能不准，可能会更少，因为[RoomInfoResponse.audiences]可能不会全量返回
    private val allUserIds = mutableScatterSetOf<String>()

    /**
     * 除去麦上，自己，房主，以外的，引用追踪数（包括观众）
     */
    @Volatile
    private var extraReferenceTrackCount = 0

    @Volatile
    private var released = false

    private val createTimestamp = DateTimeUtils.elapsedRealtime()

    private val eventChannel =
        MutableSharedFlow<RoomEvent>(
            extraBufferCapacity = 100,
            onBufferOverflow = BufferOverflow.DROP_OLDEST,
        )

    val events: Flow<RoomEvent> = eventChannel.asSharedFlow()

    val rtcHelper: AppRtcHelper = AppRtcHelper(TencentRtcFactory(), false)

    init {
        // 默认最多两人，自己和房主
        mutableState.withMutableSnapshot {
            val id =
                SelfUser
                    ?.also {
                        allUserMapState.put(
                            it.id,
                            RoomMember(
                                user = it,
                                inRoom = false,
                                roomInfo = roomInfoState,
                            ),
                        )
                    }?.id

            if (!basicInfo.ownerIsInvalid && id != basicInfo.ownerId) {
                allUserMapState.put(
                    basicInfo.ownerId,
                    RoomMember(
                        user = basicInfo.owner,
                        inRoom = false,
                        roomInfo = roomInfoState,
                    ),
                )
            }
        }
        // 加入语音房，连接rtc，连接im
        joinRoom()

        events
            .onEach {
                handleEvent(it)
            }.launchIn(coroutineScope)

        IMCompatCore.addIMListener(this)
    }

    fun release(reason: String?) {
        if (released) {
            return
        }
        released = true
        IMCompatCore.removeIMListener(this)
        if (!reason.isNullOrBlank()) {
            showToast(reason)
        }
        coroutineScope.launch(NonCancellable) {
            IMCompatCore.quitConversation(id, ConversationType.CHATROOM, true)
        }
        coroutineScope.launch(NonCancellable) {
            rtcHelper.levelChannel()
        }
        coroutineScope.cancel()
    }

    fun sendEvent(event: RoomEvent) {
        coroutineScope.launch {
            eventChannel.emit(event)
        }
    }

    //
    fun requireRoomUser(user: User): User =
        mutableState.withMutableSnapshot {
            allUserMap[user.id]?.user ?: user
        }

    @Composable
    fun trackRoomMember(
        user: User,
        needFetchLatest: Boolean = true,
    ) = (
        mutableState.withMutableSnapshot {
            allUserMap[user.id] ?: run {
                val member =
                    RoomMember(
                        user = user,
                        roomInfo = roomInfoState,
                        inRoom = false,
                    )
                extraReferenceTrackCount++
                allUserMapState[user.id] = member
                member
            }
        }
    ).also {
        DisposableEffect(user.id) {
            if (needFetchLatest) {
                refreshRoomUserInfo(user.id)
            }
            it.referenceCounter++
            onDispose {
                it.referenceCounter--
                // todo 检查是否需要移除
            }
        }
    }

    private fun handleMemberChangeEvent(
        isAdd: Boolean,
        user: User,
    ) {
        if (user.sIsSelf) {
            return
        }
        mutableState.withMutableSnapshot {
            val id = user.id
            val exist = allUserMap[id]
            val ownerId = basicInfo.ownerId
            if (isAdd) {
                var shouldAdd = false
                if (exist != null) {
                    //  allUserMapState[id] = exist.copy(user = user)
                    if (exist.inRoom) { // 已经在房间里面了什么都不处理
                        return
                    } else {
                        exist.inRoom = true
                        shouldAdd = true
                    }
                } else {
                    if (extraReferenceTrackCount <= 60) { // 可以加进来
                        extraReferenceTrackCount++
                        allUserMapState.put(
                            id,
                            RoomMember(
                                user = user,
                                inRoom = true,
                                roomInfo = roomInfoState,
                            ),
                        )
                    }

                    if (!allUserIds.contains(id)) { // 双重判断判断是否存在
                        shouldAdd = true
                    }
                }

                if (shouldAdd) {
                    onlineCountState.intValue = onlineCount + 1
                    if (onlinePreviewList.size < 3) {
                        onlinePreviewState.add(user)
                    }
                }
                allUserIds.add(id)
            } else {
                var shouldDelete = false
                if (exist != null) {
                    val role =
                        when (id) {
                            ownerId -> RoomRole.Owner
                            in adminIdsState -> RoomRole.Admin
                            else -> RoomRole.Member
                        }

                    if (exist.inRoom) {
                        exist.inRoom = false
                        shouldDelete = true
                    }

                    if (!exist.inRoom && exist.referenceCounter == 0 && role != RoomRole.Owner) { // 可以去除了
                        extraReferenceTrackCount--
                        allUserMapState.remove(id)
                    }
                } else if (allUserIds.contains(id)) {
                    shouldDelete = true
                }

                if (shouldDelete) {
                    val oldOnlineCount = onlineCount
                    onlineCountState.intValue = (oldOnlineCount - 1).coerceAtLeast(0)
                    val index = onlinePreviewList.indexOfFirst { it.id == id }
                    if (index > -1) {
                        if (onlineCount < 3 && onlinePreviewList.size >= oldOnlineCount) {
                            onlinePreviewState.removeAt(index)
                        } else {
                            updateUserPreview()
                        }
                    }
                }

                allUserIds.remove(id)
            }
        }
    }

    private fun StateScope.handleMicSeatsChange(
        seats: List<Seat>,
        updateAllUserMap: Boolean = true,
    ) {
        val micSeatCount =
            when (basicInfo.roomMode) {
                LiveRoomMode.Radio -> 5
                else -> 8
            }

        var inMicList = false

        val tempList =
            buildList {
                seats.forEachIndexed { index, it ->
                    add(
                        if (it.hasUser && it.user != null) {
                            val user = UserInfo.fromResponse(it.user)
                            allUserIds.add(user.id)
                            if (updateAllUserMap) {
                                if (!allUserMapState.contains(user.id)) { // 没有这个用户需要补充, 要保证allUserMapState一定有麦上用户
                                    allUserMapState[user.id] =
                                        RoomMember(
                                            user = user,
                                            inRoom = true,
                                            roomInfo = roomInfoState,
                                        )
                                }
                            }
                            if (!inMicList && user.id == SelfUser?.id) {
                                inMicList = true
                            }
                            MicSeatsInfo.User(index, user, it.heartValue ?: 0)
                        } else {
                            MicSeatsInfo.Empty(index)
                        },
                    )
                    if (size >= micSeatCount) {
                        return@buildList
                    }
                }

                val count = size
                repeat(micSeatCount - count) {
                    add(MicSeatsInfo.Empty(count + it))
                }
            }

        micListState.clear()
        micListState.addAll(tempList)

        if (!inMicList) {
            rtcHelper.downMic(null)
        }
    }

    private fun updateRoomInfo(infoResp: RoomInfoResponse) {
        mutableState.withMutableSnapshot {
            basicInfoState.value =
                basicInfo.run {
                    copy(
                        publicId = infoResp.publicId,
                        title = infoResp.title,
                        owner = UserInfo.fromResponse(infoResp.owner),
                        roomMode = LiveRoomMode.valueOf(infoResp.roomMode),
                        micMode = LiveMicMode.valueOf(infoResp.occupyMicMode),
                        desc = infoResp.desc?.takeIf { it.isNotBlank() },
                        notice = infoResp.notice?.takeIf { it.isNotBlank() },
                        background = infoResp.roomBackground.takeIf { it.isNotBlank() },
                        tagIds =
                            infoResp.tags?.map {
                                it.id
                            },
                        updateTimestamp = infoResp.updateTimestamp,
                    )
                }

            if (basicInfo.roomMode == LiveRoomMode.UnKnown) {
                onExitRoom("不支持此房间模式，已退出")
                return
            }

            handleMicSeatsChange(infoResp.seats, false)

            val idSet = mutableScatterSetOf<String>()

            if (infoResp.audienceCnt < 60) { //  这个要根据后端最多返回多少个audiences做调整
                allUserIds.clear()
            }

            var tempExtraCount = 0

            extraReferenceTrackCount = 0

            val tempMap =
                buildMap {
                    micListState.forEach {
                        if (it is MicSeatsInfo.User && !idSet.contains(it.user.id)) {
                            val id = it.user.id
                            idSet.add(id)
                            allUserIds.add(id)
                            val exist = allUserMap[id]
                            val member =
                                RoomMember(
                                    user = it.user,
                                    inRoom = true,
                                    referenceCounter = exist?.referenceCounter ?: 0,
                                    roomInfo = roomInfoState,
                                )
                            put(id, member)
                        }
                    }

                    var tempCount = 0
                    infoResp.audiences.forEach {
                        val id = it.id
                        allUserIds.add(id)
                        if (tempCount < 50 && !idSet.contains(id)) { //  不加太多，为了控制内存，50个足够了
                            idSet.add(id)
                            tempCount++
                            extraReferenceTrackCount++
                            val exist = allUserMapState[id]
                            val member =
                                RoomMember(
                                    user =
                                        if (exist != null && exist.user is UserInfo) { // 如果是UserInfo说明需要额外信息
                                            exist.user.copy(basic = it)
                                        } else {
                                            it
                                        },
                                    inRoom = true,
                                    referenceCounter = exist?.referenceCounter ?: 0,
                                    roomInfo = roomInfoState,
                                )
                            put(id, member)
                        }
                    }

                    val id = basicInfo.ownerId
                    if (!idSet.contains(id)) { // 补全房主信息
                        idSet.add(id)
                        val exist = allUserMap[id]
                        val member =
                            RoomMember(
                                user = basicInfo.owner,
                                inRoom = false, // 房主好像无法判断在没在线
                                referenceCounter = exist?.referenceCounter ?: 0,
                                roomInfo = roomInfoState,
                            )
                        put(id, member)
                    }

                    allUserIds.add(id)

                    val self = SelfUser
                    if (self != null) { // 补全自己的信息
                        val id = self.id
                        if (!idSet.contains(id)) {
                            idSet.add(id)
                            val exist = allUserMap[id]
                            val member =
                                RoomMember(
                                    user = self,
                                    inRoom = true, // 自己就设定为一定在房间
                                    referenceCounter = exist?.referenceCounter ?: 0,
                                    roomInfo = roomInfoState,
                                )
                            put(id, member)

                            tempExtraCount++
                        }

                        allUserIds.add(id)
                    }
                }

            // 把那些还在追踪的value添加进去回去继续追踪
            val referenceMap =
                buildMap {
                    allUserMapState.forEach { key, value ->
                        if (!tempMap.contains(key) && value.referenceCounter > 0) {
                            extraReferenceTrackCount++
                            put(key, value)
                        }
                    }
                }

            allUserMapState.clear()
            allUserMapState.putAll(tempMap)
            allUserMapState.putAll(referenceMap)

            // 在线人数
            onlineCountState.intValue = infoResp.audienceCnt + tempExtraCount

            adminIdsState.clear()
            adminIdsState.addAll(infoResp.admins.map { it.id })

            updateUserPreview()

            extraInfoState.value =
                extraInfo
                    .run {
                        copy(
                            reqUpMicCount = if (basicInfo.micMode != LiveMicMode.Request) 0 else infoResp.requestMicCount,
                            reqUpMicIng = if (basicInfo.micMode != LiveMicMode.Request) false else reqUpMicIng,
                        )
                    }
        }
    }

    private fun StateScope.updateUserPreview() {
        val tempList =
            allUserMapState.values
                .asSequence()
                .filter {
                    it.inRoom
                }.sortedWith { member1, member2 ->
                    if (member1.id == member2.id) {
                        0
                    } else {
                        val role1 = roomInfoState.getRoomRole(member1.id)
                        val role2 = roomInfoState.getRoomRole(member2.id)
                        if (role1 == role2) {
                            if (member1.user.sIsSelf) {
                                1
                            } else if (member2.user.sIsSelf) {
                                -1
                            } else {
                                member1.id.compareTo(member2.id)
                            }
                        } else {
                            if (role1 > role2) {
                                -1
                            } else {
                                1
                            }
                        }
                    }
                }.take(onlineCountState.intValue.coerceAtMost(3))
                .map { it.user }
                .toList()
        // 在线用户
        onlinePreviewState.clear()
        onlinePreviewState.addAll(tempList)
    }

    // /////////////////////////////////////////////////////////////////////////
    // API接口区域
    // /////////////////////////////////////////////////////////////////////////

    fun refreshRoomInfo() {
        if (DateTimeUtils.elapsedRealtime() - createTimestamp < 2000) {
            return
        }
        coroutineScope.launch {
            executeApiCallExpectingData {
                VoiceRoomApiService.instance.getVoiceRoomInfo(id)
            }.onSuccess { infoResp ->
                updateRoomInfo(infoResp)
            }
        }
    }

    fun refreshRoomUserInfo(userId: String) {
        coroutineScope.launch {
            executeApiCallExpectingData {
                VoiceRoomApiService.instance.getVoiceRoomMemberDetail(id, userId)
            }.onSuccess { userResp ->
                mutableState.withMutableSnapshot {
                    allUserMapState[userId]?.also {
                        allUserMapState[userId] = it.copy(UserInfo.fromResponse(userResp))
                    }
                    if (userResp.isRoomAdmin == true) {
                        adminIdsState.add(userId)
                    } else {
                        adminIdsState.remove(userId)
                    }
                    if (userResp.inRoomBlackList == true) {
                        blackIdsState.add(userId)
                    } else {
                        blackIdsState.remove(userId)
                    }
                }
            }
        }
    }

    private fun joinRoom() {
        fun connectIM(
            channelId: String,
            retry: Boolean = false,
        ) {
            coroutineScope.launch {
                val ret = IMCompatCore.joinConversation(channelId, ConversationType.CHATROOM, true)
                if (!ret) {
                    if (retry) {
                        onExitRoom("im连接失败，已退出")
                    } else {
                        delay(2000)
                        connectIM(channelId, true)
                    }
                }
            }
        }

        fun connectRtc(
            token: RoomRtcToken,
            retry: Boolean = false,
        ) {
            coroutineScope.launch {
                if (retry) {
                    delay(2000)
                }
                rtcHelper.joinChannel(token.channelName, token.token) {
                    if (!retry) {
                        connectRtc(token, true)
                    } else {
                        onExitRoom("rtc连接失败，已退出")
                    }
                }
            }
        }

        fun fetchRoomInfo(retry: Boolean = false) {
            coroutineScope.launch {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.getVoiceRoomInfo(id)
                }.onSuccess { infoResp ->
                    updateRoomInfo(infoResp)
                }.onFailure {
                    if (!retry) {
                        delay(2000)
                        fetchRoomInfo(true)
                    } else {
                        onExitRoom("房间信息获取失败，已退出")
                    }
                }
            }
        }

        suspend fun fetchToken(retry: Boolean = false) {
            executeApiCallExpectingData {
                VoiceRoomApiService.instance.getVoiceRoomToken(mapOf("live_house_id" to id))
            }.onSuccess { tokenResp ->
                connectIM(tokenResp.roomId)
                connectRtc(
                    RoomRtcToken(
                        channelName = tokenResp.rtcChannelName,
                        channelType = tokenResp.rtcChannelType,
                        config = tokenResp.rtcConfig,
                        token = tokenResp.rtcToken,
                    ),
                )
                fetchRoomInfo()
            }.onFailure {
                if (!retry && it.isNetworkException()) {
                    delay(2000)
                    fetchToken(true)
                } else {
                    onExitRoom("进入房间失败，已退出")
                }
            }
        }

        coroutineScope.launch {
            fetchToken(false)
        }
    }

    // /////////////////////////////////////////////////////////////////////////
    // API接口区域
    // /////////////////////////////////////////////////////////////////////////

    // /////////////////////////////////////////////////////////////////////////
    // 消息处理
    // /////////////////////////////////////////////////////////////////////////

    override fun onRecvNewCustomMessage(
        message: UCCustomMessage,
        offline: Boolean,
    ) {
        coroutineScope.launch {
            when (message.cmd) {
                IMEvent.BLACK_USER -> { // 用户被拉黑
                    message
                        .getJsonValue<BasicUser>("user")
                        ?.takeIf {
                            it.sIsSelf
                        }?.also {
                            onExitRoom("你已被房间拉黑")
                        }
                }

                IMEvent.TAKEAWAY_MIC -> { // 用户被踢下麦
                    message
                        .getJsonValue<BasicUser>("target_user")
                        ?.takeIf {
                            it.sIsSelf
                        }?.also {
                            sendEvent(RoomEvent.DownMic)
                        }
                }

                IMEvent.VOICE_MIC_ABANDONED -> { // 用户被禁麦
                    if (message.getJsonString("userid") == SelfUser?.id) {
                        sendEvent(RoomEvent.DownMic)
                    }
                }

                IMEvent.USER_EXIT -> { // 用户退出
                    val user = message.getJsonValue<BasicUser>("user") ?: return@launch
                    if (user.sIsSelf && message.getJsonBoolean("is_forced", false)) {
                        onExitRoom("你已被踢出房间")
                        return@launch
                    }
                    handleMemberChangeEvent(false, user)
                }

                IMEvent.USER_ENTRANCE -> { // 用户进入
                    val user = message.getJsonValue<UserResponse>("user") ?: return@launch
                    handleMemberChangeEvent(true, UserInfo.fromResponse(user))
                }

                IMEvent.ROOM_SETTINGS -> { // 房间设置变更
                    when (message.getJsonString("settings_name")) {
                        Const.RoomInfoChangeKey.ROOM_MODE -> {
                            mutableState.withMutableSnapshot {
                                basicInfoState.value =
                                    basicInfo.run {
                                        copy(
                                            roomMode = LiveRoomMode.valueOf(message.getJsonInt("value", -1)),
                                            background =
                                                message.getJsonString("room_background").takeIsNotBlank()
                                                    ?: background,
                                            updateTimestamp = DateTimeUtils.currentTimeMillis(),
                                        )
                                    }
                                if (basicInfo.roomMode == LiveRoomMode.UnKnown) {
                                    onExitRoom("不支持此房间模式，已退出")
                                } else {
                                    handleMicSeatsChange(emptyList())
                                }
                            }
                        }

                        Const.RoomInfoChangeKey.MIC_MODE -> {
                            mutableState.withMutableSnapshot {
                                basicInfoState.value =
                                    basicInfo.copy(
                                        micMode = LiveMicMode.valueOf(message.getJsonInt("value", -1)),
                                        updateTimestamp = DateTimeUtils.currentTimeMillis(),
                                    )
                                extraInfoState.value =
                                    extraInfo.run {
                                        copy(
                                            reqUpMicCount = if (basicInfo.micMode != LiveMicMode.Request) 0 else reqUpMicCount,
                                            reqUpMicIng = if (basicInfo.micMode != LiveMicMode.Request) false else reqUpMicIng,
                                        )
                                    }
                            }
                        }

                        Const.RoomInfoChangeKey.TITLE -> {
                            mutableState.basicInfoState.value =
                                mutableState.basicInfo.copy(
                                    title = message.getJsonString("value", ""),
                                    updateTimestamp = DateTimeUtils.currentTimeMillis(),
                                )
                        }

                        Const.RoomInfoChangeKey.DESC -> {
                            mutableState.basicInfoState.value =
                                mutableState.basicInfo.copy(
                                    desc = message.getJsonString("value", ""),
                                    updateTimestamp = DateTimeUtils.currentTimeMillis(),
                                )
                        }

                        Const.RoomInfoChangeKey.TAGS -> {
                            mutableState.basicInfoState.value =
                                mutableState.basicInfo.copy(
                                    tagIds = message.getJsonValue<List<VoiceTag>>("value")?.map { it.id },
                                    updateTimestamp = DateTimeUtils.currentTimeMillis(),
                                )
                        }

                        Const.RoomInfoChangeKey.ROOM_BACKGROUND -> {
                            mutableState.basicInfoState.value =
                                mutableState.basicInfo.copy(
                                    background = message.getJsonString("value", ""),
                                    updateTimestamp = DateTimeUtils.currentTimeMillis(),
                                )
                        }
                    }
                }

                IMEvent.AGREE_MIC -> { // 同意上麦
                    message
                        .getJsonValue<BasicUser>("apply_user")
                        ?.takeIf {
                            it.sIsSelf
                        }?.also {
                            mutableState.extraInfoState.value = mutableState.extraInfo.copy(reqUpMicIng = false)
                        }
                }

                IMEvent.REFUSE_MIC -> { // 拒绝上麦
                    message
                        .getJsonValue<BasicUser>("apply_user")
                        ?.takeIf {
                            it.sIsSelf
                        }?.also {
                            mutableState.extraInfoState.value = mutableState.extraInfo.copy(reqUpMicIng = false)
                        }
                }

                IMEvent.MIC_APPLY_COUNT -> { // 申请上麦人数变
                    val requestCount = message.getJsonInt("mic_apply_cnt", 0)
                    mutableState.extraInfoState.value = mutableState.extraInfo.copy(reqUpMicCount = requestCount)
                }

                IMEvent.GRANT_ADMIN -> { // 设置管理员
                    message.getJsonValue<BasicUser>("user")?.id?.also {
                        mutableState.adminIdsState.add(it)
                    }
                }

                IMEvent.REVOKE_ADMIN -> { // 取消管理员
                    message.getJsonValue<BasicUser>("user")?.id?.also {
                        mutableState.adminIdsState.remove(it)
                    }
                }

                IMEvent.SEATS_CHANGE -> { // 麦位发生变化
                    message.getJsonValue<List<Seat>>("seats")?.also {
                        mutableState.withMutableSnapshot {
                            handleMicSeatsChange(it, true)
                        }
                    }
                }
            }
        }
    }

    // /////////////////////////////////////////////////////////////////////////
    // 消息处理
    // /////////////////////////////////////////////////////////////////////////

    // /////////////////////////////////////////////////////////////////////////
    // 事件处理
    // /////////////////////////////////////////////////////////////////////////
    private suspend fun handleEvent(event: RoomEvent) {
        when (event) {
            is RoomEvent.CollapseRoom -> {
                LiveRoomManager.collapseRoom(id)
            }

            is RoomEvent.ExitRoom -> {
                onExitRoom(null)
            }

            is RoomEvent.FollowUser -> {
                executeApiCallExpectingData {
                    UserApiService.instance.followUser(
                        mapOf("userid" to event.userId, "follow" to true.toString()),
                    )
                }.onSuccess {
                    mutableState.allUserMap[event.userId]?.also {
                        if (it.user is UserInfo) {
                            mutableState.allUserMapState[event.userId] =
                                it.copy(
                                    user =
                                        it.user.copy(
                                            extra = it.user.extra.copy(isFollowed = true),
                                        ),
                                )
                        }
                    }
                }
            }

            is RoomEvent.EditRoomInfo -> {
                val edit = event.edit
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.updateVoiceRoom(
                        buildMap {
                            put("live_house_id", id)
                            put("title", edit.title)
                            put("desc", edit.desc)
                            put("tag_ids", edit.tagIds.joinToString(","))
                        },
                    )
                }.onSuccess {
                    mutableState.basicInfoState.value =
                        mutableState.basicInfo.run {
                            copy(
                                title = edit.title,
                                desc = edit.desc,
                                tagIds = edit.tagIds,
                                updateTimestamp = DateTimeUtils.currentTimeMillis(),
                            )
                        }
                }
            }

            is RoomEvent.UpdateMicMode -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.updateVoiceRoom(
                        buildMap {
                            put("live_house_id", id)
                            put(Const.RoomInfoChangeKey.MIC_MODE, event.value)
                        },
                    )
                }
            }

            is RoomEvent.UpdateRoomMode -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.updateVoiceRoom(
                        buildMap {
                            put("live_house_id", id)
                            put(Const.RoomInfoChangeKey.ROOM_MODE, event.value)
                        },
                    )
                }
            }

            is RoomEvent.UpMic -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.joinVoiceRoomMic(
                        buildMap {
                            put("live_house_id", id)
                            if (event.index > -1) {
                                put("mic_order", event.index.plus(1).toString())
                            }
                        },
                    )
                }.onSuccess {
                    if (it.isEmpty()) { // 说明不是自由上麦模式
                        mutableState.extraInfoState.value = mutableState.extraInfo.copy(reqUpMicIng = true)
                    }
                }
            }

            is RoomEvent.DownMic -> {
                rtcHelper.downMic(null)
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.leaveVoiceRoomMic(
                        mapOf("live_house_id" to id),
                    )
                }
            }

            is RoomEvent.ToggleMic -> {
                val muted = rtcHelper.toggleMuteAudio()
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.muteVoiceRoomMic(
                        buildMap {
                            put("live_house_id", id)
                            put("muted", muted.toString())
                        },
                    )
                }
            }

            is RoomEvent.KickMic -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.kickVoiceRoomMic(
                        buildMap {
                            put("live_house_id", id)
                            put("target_user_id", event.userId)
                        },
                    )
                }
            }

            is RoomEvent.InviteUpMic -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.sendInviteVoiceRoomMic(
                        buildMap {
                            put("live_house_id", id)
                            put("invited_user_id", event.userId)
                        },
                    )
                }
            }

            is RoomEvent.HandleMicReq -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.allowVoiceRoomMicRequest(
                        buildMap {
                            put("live_house_id", id)
                            put("apply_user_id", event.userId)
                            put("agree", event.agree.toString())
                        },
                    )
                }
            }

            is RoomEvent.CancelMicReq -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.cancelVoiceRoomMicRequest(
                        buildMap {
                            put("live_house_id", id)
                        },
                    )
                }.onFailure {
                    mutableState.extraInfoState.value = mutableState.extraInfo.copy(reqUpMicIng = false)
                }
            }

            is RoomEvent.ClearLoveValue -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.resetLoveValue(
                        buildMap {
                            put("live_house_id", id)
                        },
                    )
                }
            }

            is RoomEvent.AgreeUpMic -> {
                executeApiCallExpectingData {
                    if (event.from == 1) {
                        VoiceRoomApiService.instance.joinInvitedVoiceRoomMic(
                            mapOf("live_house_id" to id),
                        )
                    } else {
                        VoiceRoomApiService.instance.takeSeatVoiceRoomMic(
                            mapOf("live_house_id" to id),
                        )
                    }
                }
            }

            is RoomEvent.SetBlackEvent -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.blockVoiceRoomUser(
                        mapOf(
                            "live_house_id" to id,
                            "userid" to event.userId,
                            "black" to event.black.toString(),
                        ),
                    )
                }.onSuccess { infoResp ->
                    if (event.black) {
                        mutableState.blackIdsState.add(event.userId)
                    } else {
                        mutableState.blackIdsState.remove(event.userId)
                    }
                }
            }

            is RoomEvent.SetAdminEvent -> {
                executeApiCallExpectingData {
                    VoiceRoomApiService.instance.setVoiceRoomAdmin(
                        mapOf(
                            "live_house_id" to id,
                            "userid" to event.userId,
                            "is_admin" to event.admin.toString(),
                        ),
                    )
                }.onSuccess { infoResp ->
                    if (event.admin) {
                        mutableState.adminIdsState.add(event.userId)
                    } else {
                        mutableState.adminIdsState.remove(event.userId)
                    }
                }
            }

            else -> Unit
        }
    }
    // /////////////////////////////////////////////////////////////////////////
    // 事件处理
    // /////////////////////////////////////////////////////////////////////////
}
