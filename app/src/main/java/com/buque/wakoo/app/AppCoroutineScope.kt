package com.buque.wakoo.app

import com.buque.wakoo.manager.EnvironmentManager.isProdRelease
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

val appCoroutineScope = CoroutineScope(Dispatchers.Main.immediate + SupervisorJob() + createSafeContext())

val invalidCoroutineScope =
    CoroutineScope(SupervisorJob() + Dispatchers.Main + createSafeContext()).apply {
        cancel()
    }

fun createSafeContext() =
    if (isProdRelease) {
        CoroutineExceptionHandler { _, throwable ->
//            Firebase.crashlytics.recordException(throwable)
        }
    } else {
        EmptyCoroutineContext
    }

fun createSafeCoroutineScope(context: CoroutineContext = Dispatchers.Main.immediate + SupervisorJob()) =
    CoroutineScope(context + createSafeContext())
