package com.buque.wakoo.app

object Const {
    object KVKey {
        const val FIRST_ENTER_PUBLISH = "first_enter_publish"

        const val FIRST_ENTER_VOICE_FEED = "first_enter_voice_feed"
    }

    object RoomInfoChangeKey {
        const val TITLE = "title"
        const val DESC = "desc"
        const val TAGS = "tags"
        const val ROOM_MODE = "room_mode"
        const val MIC_MODE = "occupy_mic_mode"
        const val ROOM_LOCK = "room_locked"
        const val ROOM_BACKGROUND = "room_background"
        const val GAME_START = "game_start"
        const val NOTICE = "notice"
    }

    object SavedState {
        const val DATA = "SavedState_data"
        const val LIVE_ROOM = "SavedState_LIVE_ROOM"
    }
}
