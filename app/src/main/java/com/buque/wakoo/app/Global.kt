package com.buque.wakoo.app

import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.manager.AccountManager
import com.tencent.mmkv.MMKV

val SelfUser: UserInfo?
    get() = AccountManager.selfUser

val IsLoggedIn: Boolean
    get() = AccountManager.isLoggedIn

private val InvalidKV: MMKV by lazy {
    MMKV.mmkvWithID("invalid_prefs")
}

/**
 * 设备相关的kv, 所有用户共享
 */
val DevicesKV: MMKV by lazy {
    MMKV.mmkvWithID("devices_prefs")
}

val currentUserKV: MMKV
    get() =
        SelfUser?.let {
            MMKV.mmkvWithID("users_${it.id}_prefs")
        } ?: InvalidKV

/**
 * 用户专属kv
 */
fun createUserKv(
    id: String,
    mmapId: String,
): MMKV = MMKV.mmkvWithID("users_${id}_${mmapId}_prefs")
