package com.buque.wakoo.app

import androidx.compose.ui.graphics.Color
import kotlinx.serialization.ContextualSerializer
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.InternalSerializationApi
import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.Json
import kotlinx.serialization.modules.SerializersModule
import kotlinx.serialization.serializer

val AppJson =
    Json {
        ignoreUnknownKeys = true // 忽略 API 返回中未在数据模型中定义的字段
        isLenient = true // 允许一些不严格的 JSON 格式
        prettyPrint = true // 开发时可以设置为 true 来格式化打印 JSON 日志，发布时建议 false
        coerceInputValues = true // 如果API返回的类型与模型不完全匹配（例如string对应int），尝试转换
        encodeDefaults = true
        explicitNulls = false
        serializersModule =
            SerializersModule {
                contextual(Color::class, ColorAsULongSerializer)
                contextual(Any::class, DynamicLookupSerializer)
            }
    }

object ColorAsULongSerializer : KSerializer<Color> {
    // 描述符告诉序列化框架，我们将把 Color 序列化为一个长整型（LONG）
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("Color", PrimitiveKind.LONG)

    // 序列化：将 Color 对象转换为 Long
    override fun serialize(
        encoder: Encoder,
        value: Color,
    ) {
        // Color.value 是一个 ULong，我们将其转换为 Long 进行编码
        encoder.encodeLong(value.value.toLong())
    }

    // 反序列化：将 Long 转换回 Color 对象
    override fun deserialize(decoder: Decoder): Color {
        // 解码 Long，然后转换为 ULong，最后创建 Color 实例
        return Color(value = decoder.decodeLong().toULong())
    }
}

@OptIn(ExperimentalSerializationApi::class)
internal object DynamicLookupSerializer : KSerializer<Any> {
    override val descriptor: SerialDescriptor =
        ContextualSerializer(Any::class, null, emptyArray()).descriptor

    @Suppress("UNCHECKED_CAST")
    @OptIn(InternalSerializationApi::class)
    override fun serialize(
        encoder: Encoder,
        value: Any,
    ) {
        val actualSerializer =
            encoder.serializersModule.getContextual(value::class) ?: value::class.serializer()
        encoder.encodeSerializableValue(actualSerializer as KSerializer<Any>, value)
    }

    override fun deserialize(decoder: Decoder): Any {
        error("Unsupported")
    }
}
