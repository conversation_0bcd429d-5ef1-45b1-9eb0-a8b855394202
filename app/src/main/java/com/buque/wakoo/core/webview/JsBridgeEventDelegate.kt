package com.buque.wakoo.core.webview

import android.content.Context
import com.buque.wakoo.ext.showToast
import com.buque.webview.handler.BaseBridgeHandler
import com.smallbuer.jsbridge.core.CallBackFunction
import kotlinx.serialization.json.JsonObject

interface JsBridgeEventDelegate {
    fun onApiError(code: Int, msg: String) {
        when (code) {
            -11 -> {//need charge coin
                showToast(msg)
            }

            -12 -> {//need vip

            }

            else -> {}
        }
    }

    fun onFinish()

    fun onSetTitle(title: String?) {}

    fun onImmersive(statusBarVisible: Boolean, navigationBarVisible: Boolean) {}

    fun onGetStatusBarHeight(context: Context): Float = 0f

    fun onGetNavigationBarHeight(context: Context): Float = 0f

    fun onRequestAndroidPermission(bridgeHandler: BaseBridgeHandler,permissions:Array<String>?,callback:CallBackFunction){}

    fun onPay(context: Context,productId:String,fkLink:String,fkType:Int,orderType:Int){}

    fun onPlayVideoEffect(context: Context,effect:String){}

    fun onJoinChannel(context: Context,channel:String){}

    fun onJumpAppPage(context: Context,jsonObject: JsonObject){}

}