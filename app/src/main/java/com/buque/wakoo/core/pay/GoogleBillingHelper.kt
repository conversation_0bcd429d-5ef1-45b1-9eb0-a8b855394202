package com.buque.wakoo.core.pay

import android.app.Activity
import android.content.Context
import android.util.ArrayMap
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.SkuDetails
import com.android.billingclient.api.SkuDetailsParams
import com.android.billingclient.api.queryProductDetails
import com.android.billingclient.api.querySkuDetails
import com.buque.wakoo.BuildConfig
import com.buque.wakoo.R
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.consts.Pay
import com.buque.wakoo.network.api.bean.ChargeDataEntity
import com.buque.wakoo.network.api.bean.VipInfo
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.utils.AsyncValue
import com.buque.wakoo.utils.AwaitContinuation
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.awaitContinuation
import com.buque.wakoo.utils.isSuccess
import com.buque.wakoo.utils.toAsyncData
import com.buque.wakoo.utils.toAsyncError
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


interface IPurchasesUpdatedListener {
    fun onPurchasesUpdated(completed: Boolean = false)

    fun onPurchasesMessage(message: String)
}


object GoogleBillingHelper {

    private const val TAG = "GoogleBillingHelper"
    private val context: Context
        get() = WakooApplication.instance

    private val purchasesUpdatedListener = PurchasesUpdatedListener { billingResult, purchases ->
        currentOrder?.apply {
            val (orderNo, productId) = this
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK && purchases != null) {
                for (purchase in purchases) {
                    if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED && purchase.products.firstOrNull() == productId && purchase.accountIdentifiers?.obfuscatedAccountId == orderNo) {
                        orderCompleted(orderNo, productId, purchase.purchaseToken)
                    }
                }
            } else if (billingResult.responseCode == BillingClient.BillingResponseCode.USER_CANCELED) {
                // Handle an error caused by a user cancelling the purchase flow.
                listener?.onPurchasesMessage(
                    context.getString(R.string.cancel_pay)
                )
                appCoroutineScope.launch {
                    repository.orderCancel(orderNo)
                }
            } else {
                if (BuildConfig.DEBUG) {
                    listener?.onPurchasesMessage(billingResult.debugMessage)
                }
                // Handle any other error codes.
                appCoroutineScope.launch {
                    repository.orderCancel(orderNo)
                }
            }
        }
    }

    private val billingClient by lazy {
        BillingClient.newBuilder(context)
            .setListener(purchasesUpdatedListener)
            .enablePendingPurchases()
            .build()
    }

    private var readyContinuation: AwaitContinuation? = null

    private val productDetailsMap = ArrayMap<String, GoogleGood>()

    private val repository = GlobalRepository.walletRepo

    private val _chargeItemsFlow = MutableStateFlow<AsyncValue<ChargeDataEntity>>(AsyncValue.Loading())
    val chargeItemsFlow = _chargeItemsFlow.asStateFlow()

    private val _vipInfoFlow = MutableStateFlow<AsyncValue<VipInfo>>(AsyncValue.Loading())
    val vipInfoFlow = _vipInfoFlow.asStateFlow()

    private var currentOrder: Pair<String, String>? = null

    private var listener: IPurchasesUpdatedListener? = null

    private var isUnavailable = false

    fun bindOrder(orderNo: String, productId: String, listener: IPurchasesUpdatedListener?) {
        currentOrder = orderNo to productId
        GoogleBillingHelper.listener = listener
    }

    fun clearListener() {
        listener = null
    }

    @JvmStatic
    fun initGoogleBilling() {
        connection()
    }

    fun isReady() = billingClient.isReady


    private fun connection() {
        billingClient.startConnection(object : BillingClientStateListener {
            override fun onBillingSetupFinished(billingResult: BillingResult) {
                when (billingResult.responseCode) {
                    BillingClient.BillingResponseCode.OK -> {
                        logI("google支付连接成功")
                        readyContinuation?.resume()
                    }

                    BillingClient.BillingResponseCode.BILLING_UNAVAILABLE -> {
                        reportBillingError("google支付服务不可用，code: ${billingResult.responseCode}")
                        isUnavailable = true
                    }

                    else -> {
                        reportBillingError("google支付服务连接出错，code: ${billingResult.responseCode}")
                    }
                }
            }

            override fun onBillingServiceDisconnected() {
                val listener = this
                appCoroutineScope.launch {
                    delay(1000)
                    billingClient.startConnection(listener)
                }
            }
        })
    }

    private suspend fun awaitReady() {
        if (!isReady()) {
            if (!isUnavailable) { // 结算服务不可用
                readyContinuation = awaitContinuation()
                readyContinuation?.suspendUntilWithTimeout(5000)
            }
        }
    }

    private suspend fun processPurchases(products: List<BillingProduct>) {
        if (products.isEmpty()) {
            return
        }
        if (!isReady()) {
            return
        }
        val cached = products.all {
            productDetailsMap.contains(it.id)
        }
        if (cached) {
            return
        }
        if (billingClient.isFeatureSupported(BillingClient.FeatureType.PRODUCT_DETAILS).responseCode == BillingClient.BillingResponseCode.OK) {
            val productList = products.map {
                QueryProductDetailsParams.Product.newBuilder()
                    .setProductId(it.id)
                    .setProductType(it.type)
                    .build()
            }
            val params = QueryProductDetailsParams.newBuilder()
            params.setProductList(productList)

            // leverage queryProductDetails Kotlin extension function
            val productDetailsResult = withContext(Dispatchers.IO) {
                billingClient.queryProductDetails(params.build())
            }
            logI("productDetailsResult：$productDetailsResult")
            // Process the result.
            if (productDetailsResult.billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                productDetailsResult.productDetailsList?.forEach { item ->
                    val product = if (item.productType == BillingClient.ProductType.INAPP) {
                        item.oneTimePurchaseOfferDetails?.let {
                            logI("goodItem1, id:${item.productId}, type:${item.productType}, priceCurrencyCode:${it.priceCurrencyCode}, formattedPrice:${it.formattedPrice}")
                            BillingProduct(item.productId, item.productType, it.formattedPrice)
                        }
                    } else {
                        item.subscriptionOfferDetails?.firstOrNull()?.pricingPhases?.pricingPhaseList?.firstOrNull()
                            ?.let {
                                logI("goodItem2, id:${item.productId}, type:${item.productType}, priceCurrencyCode:${it.priceCurrencyCode}, formattedPrice:${it.formattedPrice}")
                                BillingProduct(item.productId, item.productType, it.formattedPrice)
                            }
                    }
                    logI("cache goodItem 1, id: ${item.productId}, product: $product")
                    productDetailsMap[item.productId] = GoogleGood(item, product)
                }
            }
        } else {
            val type = products.first().type
            val skuList = products.map {
                it.id
            }
            val params = if (type == BillingClient.ProductType.INAPP) {
                SkuDetailsParams.newBuilder()
                    .setType(BillingClient.SkuType.INAPP)
                    .setSkusList(skuList)
                    .build()
            } else {
                SkuDetailsParams.newBuilder()
                    .setType(BillingClient.SkuType.SUBS)
                    .setSkusList(skuList)
                    .build()
            }
            val skuDetailsResult = withContext(Dispatchers.IO) {
                billingClient.querySkuDetails(params)
            }
            logI("skuDetailsResult：$skuDetailsResult")
            if (skuDetailsResult.billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                skuDetailsResult.skuDetailsList?.forEach { item ->
                    logI("goodItem3, id:${item.sku}, type:${type}, priceCurrencyCode:${item.priceCurrencyCode}, formattedPrice:${item.price}, originalPrice:${item.originalPrice}")
                    val product = BillingProduct(item.sku, type, item.price)
                    logI("cache goodItem 2, id: ${item.sku}, product: $product")
                    productDetailsMap[item.sku] = GoogleGood(item, product)
                }
            }
        }
    }

    fun checkProduct(productId: String): Boolean {
        if (!isReady()) {
            return false
        }
        productDetailsMap[productId] ?: kotlin.run {
            return false
        }
        return true
    }

    fun getProductPrice(productId: String): String? {
        return productDetailsMap[productId]?.product?.price
    }

    fun buy(activity: Activity, productId: String, orderNo: String): Int {
        if (!isReady()) {
            return -100
        }
        val good = productDetailsMap[productId] ?: kotlin.run {
            return -101
        }

        val productDetails = good.details

        val billingFlowParams = if (productDetails is ProductDetails) {
            // An activity reference from which the billing flow will be launched.
            val productDetailsParamsList = listOf(
                BillingFlowParams.ProductDetailsParams.newBuilder()
                    // retrieve a value for "productDetails" by calling queryProductDetailsAsync()
                    .setProductDetails(productDetails)
                    .apply {
                        // to get an offer token, call ProductDetails.subscriptionOfferDetails()
                        // for a list of offers that are available to the user
                        if (productDetails.productType == BillingClient.ProductType.SUBS) {
                            productDetails.subscriptionOfferDetails?.firstOrNull()?.offerToken?.also {
                                setOfferToken(it)
                            }
                        }
                    }
                    .build()
            )

            BillingFlowParams.newBuilder()
                .setProductDetailsParamsList(productDetailsParamsList)
                .setObfuscatedAccountId(orderNo)
                .build()
        } else {
            BillingFlowParams.newBuilder()
                .setSkuDetails(productDetails as SkuDetails)
                .setObfuscatedAccountId(orderNo)
                .build()
        }

        // Launch the billing flow
        return billingClient.launchBillingFlow(activity, billingFlowParams).responseCode
    }

    private fun orderCompleted(orderNo: String, productId: String, token: String) {
        appCoroutineScope.launch {
            listener?.onPurchasesUpdated(false)
            try {
                repository.orderCompleted(orderNo, productId, token).onFailure {
                    listener?.onPurchasesMessage(it.message.orEmpty())
                }.onSuccess {
                    currentOrder = null
                    listener?.onPurchasesMessage(
                        context.getString(R.string.pay_success)
                    )
                    //刷新用户信息，mineTab自动刷新过了
                }
            } finally {
                listener?.onPurchasesUpdated(true)
            }
        }
    }

    suspend fun fetchRechargeList(): Result<ChargeDataEntity> {

        return repository.fetchRechargeList().onSuccess { data ->
            val chargeItems = data.chargeItems
            fun run() {
                chargeItems.onEach {
                    val price = productDetailsMap[it.productId]?.product?.price
                    if (!price.isNullOrBlank()) {
                        logI("金币('${it.productId}') google pay 原始价格: $price")
                    }
                }
                _chargeItemsFlow.value = data.toAsyncData()
            }

            run()

            appCoroutineScope.launch {
                chargeItems.map {
                    inAppProduct(it.productId, it.dollar)
                }.also {
                    awaitReady()
                    processPurchases(it)
                }
                run()
            }
        }.onFailure {
            val v = _chargeItemsFlow.value
            if(!v.isSuccess()){
                _chargeItemsFlow.value = it.toAsyncError()
            }
        }
    }

    suspend fun fetchActivateVipConfig(): Result<VipInfo> {
        return repository.getActivateVipConfig().onSuccess { vipInfo ->

            fun run() {
                vipInfo.goods.find {
                    it.fkChannel == 1
                }?.activateOptions?.onEach {
                    val price = productDetailsMap[it.productId]?.product?.price
                    if (!price.isNullOrBlank()) {
                        logI("vip('${it.productId}') google pay 原始价格: $price")
                        it.setGooglePrice(price)
                    }
                }

                _vipInfoFlow.value = vipInfo.copy(
                    goods = vipInfo.goods.filter {
                        it.fkChannel != Pay.CHANNEL_APPLE_IAP
                    }
                ).toAsyncData()
            }

            run()

            appCoroutineScope.launch {
                vipInfo.goods.find {
                    it.fkChannel == Pay.CHANNEL_GOOGLE_IAP
                }?.activateOptions?.groupBy({
                    it.fkType
                }) {
                    if (it.fkType == Pay.CALL_TYPE_APPLE_SDK) {
                        subsProduct(it.productId, it.dollar)
                    } else {
                        inAppProduct(it.productId, it.dollar)
                    }
                }?.also { map ->
                    awaitReady()
                    for (list in map.values) {
                        processPurchases(list)
                    }
                }
                run()
            }
        }
    }


    private fun reportBillingError(errorMsg: String) {
        logE(errorMsg)
//        appCoroutineScope.launch {
//            repository.reportBillingError(errorMsg)
//        }
    }

    private fun logE(errorMsg: String) {
        LogUtils.e("billing:$errorMsg")
    }

    fun logI(s: String) {
        LogUtils.i("billing:$s")
    }
}

private data class BillingProduct constructor(val id: String, val type: String, val price: String)

private data class GoogleGood(
    val details: Any,
    val product: BillingProduct?,
)

private fun inAppProduct(id: String, price: String): BillingProduct {
    return BillingProduct(id, BillingClient.ProductType.INAPP, price)
}

private fun subsProduct(id: String, price: String): BillingProduct {
    return BillingProduct(id, BillingClient.ProductType.SUBS, price)
}