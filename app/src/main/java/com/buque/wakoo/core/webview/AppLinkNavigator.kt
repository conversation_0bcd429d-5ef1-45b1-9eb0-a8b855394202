package com.buque.wakoo.core.webview

import android.net.Uri
import androidx.core.net.toUri
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.navigation.AppNavController
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.DialogController
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.ui.dialog.AnyPopDialogProperties
import com.buque.wakoo.ui.screens.WebViewDialogContent
import com.buque.wakoo.utils.LogUtils
import java.net.URLDecoder
import kotlin.reflect.full.companionObjectInstance
import kotlin.reflect.full.memberFunctions

object AppLinkNavigator {
    private val mapRouteObject = mutableMapOf<String, (Uri) -> AppNavKey>()

    init {
        val routeClasses = Route::class.nestedClasses
        try {
            routeClasses.forEach { rc ->
                val path = "/${camelToUnderscore(rc.simpleName.orEmpty())}"
                // data object
                if (rc.objectInstance != null) {
                    mapRouteObject[path] = {
                        rc.objectInstance as AppNavKey
                    }
                } else {
                    val comp = rc.companionObjectInstance
                    var func: ((Uri) -> AppNavKey)? = null
                    if (comp != null) {
                        val funFromUri = comp::class.memberFunctions.firstOrNull { it.name == "fromUri" }
                        if (funFromUri != null) {
                            func = {
                                try {
                                    funFromUri.call(comp, it) as AppNavKey
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                    Route.AboutApp
                                }
                            }
                        }
                    }
                    if (func == null) {
                        func = {
                            throw IllegalAccessError("${rc.simpleName} 类未找到 companion.fromUri Method")
                        }
                    }
                    mapRouteObject[path] = func
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun initialize() = Unit

    fun isSupport(link: String): Boolean = link.startsWith("wakoo://") || link.startsWith("http://") || link.startsWith("https://")

    fun go(
        link: String,
        appNavController: AppNavController,
        dialogController: DialogController,
    ) {
        if (link.startsWith("http://") || link.startsWith("https://")) {
            appNavController.push(Route.Web(link))
            return
        }
        if (link.startsWith("wakoo://")) {
            try {
                val uri = link.toUri()
                val path = uri.path ?: return

                if (handleSpecialPath(appNavController, dialogController, uri)) {
                    return
                }
                mapRouteObject[path]?.invoke(uri)?.also {
                    appNavController.push(it)
                } ?: run {
                    LogUtils.e("not support:$link")
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        } else {
            LogUtils.e("not support:$link")
        }
    }

    private fun handleSpecialPath(
        appNavController: AppNavController,
        dialogController: DialogController,
        uri: Uri,
    ): Boolean =
        when (uri.path.orEmpty()) {
            "/web_frame" -> {
                try {
                    val json = uri.getQueryParameter("info")?.let { URLDecoder.decode(it) } ?: "{}"
                    LogUtils.d("web frame:$json")
                    val webFrameInfo = AppJson.decodeFromString<WebFrameInfo>(json)
                    dialogController.easyPost(
                        dialogProperties =
                            AnyPopDialogProperties(
                                useSystemDialog = false,
                                dismissOnClickOutside = webFrameInfo.cancelable,
                                dismissOnBackPress = webFrameInfo.cancelable,
                                useCustomAnimation = webFrameInfo.gravity == "bottom",
                            ),
                    ) {
                        WebViewDialogContent(webFrameInfo, onOpenLink = { link ->
                            go(link, appNavController, dialogController)
                        }, onOpenPage = {
                            appNavController.push(it)
                        }) {
                            this.dismiss()
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                true
            }

            "/webview" -> {
                val url = uri.getQueryParameter("url")?.let { URLDecoder.decode(it) }.orEmpty()
                if (url.isNotEmpty()) {
                    appNavController.push(Route.Web(url))
                } else {
                    showToast("WARNING:URL IS EMPTY")
                }
                true
            }

            else -> {
                false
            }
        }

    private fun camelToUnderscore(input: String): String {
        // 正则匹配大写字母，并在前面插入下划线，最后统一转小写
        return input.replace(Regex("([A-Z])"), "_$1").trimStart('_').lowercase()
    }
}
