package com.buque.wakoo.core.webview

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.util.ArrayMap
import androidx.core.net.toUri
import com.buque.wakoo.BuildConfig
import com.buque.wakoo.core.TaskHelper
import com.buque.wakoo.core.webview.WebBridgeHandler.globalCacheEnable
import com.buque.wakoo.ext.showToast
import com.buque.webview.handler.BaseBridgeHandler
import com.buque.webview.handler.HandlerName
import com.buque.webview.utils.NetworkManager
import com.smallbuer.jsbridge.core.Bridge
import com.smallbuer.jsbridge.core.BridgeHandler
import com.smallbuer.jsbridge.core.CallBackFunction
import kotlinx.serialization.json.JsonObject

object WebBridgeHandler {

    var globalCacheEnable = true

    private var inited = false

    fun init() {
        if (inited) {
            return
        }
        if (BuildConfig.DEBUG) {
            Bridge.INSTANCE.openLog()
        }
        val handlerMap = ArrayMap<String, BridgeHandler>()
        handlerMap[HandlerName.PLATFORM] = AppPlatformBridgeHandler()
        handlerMap[HandlerName.CHANNEL] = AppChannelBridgeHandler()
        handlerMap[HandlerName.VERSION] = AppVersionBridgeHandler()
        handlerMap[HandlerName.TOAST] = ToastBridgeHandler()
        handlerMap[HandlerName.JUMP_TASK] = TaskBridgeHandler()
        handlerMap[HandlerName.GO_TO_PUBLIC] = GotoCpPublishHandler()
        handlerMap[HandlerName.CACHE_ENABLE] = CacheEnableBridgeHandler()
        handlerMap[HandlerName.EVENT_COPY] = CopyTextHandler()
        handlerMap[HandlerName.APP_ISM_DEVICE_ID] = ISMHandler()
        handlerMap[HandlerName.APP_IS_USING_VPN] = object : BaseBridgeHandler() {
            override fun handlerV2(context: Context, data: JsonObject, callback: CallBackFunction) {
                callback.sendSuccess(context, "${NetworkManager.isUsingVPN() || NetworkManager.isUsingProxy()}")
            }
        }
        handlerMap[HandlerName.APP_OPEN_BROWSER] = object : BaseBridgeHandler() {
            override fun handlerV2(context: Context, data: JsonObject, callback: CallBackFunction) {
                val link = data.getString("link") ?: return
                val intent = Intent(Intent.ACTION_VIEW, link.toUri())
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
            }
        }
        Bridge.INSTANCE.registerHandler(handlerMap)
        inited = true
    }
}


class ISMHandler : BaseBridgeHandler() {
    override fun handlerV2(context: Context, data: JsonObject, callback: CallBackFunction) {
        // FIXME: 数美 boxid
        callback.sendSuccess(context, "hahah")
    }
}

class CopyTextHandler : BaseBridgeHandler() {
    override fun handlerV2(context: Context, data: JsonObject, callback: CallBackFunction) {
        val content = data.getStringOrElse("content", "")
        if (content.isNotEmpty()) {
            val service = context.getSystemService(ClipboardManager::class.java)
            service?.setPrimaryClip(ClipData.newPlainText("app", content))
        }
    }
}


class ToastBridgeHandler : BaseBridgeHandler() {

    override fun handlerV2(context: Context, data: JsonObject, callback: CallBackFunction) {
        data.getString("content")?.also {
            showToast(it)
        }
    }
}

class AppVersionBridgeHandler : BaseBridgeHandler() {

    override fun handlerV2(context: Context, data: JsonObject, callback: CallBackFunction) {
        callback.sendSuccess(context, BuildConfig.VERSION_NAME)
    }
}


class AppPlatformBridgeHandler : BaseBridgeHandler() {

    override fun handlerV2(context: Context, data: JsonObject, callback: CallBackFunction) {
        callback.sendSuccess(context, "Android")
    }
}

class AppChannelBridgeHandler : BaseBridgeHandler() {

    override fun handlerV2(context: Context, data: JsonObject, callback: CallBackFunction) {
        callback.sendSuccess(context, BuildConfig.FLAVOR)
    }
}

class TaskBridgeHandler : BaseBridgeHandler() {

    override fun handlerV2(context: Context, data: JsonObject, callback: CallBackFunction) {
        when (data.getIntOrElse("jump_num", 0)) {
            1 -> TaskHelper.routerMessageTask()
            2 -> TaskHelper.routerChatWithPrivateRoomInvite()
            3 -> TaskHelper.routerChatWithCpInvite()
            else -> {
                callback.sendFailure(context, msg = "not support")
                return
            }
        }
        callback.sendSuccess(context)
    }
}

class GotoCpPublishHandler : BaseBridgeHandler() {

    override fun handlerV2(context: Context, data: JsonObject, callback: CallBackFunction) {
        //跳转到部落
    }
}

class CacheEnableBridgeHandler : BaseBridgeHandler() {

    override fun handlerV2(context: Context, data: JsonObject, callback: CallBackFunction) {
        globalCacheEnable = data.getBooleanOrElse("enable", false)
    }
}

