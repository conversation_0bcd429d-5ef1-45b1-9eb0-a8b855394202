package com.buque.wakoo.viewmodel.chatgroup

import com.buque.wakoo.bean.chatgroup.ChatGroupMember
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.viewmodel.BasicListPaginateViewModel

class ChatGroupMemberListViewModel(
    val groupId: String,
    val roles: String,
) : BasicListPaginateViewModel<Int, ChatGroupMember>() {
    private val repo = GlobalRepository.chatGroupRepo

    override suspend fun loadData(
        pageKey: Int,
        pageSize: Int,
    ): Result<List<ChatGroupMember>> = repo.getRoleMemberList(groupId, roles, pageKey)

    override fun getFirstPageKey(dataKey: Any): Int = 0

    override fun getNextPageKey(
        cState: CState<List<ChatGroupMember>>,
        dataKey: Any,
        pageKey: Int?,
    ): Int = cState.dataOrNull?.lastOrNull()?.memberId ?: -1

    override fun getDistinctSelector(): (ChatGroupMember) -> String =
        {
            it.memberId.toString()
        }

    suspend fun setAdmin(
        member: ChatGroupMember,
        active: Boolean,
    ) = repo
        .setAdmin(groupId, member.memberId.toString(), active)
        .onSuccess { obj ->
            val st = getListState()
            if (st is CState.Success) {
                val l = st.data
                l.remove(member)
            }
        }

    suspend fun kickOutMember(member: ChatGroupMember)=repo.kickOutMember(groupId,member.memberId.toString())
        .onSuccess {
            remove(member)
        }

    fun remove(item: ChatGroupMember) {
        val st = getListState()
        if (st is CState.Success) {
            val l = st.data
            l.remove(item)
        }
    }
}
