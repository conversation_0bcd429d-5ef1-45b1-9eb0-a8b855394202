package com.buque.wakoo.viewmodel.chatgroup

import com.buque.wakoo.bean.chatgroup.JoinApplyItem
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.ui.widget.state.stateWhen
import com.buque.wakoo.viewmodel.BasicListPaginateViewModel

class ChatGroupApplyJoinListViewModel(
    val groupId: String,
) : BasicListPaginateViewModel<Int, JoinApplyItem>() {
    private val repo = GlobalRepository.chatGroupRepo

    override fun getFirstPageKey(dataKey: Any): Int = 0

    override fun getNextPageKey(
        cState: CState<List<JoinApplyItem>>,
        dataKey: Any,
        pageKey: Int?,
    ): Int = cState.dataOrNull?.lastOrNull()?.applyId ?: -1

    override fun getDistinctSelector(): (JoinApplyItem) -> String =
        {
            it.applyId.toString()
        }

    override suspend fun loadData(
        pageKey: Int,
        pageSize: Int,
    ): Result<List<JoinApplyItem>> = repo.getJoinApplyList(groupId, pageKey)

    suspend fun handleApply(
        userId: String,
        agree: Boolean,
    ) = repo
        .handleApply(groupId, userId, agree)
        .onSuccess {
            val st = getListState()
            val newSt =
                st.stateWhen(onSuccess = {
                    it.retainAll(it.filter { it.user.id != userId })
                    CState.Success(it)
                }) {
                    st
                }
            setCState(Unit, newSt)
        }
}
