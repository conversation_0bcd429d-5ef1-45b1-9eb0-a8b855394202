package com.buque.wakoo.viewmodel.liveroom

import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.app.Const
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.bean.BasicUser
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.bean.SendParams
import com.buque.wakoo.im.isSent
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.UIMessageEntry
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.message.types.UCGiftMessage
import com.buque.wakoo.im_business.message.types.UCTextMessage
import com.buque.wakoo.im_business.message.types.UCUnknownMessage
import com.buque.wakoo.im_business.message.ui.entry.LiveRoomSystemMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.LiveRoomUserSystemMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.MsgUIEntry
import com.buque.wakoo.im_business.message.ui.entry.NoProviderEntry
import com.buque.wakoo.im_business.message.ui.entry.RecallMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.TextMsgEntry
import com.buque.wakoo.im_business.viewmodel.IMMessageConfig
import com.buque.wakoo.im_business.viewmodel.ListStateMessageViewModel
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.ui.screens.liveroom.InputTextState
import com.buque.wakoo.ui.screens.liveroom.LiveMicMode
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.panel.LiveInviteUpMicPanel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

class LiveRoomViewModel(
    basicInfo: BasicRoomInfo,
) : ListStateMessageViewModel(
        sendParams = SendParams(basicInfo.id, ConversationType.CHATROOM),
        config =
            IMMessageConfig(
                loadHistoryEnable = LiveRoomManager.roomHasCollapseHistory(basicInfo.id),
                insetTimeLine = false,
                autoCleanUnreadCount = false,
                autoScrollLatestAndFirstVisibleCount = 4,
                smoothScroll = false,
            ),
    ) {
    class Factory(
        private val basicInfo: BasicRoomInfo,
    ) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T = LiveRoomViewModel(basicInfo) as T
    }

    private val repository = LiveRoomManager.createLiveRoomRepository(basicInfo)

    val roomId get() = roomInfoState.id

    val roomInfoState get() = repository.roomInfoState

    private val inputTextStateFlow = MutableStateFlow<InputTextState>(InputTextState.Hidden)

    val inputTextState: State<InputTextState>
        @Composable get() = inputTextStateFlow.collectAsStateWithLifecycle()

    fun setInputTextState(state: InputTextState) {
        inputTextStateFlow.value = state
    }

    fun refreshRoomInfo() {
        repository.refreshRoomInfo()
    }

    fun sendEvent(event: RoomEvent) {
        repository.sendEvent(event)
    }

    fun rtcUpMic() {
        repository.rtcHelper.upMic(null)
    }

    /**
     * 收到的新消息
     */
    override fun onRecvNewCustomMessage(
        message: UCCustomMessage,
        offline: Boolean,
    ) {
        super.onRecvNewCustomMessage(message, offline)
        viewModelScope.launch {
            when (message.cmd) {
                IMEvent.INVITE_MIC -> { // 邀请上麦
                    if (message.getJsonValue<UserResponse>("invited_user")?.id == SelfUser?.id) {
                        message.getJsonValue<UserResponse>("admin_user")?.also { response ->
                            sendEvent(
                                RoomEvent.PanelDialog {
                                    LiveInviteUpMicPanel(
                                        user = BasicUser.fromResponse(response),
                                        roomInfoState = roomInfoState,
                                    )
                                },
                            )
                        }
                    }
                }

                IMEvent.AGREE_MIC -> { // 同意上麦
                    showToast("你已被同意上麦")
                    sendEvent(RoomEvent.AgreeUpMic(2))
                }

                IMEvent.REFUSE_MIC -> { // 拒绝上麦
                    showToast("你的上麦请求被拒绝")
                }
            }
        }
    }

    /**
     * 过滤出需要显示的消息
     */
    override fun filterShownMessage(message: UCInstanceMessage): Boolean {
        if (message is UCUnknownMessage) {
            return false
        }

        if (message !is UCCustomMessage) {
            return message is UCTextMessage || message is UCGiftMessage
        }

        var filter = false
        message.apply {
            when (cmd) {
                IMEvent.USER_ENTRANCE -> {
                    message.getJsonValue<UserResponse>("user")?.also {
                        filter = !it.isHidden
                    }
                }

                IMEvent.ROOM_SETTINGS -> {
                    filter =
                        when (message.getJsonString("settings_name")) {
                            Const.RoomInfoChangeKey.ROOM_MODE,
                            Const.RoomInfoChangeKey.MIC_MODE,
                            -> true

                            else -> false
                        }
                }

                IMEvent.AGREE_MIC -> {
                    val sendUser = message.getJsonValue<UserResponse>("admin_user")
                    val targetUser = message.getJsonValue<UserResponse>("apply_user")
                    sendUser?.also {
                        if (targetUser?.id == SelfUser?.id) {
                            filter = true
                        }
                    }
                }

                IMEvent.REFUSE_MIC -> {
                    val sendUser = message.getJsonValue<UserResponse>("admin_user")
                    val targetUser = message.getJsonValue<UserResponse>("apply_user")
                    sendUser?.also {
                        if (targetUser?.id == SelfUser?.id) {
                            filter = true
                        }
                    }
                }
            }
        }

        return filter
    }

    override fun createRecallUIMessageEntry(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
        oldEntry: UIMessageEntry?,
    ): MsgUIEntry {
        val user = message.user
        return RecallMsgEntry(
            buildAnnotatedString {
                if (message.isSent) {
                    append("你撤回了一条消息")
                } else if (user != null) {
                    withStyle(SpanStyle(color = Color(0xFFFFD683))) {
                        append(user.name)
                    }
                    append(" ")
                    append("撤回了一条消息")
                } else {
                    withStyle(SpanStyle(color = Color(0xFFFFD683))) {
                        append("系统")
                    }
                    append(" ")
                    append("撤回了一条消息")
                }
            },
        )
    }

    override fun sceneConvertToUIMessageEntry(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
        oldEntry: UIMessageEntry?,
    ): MsgUIEntry? =
        when (message) {
            is UCTextMessage -> {
                TextMsgEntry(message.text, message.user!!)
            }

            is UCCustomMessage -> {
                when (message.cmd) {
                    IMEvent.USER_ENTRANCE -> {
                        val user = message.getJsonValue<UserResponse>("user")!!
                        LiveRoomUserSystemMsgEntry(
                            content =
                                buildAnnotatedString {
                                    withStyle(SpanStyle(color = Color(0xFFFFD683))) {
                                        append(user.nickname)
                                    }
                                    append(" ")
                                    append("进入了房间")
                                },
                            user = BasicUser.fromResponse(user),
                        )
                    }

                    IMEvent.ROOM_SETTINGS -> {
                        when (message.getJsonString("settings_name")) {
                            Const.RoomInfoChangeKey.ROOM_MODE -> {
                                LiveRoomSystemMsgEntry(
                                    buildAnnotatedString {
                                        append("房间已切换为")
                                        withStyle(SpanStyle(color = Color(0xFFFFD683))) {
                                            append(LiveRoomMode.valueOf(message.getJsonInt("value", -1)).stringOf())
                                        }
                                    },
                                )
                            }

                            Const.RoomInfoChangeKey.MIC_MODE -> {
                                LiveRoomSystemMsgEntry(
                                    buildAnnotatedString {
                                        append("麦位已切换为")
                                        withStyle(SpanStyle(color = Color(0xFFFFD683))) {
                                            append(LiveMicMode.valueOf(message.getJsonInt("value", -1)).stringOf())
                                        }
                                    },
                                )
                            }

                            else -> NoProviderEntry
                        }
                    }

                    IMEvent.AGREE_MIC -> {
                        val sendUser = message.getJsonValue<UserResponse>("admin_user")
                        LiveRoomSystemMsgEntry(
                            buildAnnotatedString {
                                withStyle(SpanStyle(color = Color(0xFFFFD683))) {
                                    append(sendUser?.nickname)
                                }
                                append(" ")
                                append("同意了你的上麦申请")
                            },
                        )
                    }

                    IMEvent.REFUSE_MIC -> {
                        val sendUser = message.getJsonValue<UserResponse>("admin_user")
                        LiveRoomSystemMsgEntry(
                            buildAnnotatedString {
                                withStyle(SpanStyle(color = Color(0xFFFFD683))) {
                                    append(sendUser?.nickname)
                                }
                                append(" ")
                                append("拒绝了你的上麦申请")
                            },
                        )
                    }

                    else -> {
                        null
                    }
                }
            }

            else -> null
        }
}

//                IMEvent.USER_LEVEL_CHANGE -> {
//                    message.getJsonValue<UserResponse>("user")?.also {
//                        filter = true
//                    }
//                }

//                // cp邀请
//                IMEvent.GIVE_CONFESSION_GIFT, IMEvent.CONFIRM_CP -> {
//                    val sendUser = message.getJsonValue<UserResponse>("inviter") ?: return false
//                    val targetUser = message.getJsonValue<UserResponse>("invitee") ?: return false
//                    val singleGift = message.getJsonValue<Gift>("gift") ?: return false
//                    filter = true
//                }
//
//                IMEvent.INVITE_PRIVATE_ROOM2 -> {
//                    val sendUser = message.getJsonValue<UserResponse>("user")
//                    val targetUser = message.getJsonValue<UserResponse>("target_user")
//                    if (sendUser != null && targetUser?.isSelf == true) { // 邀请进入小屋
//                        filter = true
//                    }
//                }
//
//                IMEvent.INTERACTIVE_MSG -> {
//                    filter = true
//                }
//
//                IMEvent.TEAM_PK_EVENT -> {
//                    val pkEvent = message.parseDataJson<PKEvent>()
//                    if (pkEvent != null) {
//                        filter = true
//                    }
//                }
//
//                IMEvent.SPEED_RACING_WIN -> {
//                    val targetUser = message.getJsonValue<UserResponse>("user")
//                    if (targetUser != null) {
//                        filter = UIConfig.userConf.showSpeedRacing
//                    }
//                }
//
//                IMEvent.MIC_SPEECH_TEXT -> {
//                    filter =
//                        !message.isSelf &&
//                        UIConfig.userConf.micAsrEnabled &&
//                        message.currentTranslateText
//                            .isNullOrEmpty()
//                            .not()
//                }
//
//                IMEvent.FOLLOW_CHATROOM_PUBLIC_MESSAGES -> {
//                    filter = sUser.hquType != 0
//                }
//
//                IMEvent.RED_PACKET_CREATED -> {
//                    filter = true
//                }
//
//                IMEvent.COMMON_CHATROOM_PUBLIC_MESSAGES -> {
//                    filter = true
//                }
