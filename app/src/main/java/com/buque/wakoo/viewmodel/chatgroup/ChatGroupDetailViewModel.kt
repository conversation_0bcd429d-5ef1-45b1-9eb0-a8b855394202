package com.buque.wakoo.viewmodel.chatgroup

import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.app.currentUserKV
import com.buque.wakoo.bean.chatgroup.ChatGroupBean
import com.buque.wakoo.repository.ChatGroupRepo
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.toCState
import com.buque.wakoo.utils.CacheKeys
import com.buque.wakoo.utils.useCache
import com.buque.wakoo.viewmodel.CSViewModel

open class ChatGroupDetailViewModel(
    val groupId: String,
) : CSViewModel<ChatGroupBean>() {
    protected val repo: ChatGroupRepo
        get() = GlobalRepository.chatGroupRepo

    // 如果是
    override suspend fun loadState() =
        if (SelfUser?.group?.id?.toString() == groupId) getSelfGroup() else repo.getGroupDetail(groupId).toCState()

    suspend fun getSelfGroup(): CState<ChatGroupBean> {
        useCache<ChatGroupBean>(CacheKeys.SELF_CHAT_GROUP, currentUserKV, fetchFunction = {
            repo.getGroupDetail(groupId)
        }) {
            rawState.value = it.toCState()
        }
        return rawState.value
    }
}
