package com.buque.wakoo.viewmodel

import android.annotation.SuppressLint
import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateSetOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.VoiceCardItem
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AudioRecordManager
import com.buque.wakoo.network.api.bean.PublishVoiceRequest
import com.buque.wakoo.network.api.bean.VoiceTag
import com.buque.wakoo.repository.VoiceRepository
import com.buque.wakoo.ui.widget.state.requireData
import com.buque.wakoo.utils.preload.PublishVoicePreload
import com.buque.wakoo.utils.upload.CosTransferHelper
import com.buque.wakoo.utils.upload.TransferResult
import com.buque.wakoo.utils.upload.UploadCallbacks
import com.buque.wakoo.utils.upload.UploadUtils

@SuppressLint("StaticFieldLeak")
class VoicePublishViewModel(
    val applicationContext: Context,
) : BaseViewModel() {
    private val voiceRepository = VoiceRepository()

    class Factory(
        private val context: Context,
    ) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T = VoicePublishViewModel(context.applicationContext) as T
    }

    private val cosTransferHelper = CosTransferHelper()

    val audioRecordManager: AudioRecordManager
        get() = AudioRecordManager.singletonInstance

    val configStateFlow = PublishVoicePreload.getPublishConfigFlow(true)

    var selectedThemeIndex by mutableIntStateOf(0)

    val selectedTagIds = mutableStateSetOf<Int>()

    fun loadPublishConfig() {
        PublishVoicePreload.refreshPublishConfig(false)
    }

    fun toggleSelectedByTag(tag: VoiceTag) {
        if (selectedTagIds.contains(tag.id)) {
            selectedTagIds.remove(tag.id)
        } else {
            selectedTagIds.add(tag.id)
        }
    }

    fun startPreview(
        content: String,
        isPublish: Boolean,
        filePath: String,
        duration: Int,
    ) = VoiceCardItem(
        user = SelfUser!!.basic,
        title = content,
        tags =
            configStateFlow.value.requireData.tags
                .filter { selectedTagIds.contains(it.id) },
        visibility = if (isPublish) 1 else 2,
        duration = duration,
        background =
            configStateFlow.value.requireData.background[selectedThemeIndex]
                .resource,
    ).also {
        it.filePath = filePath
    }

    suspend fun publish(item: VoiceCardItem): Boolean {
        // 定义回调
        val callbacks =
            UploadCallbacks(
                onProgress = { progress, max ->
                    val percentage = if (max > 0) (progress * 100 / max).toInt() else 0
                },
                onStateChanged = { state ->
                },
                onInitUploadId = { uploadId ->
                },
            )

        val path = UploadUtils.generateOSSPath(item.filePath, UploadUtils.DEFAULT_VOICE_PATH)
        val currentTaskWrapper =
            cosTransferHelper.upload(
                cosPath = path,
                filePath = item.filePath,
                callbacks = callbacks,
            )

        // 挂起等待上传结果
        return when (val result = currentTaskWrapper.await()) {
            is TransferResult.Success -> {
                voiceRepository
                    .publishVoice(
                        PublishVoiceRequest(
                            resource = result.url,
                            duration = item.duration,
                            title = item.title,
                            tags = item.tags.joinToString(",") { it.id.toString() },
                            visibility = item.visibility,
                            backgroundId =
                                configStateFlow.value.requireData.background[selectedThemeIndex]
                                    .id,
                        ),
                    ).isSuccess
            }

            is TransferResult.Failure -> {
                showToast("上传失败")
                false
            }
        }
    }
}
