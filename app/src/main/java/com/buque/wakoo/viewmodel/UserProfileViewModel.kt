package com.buque.wakoo.viewmodel

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.bean.BasicUser
import com.buque.wakoo.bean.User
import com.buque.wakoo.bean.UserExtraInfo
import com.buque.wakoo.bean.UserInfo
import com.buque.wakoo.repository.UserRepository
import com.buque.wakoo.utils.handleOptimisticRequest
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject

class UserProfileViewModel : BaseViewModel() {
    private val userRepository = UserRepository()
    private val userInfoState = mutableStateOf<UserInfo?>(null)


    val state: State<UserInfo?> = userInfoState

    val user: User?
        get() = userInfoState.value?.basic

    val hasExtraInfo: <PERSON>olean
        get() = userInfoState.value?.extra != null

    val extraInfo: UserExtraInfo
        get() = userInfoState.value?.extra ?: UserExtraInfo.empty

    private var followRequesting = false

    fun preLoad(user: User) {
        if (user is UserInfo) {
            userInfoState.value = user
        } else if (user is BasicUser) {
            userInfoState.value = UserInfo(basic = user)
        }
    }

    fun requestUserInfo(userId: String) {
        viewModelScope.launch {
            userRepository.getUserInfo(userId).onSuccess {
                userInfoState.value = it
            }
        }
    }

    fun toggleFollowState(userId: String) {
        viewModelScope.launch {
            if (hasExtraInfo) {
                handleOptimisticRequest(
                    getItem = {
                        userInfoState.value
                    },
                    setItem = {
                        userInfoState.value = it
                    },
                    getProperty = { it.extra.isFollowed },
                    isRequesting = { followRequesting },
                    copy = { item, isFollowed, isRequesting ->
                        followRequesting = isRequesting
                        item.copy(extra = item.extra.copy(isFollowed = isFollowed))
                    },
                    apiCall = { isFollow -> userRepository.updateFollowState(userId, isFollow).isSuccess },
                )
            }
        }
    }

    suspend fun updateBlackState(
        userId: String,
        isBlack: Boolean,
    ): Result<JsonObject> = userRepository.updateBlackState(userId, isBlack)
}
