package com.buque.wakoo.viewmodel.liveroom

import com.buque.wakoo.bean.RankListContainer
import com.buque.wakoo.bean.UserPages
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.UserListResponse
import com.buque.wakoo.network.api.service.VoiceRoomApiService
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.requireData
import com.buque.wakoo.viewmodel.ListPaginateViewModel

class RoomRankListViewModel : ListPaginateViewModel<String, Int, RankListContainer, RankListContainer>() {
    private val apiService
        get() = VoiceRoomApiService.instance

    override fun getFirstPageKey(dataKey: Any): Int = 0

    override fun getNextPageKey(
        cState: CState<List<RankListContainer>>,
        dataKey: Any,
        pageKey: Int?,
    ): Int = 0

    override suspend fun getData(
        reqKey: String,
        dataKey: Any,
        pageKey: Int,
        pageSize: Int,
    ): ApiResponse<RankListContainer> = apiService.getRankList(reqKey)

    override fun getDataListFromResponse(
        dataKey: Any,
        response: RankListContainer,
    ): List<RankListContainer> = listOf(response)

    override fun getHasNextPageKeyFromResponse(
        dataKey: Any,
        pageKey: Int,
        response: RankListContainer,
    ): Int? = null

    override fun getDistinctSelector(): (RankListContainer) -> String =
        {
            ""
        }
}
