package com.buque.wakoo.viewmodel.chatgroup

import androidx.lifecycle.viewModelScope
import com.buque.wakoo.bean.chatgroup.ChatGroupMember
import com.buque.wakoo.repository.ChatGroupRepo
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.viewmodel.BasicListPaginateViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

data class OnlineKey(val lastId: String, val lastIsOnline: Boolean)

class ChatGroupOnlineMemberListViewModel(val groupId: String, val firstPageMembers: List<ChatGroupMember> = emptyList()) :
    BasicListPaginateViewModel<OnlineKey, ChatGroupMember>() {
    val repo: ChatGroupRepo
        get() = GlobalRepository.chatGroupRepo

    private val firstKey = OnlineKey("0", true)

    override suspend fun loadData(
        pageKey: OnlineKey,
        pageSize: Int
    ): Result<List<ChatGroupMember>> = repo.getGroupMembers(groupId, pageKey.lastId, pageKey.lastIsOnline)
        .let { result ->
            if (pageKey == firstKey) {
                result.map { list ->
                    buildList {
                        addAll(firstPageMembers)
                        addAll(list.filter { !firstPageMembers.contains(it) })
                    }
                }
            } else {
                result
            }
        }

    override fun getFirstPageKey(dataKey: Any): OnlineKey = firstKey

    override fun getNextPageKey(
        cState: CState<List<ChatGroupMember>>,
        dataKey: Any,
        pageKey: OnlineKey?
    ): OnlineKey {
        return cState.dataOrNull?.lastOrNull()?.let {
            OnlineKey(it.memberId.toString(), it.isOnline)
        } ?: OnlineKey("-1", false)
    }

    override fun getDistinctSelector(): (ChatGroupMember) -> String {
        return {
            it.memberId.toString()
        }
    }

    override fun onLoadComplete(hasMore: Boolean) {
        if (!hasMore) return
        if ((listCState.value.dataOrNull?.size ?: 0) < 20) {
            viewModelScope.launch {
                delay(100)
                loadNexPageData()
            }
        }
    }

}