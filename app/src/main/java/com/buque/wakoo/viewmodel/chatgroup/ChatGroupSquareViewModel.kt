package com.buque.wakoo.viewmodel.chatgroup

import androidx.lifecycle.viewModelScope
import com.buque.wakoo.bean.chatgroup.ChatGroupSquareListBean
import com.buque.wakoo.bean.chatgroup.ChatGroupSquareResponse
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.service.ChatGroupApi
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.ui.widget.state.requireData
import com.buque.wakoo.utils.handleOptimisticAnyRequest
import com.buque.wakoo.viewmodel.ListPaginateViewModel
import kotlinx.coroutines.launch

class ChatGroupSquareViewModel : ListPaginateViewModel<String, Int, ChatGroupSquareListBean, ChatGroupSquareResponse>() {
    override fun getFirstPageKey(dataKey: Any): Int = 0

    override fun getNextPageKey(
        cState: CState<List<ChatGroupSquareListBean>>,
        dataKey: Any,
        pageKey: Int?,
    ): Int = cState.requireData.last().id

    override suspend fun getData(
        reqKey: String,
        dataKey: Any,
        pageKey: Int,
        pageSize: Int,
    ): ApiResponse<ChatGroupSquareResponse> = ChatGroupApi.instance.getGroupSquareList(pageKey)

    override fun getDataListFromResponse(
        dataKey: Any,
        response: ChatGroupSquareResponse,
    ): List<ChatGroupSquareListBean> = response.tribes

    override fun getHasNextPageKeyFromResponse(
        dataKey: Any,
        pageKey: Int,
        response: ChatGroupSquareResponse,
    ): Int? =
        if (response.tribes.isNotEmpty()) {
            response.tribes.last().id
        } else {
            null
        }

    override fun getDistinctSelector(): (ChatGroupSquareListBean) -> String =
        {
            it.id.toString()
        }

    fun requestJoinGroup(item: ChatGroupSquareListBean) {
        viewModelScope.launch {
            // 0: 无关系, 5: 申请中, 10: 成员
            if (item.relationWithMe == 0) {
                // 没关系可以申请
            } else if (item.relationWithMe == 5) {
                // 申请中
                return@launch
            } else if (item.relationWithMe == 10) {
                return@launch
            }
            val state = getCState("")
            handleOptimisticAnyRequest(
                newValue = 5,
                itemId = item.id,
                items = state.dataOrNull ?: mutableListOf<ChatGroupSquareListBean>(),
                findItemIdFromData = { it.id },
                getProperty = {
                    it.relationWithMe
                },
                isRequesting = { it.useInterfaceRequesting },
                copy = { item, relation, isRequesting ->
                    item.copy(relationWithMe = relation, useInterfaceRequesting = isRequesting)
                },
                apiCall = {
                    executeApiCallExpectingData {
                        ChatGroupApi.instance.applyJoinChatGroup(mapOf("group_id" to item.id.toString()))
                    }.isSuccess
                },
            )
        }
    }
}
