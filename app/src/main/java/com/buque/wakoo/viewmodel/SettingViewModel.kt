package com.buque.wakoo.viewmodel

import androidx.lifecycle.viewModelScope
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.network.api.service.LoginApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

class SettingViewModel : BaseViewModel() {
    private val apiService
        get() = LoginApiService.instance

    private var isLoading = false

    fun writeOffUser(): Job {
        return viewModelScope.launch {
            if (isLoading) {
                return@launch
            }
            SelfUser?.id ?: return@launch
            isLoading = true
            val ret =
                executeApiCallExpectingData {
                    apiService.writeOffUser()
                }
            isLoading = false
            if (ret.isSuccess) {
                AccountManager.logout()
            }
        }
    }
}
