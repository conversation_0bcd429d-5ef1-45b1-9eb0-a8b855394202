package com.buque.wakoo.viewmodel

import android.net.Uri
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.repository.UserRepository
import com.buque.wakoo.utils.upload.CosTransferHelper
import com.buque.wakoo.utils.upload.TransferResult
import com.buque.wakoo.utils.upload.UploadUtils
import kotlinx.coroutines.launch

class EditUserInfoViewModel : BaseViewModel() {
    private val cosTransferHelper = CosTransferHelper()

    private val userRepository = UserRepository()

    suspend fun updateNickName(nickname: String) {
        userRepository.updateUserInfo(mapOf("nickname" to nickname)).onSuccess {
            AccountManager.updateUserInfo {
                it.copyUser(name = nickname)
            }
        }
    }

    suspend fun updateAvatar(uri: Uri) {
        // 定义回调
//            val callbacks =
//                UploadCallbacks(
//                    onProgress = { progress, max ->
//                        val percentage = if (max > 0) (progress * 100 / max).toInt() else 0
//                        Log.d("sssssssssssssss", "任务进度：$percentage")
//                    },
//                    onStateChanged = { state ->
//                        Log.d("sssssssssssssss", "任务已暂停，可以保存 Upload ID: $state 用于续传")
//                    },
//                    onInitUploadId = { uploadId ->
//                        Log.d("sssssssssssssss", "获取到 Upload ID: $uploadId")
//                    },
//                )

        val path = UploadUtils.generateOSSPath(WakooApplication.instance, uri, UploadUtils.DEFAULT_AVATAR_PATH)
        val currentTaskWrapper =
            cosTransferHelper.upload(
                cosPath = path,
                uri = uri,
//                callbacks = callbacks
            )

        // 挂起等待上传结果
        when (val result = currentTaskWrapper.await()) {
            is TransferResult.Success -> {
                userRepository.updateUserInfo(mapOf("avatar_url" to result.url)).onSuccess {
                    AccountManager.updateUserInfo {
                        it.copyUser(avatar = result.url)
                    }
                    showToast("更新成功")
                }
            }

            is TransferResult.Failure -> {
                showToast("头像上传失败")
            }
        }
    }

    fun updateBirthday(birthday: String) {
        viewModelScope.launch {
            userRepository.updateUserInfo(mapOf("birthday" to birthday)).onSuccess {
                AccountManager.updateUserInfo {
                    it.copyUser(birthday = birthday)
                }
            }
        }
    }

    fun updateGender(gender: Int) {
        viewModelScope.launch {
            userRepository.updateUserInfo(mapOf("gender" to gender.toString())).onSuccess {
                AccountManager.updateUserInfo {
                    it.copyUser(gender = gender)
                }
            }
        }
    }
}
