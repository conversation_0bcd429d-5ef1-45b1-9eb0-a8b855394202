package com.buque.wakoo.viewmodel

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.core.pay.GoogleBillingHelper
import com.buque.wakoo.core.pay.IPurchasesUpdatedListener
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.ext.toast
import com.buque.wakoo.repository.GlobalRepository
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonPrimitive

sealed class WalletEvent {
    data object Fetch : WalletEvent()

    data object FetchVipConfig : WalletEvent()

    data class DrawGold(val bonusIndex: Int) : WalletEvent()
}

open class WalletViewModel : ViewModel(), IPurchasesUpdatedListener {
    companion object {
        /**
         * 打开了bi shang c2c
         */
        const val ACTION_OPEN_AGENT_CHAT = "action_open_agent_chat"
    }

    private val repo = GlobalRepository.walletRepo

    private val _loading: MutableState<Boolean> = mutableStateOf(false)
    val loading: State<Boolean> = _loading

    private var isLoading: Boolean
        get() = _loading.value
        set(value) {
            _loading.value = value
        }

    private val _actionFlow = MutableSharedFlow<String>()
    val actionFlow = _actionFlow.asSharedFlow()

    val rechargeGoodsState = GoogleBillingHelper.chargeItemsFlow

    val vipConfigState = GoogleBillingHelper.vipInfoFlow


    private fun handleEvent(event: WalletEvent) {
        when (event) {
            is WalletEvent.Fetch -> {
                fetchRechargeList()
            }


            is WalletEvent.FetchVipConfig -> {
                fetchActivateVipConfig()
            }

            is WalletEvent.DrawGold -> {
                viewModelScope.launch {
                    isLoading = true
                    repo.getGold(event.bonusIndex).onSuccess {
                        it["hint"]?.jsonPrimitive?.contentOrNull?.also { hint -> showToast(hint) }
                        sendEvent(WalletEvent.FetchVipConfig)
                    }.onFailure {
                        it.toast()
                    }
                    isLoading = false
                }
            }

            else -> {}
        }
    }

    fun sendEvent(event: WalletEvent) {
        handleEvent(event)
    }


    override fun onPurchasesUpdated(completed: Boolean) {
        viewModelScope.launch {
            if (completed) {
                isLoading = false
                GoogleBillingHelper.clearListener()
            } else {
                isLoading = true
            }
        }
    }

    override fun onPurchasesMessage(message: String) {
        showToast(message)
    }

    override fun onCleared() {
        super.onCleared()
        GoogleBillingHelper.clearListener()
    }

    private fun fetchRechargeList() {
        viewModelScope.launch {
            GoogleBillingHelper.fetchRechargeList()
                .onFailure {
                    it.toast()
                }
        }
    }

    private fun fetchActivateVipConfig() {
        viewModelScope.launch {
            GoogleBillingHelper.fetchActivateVipConfig().onFailure { it.toast() }
        }
    }

}