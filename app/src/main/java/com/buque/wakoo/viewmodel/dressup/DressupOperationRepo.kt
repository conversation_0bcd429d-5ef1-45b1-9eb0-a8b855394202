package com.buque.wakoo.viewmodel.dressup

import com.buque.wakoo.ext.getIntOrNull
import com.buque.wakoo.network.api.service.DressupApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.serialization.json.JsonObject

class DressupOperationRepo {
    private val _diamondBalance = MutableStateFlow(0)
    val diamondBalance = _diamondBalance.asStateFlow()
    private val _integralBalance = MutableStateFlow(0)
    val integralBalance = _integralBalance.asStateFlow()

    fun updateBalance(
        diamond: Int? = null,
        integral: Int? = null,
    ) {
        if (diamond != null) {
            _diamondBalance.tryEmit(diamond)
        }
        if (integral != null) {
            _integralBalance.tryEmit(integral)
        }
    }

    suspend fun purchaseDressUpPropItem(
        id: String,
        type: Int,
        days: Int,
    ): Result<JsonObject> =
        executeApiCallExpectingData {
            DressupApiService.instance.buy(
                mapOf("decoration_id" to id, "decoration_type" to type, "days" to days),
            )
        }.onSuccess {
            updateBalance(integral = it.getIntOrNull("my_balance"))
        }
}
