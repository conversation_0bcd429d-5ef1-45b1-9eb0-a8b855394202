package com.buque.wakoo

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.InputEdit
import com.buque.wakoo.bean.LocalSelfUserProvider
import com.buque.wakoo.core.webview.AppLinkNavigator
import com.buque.wakoo.im.utils.WatchMessageEventEffect
import com.buque.wakoo.im_business.AppEventMessageHandler
import com.buque.wakoo.im_business.viewmodel.C2CChatViewModel
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.navigation.AppNavDisplay
import com.buque.wakoo.navigation.GalleryScreenKey
import com.buque.wakoo.navigation.RootNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.appEntry
import com.buque.wakoo.navigation.appEntryProvider
import com.buque.wakoo.navigation.appResultEntry
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.navigation.getVerticalTransitionMetadata
import com.buque.wakoo.ui.screens.WebScreen
import com.buque.wakoo.ui.screens.chatgroup.ChatGroupHostScreen
import com.buque.wakoo.ui.screens.chatgroup.screen.ChatGroupDetailScreen
import com.buque.wakoo.ui.screens.chatgroup.screen.GroupSquareScreen
import com.buque.wakoo.ui.screens.dressup.DressupCenterScreen
import com.buque.wakoo.ui.screens.dressup.DressupShopListScreen
import com.buque.wakoo.ui.screens.dressup.DressupShopTabScreen
import com.buque.wakoo.ui.screens.dressup.GiftWallScreen
import com.buque.wakoo.ui.screens.home.HomeHostScreen
import com.buque.wakoo.ui.screens.japan.boost.BoostPage
import com.buque.wakoo.ui.screens.liveroom.screen.LiveRoomHostScreen
import com.buque.wakoo.ui.screens.messages.MessageScreen
import com.buque.wakoo.ui.screens.messages.chat.C2CChatScreen
import com.buque.wakoo.ui.screens.profile.EditTextUserInfoScreen
import com.buque.wakoo.ui.screens.profile.EditUserInfoScreen
import com.buque.wakoo.ui.screens.profile.SelectUserScreen
import com.buque.wakoo.ui.screens.profile.UserProfileScreen
import com.buque.wakoo.ui.screens.profile.UserRelationsScreen
import com.buque.wakoo.ui.screens.recharge.RechargeRecordScreenUI
import com.buque.wakoo.ui.screens.recharge.RechargeScreen
import com.buque.wakoo.ui.screens.settings.AboutAppScreen
import com.buque.wakoo.ui.screens.settings.BlackListScreen
import com.buque.wakoo.ui.screens.settings.DebugScreen
import com.buque.wakoo.ui.screens.settings.FeedbackScreen
import com.buque.wakoo.ui.screens.settings.ReportScreen
import com.buque.wakoo.ui.screens.settings.SettingsScreen
import com.buque.wakoo.ui.screens.vip.MemberScreen
import com.buque.wakoo.ui.screens.voice.VoicePublishHostScreen
import com.buque.wakoo.ui.widget.media.previewer.GalleryScreen
import com.buque.wakoo.ui.widget.media.selector.MediaSelectorScreen

@Composable
fun LoggedInHostScreen(controller: RootNavController) {
    val dq = rememberDialogController()
    val globalEventMessageHandler =
        remember {
            AppEventMessageHandler(controller, dq)
        }
    WatchMessageEventEffect(globalEventMessageHandler)
    LocalSelfUserProvider.Provide {
        AppNavDisplay(
            backStack = controller.loggedInBackStack,
            modifier = Modifier.fillMaxSize(),
            entryProvider =
                appEntryProvider {
                    appEntry<Route.Web> { route ->
                        val dc = rememberDialogController()
                        Box(modifier = Modifier.fillMaxSize()) {
                            WebScreen(route, onOpenLink = { link ->
                                AppLinkNavigator.go(link, controller, dc)
                            }, onOpenPage = {
                                controller.push(it)
                            }) {
                                controller.pop()
                            }
                            dc.RenderDialogs(Unit)
                        }
                    }
                    appEntry<Route.Home> {
                        val user = LocalSelfUserProvider.current
                        HomeHostScreen(
                            toNotification = {
                                controller.push(Route.InfoNotification)
                            },
                            toEditUserInfo = {
                                controller.push(Route.EditUserInfo)
                            },
                            toSettings = {
                                controller.push(Route.Settings)
                            },
                            toUserRelations = {
                                controller.push(Route.UserRelations(it, user.id))
                            },
                            toPublishVoice = {
                                controller.push(Route.VoicePublish)
                            },
                            onRechargeClick = {
                                controller.push(Route.Recharge)
                            },
                            onMemberClick = {
                                controller.push(Route.Member)
                            },
                            toLiveRoom = {
                                LiveRoomManager.joinRoom(it)
                            },
                            toGiftWall = {
                                controller.push(Route.GiftWall(SelfUser?.id?.toIntOrNull() ?: 0))
                            },
                            toDressUpCenter = {
                                controller.push(Route.DressUpMine())
                            },
                            toDressUpShop = {
                                controller.push(Route.DressUpSample)
                            },
                        )
                    }
                    appEntry<Route.VoicePublish>(metadata = getVerticalTransitionMetadata()) {
                        VoicePublishHostScreen {
                            controller.popIf(Route.VoicePublish)
                        }
                    }
                    appEntry<Route.LiveRoom> {
                        LiveRoomHostScreen(it.basicInfo)
                    }
                    appResultEntry<Route.MediaSelector> { it, resultKey ->
                        MediaSelectorScreen(resultKey, it.mediaType, it.maxSelectCount.coerceAtLeast(1))
                    }
                    appEntry<Route.UserProfile> {
                        val user = it.user
                        UserProfileScreen(
                            user = user,
                            toEditUserInfo = {
                                controller.push(Route.EditUserInfo)
                            },
                            toSettings = {
                                controller.push(Route.Settings)
                            },
                            toUserRelations = {
                                // 暂时不支持看他人关注信息
                                if (user.sIsSelf) {
                                    controller.push(Route.UserRelations(it, user.id))
                                }
                            },
                        )
                    }
                    appEntry<Route.EditUserInfo> {
                        val selfUser = LocalSelfUserProvider.current
                        EditUserInfoScreen(
                            onNicknameClick = {
                                controller.push(
                                    Route.EditTextUserInfo(
                                        InputEdit.createNickNameEdit(
                                            selfUser.name,
                                        ),
                                    ),
                                )
                            },
                        )
                    }
                    appEntry<Route.EditTextUserInfo> {
                        EditTextUserInfoScreen(it.edit)
                    }
                    appEntry<Route.UserRelations> {
                        UserRelationsScreen(it.initIndex, it.userId)
                    }
                    appEntry<Route.Report> {
                        ReportScreen(it)
                    }
                    appEntry<Route.Settings> {
                        SettingsScreen(
                            onBlacklistClick = {
                                controller.push(Route.BlackList)
                            },
                            onFeedbackClick = {
                                controller.push(Route.Feedback)
                            },
                            onAboutAppClick = {
                                controller.push(Route.AboutApp)
                            },
                            onDebug = {
                                controller.push(Route.Debug)
                            },
                        )
                    }
                    appEntry<Route.BlackList> {
                        BlackListScreen()
                    }
                    appEntry<Route.Feedback> {
                        FeedbackScreen()
                    }
                    appEntry(Route.RechargeRecord) {
                        RechargeRecordScreenUI()
                    }
                    appEntry(Route.Recharge) {
                        RechargeScreen(onRecordClick = {
                            controller.push(Route.RechargeRecord)
                        }, onOpenWeb = {
                            controller.push(Route.Web(it))
                        })
                    }
                    appEntry(Route.Member) {
                        MemberScreen(onOpenWeb = {
                            controller.push(Route.Web(it))
                        })
                    }
                    appEntry<Route.AboutApp> {
                        AboutAppScreen(
                            onUserAgreementClick = {
                                controller.push(
                                    Route.Web(
                                        url = "${EnvironmentManager.current.apiUrl}h5/agreement",
                                        title = "用户服务协议",
                                    ),
                                )
                            },
                            onPrivacyPolicyClick = {
                                controller.push(
                                    Route.Web(
                                        url = "${EnvironmentManager.current.apiUrl}h5/privacy",
                                        title = "隐私协议",
                                    ),
                                )
                            },
                        )
                    }
                    appEntry<Route.InfoNotification> {
                        MessageScreen()
                    }
                    appEntry<Route.Chat> {
                        val viewModel =
                            viewModel<C2CChatViewModel>(initializer = {
                                C2CChatViewModel(it.user)
                            })
                        C2CChatScreen(it.user, viewModel)
                    }
                    appEntry<Route.SelectUser> {
                        SelectUserScreen(it)
                    }
                    appEntry<Route.Debug> {
                        DebugScreen(controller)
                    }
                    appEntry<Route.ChatGroup> {
                        ChatGroupHostScreen(it.groupId)
                    }
                    appEntry<Route.ChatGroupSquare> {
                        GroupSquareScreen()
                    }

                    appEntry<Route.GiftWall> {
                        GiftWallScreen(it.userId)
                    }
                    appEntry(Route.DressUpSample) {
                        DressupShopTabScreen(
                            toTabListScreen = { tabName, tabType ->
                                controller.push(Route.DressUpShopList(tabType, tabName))
                            },
                            toDressUpCenter = {
                                controller.push(Route.DressUpMine(it ?: -1))
                            },
                        )
                    }
                    appEntry<Route.DressUpShopList> {
                        DressupShopListScreen(it.type, it.title, toDressUpCenter = {
                            controller.push(Route.DressUpMine(-1))
                        })
                    }
                    appEntry<Route.DressUpMine> {
                        DressupCenterScreen(initTab = it.initialTab, toDressShop = { tabType, tabName ->
                            if (tabType == null) {
                                controller.push(Route.DressUpSample)
                            } else {
                                controller.push(Route.DressUpShopList(tabType, tabName))
                            }
                        })
                    }
                    appEntry<Route.ChatGroupDetail> {
                        ChatGroupDetailScreen(it.groupId)
                    }
                    appEntry<Route.BoostPage> {
                        BoostPage(it.type)
                    }

                    appEntry<GalleryScreenKey> {
                        GalleryScreen(it)
                    }
                },
        )
    }
}
