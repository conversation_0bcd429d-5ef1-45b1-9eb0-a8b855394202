package com.buque.wakoo.rtc

import android.content.Context
import android.view.TextureView
import android.view.View
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.ext.getBoolOrNull
import com.buque.wakoo.ext.getIntOrNull
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.utils.LogUtils
import com.tencent.liteav.TXLiteAVCode
import com.tencent.rtmp.ui.TXCloudVideoView
import com.tencent.trtc.TRTCCloud
import com.tencent.trtc.TRTCCloudDef
import com.tencent.trtc.TRTCCloudDef.TRTCParams
import com.tencent.trtc.TRTCCloudDef.TRTCRenderParams
import com.tencent.trtc.TRTCCloudListener
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject

class TencentRtcFactory(
    private val scenario: RtcScenario = RtcScenario.Room,
    private val enableAudioVolumeIndication: Boolean = true,
    private val defaultSpeakerphone: Boolean = true,
    private val jsonConfig: JsonObject? = null,
) : IRtcEngineFactory {
    override fun create(
        context: Context,
        handle: IRtcEventHandle,
    ): IRtcEngine =
        TencentRtcEngine(
            context = context,
            eventHandle = handle,
            scenario = scenario,
            config =
                jsonConfig?.let {
                    val jsonWith3A = it["audio_3a"]?.jsonObject
                    TencentRtcConfig(
                        quality = it.getIntOrNull("quality") ?: TRTCCloudDef.TRTC_AUDIO_QUALITY_DEFAULT,
                        aecEnabled = jsonWith3A?.getBoolOrNull("aec_enabled") ?: true,
                        agcEnabled = jsonWith3A?.getBoolOrNull("agc_enabled") ?: true,
                        ansEnabled = jsonWith3A?.getBoolOrNull("ans_enabled") ?: true,
                    )
                },
            enableAudioVolumeIndication = enableAudioVolumeIndication,
            defaultSpeakerphone = defaultSpeakerphone,
        )
}

class TencentRtcEngine(
    context: Context,
    private val eventHandle: IRtcEventHandle,
    private val scenario: RtcScenario = RtcScenario.Room,
    private val config: TencentRtcConfig? = null,
    enableAudioVolumeIndication: Boolean = true,
    defaultSpeakerphone: Boolean = true,
) : TRTCCloudListener(),
    IRtcEngine {
//    private val iAudioFrameListener =
//        object : TRTCAudioFrameListener {
//            override fun onCapturedAudioFrame(trtcAudioFrame: TRTCCloudDef.TRTCAudioFrame) {
//                if (!isStartedPCMData || isLocalAudioMuted()) {
//                    return
//                }
//                val buffer = ByteBuffer.wrap(trtcAudioFrame.data)
//                val pcmData = AudioPCMData(buffer, buffer.remaining())
//                eventHandle.onCapturedPCMData(pcmData)
//                AppAsrManager.writeByte(pcmData)
//            }
//
//            override fun onLocalProcessedAudioFrame(p0: TRTCCloudDef.TRTCAudioFrame?) = Unit
//
//            override fun onRemoteUserAudioFrame(
//                p0: TRTCCloudDef.TRTCAudioFrame?,
//                p1: String?,
//            ) = Unit
//
//            override fun onMixedPlayAudioFrame(p0: TRTCCloudDef.TRTCAudioFrame?) = Unit
//
//            override fun onMixedAllAudioFrame(p0: TRTCCloudDef.TRTCAudioFrame?) = Unit
//
//            override fun onVoiceEarMonitorAudioFrame(p0: TRTCCloudDef.TRTCAudioFrame?) = Unit
//        }

    private var audioMuted = false

    private var isStartedPCMData = false

    private var tempCurRole = TRTCCloudDef.TRTCRoleAudience

    private var tempSettingRole = TRTCCloudDef.TRTCRoleAudience

    private val rtcEngine =
        requireNotNull(TRTCCloud.sharedInstance(context)).also {
            if (EnvironmentManager.isDebugBuild) {
                TRTCCloud.setLogLevel(TRTCCloudDef.TRTC_LOG_LEVEL_VERBOSE)
                TRTCCloud.setConsoleEnabled(true)
            }
            it.addListener(this)
            if (enableAudioVolumeIndication) {
                it.enableAudioVolumeEvaluation(
                    true,
                    TRTCCloudDef.TRTCAudioVolumeEvaluateParams().apply {
                        enableVadDetection = true
                        interval = 800
                    },
                )
            }
            if (defaultSpeakerphone) {
                it.setAudioRoute(TRTCCloudDef.TRTC_AUDIO_ROUTE_SPEAKER)
            } else {
                it.setAudioRoute(TRTCCloudDef.TRTC_AUDIO_ROUTE_EARPIECE)
            }

            if (config != null) {
                if (config.agcEnabled) {
                    it.callExperimentalAPI("{\"api\":\"enableAudioAGC\",\"params\":{\"enable\":1}}")
                } else {
                    it.callExperimentalAPI("{\"api\":\"enableAudioAGC\",\"params\":{\"enable\":0}}")
                }

                if (config.aecEnabled) {
                    it.callExperimentalAPI("{\"api\":\"enableAudioAEC\",\"params\":{\"enable\":1}}")
                } else {
                    it.callExperimentalAPI("{\"api\":\"enableAudioAEC\",\"params\":{\"enable\":0}}")
                }

                if (config.ansEnabled) {
                    it.callExperimentalAPI("{\"api\":\"enableAudioANS\",\"params\":{\"enable\":1}}")
                } else {
                    it.callExperimentalAPI("{\"api\":\"enableAudioANS\",\"params\":{\"enable\":0}}")
                }
            }
        }

    override fun joinChannel(
        channelId: String,
        token: String,
        upMic: Boolean,
        muted: Boolean,
        speaker: Boolean,
    ): Boolean {
        val params = TRTCParams()
        params.sdkAppId = EnvironmentManager.current.tencentRtcId
        params.userId = SelfUser?.id.orEmpty() // Please replace with your own userid
        params.strRoomId = channelId // Please replace with your own room number
        params.userSig = token // Please replace with your own userSig
        val rtcScene =
            when (scenario) {
                RtcScenario.Room -> {
                    TRTCCloudDef.TRTC_APP_SCENE_VOICE_CHATROOM
                }

                RtcScenario.OneToOneAudio -> {
                    TRTCCloudDef.TRTC_APP_SCENE_AUDIOCALL
                }

                RtcScenario.OneToOneVideo -> {
                    TRTCCloudDef.TRTC_APP_SCENE_VIDEOCALL
                }
            }
        params.role = if (upMic) TRTCCloudDef.TRTCRoleAnchor else TRTCCloudDef.TRTCRoleAudience
        tempCurRole = TRTCCloudDef.TRTCRoleAudience
        tempSettingRole = params.role
        muteLocalAudioStream(muted)
        rtcEngine.enterRoom(params, rtcScene)
        return true
    }

    override fun levelChannel(): Boolean {
        rtcEngine.exitRoom()
        return true
    }

    override fun upMic(token: String?): Boolean {
        if (tempCurRole == TRTCCloudDef.TRTCRoleAnchor) {
            return true
        }
        tempSettingRole = TRTCCloudDef.TRTCRoleAnchor
        rtcEngine.switchRole(TRTCCloudDef.TRTCRoleAnchor)
        rtcEngine.startLocalAudio(config?.quality ?: TRTCCloudDef.TRTC_AUDIO_QUALITY_DEFAULT)
        return true
    }

    override fun downMic(token: String?): Boolean {
        if (tempCurRole == TRTCCloudDef.TRTCRoleAudience) {
            return true
        }
        LogUtils.dTag("app_rtc_manager", "downMic")
        tempSettingRole = TRTCCloudDef.TRTCRoleAudience
        rtcEngine.stopLocalAudio()
        rtcEngine.switchRole(TRTCCloudDef.TRTCRoleAudience)
        return true
    }

    override fun muteLocalAudioStream(muted: Boolean): Boolean {
        if (audioMuted == muted) {
            return true
        }
        rtcEngine.muteLocalAudio(muted)
        audioMuted = muted
        return true
    }

    override fun startPCMCapture() {
//        if (scenario != RtcScenario.Room) {
//            return
//        }
//        if (isStartedPCMData) {
//            return
//        }
//        isStartedPCMData = true
//        val format = TRTCAudioFrameCallbackFormat()
//        format.sampleRate = TRTCCloudDef.TRTCAudioSampleRate16000
//        format.channel = 1
//        format.samplesPerCall = 160
//        rtcEngine.setCapturedAudioFrameCallbackFormat(format)
//        rtcEngine.setAudioFrameListener(iAudioFrameListener)
    }

    override fun stopPCMCapture() {
//        if (!isStartedPCMData) {
//            return
//        }
//        isStartedPCMData = false
//        rtcEngine.setAudioFrameListener(null)
    }

    override fun startPlayingStream(
        uid: String,
        view: View?,
    ): Boolean {
        if (view == null) {
            return false
        } else {
            val param = TRTCRenderParams()
            param.fillMode = TRTCCloudDef.TRTC_VIDEO_RENDER_MODE_FILL
            param.mirrorType = TRTCCloudDef.TRTC_VIDEO_MIRROR_TYPE_DISABLE
            rtcEngine.setRemoteRenderParams(uid, TRTCCloudDef.TRTC_VIDEO_STREAM_TYPE_BIG, param)
            rtcEngine.startRemoteView(
                uid,
                TRTCCloudDef.TRTC_VIDEO_STREAM_TYPE_BIG,
                TXCloudVideoView(view.context).also {
                    it.addVideoView(view as TextureView)
                },
            )
        }
        return true
    }

    override fun stopPlayingStream(uid: String): Boolean {
        rtcEngine.stopRemoteView(uid, TRTCCloudDef.TRTC_VIDEO_STREAM_TYPE_BIG)
        return true
    }

    private var previewView: View? = null

    private var useFront = true

    override fun startVideoPreview(view: View): Boolean {
        val param = TRTCRenderParams()
        param.fillMode = TRTCCloudDef.TRTC_VIDEO_RENDER_MODE_FILL
        param.mirrorType = TRTCCloudDef.TRTC_VIDEO_MIRROR_TYPE_AUTO
        rtcEngine.setLocalRenderParams(param)

        previewView = view
        rtcEngine.startLocalPreview(
            useFront,
            TXCloudVideoView(view.context).also {
                it.addVideoView(view as TextureView)
            },
        )
        return true
    }

    override fun stopVideoPreview(): Boolean {
        rtcEngine.stopLocalPreview()
        return true
    }

    override fun mutePublishStreamVideo(muted: Boolean): Boolean {
        rtcEngine.muteLocalVideo(TRTCCloudDef.TRTC_VIDEO_STREAM_TYPE_BIG, muted)
        return true
    }

    override fun enableCamera(enabled: Boolean): Boolean {
        if (enabled) {
            previewView?.also {
                startVideoPreview(it)
            }
        } else {
            rtcEngine.stopLocalPreview()
        }
        return true
    }

    override fun useFrontCamera(front: Boolean): Boolean {
        useFront = front
        return rtcEngine.deviceManager.switchCamera(front) == 0
    }

    override fun setEnableSpeakerphone(enabled: Boolean): Boolean {
        if (enabled) {
            rtcEngine.setAudioRoute(TRTCCloudDef.TRTC_AUDIO_ROUTE_SPEAKER)
        } else {
            rtcEngine.setAudioRoute(TRTCCloudDef.TRTC_AUDIO_ROUTE_EARPIECE)
        }
        return true
    }

    override fun muteRemoteAudioStream(
        uid: String,
        muted: Boolean,
    ): Boolean {
        rtcEngine.muteRemoteAudio(uid, muted)
        return true
    }

    override fun startSubscribingStream(): Boolean {
        rtcEngine.muteAllRemoteAudio(false)
        return true
    }

    override fun stopSubscribingStream(): Boolean {
        rtcEngine.muteAllRemoteAudio(true)
        return true
    }

    override fun isSpeakerphoneEnabled(): Boolean = true

    override fun isLocalAudioMuted(): Boolean = audioMuted

    override fun renewToken(
        channelId: String,
        token: String,
    ): Boolean {
        // nothing
        return true
    }

    override fun onEnterRoom(result: Long) {
        if (result > TXLiteAVCode.ERR_NULL) {
            if (tempCurRole == TRTCCloudDef.TRTCRoleAudience && tempSettingRole == TRTCCloudDef.TRTCRoleAnchor) {
                // 第一次设置身份，不会触发onSwitchRole回调
                rtcEngine.startLocalAudio(config?.quality ?: TRTCCloudDef.TRTC_AUDIO_QUALITY_DEFAULT)
                eventHandle.onClientRoleChanged(tempCurRole.mapRoleValue(), tempSettingRole.mapRoleValue())
                tempCurRole = tempSettingRole
            }
            eventHandle.onJoinChannelSuccess()
        } else {
            eventHandle.onJoinChannelFailed()
        }
    }

    override fun onSwitchRole(
        errCode: Int,
        errMsg: String?,
    ) {
        if (errCode == TXLiteAVCode.ERR_NULL) {
            eventHandle.onClientRoleChanged(tempCurRole.mapRoleValue(), tempSettingRole.mapRoleValue())
            tempCurRole = tempSettingRole
        } else {
            eventHandle.onClientRoleChangeFailed(errCode, tempCurRole.mapRoleValue())
        }
    }

    override fun onRemoteUserEnterRoom(userId: String) {
        val uid = userId
        eventHandle.onUserJoined(uid, uid)
        eventHandle.onPlayerRenderStreamFirstFrame(userId, userId, true)
    }

    override fun onRemoteUserLeaveRoom(
        userId: String,
        reason: Int,
    ) {
        val uid = userId
        eventHandle.onUserOffline(uid, uid, reason)
    }

    override fun onUserAudioAvailable(
        userId: String,
        available: Boolean,
    ) {
        val uid = userId
        if (available) {
            eventHandle.onRemoteAudioStateChanged(uid, uid, false)
        } else {
            eventHandle.onRemoteAudioStateChanged(uid, uid, true)
        }
    }

    override fun onUserVoiceVolume(
        userVolumes: ArrayList<TRTCCloudDef.TRTCVolumeInfo>?,
        totalVolume: Int,
    ) {
        if (userVolumes.isNullOrEmpty()) {
            return
        }
        eventHandle.onAudioVolumeIndication(
            buildMap {
                userVolumes.forEach {
                    put(it.userId, it.volume.toFloat())
                }
            },
        )
    }

    override fun onUserVideoAvailable(
        userId: String,
        available: Boolean,
    ) {
        val uid = userId
        if (available) {
            eventHandle.onRemoteVideoStateChanged(uid, uid, false)
        } else {
            eventHandle.onRemoteVideoStateChanged(uid, uid, true)
            rtcEngine.stopRemoteView(uid, TRTCCloudDef.TRTC_VIDEO_STREAM_TYPE_BIG)
        }
    }

    override fun onFirstAudioFrame(userId: String) {
        eventHandle.onPlayerRenderStreamFirstFrame(userId, userId, false)
    }

    override fun onFirstVideoFrame(
        userId: String?,
        streamType: Int,
        width: Int,
        height: Int,
    ) {
        if (userId.isNullOrEmpty()) {
            eventHandle.onPublisherCapturedVideoFirstFrame(streamType)
        } else {
            eventHandle.onPlayerRenderCameraFirstFrame(userId, userId)
        }
    }

    private fun Int.mapRoleValue() =
        if (this == TRTCCloudDef.TRTCRoleAnchor) {
            1
        } else {
            2
        }
}
