package com.buque.wakoo.rtc

import androidx.compose.runtime.snapshots.SnapshotStateMap
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.ext.runInMainThread
import com.buque.wakoo.utils.LogUtils
import com.tencent.trtc.TRTCCloud
import kotlinx.coroutines.Job
import java.lang.ref.WeakReference

private const val TAG = "app_rtc_manager"

private var lastRtc: WeakReference<AppRtcHelper>? = null

class AppRtcHelper constructor(
    private val factory: IRtcEngineFactory,
    private val disableOptimization: Boolean = true,
    private val reportVad: Boolean = false,
) : IRtcEventHandle {
    init {
        lastRtc?.get()?.levelChannel()
        lastRtc?.clear()
        lastRtc = WeakReference(this)
    }

    private val micMap = SnapshotStateMap<String, MutableMicState>()

    private val unSubscribeAudioSet = mutableSetOf<String>()

    private var micUserListFromServer: Set<String>? = null

    private var remoteStreamWhiteList: Set<String>? = null

    private val rtcEngine: IRtcEngine by lazy(LazyThreadSafetyMode.NONE) {
        try {
            factory.create(WakooApplication.instance, this)
        } catch (e: Exception) {
            micUserListFromServer = null
            remoteStreamWhiteList = null
            destroyRtcEngine()
            error("rtc engine is null $e")
        }
    }

    private val selfId = SelfUser?.id.orEmpty()

    private var callbacks: OnAction? = null

    fun joinChannel(
        channelId: String,
        token: String,
        upMic: Boolean = false,
        mute: Boolean = false,
        speaker: Boolean = true,
        onError: OnAction,
    ) {
        LogUtils.dTag("app_rtc_manager", "joinChannel")
        rtcEngine.joinChannel(channelId, token, upMic, mute, speaker)
        callbacks = onError
    }

    fun levelChannel() {
        LogUtils.dTag("app_rtc_manager", "levelChannel")
        rtcEngine.levelChannel()
        destroyRtcEngine()
    }

    fun upMic(token: String?) {
        rtcEngine.upMic(token)
    }

    fun downMic(token: String?) {
        rtcEngine.downMic(token)
    }

    fun toggleMuteAudio(): Boolean {
        val state = getMicStateById(selfId)
        val muted = !state.isMuted
        state.muteState.value = muted
        if (state.isInMic) {
            setEnableMuteAudio(muted)
        }
        return muted
    }

    fun setEnableMuteAudio(muted: Boolean) {
        LogUtils.dTag("app_rtc_manager", "setEnableMuteAudio $muted")
        rtcEngine.muteLocalAudioStream(muted)
    }

    fun getMicStateById(userId: String): MutableMicState =
        micMap.getOrPut(userId) {
            MutableMicState(userId, false)
        }

    fun updateRemoteAudioStream(userListFromServer: Set<String>) {
//        if (isDestroy) {
//            return
//        }
//        if (disableOptimization) {
//            return
//        }
//        rtcCoroutineScope.launch {
//            micUserListFromServer = userListFromServer
//            if (!state.joinState.value) { // 不在频道内
//                return@launch
//            }
//            for (id in userListFromServer) {
//                if (id != sUser.id && unSubscribeAudioSet.contains(id)) { // 在未订阅音频流列表
//                    unSubscribeAudioSet.remove(id)
//                    rtcEngine.muteRemoteAudioStream(id, false)
//                    LogUtils.i(TAG, "uid: $id, 订阅音频")
//                }
//            }
//
//            micList.forEach { (id, _) ->
//                if (id != sUser.id &&
//                    !unSubscribeAudioSet.contains(id) &&
//                    !userListFromServer.contains(id) &&
//                    remoteStreamWhiteList?.contains(id) != true
//                ) { // 取消订阅非主播的音频流
//                    unSubscribeAudioSet.add(id)
//                    rtcEngine.muteRemoteAudioStream(id.toString(), true)
//                    LogUtils.i(TAG, "uid: $id, 取消订阅音频")
//                }
//            }
//        }
    }

    override fun onJoinChannelSuccess() {
        callbacks = null
        LogUtils.dTag("app_rtc_manager", "onJoinChannelSuccess")
    }

    override fun onJoinChannelFailed() {
        callbacks?.invoke()
        callbacks = null
        LogUtils.dTag("app_rtc_manager", "onJoinChannelFailed")
    }

    override fun onUserJoined(
        uid: String,
        streamId: String,
    ) {
        LogUtils.dTag("app_rtc_manager", "onUserJoined $uid")
        getMicStateById(uid).isInMic = true
//        if (isDestroy) {
//            return
//        }
//        rtcCoroutineScope.launch {
//            LogUtils.i(TAG, "uid: $uid, streamId:$streamId 加入频道")
//            micList.put(uid, Mic(0, false))
//            if (disableOptimization) {
//                return@launch
//            }
//            if (micUserListFromServer?.contains(uid) != true && remoteStreamWhiteList?.contains(uid) != true) { // 不在主播列表, 取消订阅音频流
//                if (!unSubscribeAudioSet.contains(uid)) {
//                    unSubscribeAudioSet.add(uid)
//                    rtcEngine.muteRemoteAudioStream(streamId, true)
//                    LogUtils.i(TAG, "uid: $uid, 取消订阅音频")
//                }
//            } else {
//                if (unSubscribeAudioSet.contains(uid)) {
//                    unSubscribeAudioSet.remove(uid)
//                    rtcEngine.muteRemoteAudioStream(streamId, false)
//                    LogUtils.i(TAG, "uid: $uid, 订阅音频")
//                }
//            }
//        }
    }

    override fun onUserOffline(
        uid: String,
        streamId: String,
        reason: Int,
    ) {
        micMap.remove(uid)
        LogUtils.dTag("app_rtc_manager", "onUserOffline $uid $reason")
//        if (isDestroy) {
//            return
//        }
//        rtcCoroutineScope.launch {
//            LogUtils.i(TAG, "uid: $uid, 离开频道")
//            micList.remove(uid)
//            if (disableOptimization) {
//                return@launch
//            }
//            if (unSubscribeAudioSet.contains(uid)) {
//                unSubscribeAudioSet.remove(uid)
//            }
//        }
    }

    private var dispatchJob: Job? = null

    private val reportedSet by lazy {
        mutableSetOf<String>()
    }

    override fun onRemoteAudioStateChanged(
        uid: String,
        streamId: String,
        muted: Boolean,
    ) {
        getMicStateById(uid).muteState.value = muted
        LogUtils.dTag("app_rtc_manager", "onRemoteAudioStateChanged $uid $muted")
//        if (isDestroy) {
//            return
//        }
//        val userId = if (uid == "0") sUser.id else uid
//        rtcCoroutineScope.launch {
//            micList[userId]?.also {
//                if (it.mute != muted) {
//                    LogUtils.i(TAG, "uid: $userId, ${if (muted) "关麦" else "开麦"}")
//                    micList[userId] = it.copy(mute = muted)
//                    dispatchMicStatus()
//                }
//            }
//        }
    }

    override fun onAudioVolumeIndication(speakers: Map<String, Float>) {
        micMap.forEach { (key, value) ->
            value.volumeState.floatValue = speakers[key] ?: 0f
        }
    }

    override fun onClientRoleChanged(
        oldRole: Int,
        newRole: Int,
    ) {
        LogUtils.dTag("app_rtc_manager", "onClientRoleChanged $oldRole $newRole")
        if (newRole == 1) {
            val state = getMicStateById(selfId)
            state.isInMic = true
            setEnableMuteAudio(state.isMuted)
        } else {
            micMap.remove(selfId)
        }

//        if (isDestroy) {
//            return
//        }
//        val upMic = newRole == Constants.CLIENT_ROLE_BROADCASTER
//        rtcCoroutineScope.launch {
//            state.upMicState.apply {
//                if (value != upMic) {
//                    LogUtils.w(TAG, "rtc状态不正确, 麦位状态不一致1, expect: $value  actual: $upMic")
//                }
//                if (Status.InProgress != status) {
//                    LogUtils.w(TAG, "rtc状态不正确, 麦位状态不一致2, expect: ${Status.InProgress}  actual: $status")
//                }
//                LogUtils.i(TAG, "onClientRoleChanged: 切换角色成功，newRole: $newRole, upMic: $upMic")
//                state.upMicState = this.copy(upMic, Status.Done)
//            }
//            if (upMic) {
//                Log.d("AppAsrManager", "上麦")
//                micList.put(sUser.id, Mic(0, state.muteState.value))
//                if (autoPCMCapture) {
//                    startPCMCapture()
//                }
//            } else {
//                Log.d("AppAsrManager", "下麦")
//                micList.remove(sUser.id)
//                stopPCMCapture()
//            }
//            if (upMic && state.muteState.value != rtcEngine.isLocalAudioMuted()) {
//                setEnableMuteAudio(state.muteState.value)
//            }
//        }
    }

    override fun onClientRoleChangeFailed(
        reason: Int,
        currentRole: Int,
    ) {
        LogUtils.dTag("app_rtc_manager", "onClientRoleChangeFailed $currentRole $reason")
//        if (isDestroy) {
//            return
//        }
//        LogUtils.e(TAG, "onClientRoleChangeFailed, reason: $reason, currentRole: $currentRole")
//        val upMic = currentRole == Constants.CLIENT_ROLE_BROADCASTER
//        rtcCoroutineScope.launch {
//            state.upMicState.apply {
//                if (Status.InProgress != status) {
//                    LogUtils.w(TAG, "rtc状态不正确, 麦位状态不一致3, expect: ${Status.InProgress}  actual: $status")
//                }
//                state.upMicState = this.copy(upMic, Status.Done)
//            }
//        }
    }

    override fun onStreamQualityUpdate(
        uid: String,
        streamId: String,
        videoRecvFPS: Double,
        videoRenderFPS: Double,
        audioRecvFPS: Double,
        audioRenderFPS: Double,
        level: Int,
    ) = Unit

    override fun onRemoteVideoStateChanged(
        uid: String,
        streamId: String,
        muted: Boolean,
    ) = Unit

    override fun onPlayerRenderStreamFirstFrame(
        uid: String,
        streamId: String,
        video: Boolean,
    ) = Unit

    override fun onPlayerRenderCameraFirstFrame(
        uid: String,
        streamId: String,
    ) = Unit

    override fun onPublisherCapturedVideoFirstFrame(value: Int) = Unit

    override fun onTokenPrivilegeWillExpire() = Unit
}

private fun destroyRtcEngine() {
    runInMainThread {
        try {
            TRTCCloud.destroySharedInstance()
        } catch (e: Exception) {
        }
    }
}
