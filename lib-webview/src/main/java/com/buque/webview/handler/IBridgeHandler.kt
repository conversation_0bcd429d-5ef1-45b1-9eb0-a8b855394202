package com.buque.webview.handler

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.buque.webview.utils.LogUtils
import com.buque.webview.utils.scope
import com.smallbuer.jsbridge.core.BridgeHandler
import com.smallbuer.jsbridge.core.CallBackFunction
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.booleanOrNull
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.json.longOrNull

private val sJson =
    Json {
        ignoreUnknownKeys = true // 忽略 API 返回中未在数据模型中定义的字段
        isLenient = true // 允许一些不严格的 JSON 格式
        prettyPrint = true // 开发时可以设置为 true 来格式化打印 JSON 日志，发布时建议 false
        coerceInputValues = true // 如果API返回的类型与模型不完全匹配（例如string对应int），尝试转换
        encodeDefaults = true
        explicitNulls = false
    }

abstract class BaseBridgeHandler : BridgeHandler() {

    var tag = this::class.java.simpleName

    abstract fun handlerV2(context: Context, data: JsonObject, callback: CallBackFunction)

    final override fun handler(context: Context, data: String, callback: CallBackFunction) {
        LogUtils.d("BridgeHandler", "tag: ${tag}, receive data: $data")
        obtainCoroutineScope(context).launch(Dispatchers.Main) {
            try {
                if (data.isBlank()) {
                    handlerV2(context, buildJsonObject { }, callback)
                } else {
                    handlerV2(context, withContext(Dispatchers.IO) {
                        sJson.decodeFromString(data)
                    }, callback)
                }
            } catch (e: Exception) {
                LogUtils.e("BridgeHandler", e)
                callback.sendFailure(context, msg = e.message)
            }
        }
    }

    fun CallBackFunction.sendSuccess(context: Context, data: Any? = null, msg: String? = "success") {
        obtainCoroutineScope(context).launch(Dispatchers.IO) {
            try {
                val responseContent = sJson.encodeToString(JsBridgeResponse(0, msg, data))
                onCallBack(responseContent)
                LogUtils.i("BridgeHandler", "tag: ${tag}, success callback: $responseContent")
            } catch (e: Exception) {
                LogUtils.e("BridgeHandler", e)
            }
        }
    }

    fun CallBackFunction.sendFailure(context: Context, status: Int = -1, msg: String? = "failure") {
        obtainCoroutineScope(context).launch(Dispatchers.IO) {
            try {
                val responseContent = sJson.encodeToString(JsBridgeResponse(status, msg, null))
                onCallBack(responseContent)
                LogUtils.i("BridgeHandler", "tag: ${tag}, failure callback: $responseContent")
            } catch (e: Exception) {
                LogUtils.e("BridgeHandler", e)
            }
        }
    }

    protected fun JsonObject.getString(key: String): String? {
        return  get(key)?.jsonPrimitive?.contentOrNull
    }

    protected fun JsonObject.getInt(key: String): Int? {
        return get(key)?.jsonPrimitive?.intOrNull
    }

    protected fun JsonObject.getLong(key: String): Long? {
        return get(key)?.jsonPrimitive?.longOrNull
    }

    protected fun JsonObject.getBoolean(key: String): Boolean? {
        return get(key)?.jsonPrimitive?.booleanOrNull
    }

    protected fun JsonObject.getStringOrElse(key: String, default: String): String {
        return getString(key) ?: default
    }

    protected fun JsonObject.getIntOrElse(key: String, default: Int): Int {
        return getInt(key) ?: default
    }

    protected fun JsonObject.getLongOrElse(key: String, default: Long): Long {
        return getLong(key) ?: default
    }

    protected fun JsonObject.getBooleanOrElse(key: String, default: Boolean): Boolean {
        return getBoolean(key) ?: default
    }

    protected fun obtainCoroutineScope(context: Context): CoroutineScope {
        return (context as? LifecycleOwner)?.lifecycleScope ?: scope
    }
}

data class JsBridgeResponse<T>(
    val status: Int,
    val msg: String?,
    val data: T?,
)