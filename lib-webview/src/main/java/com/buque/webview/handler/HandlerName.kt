package com.buque.webview.handler

object HandlerName {
    const val PREFIX = "wakoo"
    const val PLATFORM = "${PREFIX}_app_platform"
    const val CHANNEL = "${PREFIX}_app_channel"
    const val VERSION = "${PREFIX}_app_version"
    const val TOAST = "${PREFIX}_toast"
    const val FINISH = "${PREFIX}_finish"
    const val TITLE = "${PREFIX}_title_bar"
    const val IMMERSIVE = "${PREFIX}_immersive"
    const val JUMP_TASK = "jump"
    const val STATUS_BAR = "${PREFIX}_status_bar_height"
    const val NAVIGATION_BAR = "${PREFIX}_navigation_bar_height"
    const val GO_TO_PUBLIC = "go_to_public"
    const val REQUEST_ANDROID_PERMISSION = "${PREFIX}_android_request_permission"
    const val REQUEST_PERMISSION = "${PREFIX}_request_permission"
    const val CACHE_ENABLE = "${PREFIX}_android_cache_enable"
    const val APP_H5_JUMP_APP = "${PREFIX}_h5_jump_app"
    const val UCOO_PAY = "${PREFIX}_fk"
    const val RTM_CHANNEL = "${PREFIX}_rtm_channel"
    const val RC_CHANNEL = "${PREFIX}_rc_channel"
    const val APP_TENCENT_CHANNEL = "${PREFIX}_tencent_channel"
    const val WEB_VIDEO_EFFECT = "${PREFIX}_video_effect"
    const val API_ERROR = "${PREFIX}_api_error"
    const val EVENT_COPY = "${PREFIX}_copy"
    const val APP_ISM_DEVICE_ID = "${PREFIX}_ism_device_id"
    const val APP_IS_USING_VPN = "${PREFIX}_is_using_vpn"
    const val APP_OPEN_BROWSER = "${PREFIX}_open_browser"
    const val WARM_TIP  = "${PREFIX}_warm_top"
}