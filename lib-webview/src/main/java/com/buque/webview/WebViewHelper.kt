package com.buque.webview

import android.net.Uri
import android.webkit.WebView
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.core.net.toUri


fun String.getValueByUrl(key: String): String? {
    val uri = this.toUri()
    return uri.getQueryParameter(key)
}

fun String.putValueByUrl(key: String, value: String): String {
    val builder = this.toUri().buildUpon()
    builder.appendQueryParameter(key, value)
    return builder.build().toString()
}

fun String.buildUrl(builderUri: Uri.Builder.() -> Unit): String {
    val builder = this.toUri().buildUpon()
    builder.apply(builderUri)
    return builder.build().toString()
}

fun String.replaceValueByUrl(key: String, value: String): String {
    return replace("($key=[^&]*)".toRegex(), "$key=$value")
}

fun String.addTitleByUrl(title: String): String {
    return putValueByUrl("title", title)
}

fun String.cacheEnableByUrl(enable: Boolean): String {
    return putValueByUrl("cacheEnable", enable.toString())
}
