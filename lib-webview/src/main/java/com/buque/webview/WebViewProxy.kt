package com.buque.webview

import android.content.Context
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebView.setWebContentsDebuggingEnabled
import com.buque.webview.client.LibWebChromeClient
import com.buque.webview.client.LibWebViewClient
import com.buque.webview.utils.LogUtils
import com.kevinnzou.web.AccompanistWebChromeClient
import com.kevinnzou.web.AccompanistWebViewClient
import com.smallbuer.jsbridge.core.Bridge
import com.smallbuer.jsbridge.core.BridgeHandler
import com.smallbuer.jsbridge.core.BridgeTiny

class WebViewProxy(
    private val eventHandlers: List<Pair<String, BridgeHandler>> = emptyList(),
    private val onLackCameraPermission: () -> Unit = {}
) {

    private lateinit var bridgeTiny: BridgeTiny
    val client: AccompanistWebViewClient by lazy { LibWebViewClient() }

    val chromeClient: AccompanistWebChromeClient by lazy { LibWebChromeClient(onLackCameraPermission) }

    val factory: (Context) -> WebView = {
        val webView = AppBridgeWebView(it).apply {
            this.clearCache(true)
            settings.useWideViewPort = true
            settings.cacheMode = WebSettings.LOAD_NO_CACHE
            settings.javaScriptEnabled = true
            settings.javaScriptCanOpenWindowsAutomatically = true
            if (Bridge.INSTANCE.debug) {
                setWebContentsDebuggingEnabled(true)
            }
        }

        bridgeTiny = BridgeTiny(webView)

        webView.bridgeTiny = bridgeTiny
        eventHandlers.forEach { pair ->
            webView.addHandlerLocal(pair.first, pair.second)
        }
        if (Bridge.INSTANCE.debug) {
            LogUtils.i("Bridge", "bridgeTiny handlers:${bridgeTiny.messageHandlers.keys}")
            LogUtils.i("Bridge", "webview handlers:${webView.localMessageHandlers.keys}")
        }
        (client as? LibWebViewClient)?.bridge = bridgeTiny
        (chromeClient as? LibWebChromeClient)?.bridge = bridgeTiny
        webView
    }

    val onCreated: (WebView) -> Unit = {
        with(it.settings) {
            setSupportZoom(false)
            builtInZoomControls = false
            defaultTextEncodingName = "utf-8"
            defaultFontSize = 14
            layoutAlgorithm = android.webkit.WebSettings.LayoutAlgorithm.SINGLE_COLUMN
            // 两者都可以
            mixedContentMode = mixedContentMode
            mixedContentMode = android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            useWideViewPort = false
            domStorageEnabled = true
            useWideViewPort = true
            cacheMode = WebSettings.LOAD_NO_CACHE
            javaScriptCanOpenWindowsAutomatically = true
        }

        with(it) {
            clearCache(true)
            if (Bridge.INSTANCE.debug) {
                setWebContentsDebuggingEnabled(true)
            }
            android.webkit.CookieManager.getInstance().setAcceptThirdPartyCookies(this, true)
            setVerticalScrollbarOverlay(false)
            isHorizontalScrollBarEnabled = false
            setHorizontalScrollbarOverlay(false)
            overScrollMode = WebView.OVER_SCROLL_NEVER
            isFocusable = true
            isVerticalScrollBarEnabled = false
            isHorizontalScrollBarEnabled = false

            WBH5FaceVerifySDK.getInstance().setWebViewSettings(this, context.applicationContext)
        }


    }
    val onDispose: (WebView) -> Unit = {}


}

