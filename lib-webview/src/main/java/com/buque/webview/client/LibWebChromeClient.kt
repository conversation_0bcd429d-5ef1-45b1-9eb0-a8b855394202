package com.buque.webview.client

import android.Manifest
import android.app.Activity
import android.content.pm.PackageManager
import android.net.Uri
import android.webkit.JsPromptResult
import android.webkit.PermissionRequest
import android.webkit.ValueCallback
import android.webkit.WebView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.buque.webview.WBH5FaceVerifySDK
import com.buque.webview.asIWebVIew
import com.buque.webview.utils.scope
import com.kevinnzou.web.AccompanistWebChromeClient
import com.smallbuer.jsbridge.core.BridgeLog
import com.smallbuer.jsbridge.core.BridgeTiny
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


private const val TAG = "BridgeWebViewChromeClient"

open class LibWebChromeClient(private val onLackCameraPermission: () -> Unit = {}) :
    AccompanistWebChromeClient() {

    var bridge: BridgeTiny? = null

    override fun onJsPrompt(
        view: WebView?,
        url: String?,
        message: String?,
        defaultValue: String?,
        result: JsPromptResult
    ): Boolean {
        BridgeLog.d(TAG, "message->" + message);
        view.asIWebVIew()?.let {
            bridge?.onJsPrompt(it, message)
        }
        result.confirm("do")
        return true;
    }

    override fun onPermissionRequest(request: PermissionRequest?) {
        if (request != null && request.origin != null //判断是否为腾讯H5刷脸验证
            && WBH5FaceVerifySDK.getInstance().isTencentH5FaceVerify(request.origin.toString())
        ) {
        } else {
            scope.launch(Dispatchers.Main) {
                request?.grant(request.resources)
            }
        }
    }

    override fun onShowFileChooser(
        webView: WebView,
        filePathCallback: ValueCallback<Array<Uri>>,
        fileChooserParams: FileChooserParams
    ): Boolean {
        if (WBH5FaceVerifySDK.getInstance().isTencentH5FaceVerify(webView, fileChooserParams, null)) {
            val activity = webView.context as Activity
            if (checkPermission(activity)) {
                return WBH5FaceVerifySDK.getInstance()
                    .recordVideoForApi21(webView, filePathCallback, activity, fileChooserParams)
            }
        }
        return true
    }

    private fun checkPermission(activity: Activity): Boolean {
        if (ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.CAMERA
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            // 进入这儿表示没有权限
            if (ActivityCompat.shouldShowRequestPermissionRationale(activity, Manifest.permission.CAMERA)) {
                onLackCameraPermission()
                // 提示已经禁止
                return false
            } else {
                ActivityCompat.requestPermissions(activity, arrayOf<String>(Manifest.permission.CAMERA), 100)
                return false
            }
        } else {
            return true
        }
    }
}