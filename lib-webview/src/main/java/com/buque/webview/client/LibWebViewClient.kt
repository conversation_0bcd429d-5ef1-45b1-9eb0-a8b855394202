package com.buque.webview.client

import android.webkit.WebView
import com.buque.webview.asIWebVIew
import com.kevinnzou.web.AccompanistWebViewClient
import com.smallbuer.jsbridge.core.BridgeTiny

open class LibWebViewClient : AccompanistWebViewClient() {
    var bridge: BridgeTiny? = null

    override fun onPageFinished(view: WebView, url: String?) {
        super.onPageFinished(view, url)
        (view.asIWebVIew())?.also {
            bridge?.webViewLoadJs(it)
        }
    }
}