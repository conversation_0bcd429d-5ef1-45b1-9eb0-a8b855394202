```java
// PayChannel = Enum((  # 支付渠道，程序购买的支付渠道
//     (1, "GOOGLE_IAP", "Google应用内购买"),
//     (2, "APPLE_IAP", "Apple应用内购买"),
//     (3, "WXPAY", "微信支付"),
//     (4, "ALIPAY", "支付宝"),
//     (5, "PAYERMAX", "PAYERMAX"),
//     (6, "JKOPAY", "街口支付"),  # 目前只支持台湾
//     (7, "LINEPAY", "LinePay"),  # 目前只支持台湾
//     (8, "PAYPAL", "PayPal"),  # 目前只支持美金，全球
//     (9, "AGENT", "Agent"),  # 代充
// ))

// PayCallType = Enum((
//     (1, "GOOGLE_SDK", "谷歌SDK"),
//     (2, "APPLE_SDK", "苹果SDK"),
//     (3, "WXPAY_SDK", "微信SDK"),
//     (4, "LINK_WEB", "浏览器打开链接"),
//     (5, "LINK_WEB1", "浏览器打开链接(适配客户端)"),
//     (6, "LINK_WEBVIEW", "webview打开链接"),
//     (7, "ORDER_LINK", "获取订单链接"),
//     (8, "AGENT", "代充"),
// ))
```