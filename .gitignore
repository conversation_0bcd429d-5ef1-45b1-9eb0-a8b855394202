# Created by https://www.toptal.com/developers/gitignore/api/android,kotlin,gradle
# Edit at https://www.toptal.com/developers/gitignore?templates=android,kotlin,gradle

### Android ###
# Built application files
*.apk
*.aar
*.ap_
*.aab

# Files for the ART/Dalvik VM
*.dex

# Java class files
*.class

# Generated files
bin/
gen/
out/

# Gradle files
.gradle/
build/

# Local configuration file (sdk path, etc)
local.properties

# Proguard folder generated by Eclipse
proguard/

# Log Files
*.log

# Android Studio Navigation editor temp files
.navigation/

# Android Studio captures folder
captures/

# Keystore files
# Uncomment the following lines if you do not want to check your keystore files in.
#*.jks
#*.keystore

# External native build folder generated in Android Studio 2.2 and later
.externalNativeBuild/
.cxx/

# Google Services (e.g. APIs or Firebase)
google-services.json

# Freeline
freeline.py
freeline/
freeline_project_description.json

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output
fastlane/readme.md

#HEAP Stacks
*.hprof

# Crashlytics plugin
com_crashlytics_export_strings.xml

### Gradle ###
.gradle

# Ignore Gradle GUI config
gradle-app.setting

# Avoid ignoring Gradle wrapper jar file (.jar files are usually ignored)
!gradle-wrapper.jar

# Avoid ignoring Gradle wrapper properties file (wrapper block needs this)
!gradle-wrapper.properties

# Cache files for Kotlin DSL
.gradle-kotlin-dsl/

# Ignore Multiple Android Studio windows
.idea/navEditor.xml

# Kotlin
*.jar
*.war
*.ear
*.kdoc
*.tmp
.project
.classpath

# Compiled class files
*.classes

# Compiled sketch files
*.pde

# Log files

# Package files
*.nar
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*

# IntelliJ
*.iml
.idea/workspace.xml
.idea/tasks.xml
.idea/gradle.xml
.idea/assetWizardSettings.xml
.idea/dictionaries
.idea/libraries
.idea/caches
.idea/modules.xml
.idea/deploymentTargetSelector.xml


# macOS
.DS_Store
/.idea/
/.idea/*
/.kotlin/
/.cursor/
