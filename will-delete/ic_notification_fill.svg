<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="notification-2-fill">
<g id="Vector" filter="url(#filter0_i_241_12574)">
<path d="M22 20H2V18H3V11.0314C3 6.04348 7.02944 2 12 2C16.9706 2 21 6.04348 21 11.0314V18H22V20ZM9.5 21H14.5C14.5 22.3807 13.3807 23.5 12 23.5C10.6193 23.5 9.5 22.3807 9.5 21Z" fill="url(#paint0_radial_241_12574)"/>
</g>
</g>
<defs>
<filter id="filter0_i_241_12574" x="1.5" y="1" width="20.5" height="22.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.5" dy="-1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.70391 0 0 0 0 0.00892052 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_241_12574"/>
</filter>
<radialGradient id="paint0_radial_241_12574" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(18.5 5) rotate(109.359) scale(19.6087 18.2406)">
<stop stop-color="#FFFAE0"/>
<stop offset="1" stop-color="#FFC02E"/>
</radialGradient>
</defs>
</svg>
